/* empty css             *//* empty css                *//* empty css               *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css               *//* empty css                  */import{l as t,G as a,A as e,ag as s,r as l,c as r,o,m as n,C as i,q as u,E as c,F as d,Y as p}from"./vue-vendor-BcnDv-68.js";import{_ as m,n as h}from"./index-eUTsTR3J.js";import{V as v,X as f,ai as g,aj as _,ak as b,Z as j,a1 as w,U as y,Y as T}from"./element-plus-C2UshkXo.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const R={class:"navigation-performance-test"},k={class:"card-header"},H={key:0,class:"test-results"},M={class:"navigation-test"},C={class:"test-buttons"},P={class:"optimization-stats"},S={class:"stat-item"},z={class:"stat-value"},x={class:"stat-item"},O={class:"stat-value"},U={class:"stat-item"},q={class:"stat-value"},A={class:"stat-item"},E={class:"stat-value"};const I=m({__name:"NavigationPerformanceTest",setup(t,{expose:a}){a();const e=s(),n=l([]),i=l(""),u=l({preloaded:0,loading:0,routes:[]}),c=[{name:"仪表板",path:"/admin/dashboard"},{name:"用户管理",path:"/admin/users/list"},{name:"群组管理",path:"/admin/community/groups"},{name:"财务管理",path:"/admin/finance/dashboard"},{name:"防红系统",path:"/admin/anti-block/dashboard"}],d=r(()=>{if(0===n.value.length)return 0;const t=n.value.reduce((t,a)=>t+a.loadTime,0);return Math.round(t/n.value.length)}),p=r(()=>{if(0===n.value.length)return 0;const t=n.value.filter(t=>t.cacheHit).length;return Math.round(t/n.value.length*100)}),m=()=>{u.value=h.getStats()};o(()=>{m(),setInterval(m,2e3)});const v={router:e,testResults:n,currentTest:i,optimizationStats:u,testRoutes:c,averageLoadTime:d,cacheHitRate:p,runPerformanceTest:async()=>{n.value=[];for(const a of c){const s=performance.now();try{i.value=a.path,await e.push(a.path),await new Promise(t=>setTimeout(t,100));const t=performance.now(),l=Math.round(t-s);n.value.push({route:a.name,loadTime:l,status:"success",cacheHit:h.preloadCache.has(a.path)})}catch(t){const e=performance.now(),l=Math.round(e-s);n.value.push({route:a.name,loadTime:l,status:"error",cacheHit:!1})}i.value=""}f.success("性能测试完成")},navigateToRoute:async t=>{const a=performance.now();i.value=t;try{await e.push(t);const s=performance.now(),l=Math.round(s-a);f.success(`导航完成，耗时: ${l}ms`)}catch(s){f.error("导航失败")}finally{i.value=""}},updateOptimizationStats:m,ref:l,computed:r,onMounted:o,get useRouter(){return s},get ElMessage(){return f},get navigationOptimizer(){return h}};return Object.defineProperty(v,"__isScriptSetup",{enumerable:!1,value:!0}),v}},[["render",function(s,l,r,o,m,h){const f=w,I=_,L=b,N=g,Y=T,D=y,F=v;return n(),t("div",R,[a(F,{class:"test-card"},{header:e(()=>[u("div",k,[l[1]||(l[1]=u("h2",null,"🚀 导航性能测试",-1)),a(f,{type:"primary",onClick:o.runPerformanceTest},{default:e(()=>l[0]||(l[0]=[c(" 开始测试 ",-1)])),_:1,__:[0]})])]),default:e(()=>[o.testResults.length>0?(n(),t("div",H,[l[2]||(l[2]=u("h3",null,"测试结果",-1)),a(N,{data:o.testResults,style:{width:"100%"}},{default:e(()=>[a(I,{prop:"route",label:"路由",width:"200"}),a(I,{prop:"loadTime",label:"加载时间(ms)",width:"120"}),a(I,{prop:"status",label:"状态",width:"100"},{default:e(({row:t})=>[a(L,{type:"success"===t.status?"success":"danger"},{default:e(()=>[c(j(t.status),1)]),_:2},1032,["type"])]),_:1}),a(I,{prop:"cacheHit",label:"缓存命中",width:"100"},{default:e(({row:t})=>[a(L,{type:t.cacheHit?"success":"info"},{default:e(()=>[c(j(t.cacheHit?"是":"否"),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])])):i("",!0),u("div",M,[l[3]||(l[3]=u("h3",null,"实时导航测试",-1)),u("div",C,[(n(),t(d,null,p(o.testRoutes,t=>a(f,{key:t.path,onClick:a=>o.navigateToRoute(t.path),loading:o.currentTest===t.path},{default:e(()=>[c(j(t.name),1)]),_:2},1032,["onClick","loading"])),64))])]),u("div",P,[l[8]||(l[8]=u("h3",null,"导航优化统计",-1)),a(D,{gutter:20},{default:e(()=>[a(Y,{span:6},{default:e(()=>[u("div",S,[u("div",z,j(o.optimizationStats.preloaded),1),l[4]||(l[4]=u("div",{class:"stat-label"},"预加载路由",-1))])]),_:1}),a(Y,{span:6},{default:e(()=>[u("div",x,[u("div",O,j(o.averageLoadTime)+"ms",1),l[5]||(l[5]=u("div",{class:"stat-label"},"平均加载时间",-1))])]),_:1}),a(Y,{span:6},{default:e(()=>[u("div",U,[u("div",q,j(o.cacheHitRate)+"%",1),l[6]||(l[6]=u("div",{class:"stat-label"},"缓存命中率",-1))])]),_:1}),a(Y,{span:6},{default:e(()=>[u("div",A,[u("div",E,j(o.testResults.length),1),l[7]||(l[7]=u("div",{class:"stat-label"},"测试次数",-1))])]),_:1})]),_:1})])]),_:1})])}],["__scopeId","data-v-752fab0c"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/NavigationPerformanceTest.vue"]]);export{I as default};
