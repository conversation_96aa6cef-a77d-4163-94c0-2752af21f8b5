<script setup>
// 页面需要认证
definePageMeta({
  middleware: 'auth'
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-2xl font-bold text-gray-900">
                欢迎回来，{{ user?.nickname || '用户' }}！
              </h1>
              <p class="mt-1 text-sm text-gray-600">
                这是您的控制台概览，{{ new Date().toLocaleDateString() }}
              </p>
            </div>
            <div class="flex items-center space-x-4">
              <button 
                @click="loadDashboardData"
                :disabled="loading"
                class="btn btn-secondary"
              >
                <svg class="w-4 h-4 mr-2 animate-spin" v-if="loading" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>{{ loading ? '刷新中...' : '刷新数据' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 加载状态 -->
      <div v-if="loading && !stats" class="text-center py-12">
        <div class="spinner mx-auto mb-4"></div>
        <p class="text-gray-600">正在加载仪表盘数据...</p>
      </div>

      <!-- 仪表盘内容 -->
      <div v-else-if="stats" class="space-y-8">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="card hover:shadow-lg transition-shadow">
            <p class="text-sm font-medium text-gray-600">账户余额</p>
            <p class="text-2xl font-bold text-gray-900 mt-1">¥{{ stats.balance?.toFixed(2) || '0.00' }}</p>
            <p class="text-xs text-gray-500 mt-1">冻结: ¥{{ stats.frozen_balance?.toFixed(2) || '0.00' }}</p>
          </div>
          <div class="card hover:shadow-lg transition-shadow">
            <p class="text-sm font-medium text-gray-600">今日收益</p>
            <p class="text-2xl font-bold text-gray-900 mt-1">¥{{ stats.today_income?.toFixed(2) || '0.00' }}</p>
            <p class="text-xs text-gray-500 mt-1">昨日: ¥{{ stats.yesterday_income?.toFixed(2) || '0.00' }}</p>
          </div>
          <div class="card hover:shadow-lg transition-shadow">
            <p class="text-sm font-medium text-gray-600">总订单数</p>
            <p class="text-2xl font-bold text-gray-900 mt-1">{{ stats.total_orders || 0 }}</p>
            <p class="text-xs text-gray-500 mt-1">今日: {{ stats.today_orders || 0 }}</p>
          </div>
          <div class="card hover:shadow-lg transition-shadow">
            <p class="text-sm font-medium text-gray-600">下级用户</p>
            <p class="text-2xl font-bold text-gray-900 mt-1">{{ stats.total_children || 0 }}</p>
            <p class="text-xs text-gray-500 mt-1">总群组: {{ stats.total_groups || 0 }}</p>
          </div>
        </div>

        <!-- 收益图表与快捷操作 -->
        <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
          <!-- 收益趋势 -->
          <div class="card lg:col-span-3">
             <h3 class="text-lg font-semibold text-gray-900 mb-4">收益趋势 (近30天)</h3>
             <div class="h-64">
                <Line v-if="!chartLoading && chartData.labels.length" :data="chartData" :options="chartOptions" />
                <div v-else class="h-full flex items-center justify-center text-gray-500">
                  {{ chartLoading ? '图表加载中...' : '暂无图表数据' }}
                </div>
             </div>
          </div>

          <!-- 快捷操作 -->
          <div class="card lg:col-span-2">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">快捷操作</h3>
            <div class="grid grid-cols-2 gap-4">
               <NuxtLink to="/groups" class="quick-link bg-primary-50 hover:bg-primary-100 text-primary-700">
                  <span>群组管理</span>
               </NuxtLink>
               <NuxtLink to="/finance" class="quick-link bg-success-50 hover:bg-success-100 text-success-700">
                  <span>财务中心</span>
               </NuxtLink>
               <NuxtLink to="/promotion" class="quick-link bg-warning-50 hover:bg-warning-100 text-warning-700">
                  <span>推广中心</span>
               </NuxtLink>
               <NuxtLink to="/orders" class="quick-link bg-error-50 hover:bg-error-100 text-error-700">
                  <span>订单管理</span>
               </NuxtLink>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-12">
        <p class="text-red-500">加载数据失败: {{ error }}</p>
        <button @click="loadDashboardData" class="btn btn-primary mt-4">重试</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useAuthStore } from '~/stores/auth';
import { useApi } from '~/composables/useApi';
import { Line } from 'vue-chartjs';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

// 页面元数据
definePageMeta({
  middleware: 'auth'
});
useHead({
  title: '用户仪表盘 - 晨鑫流量变现系统',
});

// API 和状态
const { dashboard: dashboardApi } = useApi();
const authStore = useAuthStore();
const user = computed(() => authStore.user);

// 响应式数据
const loading = ref(true);
const chartLoading = ref(true);
const error = ref(null);
const stats = ref(null);

const chartData = ref({
  labels: [],
  datasets: [
    {
      label: '每日收入',
      backgroundColor: '#3b82f6',
      borderColor: '#3b82f6',
      data: [],
      tension: 0.3,
    },
  ],
});

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
};

// 方法
async function loadDashboardData() {
  loading.value = true;
  chartLoading.value = true;
  error.value = null;

  try {
    const [statsResponse, chartResponse] = await Promise.all([
      dashboardApi.getStats(),
      dashboardApi.getChartData('month') 
    ]);

    if (statsResponse.success) {
      stats.value = statsResponse.data;
    } else {
      throw new Error(statsResponse.message || '获取统计数据失败');
    }

    if (chartResponse.success && chartResponse.data.income) {
      chartData.value = {
        labels: chartResponse.data.income.labels,
        datasets: [
          {
            label: '每日收入',
            backgroundColor: 'rgba(59, 130, 246, 0.2)',
            borderColor: 'rgba(59, 130, 246, 1)',
            data: chartResponse.data.income.datasets[0].data,
            fill: true,
            tension: 0.3,
          },
        ],
      };
    } else {
       // 即使图表失败，也显示统计数据
      console.warn(chartResponse.message || '获取图表数据失败');
    }

  } catch (e) {
    console.error('加载仪表盘数据出错:', e);
    error.value = e.message;
  } finally {
    loading.value = false;
    chartLoading.value = false;
  }
}

// 生命周期钩子
onMounted(() => {
  loadDashboardData();
});
</script>

<style scoped>
.card {
  @apply bg-white p-6 rounded-lg shadow-sm;
}
.quick-link {
  @apply flex flex-col items-center justify-center p-4 rounded-lg transition-colors text-sm font-medium;
}
</style> 