module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    'plugin:vue/vue3-essential',
    'plugin:vue/vue3-strongly-recommended',
    'plugin:vue/vue3-recommended'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  plugins: ['vue'],
  rules: {
    // ==========================================
    // 晨鑫流量变现系统 开发规范 - ESLint规则
    // ==========================================
    
    // 调试代码检查
    'no-console': ['warn', { 
      allow: ['warn', 'error', 'info'] 
    }],
    'no-debugger': 'error',
    'no-alert': 'warn',
    
    // 代码质量
    'no-unused-vars': ['error', { 
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_' 
    }],
    'no-undef': 'error',
    'no-unreachable': 'error',
    'no-duplicate-imports': 'error',
    
    // 代码风格
    'indent': ['error', 2, { 
      SwitchCase: 1 
    }],
    'quotes': ['error', 'single', { 
      avoidEscape: true,
      allowTemplateLiterals: true 
    }],
    'semi': ['error', 'never'],
    'comma-dangle': ['error', 'never'],
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
    
    // Vue组件规范
    'vue/component-name-in-template-casing': ['error', 'PascalCase', {
      registeredComponentsOnly: false
    }],
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/prop-name-casing': ['error', 'camelCase'],
    'vue/attribute-hyphenation': ['error', 'always'],
    'vue/v-on-event-hyphenation': ['error', 'always'],
    
    // Vue模板规范
    'vue/html-indent': ['error', 2],
    'vue/html-quotes': ['error', 'double'],
    'vue/html-self-closing': ['error', {
      html: {
        void: 'never',
        normal: 'always',
        component: 'always'
      },
      svg: 'always',
      math: 'always'
    }],
    'vue/max-attributes-per-line': ['error', {
      singleline: 3,
      multiline: 1
    }],
    'vue/multiline-html-element-content-newline': 'error',
    'vue/singleline-html-element-content-newline': 'off',
    
    // Vue脚本规范
    'vue/script-setup-uses-vars': 'error',
    'vue/no-unused-components': 'warn',
    'vue/no-unused-vars': 'error',
    'vue/require-default-prop': 'error',
    'vue/require-prop-types': 'error',
    'vue/prefer-const-type-assertion': 'error',
    
    // Vue样式规范
    'vue/component-tags-order': ['error', {
      order: ['template', 'script', 'style']
    }],
    'vue/block-tag-newline': ['error', {
      singleline: 'always',
      multiline: 'always'
    }],
    
    // 命名规范
    'camelcase': ['error', { 
      properties: 'never',
      ignoreDestructuring: false 
    }],
    
    // 函数规范
    'func-style': ['error', 'expression'],
    'arrow-spacing': 'error',
    'arrow-parens': ['error', 'as-needed'],
    
    // 对象和数组规范
    'object-shorthand': 'error',
    'prefer-const': 'error',
    'prefer-template': 'error',
    
    // 注释规范
    'spaced-comment': ['error', 'always', {
      line: {
        markers: ['/'],
        exceptions: ['-', '+']
      },
      block: {
        markers: ['!'],
        exceptions: ['*'],
        balanced: true
      }
    }],
    
    // 空格和换行
    'no-multiple-empty-lines': ['error', { 
      max: 2,
      maxEOF: 1 
    }],
    'no-trailing-spaces': 'error',
    'eol-last': 'error',
    
    // 安全规范
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-script-url': 'error',
    
    // 性能规范
    'no-loop-func': 'error',
    'no-new-object': 'error',
    'no-new-wrappers': 'error',
    
    // Vue 3 Composition API规范
    'vue/no-setup-props-destructure': 'error',
    'vue/no-ref-as-operand': 'error',
    'vue/no-watch-after-await': 'error',
    'vue/no-lifecycle-after-await': 'error'
  },
  
  // 全局变量
  globals: {
    ElMessage: 'readonly',
    ElMessageBox: 'readonly',
    ElNotification: 'readonly',
    ElLoading: 'readonly'
  },
  
  // 忽略文件
  ignorePatterns: [
    'dist/',
    'node_modules/',
    '*.min.js',
    'public/',
    'coverage/'
  ],
  
  // 覆盖规则
  overrides: [
    {
      files: ['*.vue'],
      rules: {
        // Vue文件特殊规则
        'no-undef': 'off' // Vue模板中的变量由Vue处理
      }
    },
    {
      files: ['vite.config.js', 'vitest.config.js'],
      rules: {
        // 配置文件特殊规则
        'no-console': 'off'
      }
    },
    {
      files: ['src/utils/mock-api.js'],
      rules: {
        // Mock API文件特殊规则
        'no-console': ['warn', { 
          allow: ['warn', 'error'] 
        }]
      }
    }
  ]
}
