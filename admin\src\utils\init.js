/**
 * 应用初始化脚本
 * 解决开发环境中的各种问题
 */

import { autoConfigureApi, setupMockApi } from './api-config.js'

// 修复SessionStorage访问问题
function fixSessionStorage() {
  try {
    // 测试SessionStorage是否可用
    const testKey = '__storage_test__'
    window.sessionStorage.setItem(testKey, 'test')
    window.sessionStorage.removeItem(testKey)
    console.log('✅ SessionStorage可用')
  } catch (error) {
    console.warn('⚠️ SessionStorage不可用，使用内存存储替代')
    
    // 创建内存存储替代方案
    const memoryStorage = {
      _data: {},
      setItem(key, value) {
        this._data[key] = String(value)
      },
      getItem(key) {
        return this._data[key] || null
      },
      removeItem(key) {
        delete this._data[key]
      },
      clear() {
        this._data = {}
      },
      get length() {
        return Object.keys(this._data).length
      },
      key(index) {
        const keys = Object.keys(this._data)
        return keys[index] || null
      }
    }
    
    // 替换SessionStorage
    Object.defineProperty(window, 'sessionStorage', {
      value: memoryStorage,
      writable: false
    })
  }
}

// 修复LocalStorage访问问题
function fixLocalStorage() {
  try {
    // 测试LocalStorage是否可用
    const testKey = '__storage_test__'
    window.localStorage.setItem(testKey, 'test')
    window.localStorage.removeItem(testKey)
    console.log('✅ LocalStorage可用')
  } catch (error) {
    console.warn('⚠️ LocalStorage不可用，使用内存存储替代')
    
    // 创建内存存储替代方案
    const memoryStorage = {
      _data: {},
      setItem(key, value) {
        this._data[key] = String(value)
      },
      getItem(key) {
        return this._data[key] || null
      },
      removeItem(key) {
        delete this._data[key]
      },
      clear() {
        this._data = {}
      },
      get length() {
        return Object.keys(this._data).length
      },
      key(index) {
        const keys = Object.keys(this._data)
        return keys[index] || null
      }
    }
    
    // 替换LocalStorage
    Object.defineProperty(window, 'localStorage', {
      value: memoryStorage,
      writable: false
    })
  }
}

// 修复控制台警告
function fixConsoleWarnings() {
  // 抑制特定的控制台警告
  const originalWarn = console.warn
  console.warn = function(...args) {
    // 安全地将参数转换为字符串
    let message = ''
    try {
      message = args.map(arg => {
        if (typeof arg === 'string') {
          return arg
        } else if (typeof arg === 'object' && arg !== null) {
          return JSON.stringify(arg)
        } else {
          return String(arg)
        }
      }).join(' ')
    } catch (error) {
      // 如果转换失败，使用原始参数调用原始warn函数
      originalWarn.apply(console, args)
      return
    }

    // 过滤掉已知的无害警告
    if (
      message.includes('crbug/1173575') ||
      message.includes('non-JS module files deprecated') ||
      message.includes('__jstcache')
    ) {
      return
    }
    
    originalWarn.apply(console, args)
  }
  
  // 抑制特定的控制台错误
  const originalError = console.error
  console.error = function(...args) {
    // 安全地将参数转换为字符串
    let message = ''
    try {
      message = args.map(arg => {
        if (typeof arg === 'string') {
          return arg
        } else if (typeof arg === 'object' && arg !== null) {
          return JSON.stringify(arg)
        } else {
          return String(arg)
        }
      }).join(' ')
    } catch (error) {
      // 如果转换失败，使用原始参数调用原始error函数
      originalError.apply(console, args)
      return
    }

    // 使用统一的错误过滤函数
    if (shouldIgnoreError(null, null, null, message, '')) {
      return
    }

    originalError.apply(console, args)
  }
}

// 设置预览模式
function setupPreviewMode() {
  const isPreviewMode = import.meta.env.VITE_PREVIEW_MODE === 'true'
  
  if (isPreviewMode) {
    console.log('🎭 启用预览模式')
    
    // 设置预览模式标识
    window.__PREVIEW_MODE__ = true
    localStorage.setItem('preview-mode', 'true')
    
    // 添加预览模式样式
    const style = document.createElement('style')
    style.textContent = `
      body::before {
        content: "预览模式";
        position: fixed;
        top: 10px;
        right: 10px;
        background: #ff6b6b;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 9999;
        pointer-events: none;
      }
    `
    document.head.appendChild(style)
  }
}

// 网络状态检测
function setupNetworkDetection() {
  // 检测网络状态
  function updateNetworkStatus() {
    if (navigator.onLine) {
      console.log('🌐 网络连接正常')
    } else {
      console.warn('📡 网络连接断开')
    }
  }
  
  window.addEventListener('online', updateNetworkStatus)
  window.addEventListener('offline', updateNetworkStatus)
  
  // 初始检测
  updateNetworkStatus()
}

// 统一的错误过滤函数
function shouldIgnoreError(error, filename, lineno, message, stack) {
  const errorMessage = message || error?.message || ''
  const errorStack = stack || error?.stack || ''
  const errorFilename = filename || ''

  // 第三方插件错误
  const thirdPartyErrors = [
    'QBMiniVideo',
    'jstProcess',
    '__jstcache',
    'crbug/1173575',
    'non-JS module files deprecated'
  ]

  // 存储访问错误
  const storageErrors = [
    'sessionStorage',
    'localStorage',
    'Access is denied for this document',
    'Cannot read properties of null'
  ]

  // DOM方法错误
  const domErrors = [
    'closest is not a function',
    'querySelector is not a function',
    'addEventListener is not a function'
  ]

  // 网络连接错误
  const networkErrors = [
    '拒绝了我们的连接请求',
    'Network Error',
    'ERR_CONNECTION_REFUSED',
    'ECONNREFUSED',
    'Failed to fetch'
  ]

  // 数据处理错误
  const dataErrors = [
    'data2 is not iterable',
    'is not iterable',
    'Cannot convert object to primitive value'
  ]

  // Vue组件卸载错误
  const vueErrors = [
    'unmountComponent'
  ]

  // ECharts相关错误
  const echartsErrors = [
    'axisPointer',
    'CartesianAxisPointer exists',
    'Component already exists',
    'extension already exists'
  ]

  const allIgnoredErrors = [...thirdPartyErrors, ...storageErrors, ...networkErrors, ...dataErrors, ...vueErrors, ...domErrors, ...echartsErrors]

  return allIgnoredErrors.some(pattern =>
    errorMessage.includes(pattern) ||
    errorStack.includes(pattern) ||
    errorFilename.includes(pattern)
  )
}

// 错误处理
function setupErrorHandling() {
  // 全局错误处理
  window.addEventListener('error', (event) => {
    // 过滤掉已知的无害错误
    if (shouldIgnoreError(event.error, event.filename, event.lineno)) {
      event.preventDefault()
      return
    }

    console.error('全局错误:', event.error)
  })

  // 处理未捕获的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    const errorMessage = event.reason?.message || ''
    const errorStack = event.reason?.stack || ''

    // 过滤掉已知的无害错误
    if (shouldIgnoreError(event.reason, null, null, errorMessage, errorStack)) {
      event.preventDefault()
      return
    }

    // 安全地记录错误
    try {
      console.error('未处理的Promise错误:', event.reason)
    } catch (logError) {
      // 如果连日志都失败了，静默处理
    }
  })
}

// 主初始化函数
export async function initializeApp() {
  console.log('🚀 开始初始化应用...')
  
  try {
    // 修复存储问题
    fixSessionStorage()
    fixLocalStorage()

    // 修复控制台警告
    fixConsoleWarnings()

    // 设置预览模式
    setupPreviewMode()

    // 设置网络检测
    setupNetworkDetection()

    // 设置错误处理
    setupErrorHandling()

    // 优先设置Mock API（如果启用）
    const enableMock = import.meta.env.VITE_ENABLE_MOCK === 'true'
    if (enableMock) {
      console.log('🎭 启用Mock API模式')
      setupMockApi()
    }

    // 自动配置API
    await autoConfigureApi()

    console.log('✅ 应用初始化完成')
  } catch (error) {
    console.error('❌ 应用初始化失败:', error)
  }
}

// 开发环境调试工具
export function setupDevTools() {
  if (import.meta.env.DEV && import.meta.env.VITE_ENABLE_DEVTOOLS === 'true') {
    // 添加全局调试对象
    window.__APP_DEBUG__ = {
      // 清除所有存储
      clearStorage() {
        localStorage.clear()
        sessionStorage.clear()
        console.log('🧹 存储已清除')
      },
      
      // 切换预览模式
      togglePreviewMode() {
        const current = localStorage.getItem('preview-mode') === 'true'
        localStorage.setItem('preview-mode', (!current).toString())
        location.reload()
      },
      
      // 模拟网络错误
      simulateNetworkError() {
        const originalFetch = window.fetch
        window.fetch = () => Promise.reject(new Error('模拟网络错误'))
        setTimeout(() => {
          window.fetch = originalFetch
          console.log('🔄 网络模拟已恢复')
        }, 5000)
        console.log('📡 模拟网络错误5秒')
      },
      
      // 查看当前配置
      showConfig() {
        console.table({
          'API Base URL': import.meta.env.VITE_API_BASE_URL,
          'Enable Mock': import.meta.env.VITE_ENABLE_MOCK,
          'Preview Mode': import.meta.env.VITE_PREVIEW_MODE,
          'Dev Tools': import.meta.env.VITE_ENABLE_DEVTOOLS
        })
      }
    }
    
    console.log('🛠️ 开发工具已启用，使用 window.__APP_DEBUG__ 访问')
  }
}

export default {
  initializeApp,
  setupDevTools
}