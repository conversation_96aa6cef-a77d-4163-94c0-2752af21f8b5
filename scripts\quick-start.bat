@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    晨鑫流量变现系统 快速启动                      ║
echo ║                      Quick Start Script                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 设置颜色
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "RESET=[0m"

:: 检查Node.js
echo %BLUE%🔍 检查Node.js环境...%RESET%
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Node.js未安装，请先安装Node.js 18+%RESET%
    pause
    exit /b 1
)
echo %GREEN%✅ Node.js环境正常%RESET%

:: 检查PHP
echo %BLUE%🐘 检查PHP环境...%RESET%
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ PHP未安装，请先安装PHP 8.1+%RESET%
    pause
    exit /b 1
)
echo %GREEN%✅ PHP环境正常%RESET%

:: 检查Composer
echo %BLUE%📦 检查Composer...%RESET%
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Composer未安装，请先安装Composer%RESET%
    pause
    exit /b 1
)
echo %GREEN%✅ Composer正常%RESET%

:: 安装后端依赖
echo.
echo %BLUE%📦 安装后端依赖...%RESET%
if not exist "vendor" (
    echo %YELLOW%正在安装PHP依赖包...%RESET%
    composer install --optimize-autoloader
    if %errorlevel% neq 0 (
        echo %RED%❌ 后端依赖安装失败%RESET%
        pause
        exit /b 1
    )
) else (
    echo %GREEN%✅ 后端依赖已存在%RESET%
)

:: 配置环境文件
echo.
echo %BLUE%⚙️ 配置环境文件...%RESET%
if not exist ".env" (
    echo %YELLOW%复制环境配置文件...%RESET%
    copy ".env.example" ".env"
    echo %YELLOW%生成应用密钥...%RESET%
    php artisan key:generate
    echo %YELLOW%生成JWT密钥...%RESET%
    php artisan jwt:secret
) else (
    echo %GREEN%✅ 环境配置已存在%RESET%
)

:: 安装管理端依赖
echo.
echo %BLUE%🎨 安装管理端依赖...%RESET%
cd admin
if not exist "node_modules" (
    echo %YELLOW%正在安装管理端依赖包...%RESET%
    npm install
    if %errorlevel% neq 0 (
        echo %RED%❌ 管理端依赖安装失败%RESET%
        cd ..
        pause
        exit /b 1
    )
) else (
    echo %GREEN%✅ 管理端依赖已存在%RESET%
)
cd ..

:: 安装前端依赖
echo.
echo %BLUE%🌐 安装前端依赖...%RESET%
cd frontend
if not exist "node_modules" (
    echo %YELLOW%正在安装前端依赖包...%RESET%
    npm install
    if %errorlevel% neq 0 (
        echo %RED%❌ 前端依赖安装失败%RESET%
        cd ..
        pause
        exit /b 1
    )
) else (
    echo %GREEN%✅ 前端依赖已存在%RESET%
)
cd ..

:: 数据库迁移
echo.
echo %BLUE%🗄️ 数据库设置...%RESET%
echo %YELLOW%请确保数据库服务已启动并配置正确%RESET%
php artisan migrate:status >nul 2>&1
if %errorlevel% neq 0 (
    echo %YELLOW%运行数据库迁移...%RESET%
    php artisan migrate --seed
    if %errorlevel% neq 0 (
        echo %RED%❌ 数据库迁移失败，请检查数据库配置%RESET%
        echo %YELLOW%请手动配置.env文件中的数据库连接信息%RESET%
    )
) else (
    echo %GREEN%✅ 数据库已配置%RESET%
)

:: 构建前端资源
echo.
echo %BLUE%🏗️ 构建前端资源...%RESET%
echo %YELLOW%构建管理端...%RESET%
cd admin
npm run build
cd ..

echo %YELLOW%构建用户端...%RESET%
cd frontend
npm run build
cd ..

:: 启动服务
echo.
echo %GREEN%🚀 系统安装完成！%RESET%
echo.
echo %BLUE%启动选项：%RESET%
echo %YELLOW%1. 开发模式 - 启动所有开发服务器%RESET%
echo %YELLOW%2. 生产模式 - 启动生产服务器%RESET%
echo %YELLOW%3. 仅后端 - 只启动Laravel服务器%RESET%
echo %YELLOW%4. 系统检查 - 运行系统健康检查%RESET%
echo %YELLOW%5. 退出%RESET%
echo.

:menu
set /p choice="请选择启动选项 (1-5): "

if "%choice%"=="1" goto dev_mode
if "%choice%"=="2" goto prod_mode
if "%choice%"=="3" goto backend_only
if "%choice%"=="4" goto system_check
if "%choice%"=="5" goto end
echo %RED%无效选择，请重新输入%RESET%
goto menu

:dev_mode
echo.
echo %GREEN%🚀 启动开发模式...%RESET%
echo %BLUE%后端服务: http://localhost:8000%RESET%
echo %BLUE%管理端: http://localhost:3000%RESET%
echo %BLUE%用户端: http://localhost:3001%RESET%
echo.
echo %YELLOW%按 Ctrl+C 停止服务%RESET%
echo.

:: 启动后端服务
start "Laravel Server" cmd /k "php artisan serve --host=0.0.0.0 --port=8000"
timeout /t 2 /nobreak >nul

:: 启动管理端 (端口3000)
start "Admin Dev Server" cmd /k "cd admin && npm run dev"
timeout /t 2 /nobreak >nul

:: 启动用户端 (端口3001)
start "Frontend Dev Server" cmd /k "cd frontend && npm run dev"

echo %GREEN%✅ 所有开发服务器已启动%RESET%
goto end

:prod_mode
echo.
echo %GREEN%🚀 启动生产模式...%RESET%
echo %BLUE%服务地址: http://localhost:8000%RESET%
echo.

:: 优化缓存
php artisan optimize
php artisan config:cache
php artisan route:cache
php artisan view:cache

:: 启动生产服务器
start "Production Server" cmd /k "php artisan serve --host=0.0.0.0 --port=8000"

echo %GREEN%✅ 生产服务器已启动%RESET%
goto end

:backend_only
echo.
echo %GREEN%🚀 启动后端服务...%RESET%
echo %BLUE%服务地址: http://localhost:8000%RESET%
echo.

start "Laravel Server" cmd /k "php artisan serve"

echo %GREEN%✅ 后端服务器已启动%RESET%
goto end

:system_check
echo.
echo %BLUE%🔍 运行系统检查...%RESET%
node scripts/system-check.js
pause
goto menu

:end
echo.
echo %GREEN%感谢使用 晨鑫流量变现系统！%RESET%
echo %BLUE%访问地址：%RESET%
echo %YELLOW%  - 管理后台: http://localhost:8000/admin%RESET%
echo %YELLOW%  - 用户前端: http://localhost:3001%RESET%
echo %YELLOW%  - API文档: http://localhost:8000/api/documentation%RESET%
echo.
echo %BLUE%数据大屏访问：%RESET%
echo %YELLOW%  - 演示中心: http://localhost:3000/#/data-screen%RESET%
echo %YELLOW%  - Ultra版本: http://localhost:3000/#/data-screen/ultra%RESET%
echo %YELLOW%  - Enhanced版本: http://localhost:3000/#/data-screen/enhanced%RESET%
echo.
pause