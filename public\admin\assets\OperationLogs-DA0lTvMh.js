/* empty css             *//* empty css                   *//* empty css                 *//* empty css                        *//* empty css                       *//* empty css                 *//* empty css                   *//* empty css                 *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css                  *//* empty css                       *//* empty css               *//* empty css                *//* empty css                    */import{l as e,G as a,A as l,M as t,r as s,o,m as i,q as r,E as n,F as d,Y as u,z as p,B as c,C as m}from"./vue-vendor-BcnDv-68.js";import{H as g}from"./echarts-D6CUuNS9.js";import{_}from"./index-eUTsTR3J.js";import{V as y,U as f,W as b,ag as v,X as h,aU as w,aV as V,Y as C,Z as k,a2 as x,a3 as j,a5 as D,a6 as P,a0 as T,a4 as q,a1 as L,ai as S,aj as U,ak as z,a7 as N,an as F,ar as M,as as O,aZ as Y,_ as I,aL as W,aq as A}from"./element-plus-C2UshkXo.js";import"./utils-SdQ7DxjY.js";const B={class:"app-container"},H={class:"stat-item"},E={class:"stat-content"},Q={class:"stat-number"},J={class:"stat-item"},K={class:"stat-content"},Z={class:"stat-number"},G={class:"stat-item"},R={class:"stat-content"},X={class:"stat-number"},$={class:"stat-item"},ee={class:"stat-content"},ae={class:"stat-number"},le={class:"card-header"},te={class:"user-info"},se={class:"user-name"},oe={class:"user-role"},ie={class:"module-name"},re={class:"table-pagination"},ne={key:0,class:"log-detail"},de={class:"detail-section"},ue={key:0,class:"detail-section"},pe={class:"json-data"},ce={key:1,class:"detail-section"},me={class:"json-data"},ge={key:2,class:"detail-section"},_e={class:"dialog-footer"};const ye=_({__name:"OperationLogs",setup(e,{expose:a}){a();const l=t({total:25687,today:156,active_users:45,errors:12}),i=t({page:1,per_page:20,user_id:"",operation_type:"",module:"",status:"",date_range:[],ip:""}),r=s([{id:1,user:{nickname:"管理员",role_name:"超级管理员"},operation_type:"login",module:"system",description:"用户登录系统",status:"success",ip_address:"***********",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",execution_time:120,created_at:"2024-01-01 10:00:00",request_data:{username:"admin"},response_data:{success:!0}},{id:2,user:{nickname:"普通用户",role_name:"用户"},operation_type:"create",module:"user",description:"创建新用户",status:"success",ip_address:"***********",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",execution_time:350,created_at:"2024-01-01 09:30:00"}]),n=s([{id:1,nickname:"管理员"},{id:2,nickname:"普通用户"}]),d=t({visible:!1,log:null}),u=t({visible:!1}),p=t({type:"by_date",before_date:null,keep_count:1e4,status:""}),c=s({}),m=s({}),_=s(!1),y=s(!1),f=s(0),b=s("operation"),w=()=>{c.value={title:{text:"操作类型分布",left:"center"},tooltip:{trigger:"item"},series:[{name:"操作类型",type:"pie",radius:"50%",data:[{value:1048,name:"登录"},{value:735,name:"创建"},{value:580,name:"更新"},{value:484,name:"删除"},{value:300,name:"导出"}]}]},m.value={title:{text:"操作趋势",left:"center"},tooltip:{trigger:"axis"},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{name:"操作次数",type:"line",data:[120,132,101,134,90,230,210]}]}},V=e=>({operation:"操作日志",security:"安全日志",permission:"权限日志",system:"系统日志"}[e]||"操作日志"),C=e=>{_.value=!0,setTimeout(()=>{_.value=!1},500)},k=()=>{_.value=!0,setTimeout(()=>{_.value=!1,h.success("搜索完成")},1e3)},x=()=>{k()};o(()=>{f.value=r.value.length,w()});const j={logStats:l,queryParams:i,logs:r,users:n,detailDialog:d,cleanDialog:u,cleanForm:p,operationTypeChart:c,operationTrendChart:m,loading:_,cleaning:y,total:f,activeLogType:b,initCharts:w,getOperationTypeColor:e=>({login:"success",create:"primary",update:"warning",delete:"danger",export:"info",setting:"primary"}[e]||"info"),getOperationTypeName:e=>({login:"登录",create:"创建",update:"更新",delete:"删除",export:"导出",setting:"设置"}[e]||e),getModuleName:e=>({user:"用户管理",order:"订单管理",finance:"财务管理",distribution:"分销管理",system:"系统设置"}[e]||e),getStatusColor:e=>({success:"success",failed:"danger",warning:"warning"}[e]||"info"),getStatusName:e=>({success:"成功",failed:"失败",warning:"警告"}[e]||e),handleLogTypeChange:e=>{b.value=e,C(),h.info(`切换到${V(e)}`)},getLogTypeName:V,updateLogsByType:C,searchLogs:k,resetQuery:()=>{Object.assign(i,{page:1,per_page:20,user_id:"",operation_type:"",module:"",status:"",date_range:[],ip:""}),k()},exportLogs:()=>{h.success("导出任务已创建")},refreshLogs:x,clearLogs:()=>{u.visible=!0},viewDetails:e=>{d.log=e,d.visible=!0},deleteLog:e=>{v.confirm("确定要删除这条日志吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{h.success("删除成功"),x()})},executeClean:async()=>{y.value=!0;try{await new Promise(e=>setTimeout(e,2e3)),h.success("日志清理完成"),u.visible=!1,x()}catch(e){h.error("日志清理失败")}finally{y.value=!1}},handleSizeChange:e=>{i.per_page=e,k()},handleCurrentChange:e=>{i.page=e,k()},ref:s,reactive:t,onMounted:o,get ElMessage(){return h},get ElMessageBox(){return v},get VChart(){return g}};return Object.defineProperty(j,"__isScriptSetup",{enumerable:!1,value:!0}),j}},[["render",function(t,s,o,g,_,v){const h=V,ye=w,fe=y,be=C,ve=f,he=P,we=D,Ve=j,Ce=T,ke=q,xe=L,je=x,De=U,Pe=z,Te=S,qe=F,Le=O,Se=M,Ue=Y,ze=b,Ne=W,Fe=I,Me=A,Oe=N;return i(),e("div",B,[a(fe,{class:"log-type-card",style:{"margin-bottom":"20px"}},{default:l(()=>[a(ye,{modelValue:g.activeLogType,"onUpdate:modelValue":s[0]||(s[0]=e=>g.activeLogType=e),onTabChange:g.handleLogTypeChange},{default:l(()=>[a(h,{label:"操作日志",name:"operation"},{label:l(()=>s[16]||(s[16]=[r("span",null,[r("i",{class:"el-icon-operation"}),n(" 操作日志")],-1)])),_:1}),a(h,{label:"安全日志",name:"security"},{label:l(()=>s[17]||(s[17]=[r("span",null,[r("i",{class:"el-icon-lock"}),n(" 安全日志")],-1)])),_:1}),a(h,{label:"权限日志",name:"permission"},{label:l(()=>s[18]||(s[18]=[r("span",null,[r("i",{class:"el-icon-key"}),n(" 权限日志")],-1)])),_:1}),a(h,{label:"系统日志",name:"system"},{label:l(()=>s[19]||(s[19]=[r("span",null,[r("i",{class:"el-icon-monitor"}),n(" 系统日志")],-1)])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(ve,{gutter:20},{default:l(()=>[a(be,{span:6},{default:l(()=>[a(fe,{class:"stat-card"},{default:l(()=>[r("div",H,[s[21]||(s[21]=r("div",{class:"stat-icon total-icon"},[r("i",{class:"el-icon-document"})],-1)),r("div",E,[r("div",Q,k(g.logStats.total),1),s[20]||(s[20]=r("div",{class:"stat-label"},"总日志数",-1))])])]),_:1})]),_:1}),a(be,{span:6},{default:l(()=>[a(fe,{class:"stat-card"},{default:l(()=>[r("div",J,[s[23]||(s[23]=r("div",{class:"stat-icon today-icon"},[r("i",{class:"el-icon-calendar-today"})],-1)),r("div",K,[r("div",Z,k(g.logStats.today),1),s[22]||(s[22]=r("div",{class:"stat-label"},"今日操作",-1))])])]),_:1})]),_:1}),a(be,{span:6},{default:l(()=>[a(fe,{class:"stat-card"},{default:l(()=>[r("div",G,[s[25]||(s[25]=r("div",{class:"stat-icon user-icon"},[r("i",{class:"el-icon-user"})],-1)),r("div",R,[r("div",X,k(g.logStats.active_users),1),s[24]||(s[24]=r("div",{class:"stat-label"},"活跃用户",-1))])])]),_:1})]),_:1}),a(be,{span:6},{default:l(()=>[a(fe,{class:"stat-card"},{default:l(()=>[r("div",$,[s[27]||(s[27]=r("div",{class:"stat-icon error-icon"},[r("i",{class:"el-icon-warning"})],-1)),r("div",ee,[r("div",ae,k(g.logStats.errors),1),s[26]||(s[26]=r("div",{class:"stat-label"},"错误操作",-1))])])]),_:1})]),_:1})]),_:1}),a(ve,{gutter:20,style:{"margin-top":"20px"}},{default:l(()=>[a(be,{span:12},{default:l(()=>[a(fe,null,{header:l(()=>s[28]||(s[28]=[r("div",{class:"card-header"},[r("span",null,"📊 操作类型统计")],-1)])),default:l(()=>[a(g.VChart,{class:"chart",option:g.operationTypeChart,autoresize:""},null,8,["option"])]),_:1})]),_:1}),a(be,{span:12},{default:l(()=>[a(fe,null,{header:l(()=>s[29]||(s[29]=[r("div",{class:"card-header"},[r("span",null,"📈 操作趋势")],-1)])),default:l(()=>[a(g.VChart,{class:"chart",option:g.operationTrendChart,autoresize:""},null,8,["option"])]),_:1})]),_:1})]),_:1}),a(fe,{style:{"margin-top":"20px"}},{header:l(()=>s[30]||(s[30]=[r("div",{class:"card-header"},[r("span",null,"🔍 日志筛选")],-1)])),default:l(()=>[a(je,{inline:!0,model:g.queryParams,class:"filter-form"},{default:l(()=>[a(Ve,{label:"操作用户"},{default:l(()=>[a(we,{modelValue:g.queryParams.user_id,"onUpdate:modelValue":s[1]||(s[1]=e=>g.queryParams.user_id=e),placeholder:"选择用户",clearable:"",filterable:""},{default:l(()=>[(i(!0),e(d,null,u(g.users,e=>(i(),p(he,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(Ve,{label:"操作类型"},{default:l(()=>[a(we,{modelValue:g.queryParams.operation_type,"onUpdate:modelValue":s[2]||(s[2]=e=>g.queryParams.operation_type=e),placeholder:"选择类型",clearable:""},{default:l(()=>[a(he,{label:"登录",value:"login"}),a(he,{label:"创建",value:"create"}),a(he,{label:"更新",value:"update"}),a(he,{label:"删除",value:"delete"}),a(he,{label:"导出",value:"export"}),a(he,{label:"设置",value:"setting"})]),_:1},8,["modelValue"])]),_:1}),a(Ve,{label:"模块"},{default:l(()=>[a(we,{modelValue:g.queryParams.module,"onUpdate:modelValue":s[3]||(s[3]=e=>g.queryParams.module=e),placeholder:"选择模块",clearable:""},{default:l(()=>[a(he,{label:"用户管理",value:"user"}),a(he,{label:"订单管理",value:"order"}),a(he,{label:"财务管理",value:"finance"}),a(he,{label:"分销管理",value:"distribution"}),a(he,{label:"系统设置",value:"system"})]),_:1},8,["modelValue"])]),_:1}),a(Ve,{label:"状态"},{default:l(()=>[a(we,{modelValue:g.queryParams.status,"onUpdate:modelValue":s[4]||(s[4]=e=>g.queryParams.status=e),placeholder:"选择状态",clearable:""},{default:l(()=>[a(he,{label:"成功",value:"success"}),a(he,{label:"失败",value:"failed"}),a(he,{label:"警告",value:"warning"})]),_:1},8,["modelValue"])]),_:1}),a(Ve,{label:"时间范围"},{default:l(()=>[a(Ce,{modelValue:g.queryParams.date_range,"onUpdate:modelValue":s[5]||(s[5]=e=>g.queryParams.date_range=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),a(Ve,{label:"IP地址"},{default:l(()=>[a(ke,{modelValue:g.queryParams.ip,"onUpdate:modelValue":s[6]||(s[6]=e=>g.queryParams.ip=e),placeholder:"IP地址",clearable:""},null,8,["modelValue"])]),_:1}),a(Ve,null,{default:l(()=>[a(xe,{type:"primary",onClick:g.searchLogs},{default:l(()=>s[31]||(s[31]=[r("i",{class:"el-icon-search"},null,-1),n(" 搜索 ",-1)])),_:1,__:[31]}),a(xe,{onClick:g.resetQuery},{default:l(()=>s[32]||(s[32]=[r("i",{class:"el-icon-refresh"},null,-1),n(" 重置 ",-1)])),_:1,__:[32]}),a(xe,{type:"success",onClick:g.exportLogs},{default:l(()=>s[33]||(s[33]=[r("i",{class:"el-icon-download"},null,-1),n(" 导出 ",-1)])),_:1,__:[33]})]),_:1})]),_:1},8,["model"])]),_:1}),a(fe,{style:{"margin-top":"20px"}},{header:l(()=>[r("div",le,[s[36]||(s[36]=r("span",null,"📋 操作日志",-1)),r("div",null,[a(xe,{type:"primary",onClick:g.refreshLogs},{default:l(()=>s[34]||(s[34]=[r("i",{class:"el-icon-refresh"},null,-1),n(" 刷新 ",-1)])),_:1,__:[34]}),a(xe,{type:"warning",onClick:g.clearLogs},{default:l(()=>s[35]||(s[35]=[r("i",{class:"el-icon-delete"},null,-1),n(" 清理日志 ",-1)])),_:1,__:[35]})])])]),default:l(()=>[c((i(),p(Te,{data:g.logs,style:{width:"100%"}},{default:l(()=>[a(De,{prop:"id",label:"ID",width:"80"}),a(De,{prop:"user",label:"操作用户",width:"120"},{default:l(e=>[r("div",te,[r("div",se,k(e.row.user?.nickname||"系统"),1),r("div",oe,k(e.row.user?.role_name||"System"),1)])]),_:1}),a(De,{prop:"operation_type",label:"操作类型",width:"100"},{default:l(e=>[a(Pe,{type:g.getOperationTypeColor(e.row.operation_type)},{default:l(()=>[n(k(g.getOperationTypeName(e.row.operation_type)),1)]),_:2},1032,["type"])]),_:1}),a(De,{prop:"module",label:"模块",width:"100"},{default:l(e=>[r("span",ie,k(g.getModuleName(e.row.module)),1)]),_:1}),a(De,{prop:"description",label:"操作描述","min-width":"200","show-overflow-tooltip":""}),a(De,{prop:"status",label:"状态",width:"80"},{default:l(e=>[a(Pe,{type:g.getStatusColor(e.row.status),size:"small"},{default:l(()=>[n(k(g.getStatusName(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),a(De,{prop:"ip_address",label:"IP地址",width:"120"}),a(De,{prop:"user_agent",label:"用户代理",width:"150","show-overflow-tooltip":""}),a(De,{prop:"execution_time",label:"耗时",width:"80"},{default:l(e=>[n(k(e.row.execution_time)+"ms ",1)]),_:1}),a(De,{prop:"created_at",label:"操作时间",width:"160"}),a(De,{label:"操作",width:"120"},{default:l(e=>[a(xe,{type:"primary",size:"small",onClick:a=>g.viewDetails(e.row)},{default:l(()=>s[37]||(s[37]=[n(" 详情 ",-1)])),_:2,__:[37]},1032,["onClick"]),"failed"===e.row.status?(i(),p(xe,{key:0,type:"danger",size:"small",onClick:a=>g.deleteLog(e.row)},{default:l(()=>s[38]||(s[38]=[n(" 删除 ",-1)])),_:2,__:[38]},1032,["onClick"])):m("",!0)]),_:1})]),_:1},8,["data"])),[[Oe,g.loading]]),r("div",re,[a(qe,{"current-page":g.queryParams.page,"onUpdate:currentPage":s[7]||(s[7]=e=>g.queryParams.page=e),"page-size":g.queryParams.per_page,"onUpdate:pageSize":s[8]||(s[8]=e=>g.queryParams.per_page=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:g.total,onSizeChange:g.handleSizeChange,onCurrentChange:g.handleCurrentChange},null,8,["current-page","page-size","total"])])]),_:1}),a(ze,{title:"操作详情",modelValue:g.detailDialog.visible,"onUpdate:modelValue":s[9]||(s[9]=e=>g.detailDialog.visible=e),width:"800px"},{default:l(()=>[g.detailDialog.log?(i(),e("div",ne,[a(Se,{column:2,border:""},{default:l(()=>[a(Le,{label:"操作ID"},{default:l(()=>[n(k(g.detailDialog.log.id),1)]),_:1}),a(Le,{label:"操作用户"},{default:l(()=>[n(k(g.detailDialog.log.user?.nickname||"系统"),1)]),_:1}),a(Le,{label:"操作类型"},{default:l(()=>[a(Pe,{type:g.getOperationTypeColor(g.detailDialog.log.operation_type)},{default:l(()=>[n(k(g.getOperationTypeName(g.detailDialog.log.operation_type)),1)]),_:1},8,["type"])]),_:1}),a(Le,{label:"模块"},{default:l(()=>[n(k(g.getModuleName(g.detailDialog.log.module)),1)]),_:1}),a(Le,{label:"操作描述"},{default:l(()=>[n(k(g.detailDialog.log.description),1)]),_:1}),a(Le,{label:"状态"},{default:l(()=>[a(Pe,{type:g.getStatusColor(g.detailDialog.log.status)},{default:l(()=>[n(k(g.getStatusName(g.detailDialog.log.status)),1)]),_:1},8,["type"])]),_:1}),a(Le,{label:"IP地址"},{default:l(()=>[n(k(g.detailDialog.log.ip_address),1)]),_:1}),a(Le,{label:"执行时间"},{default:l(()=>[n(k(g.detailDialog.log.execution_time)+"ms ",1)]),_:1}),a(Le,{label:"操作时间"},{default:l(()=>[n(k(g.detailDialog.log.created_at),1)]),_:1})]),_:1}),r("div",de,[s[39]||(s[39]=r("h4",null,"用户代理",-1)),a(ke,{type:"textarea",value:g.detailDialog.log.user_agent,readonly:""},null,8,["value"])]),g.detailDialog.log.request_data?(i(),e("div",ue,[s[40]||(s[40]=r("h4",null,"请求数据",-1)),r("pre",pe,k(JSON.stringify(g.detailDialog.log.request_data,null,2)),1)])):m("",!0),g.detailDialog.log.response_data?(i(),e("div",ce,[s[41]||(s[41]=r("h4",null,"响应数据",-1)),r("pre",me,k(JSON.stringify(g.detailDialog.log.response_data,null,2)),1)])):m("",!0),g.detailDialog.log.error_message?(i(),e("div",ge,[s[42]||(s[42]=r("h4",null,"错误信息",-1)),a(Ue,{title:g.detailDialog.log.error_message,type:"error","show-icon":""},null,8,["title"])])):m("",!0)])):m("",!0)]),_:1},8,["modelValue"]),a(ze,{title:"清理日志",modelValue:g.cleanDialog.visible,"onUpdate:modelValue":s[15]||(s[15]=e=>g.cleanDialog.visible=e),width:"500px"},{footer:l(()=>[r("div",_e,[a(xe,{onClick:s[14]||(s[14]=e=>g.cleanDialog.visible=!1)},{default:l(()=>s[46]||(s[46]=[n("取消",-1)])),_:1,__:[46]}),a(xe,{type:"danger",onClick:g.executeClean,loading:g.cleaning},{default:l(()=>s[47]||(s[47]=[n(" 确认清理 ",-1)])),_:1,__:[47]},8,["loading"])])]),default:l(()=>[a(je,{model:g.cleanForm,"label-width":"100px"},{default:l(()=>[a(Ve,{label:"清理方式"},{default:l(()=>[a(Fe,{modelValue:g.cleanForm.type,"onUpdate:modelValue":s[10]||(s[10]=e=>g.cleanForm.type=e)},{default:l(()=>[a(Ne,{label:"by_date"},{default:l(()=>s[43]||(s[43]=[n("按时间",-1)])),_:1,__:[43]}),a(Ne,{label:"by_count"},{default:l(()=>s[44]||(s[44]=[n("按数量",-1)])),_:1,__:[44]}),a(Ne,{label:"by_status"},{default:l(()=>s[45]||(s[45]=[n("按状态",-1)])),_:1,__:[45]})]),_:1},8,["modelValue"])]),_:1}),"by_date"===g.cleanForm.type?(i(),p(Ve,{key:0,label:"清理条件"},{default:l(()=>[a(Ce,{modelValue:g.cleanForm.before_date,"onUpdate:modelValue":s[11]||(s[11]=e=>g.cleanForm.before_date=e),type:"date",placeholder:"清理此日期之前的日志"},null,8,["modelValue"])]),_:1})):m("",!0),"by_count"===g.cleanForm.type?(i(),p(Ve,{key:1,label:"保留数量"},{default:l(()=>[a(Me,{modelValue:g.cleanForm.keep_count,"onUpdate:modelValue":s[12]||(s[12]=e=>g.cleanForm.keep_count=e),min:1e3,max:1e5},null,8,["modelValue"])]),_:1})):m("",!0),"by_status"===g.cleanForm.type?(i(),p(Ve,{key:2,label:"清理状态"},{default:l(()=>[a(we,{modelValue:g.cleanForm.status,"onUpdate:modelValue":s[13]||(s[13]=e=>g.cleanForm.status=e),placeholder:"选择要清理的状态"},{default:l(()=>[a(he,{label:"成功",value:"success"}),a(he,{label:"失败",value:"failed"}),a(he,{label:"警告",value:"warning"})]),_:1},8,["modelValue"])]),_:1})):m("",!0),a(Ve,null,{default:l(()=>[a(Ue,{title:"注意：清理操作不可恢复，请谨慎操作！",type:"warning","show-icon":"",closable:!1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-7cdc86a5"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/OperationLogs.vue"]]);export{ye as default};
