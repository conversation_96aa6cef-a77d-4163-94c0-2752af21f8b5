<template>
  <div class="group-add-enhanced-test">
    <h1>增强版群组创建测试页面</h1>
    <p>如果您能看到这个页面，说明路由配置正常</p>
    
    <el-card>
      <template #header>
        <div>基础测试</div>
      </template>
      
      <el-form :model="form" label-width="120px">
        <el-form-item label="群组名称">
          <el-input v-model="form.title" placeholder="请输入群组名称" />
        </el-form-item>
        
        <el-form-item label="群组价格">
          <el-input-number v-model="form.price" :min="0" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleTest">测试按钮</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card style="margin-top: 20px;">
      <template #header>
        <div>组件测试</div>
      </template>
      
      <div>
        <h3>测试结果：</h3>
        <p>{{ testMessage }}</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const form = reactive({
  title: '',
  price: 0
})

const testMessage = ref('页面加载成功！')

const handleTest = () => {
  ElMessage.success('测试按钮点击成功！')
  testMessage.value = `测试成功！群组名称：${form.title}，价格：${form.price}`
}
</script>

<style scoped>
.group-add-enhanced-test {
  padding: 20px;
}
</style>
