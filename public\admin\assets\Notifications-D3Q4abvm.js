/* empty css             *//* empty css                   *//* empty css                     *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                  *//* empty css                          *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css               *//* empty css                */import{l as e,G as a,A as t,M as l,r as s,o,m as i,q as n,E as d,z as r,C as u,F as c,Y as p}from"./vue-vendor-BcnDv-68.js";import{H as m}from"./echarts-D6CUuNS9.js";import{_}from"./index-eUTsTR3J.js";import{U as g,V as f,W as y,ag as b,X as v,Y as h,Z as w,a1 as V,ai as k,aj as C,ak as F,an as j,a5 as x,a6 as D,a2 as N,a3 as S,_ as U,aL as q,a4 as T,b6 as P,b7 as z,a0 as R}from"./element-plus-C2UshkXo.js";import"./utils-SdQ7DxjY.js";const B={class:"app-container"},O={class:"stat-item"},M={class:"stat-content"},$={class:"stat-number"},E={class:"stat-item"},I={class:"stat-content"},A={class:"stat-number"},Y={class:"stat-item"},G={class:"stat-content"},L={class:"stat-number"},Q={class:"stat-item"},W={class:"stat-content"},X={class:"stat-number"},Z={class:"card-header"},H={key:0},J={key:1},K={key:2},ee={class:"table-pagination"},ae={class:"card-header"},te={class:"template-card"},le={class:"template-header"},se={class:"template-title"},oe={class:"template-actions"},ie={class:"template-content"},ne={class:"template-type"},de={class:"template-preview"},re={class:"template-stats"},ue={class:"stat-item"},ce={class:"stat-item"},pe={class:"dialog-footer"},me={class:"dialog-footer"},_e={class:"stats-container"},ge={class:"stats-card"},fe={class:"stats-number"},ye={class:"stats-card"},be={class:"stats-number"},ve={class:"stats-card"},he={class:"stats-number"},we={class:"stats-chart"};const Ve=_({__name:"Notifications",setup(e,{expose:a}){a();const t=l({total:1245,unread:89,today:56,readRate:78}),i=l({page:1,per_page:10,type:"",status:""}),n=s([{id:1,title:"系统维护通知",type:"system",target:"all",send_count:1200,read_count:980,read_rate:82,status:"sent",created_at:"2024-01-01 10:00:00"},{id:2,title:"新功能上线通知",type:"user",target:"group",send_count:800,read_count:650,read_rate:81,status:"sent",created_at:"2024-01-01 09:00:00"}]),d=s([{id:1,name:"欢迎新用户",type:"user",content:"欢迎 {username} 加入我们的平台！",usage_count:120,created_at:"2024-01-01"},{id:2,name:"提现成功通知",type:"user",content:"您的提现申请已成功处理，金额：{amount}",usage_count:85,created_at:"2024-01-01"}]),r=s([{id:1,nickname:"用户1"},{id:2,nickname:"用户2"}]),u=l({visible:!1}),c=l({type:"system",target:"all",group_id:"",user_ids:[],title:"",content:"",channels:["web"],send_type:"now",scheduled_at:null,priority:"normal"}),p=l({visible:!1,title:"新增模板"}),_=l({name:"",type:"user",content:"",variables:""}),g=l({visible:!1}),f=l({send_count:0,read_count:0,click_count:0}),y=s({}),h=s(0),w=s(!1),V=s(!1),k=()=>{Object.assign(c,{type:"system",target:"all",group_id:"",user_ids:[],title:"",content:"",channels:["web"],send_type:"now",scheduled_at:null,priority:"normal"})},C=()=>{Object.assign(_,{name:"",type:"user",content:"",variables:""})},F=()=>{v.success("通知列表已刷新")};o(()=>{h.value=n.value.length});const j={notificationStats:t,queryParams:i,notifications:n,templates:d,users:r,sendDialog:u,sendForm:c,sendRules:{type:[{required:!0,message:"请选择通知类型",trigger:"change"}],target:[{required:!0,message:"请选择发送目标",trigger:"change"}],title:[{required:!0,message:"请输入通知标题",trigger:"blur"}],content:[{required:!0,message:"请输入通知内容",trigger:"blur"}]},templateDialog:p,templateForm:_,templateRules:{name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],type:[{required:!0,message:"请选择模板类型",trigger:"change"}],content:[{required:!0,message:"请输入模板内容",trigger:"blur"}]},statsDialog:g,statsData:f,statsChartOptions:y,total:h,sending:w,templateSaving:V,getNotificationTypeColor:e=>({system:"primary",user:"success",marketing:"warning",security:"danger"}[e]||"info"),getNotificationTypeName:e=>({system:"系统通知",user:"用户通知",marketing:"营销通知",security:"安全通知"}[e]||e),getStatusColor:e=>({sent:"success",pending:"warning",failed:"danger"}[e]||"info"),getStatusName:e=>({sent:"已发送",pending:"待发送",failed:"发送失败"}[e]||e),showSendNotificationDialog:()=>{k(),u.visible=!0},showBulkNotificationDialog:()=>{k(),c.target="group",u.visible=!0},showTemplateDialog:()=>{C(),p.title="新增模板",p.visible=!0},showScheduleDialog:()=>{k(),c.send_type="scheduled",u.visible=!0},resetSendForm:k,resetTemplateForm:C,sendNotification:async()=>{w.value=!0;try{await new Promise(e=>setTimeout(e,2e3)),v.success("通知发送成功"),u.visible=!1,F()}catch(e){v.error("通知发送失败")}finally{w.value=!1}},saveTemplate:async()=>{V.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),v.success("模板保存成功"),p.visible=!1}catch(e){v.error("模板保存失败")}finally{V.value=!1}},viewNotification:e=>{v.info(`查看通知：${e.title}`)},viewStats:e=>{f.send_count=e.send_count,f.read_count=e.read_count,f.click_count=Math.floor(.3*e.read_count),y.value={title:{text:"通知统计",left:"center"},tooltip:{trigger:"item"},series:[{name:"通知统计",type:"pie",radius:"50%",data:[{value:f.read_count,name:"已读"},{value:f.send_count-f.read_count,name:"未读"}]}]},g.visible=!0},resendNotification:e=>{v.info(`重新发送：${e.title}`)},deleteNotification:e=>{b.confirm(`确定要删除通知 ${e.title} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{v.success("删除成功"),F()})},editTemplate:e=>{Object.assign(_,e),p.title="编辑模板",p.visible=!0},useTemplate:e=>{c.content=e.content,c.type=e.type,u.visible=!0},deleteTemplate:e=>{b.confirm(`确定要删除模板 ${e.name} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{v.success("删除成功")})},refreshNotifications:F,handleSelectionChange:e=>{},handleSizeChange:e=>{i.per_page=e,F()},handleCurrentChange:e=>{i.page=e,F()},ref:s,reactive:l,onMounted:o,get ElMessage(){return v},get ElMessageBox(){return b},get VChart(){return m}};return Object.defineProperty(j,"__isScriptSetup",{enumerable:!1,value:!0}),j}},[["render",function(l,s,o,m,_,b){const v=f,Ve=h,ke=g,Ce=V,Fe=D,je=x,xe=C,De=F,Ne=k,Se=j,Ue=S,qe=q,Te=U,Pe=T,ze=z,Re=P,Be=R,Oe=N,Me=y;return i(),e("div",B,[a(ke,{gutter:20},{default:t(()=>[a(Ve,{span:6},{default:t(()=>[a(v,{class:"stat-card"},{default:t(()=>[n("div",O,[s[24]||(s[24]=n("div",{class:"stat-icon total-icon"},[n("i",{class:"el-icon-bell"})],-1)),n("div",M,[n("div",$,w(m.notificationStats.total),1),s[23]||(s[23]=n("div",{class:"stat-label"},"总通知数",-1))])])]),_:1})]),_:1}),a(Ve,{span:6},{default:t(()=>[a(v,{class:"stat-card"},{default:t(()=>[n("div",E,[s[26]||(s[26]=n("div",{class:"stat-icon unread-icon"},[n("i",{class:"el-icon-message"})],-1)),n("div",I,[n("div",A,w(m.notificationStats.unread),1),s[25]||(s[25]=n("div",{class:"stat-label"},"未读通知",-1))])])]),_:1})]),_:1}),a(Ve,{span:6},{default:t(()=>[a(v,{class:"stat-card"},{default:t(()=>[n("div",Y,[s[28]||(s[28]=n("div",{class:"stat-icon today-icon"},[n("i",{class:"el-icon-calendar-today"})],-1)),n("div",G,[n("div",L,w(m.notificationStats.today),1),s[27]||(s[27]=n("div",{class:"stat-label"},"今日发送",-1))])])]),_:1})]),_:1}),a(Ve,{span:6},{default:t(()=>[a(v,{class:"stat-card"},{default:t(()=>[n("div",Q,[s[30]||(s[30]=n("div",{class:"stat-icon rate-icon"},[n("i",{class:"el-icon-data-analysis"})],-1)),n("div",W,[n("div",X,w(m.notificationStats.readRate)+"%",1),s[29]||(s[29]=n("div",{class:"stat-label"},"阅读率",-1))])])]),_:1})]),_:1})]),_:1}),a(v,{style:{"margin-top":"20px"}},{header:t(()=>s[31]||(s[31]=[n("div",{class:"card-header"},[n("span",null,"⚡ 快速操作")],-1)])),default:t(()=>[a(ke,{gutter:20},{default:t(()=>[a(Ve,{span:6},{default:t(()=>[a(Ce,{type:"primary",onClick:m.showSendNotificationDialog,class:"quick-action-btn"},{default:t(()=>s[32]||(s[32]=[n("i",{class:"el-icon-s-promotion"},null,-1),d(" 发送系统通知 ",-1)])),_:1,__:[32]})]),_:1}),a(Ve,{span:6},{default:t(()=>[a(Ce,{type:"success",onClick:m.showBulkNotificationDialog,class:"quick-action-btn"},{default:t(()=>s[33]||(s[33]=[n("i",{class:"el-icon-user-solid"},null,-1),d(" 批量用户通知 ",-1)])),_:1,__:[33]})]),_:1}),a(Ve,{span:6},{default:t(()=>[a(Ce,{type:"warning",onClick:m.showTemplateDialog,class:"quick-action-btn"},{default:t(()=>s[34]||(s[34]=[n("i",{class:"el-icon-document"},null,-1),d(" 创建消息模板 ",-1)])),_:1,__:[34]})]),_:1}),a(Ve,{span:6},{default:t(()=>[a(Ce,{type:"info",onClick:m.showScheduleDialog,class:"quick-action-btn"},{default:t(()=>s[35]||(s[35]=[n("i",{class:"el-icon-alarm-clock"},null,-1),d(" 定时通知 ",-1)])),_:1,__:[35]})]),_:1})]),_:1})]),_:1}),a(v,{style:{"margin-top":"20px"}},{header:t(()=>[n("div",Z,[s[37]||(s[37]=n("span",null,"📋 通知列表",-1)),n("div",null,[a(je,{modelValue:m.queryParams.type,"onUpdate:modelValue":s[0]||(s[0]=e=>m.queryParams.type=e),placeholder:"通知类型",clearable:"",style:{width:"120px","margin-right":"10px"}},{default:t(()=>[a(Fe,{label:"系统通知",value:"system"}),a(Fe,{label:"用户通知",value:"user"}),a(Fe,{label:"营销通知",value:"marketing"}),a(Fe,{label:"安全通知",value:"security"})]),_:1},8,["modelValue"]),a(je,{modelValue:m.queryParams.status,"onUpdate:modelValue":s[1]||(s[1]=e=>m.queryParams.status=e),placeholder:"状态",clearable:"",style:{width:"100px","margin-right":"10px"}},{default:t(()=>[a(Fe,{label:"已发送",value:"sent"}),a(Fe,{label:"待发送",value:"pending"}),a(Fe,{label:"发送失败",value:"failed"})]),_:1},8,["modelValue"]),a(Ce,{type:"primary",onClick:m.refreshNotifications},{default:t(()=>s[36]||(s[36]=[d("刷新",-1)])),_:1,__:[36]})])])]),default:t(()=>[a(Ne,{data:m.notifications,style:{width:"100%"},onSelectionChange:m.handleSelectionChange},{default:t(()=>[a(xe,{type:"selection",width:"55"}),a(xe,{prop:"id",label:"ID",width:"80"}),a(xe,{prop:"title",label:"标题","min-width":"200","show-overflow-tooltip":""}),a(xe,{prop:"type",label:"类型",width:"100"},{default:t(e=>[a(De,{type:m.getNotificationTypeColor(e.row.type)},{default:t(()=>[d(w(m.getNotificationTypeName(e.row.type)),1)]),_:2},1032,["type"])]),_:1}),a(xe,{prop:"target",label:"目标",width:"120"},{default:t(a=>["all"===a.row.target?(i(),e("span",H,"全部用户")):"group"===a.row.target?(i(),e("span",J,"用户组")):(i(),e("span",K,"指定用户"))]),_:1}),a(xe,{prop:"send_count",label:"发送数",width:"100"}),a(xe,{prop:"read_count",label:"已读数",width:"100"}),a(xe,{prop:"read_rate",label:"阅读率",width:"100"},{default:t(e=>[d(w(e.row.read_rate)+"% ",1)]),_:1}),a(xe,{prop:"status",label:"状态",width:"100"},{default:t(e=>[a(De,{type:m.getStatusColor(e.row.status)},{default:t(()=>[d(w(m.getStatusName(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),a(xe,{prop:"created_at",label:"创建时间",width:"160"}),a(xe,{label:"操作",width:"200"},{default:t(e=>[a(Ce,{type:"primary",size:"small",onClick:a=>m.viewNotification(e.row)},{default:t(()=>s[38]||(s[38]=[d(" 查看 ",-1)])),_:2,__:[38]},1032,["onClick"]),a(Ce,{type:"info",size:"small",onClick:a=>m.viewStats(e.row)},{default:t(()=>s[39]||(s[39]=[d(" 统计 ",-1)])),_:2,__:[39]},1032,["onClick"]),"failed"===e.row.status?(i(),r(Ce,{key:0,type:"warning",size:"small",onClick:a=>m.resendNotification(e.row)},{default:t(()=>s[40]||(s[40]=[d(" 重发 ",-1)])),_:2,__:[40]},1032,["onClick"])):u("",!0),a(Ce,{type:"danger",size:"small",onClick:a=>m.deleteNotification(e.row)},{default:t(()=>s[41]||(s[41]=[d(" 删除 ",-1)])),_:2,__:[41]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),n("div",ee,[a(Se,{"current-page":m.queryParams.page,"onUpdate:currentPage":s[2]||(s[2]=e=>m.queryParams.page=e),"page-size":m.queryParams.per_page,"onUpdate:pageSize":s[3]||(s[3]=e=>m.queryParams.per_page=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:m.total,onSizeChange:m.handleSizeChange,onCurrentChange:m.handleCurrentChange},null,8,["current-page","page-size","total"])])]),_:1}),a(v,{style:{"margin-top":"20px"}},{header:t(()=>[n("div",ae,[s[43]||(s[43]=n("span",null,"📄 消息模板",-1)),a(Ce,{type:"primary",onClick:m.showTemplateDialog},{default:t(()=>s[42]||(s[42]=[d("新增模板",-1)])),_:1,__:[42]})])]),default:t(()=>[a(ke,{gutter:20},{default:t(()=>[(i(!0),e(c,null,p(m.templates,e=>(i(),r(Ve,{span:8,key:e.id},{default:t(()=>[n("div",te,[n("div",le,[n("div",se,w(e.name),1),n("div",oe,[a(Ce,{type:"text",onClick:a=>m.editTemplate(e)},{default:t(()=>[...s[44]||(s[44]=[d("编辑",-1)])]),_:2,__:[44]},1032,["onClick"]),a(Ce,{type:"text",onClick:a=>m.useTemplate(e)},{default:t(()=>[...s[45]||(s[45]=[d("使用",-1)])]),_:2,__:[45]},1032,["onClick"]),a(Ce,{type:"text",onClick:a=>m.deleteTemplate(e)},{default:t(()=>[...s[46]||(s[46]=[d("删除",-1)])]),_:2,__:[46]},1032,["onClick"])])]),n("div",ie,[n("div",ne,w(m.getNotificationTypeName(e.type)),1),n("div",de,w(e.content),1),n("div",re,[n("span",ue,"使用次数: "+w(e.usage_count),1),n("span",ce,"创建时间: "+w(e.created_at),1)])])])]),_:2},1024))),128))]),_:1})]),_:1}),a(Me,{title:"发送通知",modelValue:m.sendDialog.visible,"onUpdate:modelValue":s[15]||(s[15]=e=>m.sendDialog.visible=e),width:"800px"},{footer:t(()=>[n("div",pe,[a(Ce,{onClick:s[14]||(s[14]=e=>m.sendDialog.visible=!1)},{default:t(()=>s[56]||(s[56]=[d("取消",-1)])),_:1,__:[56]}),a(Ce,{type:"primary",onClick:m.sendNotification,loading:m.sending},{default:t(()=>[d(w("now"===m.sendForm.send_type?"立即发送":"定时发送"),1)]),_:1},8,["loading"])])]),default:t(()=>[a(Oe,{model:m.sendForm,rules:m.sendRules,ref:"sendFormRef","label-width":"100px"},{default:t(()=>[a(Ue,{label:"通知类型",prop:"type"},{default:t(()=>[a(je,{modelValue:m.sendForm.type,"onUpdate:modelValue":s[4]||(s[4]=e=>m.sendForm.type=e),placeholder:"请选择通知类型"},{default:t(()=>[a(Fe,{label:"系统通知",value:"system"}),a(Fe,{label:"用户通知",value:"user"}),a(Fe,{label:"营销通知",value:"marketing"}),a(Fe,{label:"安全通知",value:"security"})]),_:1},8,["modelValue"])]),_:1}),a(Ue,{label:"发送目标",prop:"target"},{default:t(()=>[a(Te,{modelValue:m.sendForm.target,"onUpdate:modelValue":s[5]||(s[5]=e=>m.sendForm.target=e)},{default:t(()=>[a(qe,{label:"all"},{default:t(()=>s[47]||(s[47]=[d("全部用户",-1)])),_:1,__:[47]}),a(qe,{label:"group"},{default:t(()=>s[48]||(s[48]=[d("用户组",-1)])),_:1,__:[48]}),a(qe,{label:"specific"},{default:t(()=>s[49]||(s[49]=[d("指定用户",-1)])),_:1,__:[49]})]),_:1},8,["modelValue"])]),_:1}),"group"===m.sendForm.target?(i(),r(Ue,{key:0,label:"用户组"},{default:t(()=>[a(je,{modelValue:m.sendForm.group_id,"onUpdate:modelValue":s[6]||(s[6]=e=>m.sendForm.group_id=e),placeholder:"请选择用户组"},{default:t(()=>[a(Fe,{label:"VIP用户",value:"vip"}),a(Fe,{label:"分销商",value:"distributor"}),a(Fe,{label:"分站管理员",value:"substation"})]),_:1},8,["modelValue"])]),_:1})):u("",!0),"specific"===m.sendForm.target?(i(),r(Ue,{key:1,label:"指定用户"},{default:t(()=>[a(je,{modelValue:m.sendForm.user_ids,"onUpdate:modelValue":s[7]||(s[7]=e=>m.sendForm.user_ids=e),multiple:"",placeholder:"请选择用户"},{default:t(()=>[(i(!0),e(c,null,p(m.users,e=>(i(),r(Fe,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):u("",!0),a(Ue,{label:"通知标题",prop:"title"},{default:t(()=>[a(Pe,{modelValue:m.sendForm.title,"onUpdate:modelValue":s[8]||(s[8]=e=>m.sendForm.title=e),placeholder:"请输入通知标题"},null,8,["modelValue"])]),_:1}),a(Ue,{label:"通知内容",prop:"content"},{default:t(()=>[a(Pe,{type:"textarea",modelValue:m.sendForm.content,"onUpdate:modelValue":s[9]||(s[9]=e=>m.sendForm.content=e),rows:4,placeholder:"请输入通知内容"},null,8,["modelValue"])]),_:1}),a(Ue,{label:"发送方式"},{default:t(()=>[a(Re,{modelValue:m.sendForm.channels,"onUpdate:modelValue":s[10]||(s[10]=e=>m.sendForm.channels=e)},{default:t(()=>[a(ze,{label:"web"},{default:t(()=>s[50]||(s[50]=[d("站内信",-1)])),_:1,__:[50]}),a(ze,{label:"email"},{default:t(()=>s[51]||(s[51]=[d("邮件",-1)])),_:1,__:[51]}),a(ze,{label:"sms"},{default:t(()=>s[52]||(s[52]=[d("短信",-1)])),_:1,__:[52]}),a(ze,{label:"wechat"},{default:t(()=>s[53]||(s[53]=[d("微信",-1)])),_:1,__:[53]})]),_:1},8,["modelValue"])]),_:1}),a(Ue,{label:"发送时间"},{default:t(()=>[a(Te,{modelValue:m.sendForm.send_type,"onUpdate:modelValue":s[11]||(s[11]=e=>m.sendForm.send_type=e)},{default:t(()=>[a(qe,{label:"now"},{default:t(()=>s[54]||(s[54]=[d("立即发送",-1)])),_:1,__:[54]}),a(qe,{label:"scheduled"},{default:t(()=>s[55]||(s[55]=[d("定时发送",-1)])),_:1,__:[55]})]),_:1},8,["modelValue"]),"scheduled"===m.sendForm.send_type?(i(),r(Be,{key:0,modelValue:m.sendForm.scheduled_at,"onUpdate:modelValue":s[12]||(s[12]=e=>m.sendForm.scheduled_at=e),type:"datetime",placeholder:"选择发送时间",style:{"margin-left":"10px"}},null,8,["modelValue"])):u("",!0)]),_:1}),a(Ue,{label:"优先级"},{default:t(()=>[a(je,{modelValue:m.sendForm.priority,"onUpdate:modelValue":s[13]||(s[13]=e=>m.sendForm.priority=e),placeholder:"请选择优先级"},{default:t(()=>[a(Fe,{label:"低",value:"low"}),a(Fe,{label:"普通",value:"normal"}),a(Fe,{label:"高",value:"high"}),a(Fe,{label:"紧急",value:"urgent"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(Me,{title:m.templateDialog.title,modelValue:m.templateDialog.visible,"onUpdate:modelValue":s[21]||(s[21]=e=>m.templateDialog.visible=e),width:"600px"},{footer:t(()=>[n("div",me,[a(Ce,{onClick:s[20]||(s[20]=e=>m.templateDialog.visible=!1)},{default:t(()=>s[57]||(s[57]=[d("取消",-1)])),_:1,__:[57]}),a(Ce,{type:"primary",onClick:m.saveTemplate,loading:m.templateSaving},{default:t(()=>s[58]||(s[58]=[d("保存",-1)])),_:1,__:[58]},8,["loading"])])]),default:t(()=>[a(Oe,{model:m.templateForm,rules:m.templateRules,ref:"templateFormRef","label-width":"100px"},{default:t(()=>[a(Ue,{label:"模板名称",prop:"name"},{default:t(()=>[a(Pe,{modelValue:m.templateForm.name,"onUpdate:modelValue":s[16]||(s[16]=e=>m.templateForm.name=e),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1}),a(Ue,{label:"模板类型",prop:"type"},{default:t(()=>[a(je,{modelValue:m.templateForm.type,"onUpdate:modelValue":s[17]||(s[17]=e=>m.templateForm.type=e),placeholder:"请选择模板类型"},{default:t(()=>[a(Fe,{label:"系统通知",value:"system"}),a(Fe,{label:"用户通知",value:"user"}),a(Fe,{label:"营销通知",value:"marketing"}),a(Fe,{label:"安全通知",value:"security"})]),_:1},8,["modelValue"])]),_:1}),a(Ue,{label:"模板内容",prop:"content"},{default:t(()=>[a(Pe,{type:"textarea",modelValue:m.templateForm.content,"onUpdate:modelValue":s[18]||(s[18]=e=>m.templateForm.content=e),rows:6,placeholder:"请输入模板内容，支持变量：{username}, {amount}, {time}等"},null,8,["modelValue"])]),_:1}),a(Ue,{label:"变量说明"},{default:t(()=>[a(Pe,{type:"textarea",modelValue:m.templateForm.variables,"onUpdate:modelValue":s[19]||(s[19]=e=>m.templateForm.variables=e),rows:3,placeholder:"请输入变量说明，如：{username}=用户名，{amount}=金额"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"]),a(Me,{title:"通知统计",modelValue:m.statsDialog.visible,"onUpdate:modelValue":s[22]||(s[22]=e=>m.statsDialog.visible=e),width:"800px"},{default:t(()=>[n("div",_e,[a(ke,{gutter:20},{default:t(()=>[a(Ve,{span:8},{default:t(()=>[n("div",ge,[n("div",fe,w(m.statsData.send_count),1),s[59]||(s[59]=n("div",{class:"stats-label"},"发送数量",-1))])]),_:1}),a(Ve,{span:8},{default:t(()=>[n("div",ye,[n("div",be,w(m.statsData.read_count),1),s[60]||(s[60]=n("div",{class:"stats-label"},"已读数量",-1))])]),_:1}),a(Ve,{span:8},{default:t(()=>[n("div",ve,[n("div",he,w(m.statsData.click_count),1),s[61]||(s[61]=n("div",{class:"stats-label"},"点击数量",-1))])]),_:1})]),_:1}),n("div",we,[a(m.VChart,{class:"chart",option:m.statsChartOptions,autoresize:""},null,8,["option"])])])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-fc8b1ebd"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/Notifications.vue"]]);export{Ve as default};
