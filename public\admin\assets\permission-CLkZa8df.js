import{u as s}from"./index-eUTsTR3J.js";import{checkMenuPermission as r}from"./navigation-DbqezFjv.js";import"./vue-vendor-BcnDv-68.js";import"./element-plus-C2UshkXo.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";function e(r){const e=s(),n=e.userInfo?.permissions||[];return Array.isArray(r)?r.some(s=>n.includes(s)):n.includes(r)}function n(n,o=null){const i=s(),t=o||i.userInfo?.role;if(!t)return!1;const{meta:m}=n;return!m||!(m.roles&&m.roles.length>0&&!m.roles.includes(t))&&(!(m.permissions&&m.permissions.length>0&&!e(m.permissions))&&r(n,t))}export{n as checkRoutePermission,e as hasPermission};
