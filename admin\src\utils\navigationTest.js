// 导航功能自动化测试工具
export class NavigationTester {
  constructor() {
    this.testResults = []
    this.currentTest = null
    this.router = null
  }

  // 初始化测试器
  init(router) {
    this.router = router
    console.log('🧪 导航测试器已初始化')
  }

  // 开始测试
  async startTest() {
    console.log('🚀 开始导航功能全面测试...')
    this.testResults = []
    
    // 1. 基础路由测试
    await this.testBasicRoutes()
    
    // 2. 导航菜单测试
    await this.testNavigationMenu()
    
    // 3. 特殊路由测试
    await this.testSpecialRoutes()
    
    // 4. 认证路由测试
    await this.testAuthRoutes()
    
    // 5. 404测试
    await this.test404Routes()
    
    // 生成测试报告
    this.generateReport()
  }

  // 测试基础路由
  async testBasicRoutes() {
    console.log('📍 测试基础路由...')
    
    const basicRoutes = [
      { path: '/login', name: '登录页面', shouldLoad: true },
      { path: '/test-dashboard', name: '测试控制台', shouldLoad: true },
      { path: '/system-status', name: '系统状态', shouldLoad: true },
      { path: '/scroll-test', name: '滚动测试', shouldLoad: true }
    ]

    for (const route of basicRoutes) {
      await this.testRoute(route)
    }
  }

  // 测试导航菜单路由
  async testNavigationMenu() {
    console.log('🧭 测试导航菜单路由...')
    
    const menuRoutes = [
      { path: '/dashboard', name: '控制台', requiresAuth: true },
      { path: '/dashboard/analytics', name: '数据分析', requiresAuth: true },
      { path: '/community/groups', name: '群组列表', requiresAuth: true },
      { path: '/community/templates', name: '模板管理', requiresAuth: true },
      { path: '/users', name: '用户管理', requiresAuth: true },
      { path: '/users/analytics', name: '用户分析', requiresAuth: true },
      { path: '/orders', name: '订单管理', requiresAuth: true },
      { path: '/finance', name: '财务管理', requiresAuth: true },
      { path: '/system/settings', name: '系统设置', requiresAuth: true },
      { path: '/system/monitor', name: '系统监控', requiresAuth: true }
    ]

    for (const route of menuRoutes) {
      await this.testRoute(route)
    }
  }

  // 测试特殊路由
  async testSpecialRoutes() {
    console.log('⚡ 测试特殊路由...')
    
    const specialRoutes = [
      { path: '/', name: '根路径重定向', shouldRedirect: '/login' },
      { path: '/community', name: '社群管理重定向', shouldRedirect: '/community/groups' },
      { path: '/system', name: '系统设置重定向', shouldRedirect: '/system/settings' }
    ]

    for (const route of specialRoutes) {
      await this.testRedirectRoute(route)
    }
  }

  // 测试认证路由
  async testAuthRoutes() {
    console.log('🔐 测试认证路由...')
    
    // 清除token测试未认证状态
    localStorage.removeItem('token')
    
    const authRoutes = [
      { path: '/dashboard', name: '控制台(未认证)', shouldRedirectToLogin: true },
      { path: '/users', name: '用户管理(未认证)', shouldRedirectToLogin: true }
    ]

    for (const route of authRoutes) {
      await this.testAuthRoute(route)
    }
  }

  // 测试404路由
  async test404Routes() {
    console.log('❌ 测试404路由...')
    
    const invalidRoutes = [
      { path: '/nonexistent', name: '不存在的路径1' },
      { path: '/invalid/path', name: '不存在的路径2' },
      { path: '/test/invalid', name: '不存在的路径3' }
    ]

    for (const route of invalidRoutes) {
      await this.test404Route(route)
    }
  }

  // 测试单个路由
  async testRoute(route) {
    try {
      this.currentTest = `测试路由: ${route.path} (${route.name})`
      console.log(`  🔍 ${this.currentTest}`)
      
      const startTime = Date.now()
      
      // 尝试导航到路由
      await this.router.push(route.path)
      
      // 等待路由加载
      await this.waitForRouteLoad()
      
      const loadTime = Date.now() - startTime
      
      // 检查当前路由
      const currentRoute = this.router.currentRoute.value
      const isCorrectPath = currentRoute.path === route.path
      
      // 检查页面标题
      const hasCorrectTitle = document.title.includes(route.name) || document.title.includes('晨鑫流量变现系统')
      
      // 检查页面内容
      const hasContent = this.checkPageContent()
      
      // 检查控制台错误
      const hasErrors = this.checkConsoleErrors()
      
      const result = {
        path: route.path,
        name: route.name,
        success: isCorrectPath && hasContent && !hasErrors,
        loadTime,
        details: {
          correctPath: isCorrectPath,
          correctTitle: hasCorrectTitle,
          hasContent,
          noErrors: !hasErrors,
          actualPath: currentRoute.path,
          actualTitle: document.title
        }
      }
      
      this.testResults.push(result)
      
      if (result.success) {
        console.log(`    ✅ 成功 (${loadTime}ms)`)
      } else {
        console.log(`    ❌ 失败:`, result.details)
      }
      
    } catch (error) {
      console.error(`    💥 错误:`, error)
      this.testResults.push({
        path: route.path,
        name: route.name,
        success: false,
        error: error.message
      })
    }
  }

  // 测试重定向路由
  async testRedirectRoute(route) {
    try {
      console.log(`  🔄 测试重定向: ${route.path} -> ${route.shouldRedirect}`)
      
      await this.router.push(route.path)
      await this.waitForRouteLoad()
      
      const currentRoute = this.router.currentRoute.value
      const isCorrectRedirect = currentRoute.path === route.shouldRedirect
      
      const result = {
        path: route.path,
        name: route.name,
        success: isCorrectRedirect,
        type: 'redirect',
        expectedPath: route.shouldRedirect,
        actualPath: currentRoute.path
      }
      
      this.testResults.push(result)
      
      if (result.success) {
        console.log(`    ✅ 重定向成功`)
      } else {
        console.log(`    ❌ 重定向失败: 期望 ${route.shouldRedirect}, 实际 ${currentRoute.path}`)
      }
      
    } catch (error) {
      console.error(`    💥 重定向错误:`, error)
    }
  }

  // 测试认证路由
  async testAuthRoute(route) {
    try {
      console.log(`  🔒 测试认证路由: ${route.path}`)
      
      await this.router.push(route.path)
      await this.waitForRouteLoad()
      
      const currentRoute = this.router.currentRoute.value
      const redirectedToLogin = currentRoute.path === '/login'
      
      const result = {
        path: route.path,
        name: route.name,
        success: redirectedToLogin,
        type: 'auth',
        expectedBehavior: '重定向到登录页',
        actualPath: currentRoute.path
      }
      
      this.testResults.push(result)
      
      if (result.success) {
        console.log(`    ✅ 认证检查成功，已重定向到登录页`)
      } else {
        console.log(`    ❌ 认证检查失败: 应重定向到登录页，实际路径 ${currentRoute.path}`)
      }
      
    } catch (error) {
      console.error(`    💥 认证测试错误:`, error)
    }
  }

  // 测试404路由
  async test404Route(route) {
    try {
      console.log(`  🚫 测试404路由: ${route.path}`)
      
      await this.router.push(route.path)
      await this.waitForRouteLoad()
      
      const currentRoute = this.router.currentRoute.value
      const isNotFound = currentRoute.name === 'NotFound' || currentRoute.path.includes('pathMatch')
      
      const result = {
        path: route.path,
        name: route.name,
        success: isNotFound,
        type: '404',
        actualRoute: currentRoute.name,
        actualPath: currentRoute.path
      }
      
      this.testResults.push(result)
      
      if (result.success) {
        console.log(`    ✅ 404处理正确`)
      } else {
        console.log(`    ❌ 404处理失败: 路由名 ${currentRoute.name}, 路径 ${currentRoute.path}`)
      }
      
    } catch (error) {
      console.error(`    💥 404测试错误:`, error)
    }
  }

  // 等待路由加载
  async waitForRouteLoad() {
    return new Promise(resolve => {
      setTimeout(resolve, 500) // 等待500ms让路由完全加载
    })
  }

  // 检查页面内容
  checkPageContent() {
    const body = document.body
    const hasMainContent = body.querySelector('.main-content') || 
                          body.querySelector('.page-container') ||
                          body.querySelector('[data-testid]') ||
                          body.textContent.trim().length > 100
    return !!hasMainContent
  }

  // 检查控制台错误
  checkConsoleErrors() {
    // 这里可以集成错误监听器的结果
    // 暂时返回false表示没有严重错误
    return false
  }

  // 生成测试报告
  generateReport() {
    console.log('\n📊 导航测试报告')
    console.log('=' * 50)
    
    const totalTests = this.testResults.length
    const successfulTests = this.testResults.filter(r => r.success).length
    const failedTests = totalTests - successfulTests
    const successRate = ((successfulTests / totalTests) * 100).toFixed(1)
    
    console.log(`总测试数: ${totalTests}`)
    console.log(`成功: ${successfulTests}`)
    console.log(`失败: ${failedTests}`)
    console.log(`成功率: ${successRate}%`)
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:')
      this.testResults.filter(r => !r.success).forEach(result => {
        console.log(`  - ${result.name} (${result.path})`)
        if (result.error) {
          console.log(`    错误: ${result.error}`)
        }
        if (result.details) {
          console.log(`    详情:`, result.details)
        }
      })
    }
    
    console.log('\n✅ 测试完成!')
    return {
      total: totalTests,
      successful: successfulTests,
      failed: failedTests,
      successRate: parseFloat(successRate),
      results: this.testResults
    }
  }
}

// 创建全局测试器实例
export const navigationTester = new NavigationTester()

// 在浏览器控制台中可用的快捷方法
if (typeof window !== 'undefined') {
  window.testNavigation = () => {
    if (navigationTester.router) {
      return navigationTester.startTest()
    } else {
      console.error('❌ 导航测试器未初始化，请先调用 navigationTester.init(router)')
    }
  }
}
