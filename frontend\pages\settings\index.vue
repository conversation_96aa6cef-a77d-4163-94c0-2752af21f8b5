

<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">账户设置</h1>
          <p class="text-gray-600 mt-1">管理您的个人资料、安全设置和通知偏好</p>
        </div>
      </div>
    </div>

    <!-- 设置导航 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="border-b border-gray-200">
        <nav class="flex space-x-8 px-6">
          <button 
            v-for="tab in settingTabs" 
            :key="tab.key"
            @click="activeTab = tab.key"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm',
              activeTab === tab.key
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="tab.icon"></path>
            </svg>
            {{ tab.name }}
          </button>
        </nav>
      </div>
      
      <div class="p-6">
        <!-- 个人资料设置 -->
        <div v-if="activeTab === 'profile'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">个人资料</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 头像上传 -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">头像</label>
                <div class="flex items-center space-x-4">
                  <div class="relative">
                    <img 
                      :src="profileForm.avatar || '/default-avatar.png'" 
                      alt="用户头像"
                      class="w-20 h-20 rounded-full border-4 border-white shadow-lg"
                    />
                    <button 
                      @click="triggerAvatarUpload"
                      class="absolute bottom-0 right-0 p-1 bg-blue-600 text-white rounded-full hover:bg-blue-700"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                    </button>
                  </div>
                  <div>
                    <p class="text-sm text-gray-600">点击头像更换照片</p>
                    <p class="text-xs text-gray-500">建议尺寸：200x200像素，格式：JPG、PNG</p>
                  </div>
                  <input 
                    ref="avatarInput"
                    type="file" 
                    accept="image/*" 
                    @change="handleAvatarUpload"
                    class="hidden"
                  />
                </div>
              </div>
              
              <!-- 基本信息 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">昵称</label>
                <input 
                  v-model="profileForm.nickname" 
                  type="text" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入昵称"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">真实姓名</label>
                <input 
                  v-model="profileForm.real_name" 
                  type="text" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入真实姓名"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">手机号码</label>
                <input 
                  v-model="profileForm.phone" 
                  type="tel" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入手机号码"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">邮箱地址</label>
                <input 
                  v-model="profileForm.email" 
                  type="email" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入邮箱地址"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">微信号</label>
                <input 
                  v-model="profileForm.wechat_id" 
                  type="text" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入微信号"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">QQ号码</label>
                <input 
                  v-model="profileForm.qq" 
                  type="text" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入QQ号码"
                />
              </div>
              
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">个人简介</label>
                <textarea 
                  v-model="profileForm.bio" 
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入个人简介"
                />
              </div>
            </div>
            
            <div class="flex justify-end mt-6">
              <button 
                @click="updateProfile"
                :disabled="profileLoading"
                class="btn-primary"
              >
                {{ profileLoading ? '保存中...' : '保存个人资料' }}
              </button>
            </div>
          </div>
        </div>
        
        <!-- 安全设置 -->
        <div v-if="activeTab === 'security'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">安全设置</h3>
            
            <!-- 修改密码 -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
              <h4 class="font-medium text-gray-900 mb-4">修改密码</h4>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">当前密码</label>
                  <input 
                    v-model="passwordForm.current_password" 
                    type="password" 
                    class="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入当前密码"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">新密码</label>
                  <input 
                    v-model="passwordForm.new_password" 
                    type="password" 
                    class="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入新密码"
                  />
                  <p class="text-xs text-gray-500 mt-1">密码长度至少8位，包含字母和数字</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">确认新密码</label>
                  <input 
                    v-model="passwordForm.confirm_password" 
                    type="password" 
                    class="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请再次输入新密码"
                  />
                </div>
                
                <button 
                  @click="updatePassword"
                  :disabled="passwordLoading || !isPasswordFormValid"
                  class="btn-primary"
                >
                  {{ passwordLoading ? '修改中...' : '修改密码' }}
                </button>
              </div>
            </div>
            
            <!-- 双因子认证 -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="font-medium text-gray-900">双因子认证</h4>
                  <p class="text-sm text-gray-600 mt-1">为您的账户添加额外的安全保护</p>
                </div>
                <div class="flex items-center">
                  <button 
                    @click="toggle2FA"
                    :disabled="twoFactorLoading"
                    :class="[
                      'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                      securitySettings.two_factor_enabled ? 'bg-blue-600' : 'bg-gray-200'
                    ]"
                  >
                    <span 
                      :class="[
                        'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                        securitySettings.two_factor_enabled ? 'translate-x-5' : 'translate-x-0'
                      ]"
                    />
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 登录设备管理 -->
            <div class="bg-gray-50 rounded-lg p-6">
              <div class="flex items-center justify-between mb-4">
                <h4 class="font-medium text-gray-900">登录设备</h4>
                <button 
                  @click="loadLoginDevices"
                  class="btn btn-secondary text-sm"
                >
                  刷新
                </button>
              </div>
              
              <div v-if="devicesLoading" class="text-center py-4">
                <div class="spinner mx-auto mb-2"></div>
                <p class="text-sm text-gray-600">加载中...</p>
              </div>
              
              <div v-else class="space-y-3">
                <div v-for="device in loginDevices" :key="device.id" class="flex items-center justify-between p-3 bg-white rounded border">
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-gray-100 rounded">
                      <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900">{{ device.device_name || device.user_agent }}</p>
                      <p class="text-sm text-gray-500">{{ device.ip_address }} · {{ formatDate(device.last_used_at) }}</p>
                      <p v-if="device.is_current" class="text-xs text-green-600">当前设备</p>
                    </div>
                  </div>
                  <button 
                    v-if="!device.is_current"
                    @click="revokeDevice(device)"
                    class="text-red-600 hover:text-red-800 text-sm"
                  >
                    移除
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 通知设置 -->
        <div v-if="activeTab === 'notifications'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">通知设置</h3>
            
            <div class="space-y-6">
              <!-- 邮件通知 -->
              <div>
                <h4 class="font-medium text-gray-900 mb-3">邮件通知</h4>
                <div class="space-y-3">
                  <div v-for="option in emailNotifications" :key="option.key" class="flex items-center justify-between">
                    <div>
                      <p class="font-medium text-gray-900">{{ option.title }}</p>
                      <p class="text-sm text-gray-600">{{ option.description }}</p>
                    </div>
                    <button 
                      @click="toggleNotification('email', option.key)"
                      :class="[
                        'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                        notificationSettings.email[option.key] ? 'bg-blue-600' : 'bg-gray-200'
                      ]"
                    >
                      <span 
                        :class="[
                          'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                          notificationSettings.email[option.key] ? 'translate-x-5' : 'translate-x-0'
                        ]"
                      />
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- 短信通知 -->
              <div>
                <h4 class="font-medium text-gray-900 mb-3">短信通知</h4>
                <div class="space-y-3">
                  <div v-for="option in smsNotifications" :key="option.key" class="flex items-center justify-between">
                    <div>
                      <p class="font-medium text-gray-900">{{ option.title }}</p>
                      <p class="text-sm text-gray-600">{{ option.description }}</p>
                    </div>
                    <button 
                      @click="toggleNotification('sms', option.key)"
                      :class="[
                        'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                        notificationSettings.sms[option.key] ? 'bg-blue-600' : 'bg-gray-200'
                      ]"
                    >
                      <span 
                        :class="[
                          'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                          notificationSettings.sms[option.key] ? 'translate-x-5' : 'translate-x-0'
                        ]"
                      />
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- 微信通知 -->
              <div>
                <h4 class="font-medium text-gray-900 mb-3">微信通知</h4>
                <div class="space-y-3">
                  <div v-for="option in wechatNotifications" :key="option.key" class="flex items-center justify-between">
                    <div>
                      <p class="font-medium text-gray-900">{{ option.title }}</p>
                      <p class="text-sm text-gray-600">{{ option.description }}</p>
                    </div>
                    <button 
                      @click="toggleNotification('wechat', option.key)"
                      :class="[
                        'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                        notificationSettings.wechat[option.key] ? 'bg-blue-600' : 'bg-gray-200'
                      ]"
                    >
                      <span 
                        :class="[
                          'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                          notificationSettings.wechat[option.key] ? 'translate-x-5' : 'translate-x-0'
                        ]"
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="flex justify-end mt-6">
              <button 
                @click="updateNotificationSettings"
                :disabled="notificationLoading"
                class="btn-primary"
              >
                {{ notificationLoading ? '保存中...' : '保存通知设置' }}
              </button>
            </div>
          </div>
        </div>
        
        <!-- 账户管理 -->
        <div v-if="activeTab === 'account'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">账户管理</h3>
            
            <!-- 账户信息 -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
              <h4 class="font-medium text-gray-900 mb-4">账户信息</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-600">用户ID:</span>
                  <span class="ml-2 font-medium">{{ accountInfo.id }}</span>
                </div>
                <div>
                  <span class="text-gray-600">注册时间:</span>
                  <span class="ml-2 font-medium">{{ formatDate(accountInfo.created_at) }}</span>
                </div>
                <div>
                  <span class="text-gray-600">最后登录:</span>
                  <span class="ml-2 font-medium">{{ formatDate(accountInfo.last_login_at) }}</span>
                </div>
                <div>
                  <span class="text-gray-600">账户状态:</span>
                  <span 
                    :class="[
                      'ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                      accountInfo.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ accountInfo.status === 'active' ? '正常' : '异常' }}
                  </span>
                </div>
              </div>
            </div>
            
            <!-- 数据导出 -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="font-medium text-gray-900">数据导出</h4>
                  <p class="text-sm text-gray-600 mt-1">导出您在平台上的所有数据</p>
                </div>
                <button 
                  @click="exportUserData"
                  :disabled="exportLoading"
                  class="btn btn-secondary"
                >
                  {{ exportLoading ? '导出中...' : '导出数据' }}
                </button>
              </div>
            </div>
            
            <!-- 账户注销 -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
              <div class="flex items-start space-x-3">
                <svg class="w-6 h-6 text-red-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div class="flex-1">
                  <h4 class="font-medium text-red-900">注销账户</h4>
                  <p class="text-sm text-red-700 mt-1">
                    注销账户将永久删除您的所有数据，此操作不可逆转。请在注销前确保已导出重要数据。
                  </p>
                  <button 
                    @click="showDeleteModal = true"
                    class="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
                  >
                    注销账户
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认删除账户弹窗 -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-red-900">确认注销账户</h3>
          <button 
            @click="showDeleteModal = false"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="space-y-4">
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <p class="text-sm text-red-700">
              <strong>警告：</strong>此操作将永久删除您的账户和所有相关数据，包括：
            </p>
            <ul class="text-sm text-red-700 mt-2 list-disc list-inside space-y-1">
              <li>个人资料和设置</li>
              <li>订单和交易记录</li>
              <li>推广链接和团队数据</li>
              <li>财务记录和余额</li>
            </ul>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              请输入您的密码以确认注销操作
            </label>
            <input 
              v-model="deleteForm.password" 
              type="password" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="请输入账户密码"
            />
          </div>
          
          <div class="flex items-center">
            <input 
              v-model="deleteForm.confirmed" 
              type="checkbox" 
              class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
            />
            <label class="ml-2 text-sm text-gray-700">
              我已了解注销后果，确认注销我的账户
            </label>
          </div>
          
          <div class="flex space-x-4">
            <button 
              @click="showDeleteModal = false"
              class="flex-1 btn btn-secondary"
            >
              取消
            </button>
            <button 
              @click="deleteAccount"
              :disabled="!deleteForm.password || !deleteForm.confirmed || deleteLoading"
              class="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              {{ deleteLoading ? '注销中...' : '确认注销' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面需要认证
definePageMeta({
  middleware: 'auth',
  layout: 'default'
})

import { useApi } from '~/composables/useApi'



// 页面头部
useHead({
  title: '账户设置 - 晨鑫流量变现系统',
  meta: [
    { name: 'description', content: '账户设置页面，管理您的个人资料、安全设置和通知偏好' }
  ]
})

const api = useApi()

// 响应式数据
const loading = ref(false)
const profileLoading = ref(false)
const passwordLoading = ref(false)
const twoFactorLoading = ref(false)
const devicesLoading = ref(false)
const notificationLoading = ref(false)
const exportLoading = ref(false)
const deleteLoading = ref(false)

// 弹窗状态
const showDeleteModal = ref(false)

// 设置选项卡
const activeTab = ref('profile')
const settingTabs = ref([
  { key: 'profile', name: '个人资料', icon: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z' },
  { key: 'security', name: '安全设置', icon: 'M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z' },
  { key: 'notifications', name: '通知设置', icon: 'M15 17h5l-5 5v-5zM5.868 12.485C6.9 11.477 8.495 11 10 11c2.485 0 4.5 2.015 4.5 4.5S12.485 20 10 20s-4.5-2.015-4.5-4.5c0-.96.298-1.85.806-2.582z' },
  { key: 'account', name: '账户管理', icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z' }
])

// 表单数据
const profileForm = ref({
  avatar: '',
  nickname: '',
  real_name: '',
  phone: '',
  email: '',
  wechat_id: '',
  qq: '',
  bio: ''
})

const passwordForm = ref({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

const deleteForm = ref({
  password: '',
  confirmed: false
})

// 安全设置
const securitySettings = ref({
  two_factor_enabled: false
})

// 登录设备
const loginDevices = ref([])

// 通知设置
const notificationSettings = ref({
  email: {
    order_updates: true,
    commission_alerts: true,
    system_notices: false,
    marketing: false
  },
  sms: {
    order_updates: true,
    commission_alerts: false,
    system_notices: false,
    marketing: false
  },
  wechat: {
    order_updates: true,
    commission_alerts: true,
    system_notices: true,
    marketing: false
  }
})

// 通知选项
const emailNotifications = ref([
  { key: 'order_updates', title: '订单更新', description: '订单状态变化时发送邮件通知' },
  { key: 'commission_alerts', title: '佣金提醒', description: '获得新佣金时发送邮件通知' },
  { key: 'system_notices', title: '系统公告', description: '接收重要的系统通知和公告' },
  { key: 'marketing', title: '营销推广', description: '接收产品更新和促销信息' }
])

const smsNotifications = ref([
  { key: 'order_updates', title: '订单更新', description: '重要订单状态变化时发送短信' },
  { key: 'commission_alerts', title: '佣金提醒', description: '大额佣金收入时发送短信提醒' },
  { key: 'system_notices', title: '系统公告', description: '紧急系统通知发送短信' },
  { key: 'marketing', title: '营销推广', description: '重要促销活动短信通知' }
])

const wechatNotifications = ref([
  { key: 'order_updates', title: '订单更新', description: '通过微信公众号推送订单通知' },
  { key: 'commission_alerts', title: '佣金提醒', description: '微信推送佣金收入提醒' },
  { key: 'system_notices', title: '系统公告', description: '微信推送系统重要通知' },
  { key: 'marketing', title: '营销推广', description: '微信推送产品和活动信息' }
])

// 账户信息
const accountInfo = ref({
  id: 0,
  created_at: '',
  last_login_at: '',
  status: 'active'
})

// 计算属性
const isPasswordFormValid = computed(() => {
  return passwordForm.value.current_password &&
         passwordForm.value.new_password &&
         passwordForm.value.confirm_password &&
         passwordForm.value.new_password === passwordForm.value.confirm_password &&
         passwordForm.value.new_password.length >= 8
})

// 引用
const avatarInput = ref(null)

// 工具函数
const formatDate = (date: string) => {
  if (!date) return ''
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

// 数据加载函数
const loadUserProfile = async () => {
  try {
    const response = await api.user.getProfile()
    if (response.success && response.data) {
      const userData = response.data;
      profileForm.value = {
        avatar: userData.avatar || '',
        nickname: userData.nickname || '',
        real_name: userData.real_name || '',
        phone: userData.phone || '',
        email: userData.email || '',
        wechat_id: userData.wechat_id || '',
        qq: userData.qq || '',
        bio: userData.bio || ''
      };
      accountInfo.value = {
        id: userData.id,
        created_at: userData.created_at,
        last_login_at: userData.last_login_at || '',
        status: userData.status === 1 ? 'active' : 'inactive'
      };
    }
  } catch (error) {
    console.error('加载用户资料失败:', error)
  }
}

const loadSecuritySettings = async () => {
  // try {
  //   const response = await api.user.getSecuritySettings()
  //   if (response.success) {
  //     securitySettings.value = response.data
  //   }
  // } catch (error) {
  //   console.error('加载安全设置失败:', error)
  // }
}

const loadLoginDevices = async () => {
  devicesLoading.value = true
  // try {
  //   const response = await api.user.getLoginDevices()
  //   if (response.success) {
  //     loginDevices.value = response.data || []
  //   }
  // } catch (error) {
  //   console.error('加载登录设备失败:', error)
  // } finally {
  //   devicesLoading.value = false
  // }
}

const loadNotificationSettings = async () => {
  // try {
  //   const response = await api.user.getNotificationSettings()
  //   if (response.success) {
  //     notificationSettings.value = response.data
  //   }
  // } catch (error) {
  //   console.error('加载通知设置失败:', error)
  // }
}

// 操作函数
const triggerAvatarUpload = () => {
  avatarInput.value?.click()
}

const handleAvatarUpload = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  try {
    const formData = new FormData()
    formData.append('avatar', file)
    
    const response = await api.user.uploadAvatar(formData)
    if (response.success) {
      profileForm.value.avatar = response.data.avatar_url
      alert('头像上传成功')
    } else {
      alert(response.message || '头像上传失败')
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    alert('头像上传失败，请重试')
  }
}

const updateProfile = async () => {
  profileLoading.value = true
  try {
    const response = await api.user.updateProfile(profileForm.value)
    if (response.success) {
      alert('个人资料更新成功')
    } else {
      alert(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新个人资料失败:', error)
    alert('更新失败，请重试')
  } finally {
    profileLoading.value = false
  }
}

const updatePassword = async () => {
  if (!isPasswordFormValid.value) {
    alert('请检查密码输入')
    return
  }
  alert('此功能正在开发中，敬请期待！');
  // passwordLoading.value = true
  // try {
  //   const response = await api.user.updatePassword(passwordForm.value)
  //   if (response.success) {
  //     alert('密码修改成功')
  //     passwordForm.value = {
  //       current_password: '',
  //       new_password: '',
  //       confirm_password: ''
  //     }
  //   } else {
  //     alert(response.message || '密码修改失败')
  //   }
  // } catch (error) {
  //   console.error('密码修改失败:', error)
  //   alert('密码修改失败，请重试')
  // } finally {
  //   passwordLoading.value = false
  // }
}

const toggle2FA = async () => {
  alert('此功能正在开发中，敬请期待！');
  // twoFactorLoading.value = true
  // try {
  //   const action = securitySettings.value.two_factor_enabled ? 'disable' : 'enable'
  //   const response = await api.user.toggle2FA(action)
  //   if (response.success) {
  //     securitySettings.value.two_factor_enabled = !securitySettings.value.two_factor_enabled
  //     alert(`双因子认证已${action === 'enable' ? '启用' : '关闭'}`)
  //   } else {
  //     alert(response.message || '操作失败')
  //   }
  // } catch (error) {
  //   console.error('切换双因子认证失败:', error)
  //   alert('操作失败，请重试')
  // } finally {
  //   twoFactorLoading.value = false
  // }
}

const revokeDevice = async (device: any) => {
  alert('此功能正在开发中，敬请期待！');
  // if (!confirm('确定要移除这个设备吗？')) return

  // try {
  //   const response = await api.user.revokeDevice(device.id)
  //   if (response.success) {
  //     await loadLoginDevices()
  //     alert('设备已移除')
  //   } else {
  //     alert(response.message || '移除失败')
  //   }
  // } catch (error) {
  //   console.error('移除设备失败:', error)
  //   alert('移除失败，请重试')
  // }
}

const toggleNotification = (type: string, key: string) => {
  notificationSettings.value[type][key] = !notificationSettings.value[type][key]
}

const updateNotificationSettings = async () => {
  alert('此功能正在开发中，敬请期待！');
  // notificationLoading.value = true
  // try {
  //   const response = await api.user.updateNotificationSettings(notificationSettings.value)
  //   if (response.success) {
  //     alert('通知设置已更新')
  //   } else {
  //     alert(response.message || '更新失败')
  //   }
  // } catch (error) {
  //   console.error('更新通知设置失败:', error)
  //   alert('更新失败，请重试')
  // } finally {
  //   notificationLoading.value = false
  // }
}

const exportUserData = async () => {
  alert('此功能正在开发中，敬请期待！');
  // exportLoading.value = true
  // try {
  //   const response = await api.user.exportData()
  //   if (response.success) {
  //     // 创建下载链接
  //     const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' })
  //     const url = URL.createObjectURL(blob)
  //     const link = document.createElement('a')
  //     link.href = url
  //     link.download = `user_data_${new Date().toISOString().split('T')[0]}.json`
  //     link.click()
  //     URL.revokeObjectURL(url)
      
  //     alert('数据导出成功')
  //   } else {
  //     alert(response.message || '导出失败')
  //   }
  // } catch (error) {
  //   console.error('导出数据失败:', error)
  //   alert('导出失败，请重试')
  // } finally {
  //   exportLoading.value = false
  // }
}

const deleteAccount = async () => {
  alert('此功能正在开发中，敬请期待！');
  // if (!deleteForm.value.password || !deleteForm.value.confirmed) {
  //   alert('请确认删除信息')
  //   return
  // }

  // deleteLoading.value = true
  // try {
  //   const response = await api.user.deleteAccount(deleteForm.value.password)
  //   if (response.success) {
  //     alert('账户已成功注销')
  //     // 清除本地数据并跳转到首页
  //     await navigateTo('/auth/login')
  //   } else {
  //     alert(response.message || '注销失败')
  //   }
  // } catch (error) {
  //   console.error('账户注销失败:', error)
  //   alert('注销失败，请重试')
  // } finally {
  //   deleteLoading.value = false
  // }
}

// 页面初始化
onMounted(async () => {
  await loadUserProfile();
  // await Promise.all([
  //   loadUserProfile(),
  //   loadSecuritySettings(),
  //   loadNotificationSettings()
  // ])
})

// 监听选项卡变化
watch(activeTab, async (newTab) => {
  if (newTab === 'security') {
    await loadLoginDevices()
  }
})
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

.spinner {
  @apply w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin;
}
</style> 