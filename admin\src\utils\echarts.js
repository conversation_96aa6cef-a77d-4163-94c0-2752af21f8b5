/**
 * ECharts 全局配置
 * 使用ECharts管理器确保组件只注册一次，防止重复注册错误
 */

import { initializeECharts } from './echartsManager.js'

// 使用管理器初始化ECharts，避免重复注册问题
initializeECharts().then(() => {
  console.log('✅ ECharts 组件管理器已初始化')
}).catch(error => {
  console.error('❌ ECharts 组件管理器初始化失败:', error)
})

// 默认主题配置
export const defaultTheme = {
  color: [
    '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
    '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f',
    '#ffdb5c', '#ff7875', '#87d068', '#108ee9', '#722ed1'
  ],
  backgroundColor: 'transparent',
  textStyle: {
    fontFamily: 'PingFang SC, Microsoft YaHei, Arial, sans-serif',
    fontSize: 12,
    color: '#333'
  },
  animation: true,
  animationDuration: 1000,
  animationEasing: 'cubicOut'
}

// 通用图表配置
export const commonOptions = {
  // 通用网格配置
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  
  // 通用工具提示配置
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(50, 50, 50, 0.9)',
    borderColor: 'rgba(50, 50, 50, 0.9)',
    textStyle: {
      color: '#fff',
      fontSize: 12
    },
    extraCssText: 'border-radius: 4px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);'
  },
  
  // 通用图例配置
  legend: {
    type: 'scroll',
    orient: 'horizontal',
    left: 'center',
    top: 'top',
    textStyle: {
      color: '#666',
      fontSize: 12
    }
  }
}

// 响应式配置
export const responsiveOptions = {
  // 移动端配置
  mobile: {
    grid: {
      left: '5%',
      right: '5%',
      bottom: '5%',
      containLabel: true
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: 'bottom'
    }
  },
  
  // 平板配置
  tablet: {
    grid: {
      left: '4%',
      right: '4%',
      bottom: '4%',
      containLabel: true
    }
  }
}

// 工具函数：获取响应式配置
export function getResponsiveConfig() {
  const width = window.innerWidth
  
  if (width < 768) {
    return responsiveOptions.mobile
  } else if (width < 1024) {
    return responsiveOptions.tablet
  }
  
  return {}
}

// 工具函数：合并配置
export function mergeOptions(baseOptions, customOptions = {}) {
  return {
    ...commonOptions,
    ...baseOptions,
    ...customOptions,
    ...getResponsiveConfig()
  }
}

export default {
  defaultTheme,
  commonOptions,
  responsiveOptions,
  getResponsiveConfig,
  mergeOptions
}
