const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ErrorPage-5JCWG06-.js","assets/vue-vendor-BcnDv-68.js","assets/element-plus-C2UshkXo.js","assets/index-eUTsTR3J.js","assets/utils-SdQ7DxjY.js","assets/echarts-D6CUuNS9.js","assets/index-XVEiIDg_.css","assets/ErrorPage-wvIK7dNr.css","assets/base-pYMXRPpM.css","assets/el-button-CDqfIFiK.css","assets/el-collapse-item-BqS7tZDP.css"])))=>i.map(i=>d[i]);
/* empty css             *//* empty css                   *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                  *//* empty css                */import{l as e,m as t,G as a,A as i,E as s,q as n,C as r,F as o,Y as l,z as c,ag as d,r as u,c as m,o as p,an as g,D as v,n as h,t as f,w as y,I as b,a8 as k,ai as _,M as w}from"./vue-vendor-BcnDv-68.js";import{_ as C,u as S,a as A}from"./index-eUTsTR3J.js";import{Z as D,a1 as x,V as R,U as T,Y as I,ak as z,ah as U,aZ as M,s as P,cj as O,b2 as $,cq as j,af as N,bX as V,a9 as L,X as H,aG as E,bV as G,aI as W,aJ as q,aH as F,W as Q,bv as B,cr as J,b5 as K,cs as Y,bx as X,b0 as Z,b1 as ee,ci as te,av as ae,b9 as ie,ct as se,bO as ne,u as re,bB as oe,a4 as le,aQ as ce,c3 as de,aF as ue,aE as me,at as pe,aT as ge,bt as ve,$ as he,_ as fe,bf as ye,bA as be,a5 as ke,aK as _e,bg as we,aN as Ce,cu as Se,bn as Ae,bH as De,au as xe,bW as Re,ae as Te,a6 as Ie}from"./element-plus-C2UshkXo.js";/* empty css                  *//* empty css                 *//* empty css               */import"./utils-SdQ7DxjY.js";import{roleHierarchy as ze,getRoleDisplayName as Ue,filterRoutesByRole as Me}from"./navigation-DbqezFjv.js";/* empty css                         *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";/* empty css                       *//* empty css                        */import{H as Pe}from"./echarts-D6CUuNS9.js";function Oe(e){const t=ze[e];return!!t&&t.groupCreatePermission}function $e(e){const t=ze[e];return t?t.dashboardScope:"user_personal"}function je(e){const t=ze[e];return t?t.financeScope:"user_consumption"}const Ne={class:"navigation-test-panel"},Ve={class:"test-content"},Le={class:"card-header"},He={class:"status-info"},Ee={class:"status-item"},Ge={class:"status-item"},We={class:"status-item"},qe={class:"card-header"},Fe={class:"role-switch-content"},Qe={class:"role-buttons"},Be={key:0,class:"role-info"},Je={class:"card-header"},Ke={class:"group-creation-content"},Ye={class:"creation-tests"},Xe={class:"test-buttons"},Ze={class:"card-header"},et={class:"navigation-content"},tt={class:"navigation-stats"},at={class:"stat-item"},it={class:"stat-number"},st={class:"stat-item"},nt={class:"stat-number"},rt={class:"stat-item"},ot={class:"stat-number"},lt={class:"route-list"},ct={class:"routes"},dt={class:"card-header"},ut={class:"log-content"},mt={class:"log-list"},pt={class:"log-time"},gt={class:"log-message"};const vt=C({__name:"NavigationTestPanel",setup(e,{expose:t}){t();const a=d(),i=S(),s=u(!1),n=u(!1),r=u(""),o=u([]),l=m(()=>i.userInfo?.role||"user"),c=m(()=>{const e=Oe(l.value);return{title:e?"✅ 群组创建功能可用":"❌ 群组创建功能不可用",type:e?"success":"error",description:`当前角色 ${Ue(l.value)} ${e?"拥有":"没有"}群组创建权限`}}),g=m(()=>{try{const e=a.options.routes.filter(e=>!(!e||"string"!=typeof e.path)&&("/login"!==e.path&&"/"!==e.path&&!e.meta?.hidden)),t=JSON.parse(JSON.stringify(e)),i=Me(t,l.value);return y("info",`成功计算可见路由: ${i.length} 个`),i.length}catch(e){return y("error",`计算可见路由失败: ${e.message}`),0}}),v=m(()=>{try{const e=a.options.routes.filter(e=>!(!e||"string"!=typeof e.path)&&("/login"!==e.path&&"/"!==e.path&&!e.meta?.hidden)),t=JSON.parse(JSON.stringify(e));return Me(t,l.value).slice(0,8)}catch(e){return y("error",`计算示例路由失败: ${e.message}`),[]}}),h=m(()=>{let e=0;return Oe(l.value)&&(e+=1),e+=1,e+=1,e}),f=m(()=>({admin:5,substation:4,agent:4,distributor:4,group_owner:4,user:3}[l.value]||3)),y=(e,t)=>{o.value.unshift({type:e,message:t,time:new Date}),o.value.length>20&&(o.value=o.value.slice(0,20))};p(()=>{y("info","导航测试面板已加载"),y("info",`当前用户角色: ${Ue(l.value)}`);const e=localStorage.getItem("current-test-role");e&&e!==l.value&&y("info",`检测到保存的测试角色: ${Ue(e)}`),Oe(l.value)?y("success","群组创建权限检测通过"):y("warning","群组创建权限检测失败"),y("info",`数据权限范围: ${$e(l.value)}`),y("info",`财务权限范围: ${je(l.value)}`)});const b={router:a,userStore:i,showTestPanel:s,switching:n,targetRole:r,testLogs:o,testRoles:[{key:"admin",name:"管理员"},{key:"substation",name:"分站管理员"},{key:"agent",name:"代理商"},{key:"distributor",name:"分销员"},{key:"group_owner",name:"群主"},{key:"user",name:"普通用户"}],currentUserRole:l,groupCreationStatus:c,visibleRoutesCount:g,sampleVisibleRoutes:v,groupCreationEntries:h,quickActionsCount:f,addLog:y,switchToRole:async e=>{if(!n.value){n.value=!0,r.value=e,y("info",`开始切换角色到: ${Ue(e)}`);try{await new Promise(e=>setTimeout(e,800));const t={id:"preview-user",username:"admin",nickname:`${Ue(e)} (预览)`,name:"预览用户",email:"<EMAIL>",avatar:"/default-avatar.png",role:e,roles:[e],permissions:"admin"===e?["*"]:[e]};i.setUserInfo(t),localStorage.setItem("preview-user-info",JSON.stringify(t)),localStorage.setItem("preview-mode","true"),localStorage.setItem("current-test-role",e),y("success",`角色切换成功: ${Ue(e)}`),H.success(`已切换到 ${Ue(e)}，页面即将刷新`),setTimeout(()=>{window.location.reload()},1500)}catch(t){y("error",`角色切换失败: ${t.message}`),H.error("角色切换失败")}finally{n.value=!1,r.value=""}}},testGroupCreation:()=>{if(y("info","测试群组创建功能"),!Oe(l.value))return y("error","当前角色没有群组创建权限"),void H.error("当前角色没有群组创建权限");try{a.push("/community/add-enhanced"),y("success","成功导航到群组创建页面"),H.success("群组创建功能测试通过")}catch(e){y("error",`导航到群组创建页面失败: ${e.message}`),H.error("群组创建功能测试失败")}},testNavigationAccess:()=>{y("info","测试导航访问权限");const e=["/dashboard","/community/groups","/user/list","/finance/dashboard"];let t=0;e.forEach(e=>{try{t++,y("success",`路由 ${e} 访问权限正常`)}catch(a){y("error",`路由 ${e} 访问权限异常: ${a.message}`)}}),H.success(`导航权限测试完成，${t}/${e.length} 个路由可访问`)},clearLogs:()=>{o.value=[],H.success("测试日志已清空")},getRoleTagType:e=>({admin:"danger",substation:"warning",agent:"primary",distributor:"success",group_owner:"info",user:"default"}[e]||"default"),formatTime:e=>e.toLocaleTimeString(),ref:u,computed:m,onMounted:p,get useRouter(){return d},get useUserStore(){return S},get ElMessage(){return H},get User(){return L},get Switch(){return V},get Plus(){return N},get Menu(){return j},get Document(){return $},get canCreateGroup(){return Oe},get getDashboardScope(){return $e},get getFinanceScope(){return je},get getRoleDisplayName(){return Ue},get filterRoutesByRole(){return Me}};return Object.defineProperty(b,"__isScriptSetup",{enumerable:!1,value:!0}),b}},[["render",function(d,u,m,p,g,v){const h=x,f=U,y=z,b=I,k=T,_=R,w=M,C=O;return t(),e("div",Ne,[a(h,{type:"primary",onClick:u[0]||(u[0]=e=>p.showTestPanel=!p.showTestPanel),class:"test-toggle-btn",icon:p.showTestPanel?"Hide":"View"},{default:i(()=>[s(D(p.showTestPanel?"隐藏测试面板":"显示导航测试面板"),1)]),_:1},8,["icon"]),a(C,{modelValue:p.showTestPanel,"onUpdate:modelValue":u[1]||(u[1]=e=>p.showTestPanel=e),title:"🧪 导航系统和权限测试面板",direction:"rtl",size:"60%","with-header":!0},{default:i(()=>[n("div",Ve,[a(_,{class:"status-card",shadow:"never"},{header:i(()=>[n("div",Le,[a(f,null,{default:i(()=>[a(p.User)]),_:1}),u[2]||(u[2]=n("span",null,"当前状态",-1))])]),default:i(()=>[n("div",He,[a(k,{gutter:16},{default:i(()=>[a(b,{span:8},{default:i(()=>[n("div",Ee,[u[3]||(u[3]=n("div",{class:"status-label"},"用户角色",-1)),a(y,{type:p.getRoleTagType(p.currentUserRole),size:"large"},{default:i(()=>[s(D(p.getRoleDisplayName(p.currentUserRole)),1)]),_:1},8,["type"])])]),_:1}),a(b,{span:8},{default:i(()=>[n("div",Ge,[u[4]||(u[4]=n("div",{class:"status-label"},"群组创建权限",-1)),a(y,{type:p.canCreateGroup(p.currentUserRole)?"success":"danger",size:"large"},{default:i(()=>[s(D(p.canCreateGroup(p.currentUserRole)?"✅ 允许":"❌ 禁止"),1)]),_:1},8,["type"])])]),_:1}),a(b,{span:8},{default:i(()=>[n("div",We,[u[5]||(u[5]=n("div",{class:"status-label"},"数据权限范围",-1)),a(y,{type:"info",size:"large"},{default:i(()=>[s(D(p.getDashboardScope(p.currentUserRole)),1)]),_:1})])]),_:1})]),_:1})])]),_:1}),a(_,{class:"role-test-card",shadow:"never"},{header:i(()=>[n("div",qe,[a(f,null,{default:i(()=>[a(p.Switch)]),_:1}),u[6]||(u[6]=n("span",null,"角色切换测试",-1))])]),default:i(()=>[n("div",Fe,[n("div",Qe,[(t(),e(o,null,l(p.testRoles,e=>a(h,{key:e.key,type:p.currentUserRole===e.key?"primary":"default",onClick:t=>p.switchToRole(e.key),loading:p.switching&&p.targetRole===e.key,size:"small"},{default:i(()=>[s(D(e.name),1)]),_:2},1032,["type","onClick","loading"])),64))]),p.currentUserRole?(t(),e("div",Be,[n("p",null,[u[7]||(u[7]=n("strong",null,"当前角色：",-1)),s(D(p.getRoleDisplayName(p.currentUserRole)),1)]),n("p",null,[u[8]||(u[8]=n("strong",null,"数据权限：",-1)),s(D(p.getDashboardScope(p.currentUserRole)),1)]),n("p",null,[u[9]||(u[9]=n("strong",null,"财务权限：",-1)),s(D(p.getFinanceScope(p.currentUserRole)),1)])])):r("",!0)])]),_:1}),a(_,{class:"group-creation-card",shadow:"never"},{header:i(()=>[n("div",Je,[a(f,null,{default:i(()=>[a(p.Plus)]),_:1}),u[10]||(u[10]=n("span",null,"群组创建功能测试",-1))])]),default:i(()=>[n("div",Ke,[a(w,{title:p.groupCreationStatus.title,type:p.groupCreationStatus.type,description:p.groupCreationStatus.description,"show-icon":"",closable:!1},null,8,["title","type","description"]),n("div",Ye,[u[13]||(u[13]=n("h4",null,"测试群组创建访问：",-1)),n("div",Xe,[a(h,{type:"success",onClick:p.testGroupCreation,disabled:!p.canCreateGroup(p.currentUserRole)},{default:i(()=>[a(f,null,{default:i(()=>[a(p.Plus)]),_:1}),u[11]||(u[11]=s(" 测试创建群组 ",-1))]),_:1,__:[11]},8,["disabled"]),a(h,{type:"primary",onClick:p.testNavigationAccess},{default:i(()=>[a(f,null,{default:i(()=>[a(p.Menu)]),_:1}),u[12]||(u[12]=s(" 测试导航访问 ",-1))]),_:1,__:[12]})])])])]),_:1}),a(_,{class:"navigation-card",shadow:"never"},{header:i(()=>[n("div",Ze,[a(f,null,{default:i(()=>[a(p.Menu)]),_:1}),u[14]||(u[14]=n("span",null,"导航权限测试",-1))])]),default:i(()=>[n("div",et,[n("div",tt,[a(k,{gutter:16},{default:i(()=>[a(b,{span:8},{default:i(()=>[n("div",at,[n("div",it,D(p.visibleRoutesCount),1),u[15]||(u[15]=n("div",{class:"stat-label"},"可访问路由",-1))])]),_:1}),a(b,{span:8},{default:i(()=>[n("div",st,[n("div",nt,D(p.groupCreationEntries),1),u[16]||(u[16]=n("div",{class:"stat-label"},"群组创建入口",-1))])]),_:1}),a(b,{span:8},{default:i(()=>[n("div",rt,[n("div",ot,D(p.quickActionsCount),1),u[17]||(u[17]=n("div",{class:"stat-label"},"快速操作",-1))])]),_:1})]),_:1})]),n("div",lt,[u[18]||(u[18]=n("h4",null,"可访问的主要路由：",-1)),n("div",ct,[(t(!0),e(o,null,l(p.sampleVisibleRoutes,e=>(t(),c(y,{key:e.path,type:e.path.includes("community/add")?"success":"default",class:"route-tag"},{default:i(()=>[s(D(e.meta?.title||e.path),1)]),_:2},1032,["type"]))),128))])])])]),_:1}),a(_,{class:"log-card",shadow:"never"},{header:i(()=>[n("div",dt,[a(f,null,{default:i(()=>[a(p.Document)]),_:1}),u[20]||(u[20]=n("span",null,"测试日志",-1)),a(h,{size:"small",onClick:p.clearLogs},{default:i(()=>u[19]||(u[19]=[s("清空",-1)])),_:1,__:[19]})])]),default:i(()=>[n("div",ut,[n("div",mt,[(t(!0),e(o,null,l(p.testLogs.slice(0,10),(a,i)=>(t(),e("div",{key:i,class:P(["log-item",a.type])},[n("span",pt,D(p.formatTime(a.time)),1),n("span",gt,D(a.message),1)],2))),128))])])]),_:1})])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-bbee7b8b"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/NavigationTestPanel.vue"]]),ht=g("preferences",()=>{const e=u({interface:{theme:"auto",primaryColor:"#3b82f6",sidebarPosition:"left",compactMode:!1,animationsEnabled:!0,language:"zh-CN"},features:{quickActions:[],dashboardWidgets:[],navigationPinned:[],searchFilters:[],notificationSettings:{desktop:!0,email:!1,sms:!1,groupCreationAlerts:!0}},workflows:{customShortcuts:[],automationRules:[],templatePreferences:[],dataViewPreferences:{pageSize:20,sortOrder:"desc",defaultView:"table"}},ai:{recommendationsEnabled:!0,learningEnabled:!0,personalizedContent:!0,smartNotifications:!0},performance:{enableAnimations:!0,lazyLoading:!0,cacheEnabled:!0,prefetchEnabled:!0}}),t=u({featureUsage:{},pageViews:{},searchHistory:[],operationTimes:{},errorCounts:{}}),a=u([]),i=u({interests:[],usagePatterns:{},preferenceWeights:{},learningData:{}}),s=m(()=>{if("auto"===e.value.interface.theme){const e=(new Date).getHours();return e>=6&&e<18?"light":"dark"}return e.value.interface.theme}),n=m(()=>e.value.interface.compactMode),r=m(()=>e.value.interface.primaryColor),o=m(()=>Object.entries(e.value.features.notificationSettings).filter(([e,t])=>t).map(([e])=>e)),l=m(()=>[{id:"create_group",title:"创建群组",description:"快速创建新的微信群组",icon:"ChatDotRound",iconClass:"text-blue-500",protected:!0,priority:1,category:"core"},{id:"user_management",title:"用户管理",description:"管理系统用户和权限",icon:"User",iconClass:"text-green-500",priority:2,category:"management"},{id:"data_analysis",title:"数据分析",description:"查看业务数据和统计报表",icon:"DataAnalysis",iconClass:"text-purple-500",priority:3,category:"analytics"},...e.value.features.quickActions||[]].sort((e,a)=>{if(e.protected&&!a.protected)return-1;if(!e.protected&&a.protected)return 1;const i=t.value.featureUsage[e.id]||0;return(t.value.featureUsage[a.id]||0)-i})),c=m(()=>[{id:"stats_overview",title:"数据概览",component:"StatsOverviewWidget",size:"large",priority:1,config:{showTrends:!0}},{id:"group_activity",title:"群组活动",component:"GroupActivityWidget",size:"medium",priority:2,config:{limit:10,showGroupCreation:!0}},...e.value.features.dashboardWidgets||[]].sort((e,t)=>e.priority-t.priority)),d=async()=>{try{localStorage.setItem("user_preferences",JSON.stringify(e.value));if(!(await fetch("/api/user/preferences",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e.value)})).ok)throw new Error("保存偏好设置失败");return!0}catch(t){return!1}},p=()=>{const e=Date.now();t.value.groupCreationHistory||(t.value.groupCreationHistory=[]),t.value.groupCreationHistory.push(e),t.value.groupCreationHistory.length>100&&(t.value.groupCreationHistory=t.value.groupCreationHistory.slice(-100))},g=async()=>{try{const e=localStorage.getItem("user_behavior_data");e&&(t.value={...t.value,...JSON.parse(e)})}catch(e){}},v=()=>{try{localStorage.setItem("user_behavior_data",JSON.stringify(t.value))}catch(e){}},h=async()=>{try{if(!e.value.ai.recommendationsEnabled)return;try{const e=await fetch("/api/ai/recommendations");if(e.ok){const t=await e.json();return void(a.value=t.recommendations||[])}}catch(t){}a.value=[{id:"ai_group_creation",title:"智能群组创建助手",description:"基于您的使用习惯，推荐创建产品交流群",icon:"ChatDotRound",confidence:.92,action:()=>{}},{id:"ai_user_analysis",title:"用户行为分析",description:"AI发现您经常使用数据分析功能，建议查看详细报表",icon:"DataAnalysis",confidence:.87,action:()=>{}}]}catch(i){a.value=[]}};return{preferences:e,behaviorData:t,aiRecommendations:a,personalizationData:i,currentTheme:s,isCompactMode:n,primaryColor:r,enabledNotifications:o,personalizedQuickActions:l,personalizedDashboardWidgets:c,loadUserPreferences:async()=>{try{const i=localStorage.getItem("user_preferences");if(i){const t=JSON.parse(i);e.value={...e.value,...t}}try{const t=await fetch("/api/user/preferences");if(t.ok){const a=await t.json();e.value={...e.value,...a.data}}}catch(t){}await g();try{await h()}catch(a){}}catch(i){}},saveUserPreferences:d,updateInterfaceSettings:t=>{e.value.interface={...e.value.interface,...t},d()},saveQuickActions:t=>{t.some(e=>"create_group"===e.id)||t.unshift({id:"create_group",title:"创建群组",description:"快速创建新的微信群组",icon:"ChatDotRound",iconClass:"text-blue-500",protected:!0}),e.value.features.quickActions=t,d()},saveDashboardWidgets:t=>{e.value.features.dashboardWidgets=t,d()},recordFeatureUsage:e=>{t.value.featureUsage[e]||(t.value.featureUsage[e]=0),t.value.featureUsage[e]++,"create_group"===e&&p(),v()},recordPageView:e=>{t.value.pageViews[e]||(t.value.pageViews[e]=0),t.value.pageViews[e]++,v()},recordSearch:(e,a)=>{const i={query:e,resultsCount:a.length,timestamp:Date.now(),hasGroupCreation:a.some(e=>"create_group"===e.id)};t.value.searchHistory.unshift(i),t.value.searchHistory.length>50&&(t.value.searchHistory=t.value.searchHistory.slice(0,50)),v()},loadAIRecommendations:h,getPersonalizedRecommendations:()=>{const e=[];Object.entries(t.value.featureUsage).sort(([,e],[,t])=>e-t).slice(0,3).map(([e])=>e).forEach(t=>{e.push({id:`feature_${t}`,type:"feature_suggestion",title:`尝试使用${t}功能`,description:"这个功能可能对您有帮助",confidence:.7})});return(t.value.featureUsage.create_group||0)<5&&e.unshift({id:"group_creation_suggestion",type:"core_feature_suggestion",title:"创建您的第一个群组",description:"群组创建是平台的核心功能，快来体验吧！",confidence:.9,priority:"high"}),e},resetPreferences:()=>{e.value={interface:{theme:"auto",primaryColor:"#3b82f6",sidebarPosition:"left",compactMode:!1,animationsEnabled:!0,language:"zh-CN"},features:{quickActions:[],dashboardWidgets:[],navigationPinned:[],searchFilters:[],notificationSettings:{desktop:!0,email:!1,sms:!1,groupCreationAlerts:!0}},workflows:{customShortcuts:[],automationRules:[],templatePreferences:[],dataViewPreferences:{pageSize:20,sortOrder:"desc",defaultView:"table"}},ai:{recommendationsEnabled:!0,learningEnabled:!0,personalizedContent:!0,smartNotifications:!0},performance:{enableAnimations:!0,lazyLoading:!0,cacheEnabled:!0,prefetchEnabled:!0}},d()},exportUserData:()=>{const a={preferences:e.value,behaviorData:t.value,exportTime:(new Date).toISOString(),version:"1.0"},i=new Blob([JSON.stringify(a,null,2)],{type:"application/json"}),s=URL.createObjectURL(i),n=document.createElement("a");n.href=s,n.download=`user_preferences_${Date.now()}.json`,n.click(),URL.revokeObjectURL(s)},importUserData:a=>new Promise((i,s)=>{const n=new FileReader;n.onload=a=>{try{const s=JSON.parse(a.target.result);s.preferences&&(e.value={...e.value,...s.preferences}),s.behaviorData&&(t.value={...t.value,...s.behaviorData}),d(),v(),i(!0)}catch(n){s(n)}},n.onerror=()=>s(new Error("文件读取失败")),n.readAsText(a)})}}),ft={admin:{visibleGroups:["workbench","dataCenter","coreBusiness","distributionNetwork","systemManagement"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/admin/groups",icon:"Plus",color:"#409eff"},{title:"分站管理",path:"/admin/substations",icon:"OfficeBuilding",color:"#67c23a"},{title:"支付设置",path:"/admin/payment-settings",icon:"CreditCard",color:"#e6a23c"},{title:"防红配置",path:"/admin/anti-block",icon:"Lock",color:"#f56c6c"}]},substation:{visibleGroups:["workbench","dataCenter","coreBusiness","distributionNetwork","systemManagement"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/admin/groups",icon:"Plus",color:"#409eff"},{title:"支付设置",path:"/admin/payment-settings",icon:"CreditCard",color:"#67c23a"},{title:"防红配置",path:"/admin/anti-block",icon:"Lock",color:"#e6a23c"},{title:"代理商管理",path:"/admin/agents",icon:"Avatar",color:"#f56c6c"}]},agent:{visibleGroups:["workbench","dataCenter","coreBusiness","distributionNetwork"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/community/add-enhanced",icon:"Plus",color:"#409eff"},{title:"团队管理",path:"/distributors/list",icon:"User",color:"#67c23a"},{title:"佣金查看",path:"/finance/commission/logs",icon:"Money",color:"#e6a23c"},{title:"推广分析",path:"/promotion/analytics",icon:"TrendCharts",color:"#f56c6c"}]},distributor:{visibleGroups:["workbench","dataCenter","coreBusiness","distributionNetwork"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/admin/groups",icon:"Plus",color:"#409eff"},{title:"客户管理",path:"/admin/users",icon:"UserFilled",color:"#67c23a"},{title:"推广链接",path:"/admin/promotion",icon:"Link",color:"#e6a23c"},{title:"佣金记录",path:"/admin/commission-logs",icon:"Money",color:"#f56c6c"}]},group_owner:{visibleGroups:["workbench","dataCenter","coreBusiness"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/community/add-enhanced",icon:"Plus",color:"#409eff"},{title:"我的群组",path:"/community/groups",icon:"Comment",color:"#67c23a"},{title:"内容模板",path:"/community/templates",icon:"DocumentCopy",color:"#e6a23c"},{title:"群组数据",path:"/dashboard",icon:"DataLine",color:"#f56c6c"}]},user:{visibleGroups:["workbench","dataCenter","coreBusiness"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/community/add-enhanced",icon:"Plus",color:"#409eff"},{title:"我的订单",path:"/orders/list",icon:"Tickets",color:"#67c23a"},{title:"个人中心",path:"/user/center",icon:"User",color:"#e6a23c"}]}};function yt(e){const t=ft[e];return t?.quickActions||[]}const bt={class:"intelligent-workbench"},kt={class:"workbench-header"},_t={class:"user-greeting"},wt={class:"greeting-content"},Ct={class:"greeting-title"},St={class:"greeting-subtitle"},At={class:"user-avatar"},Dt={class:"user-status"},xt={key:0,class:"ai-recommendation-banner"},Rt={class:"banner-icon"},Tt={class:"banner-content"},It={class:"quick-actions-section"},zt={class:"section-header"},Ut={class:"quick-actions-grid"},Mt=["onClick"],Pt={class:"action-icon"},Ot={class:"action-content"},$t={key:0,class:"action-stats"},jt={class:"stat-item"},Nt={key:0,class:"action-badge"},Vt={class:"dashboard-widgets-section"},Lt={class:"section-header"},Ht={class:"widgets-grid",ref:"widgetsContainer"},Et={class:"widget-header"},Gt={class:"widget-content"},Wt={class:"activity-section"},qt={class:"activity-card"},Ft={class:"card-header"},Qt={class:"activity-list"},Bt={class:"activity-icon"},Jt={class:"activity-content"},Kt={class:"activity-title"},Yt={class:"activity-time"},Xt={key:0,class:"activity-action"},Zt={class:"reminders-card"},ea={class:"card-header"},ta={class:"reminders-list"},aa={class:"reminder-icon"},ia={class:"reminder-content"},sa={class:"reminder-title"},na={class:"reminder-desc"},ra={class:"reminder-actions"},oa={class:"widget-gallery"},la=["onClick"],ca={class:"widget-preview"},da={class:"widget-info"},ua={class:"customize-content"},ma={class:"available-actions"},pa={class:"actions-list"},ga=["onClick"],va={class:"selected-actions"},ha={class:"actions-list"};const fa=C({__name:"IntelligentWorkbench",setup(e,{expose:t}){t();const a=d(),i=S(),s=ht(),n=u(!1),r=u(!1),o=u(null),l=m(()=>i.userInfo||{name:"用户",avatar:"",role:"user"}),c=m(()=>{const e={admin:{title:"管理员智能控制台",description:"系统全局管理和智能监控中心"},substation:{title:"分站智能管理中心",description:"分站运营和团队协作智能助手"},agent:{title:"代理商智能工作台",description:"团队管理和业绩分析智能平台"},distributor:{title:"分销员智能助手",description:"客户管理和销售优化智能工具"},group_owner:{title:"群主智能运营台",description:"群组内容和成员管理智能中心"},user:{title:"个人智能助手",description:"个人信息和订单管理智能平台"}};return e[l.value.role]||e.user}),g=m(()=>s.aiRecommendations||[{id:1,type:"feature_suggestion",title:"智能群组创建助手",description:"基于您的使用习惯，推荐使用群组批量管理功能",message:"基于您的使用习惯，推荐使用群组批量管理功能",action:"navigate",target:"/groups/batch-manage",confidence:.85}]),v=m(()=>{const e=(yt(l.value?.role||"user")||[]).map(e=>({id:e.path.replace(/\//g,"_"),title:e.title,description:f(e.title),icon:e.icon,iconClass:y(e.icon),protected:"创建群组"===e.title,stats:{usage:Math.floor(200*Math.random())+50},action:()=>a.push(e.path)}));return e.find(e=>"创建群组"===e.title)||e.unshift({id:"create_group",title:"创建群组",description:"快速创建新的微信群组",icon:"Plus",iconClass:"text-blue-500",protected:!0,stats:{usage:156},action:()=>a.push("/community/add")}),e}),f=e=>({"创建群组":"快速创建新的微信群组","支付设置":"配置支付渠道和参数","防红配置":"配置防封系统和域名管理","用户管理":"管理系统用户和权限","代理商管理":"管理代理商和团队","数据大屏":"查看全屏数据展示","系统监控":"监控系统运行状态"}[e]||`管理${e}相关功能`),y=e=>({Plus:"text-blue-500",CreditCard:"text-green-500",Lock:"text-orange-500",User:"text-purple-500",Avatar:"text-red-500",DataBoard:"text-indigo-500",Monitor:"text-gray-500"}[e]||"text-gray-500"),b=u([{id:"stats_overview",title:"数据概览",component:"StatsOverviewWidget",size:"large",config:{showTrends:!0},data:{}},{id:"recent_groups",title:"最近群组",component:"RecentGroupsWidget",size:"medium",config:{limit:5},data:{}},{id:"performance_chart",title:"性能监控",component:"PerformanceChartWidget",size:"medium",config:{timeRange:"7d"},data:{}}]),k=u([{id:"todo_list",title:"待办事项",description:"管理您的任务和提醒",icon:"Folder",component:"TodoListWidget"},{id:"system_monitor",title:"系统监控",description:"实时系统状态监控",icon:"Monitor",component:"SystemMonitorWidget"}]),_=u([{id:1,title:'创建了新群组"产品交流群"',time:new Date(Date.now()-18e5),icon:"ChatDotRound",iconClass:"text-blue-500",actionable:!0,actionText:"查看"},{id:2,title:"处理了5个用户申请",time:new Date(Date.now()-72e5),icon:"User",iconClass:"text-green-500"}]),w=u([{id:1,title:"群组活跃度下降",description:"有3个群组本周活跃度下降超过20%",priority:"high",action:"view_groups"},{id:2,title:"系统更新可用",description:"新版本包含性能优化和安全更新",priority:"medium",action:"update_system"}]),C=m(()=>w.value.filter(e=>"high"===e.priority).length),A=u([{id:"file_management",title:"文件管理",icon:"Folder",action:()=>a.push("/files")}]);p(()=>{i.userInfo||i.enterPreviewMode();try{s.loadUserPreferences()}catch(e){}});const D={router:a,userStore:i,preferencesStore:s,showAddWidgetDialog:n,showCustomizeDialog:r,widgetsContainer:o,currentUser:l,workbenchConfig:c,aiRecommendations:g,personalizedQuickActions:v,getActionDescription:f,getIconClass:y,personalizedWidgets:b,availableWidgets:k,recentActivities:_,intelligentReminders:w,unreadNotifications:C,availableActions:A,getGreetingMessage:()=>{const e=(new Date).getHours();return e<12?"早上好":e<18?"下午好":"晚上好"},handleAIRecommendation:e=>{"navigate"===e.action&&a.push(e.target)},handleQuickAction:e=>{e.action&&e.action()},customizeQuickActions:()=>{r.value=!0},customizeDashboard:()=>{},handleWidgetAction:e=>{const[t,a]=e.split("-")},handleWidgetUpdate:(e,t)=>{const a=b.value.find(t=>t.id===e);a&&(a.data={...a.data,...t})},addWidget:e=>{const t={id:`${e.id}_${Date.now()}`,title:e.title,component:e.component,size:"medium",config:{},data:{}};b.value.push(t),n.value=!1},viewAllActivities:()=>{a.push("/activities")},handleActivityAction:e=>{},customizeReminders:()=>{},handleReminder:e=>{},dismissReminder:e=>{const t=w.value.findIndex(t=>t.id===e);t>-1&&w.value.splice(t,1)},addActionToSelected:e=>{const t=A.value.findIndex(t=>t.id===e.id);t>-1&&(A.value.splice(t,1),v.value.push(e))},removeAction:e=>{const t=v.value.findIndex(t=>t.id===e);if(t>-1){const e=v.value.splice(t,1)[0];A.value.push(e)}},saveCustomization:()=>{s.saveQuickActions(v.value),r.value=!1},formatTime:e=>{const t=new Date-e,a=Math.floor(t/6e4),i=Math.floor(t/36e5);return a<60?`${a}分钟前`:i<24?`${i}小时前`:e.toLocaleDateString()},ref:u,computed:m,onMounted:p,nextTick:h,get useRouter(){return d},get useUserStore(){return S},get usePreferencesStore(){return ht},get Bell(){return ne},get MagicStick(){return se},get Setting(){return ie},get Grid(){return ae},get Plus(){return N},get MoreFilled(){return te},get TrendCharts(){return ee},get Close(){return Z},get User(){return L},get Folder(){return X},get DataAnalysis(){return Y},get ChatDotRound(){return K},get ShoppingCart(){return J},get Monitor(){return B},get getUserQuickActions(){return yt}};return Object.defineProperty(D,"__isScriptSetup",{enumerable:!1,value:!0}),D}},[["render",function(d,u,m,p,g,h){const f=E,y=U,b=G,k=x,_=z,w=q,C=W,S=F,A=I,R=T,M=Q;return t(),e("div",bt,[n("div",kt,[n("div",_t,[n("div",wt,[n("h2",Ct,D(p.getGreetingMessage())+"，"+D(p.currentUser?.name||"用户")+"！ ",1),n("p",St,D(p.workbenchConfig.description),1)]),n("div",At,[a(f,{size:60,src:p.currentUser?.avatar},{default:i(()=>[s(D(p.currentUser?.name?.charAt(0)||"U"),1)]),_:1},8,["src"]),n("div",Dt,[a(b,{value:p.unreadNotifications,max:99,class:"notification-badge"},{default:i(()=>[a(y,null,{default:i(()=>[a(p.Bell)]),_:1})]),_:1},8,["value"])])])]),p.aiRecommendations.length>0?(t(),e("div",xt,[n("div",Rt,[a(y,null,{default:i(()=>[a(p.MagicStick)]),_:1})]),n("div",Tt,[u[5]||(u[5]=n("h4",null,"🤖 AI智能推荐",-1)),n("p",null,D(p.aiRecommendations[0].message||"基于您的使用习惯，为您推荐相关功能"),1)]),a(k,{type:"primary",size:"small",onClick:u[0]||(u[0]=e=>p.handleAIRecommendation(p.aiRecommendations[0]))},{default:i(()=>u[6]||(u[6]=[s(" 立即体验 ",-1)])),_:1,__:[6]})])):r("",!0)]),n("div",It,[n("div",zt,[u[8]||(u[8]=n("h3",null,"⚡ 快速操作",-1)),a(k,{text:"",onClick:p.customizeQuickActions},{default:i(()=>[a(y,null,{default:i(()=>[a(p.Setting)]),_:1}),u[7]||(u[7]=s(" 自定义 ",-1))]),_:1,__:[7]})]),n("div",Ut,[(t(!0),e(o,null,l(p.personalizedQuickActions,o=>(t(),e("div",{key:o.id,class:P(["quick-action-card",{"protected-action":o.protected}]),onClick:e=>p.handleQuickAction(o)},[n("div",Pt,[a(y,{class:P(o.iconClass)},{default:i(()=>[(t(),c(v(o.icon)))]),_:2},1032,["class"])]),n("div",Ot,[n("h4",null,D(o.title),1),n("p",null,D(o.description),1),o.stats?(t(),e("div",$t,[n("span",jt,[a(y,null,{default:i(()=>[a(p.TrendCharts)]),_:1}),s(" "+D(o.stats.usage)+"次使用 ",1)])])):r("",!0)]),o.protected?(t(),e("div",Nt,[a(_,{type:"warning",size:"small"},{default:i(()=>[...u[9]||(u[9]=[s("核心功能",-1)])]),_:1,__:[9]})])):r("",!0)],10,Mt))),128))])]),n("div",Vt,[n("div",Lt,[u[11]||(u[11]=n("h3",null,"📊 个人仪表板",-1)),a(k,{text:"",onClick:p.customizeDashboard},{default:i(()=>[a(y,null,{default:i(()=>[a(p.Grid)]),_:1}),u[10]||(u[10]=s(" 布局设置 ",-1))]),_:1,__:[10]})]),n("div",Ht,[(t(!0),e(o,null,l(p.personalizedWidgets,r=>(t(),e("div",{key:r.id,class:P(["widget-card",`widget-${r.size}`])},[n("div",Et,[n("h4",null,D(r.title),1),a(S,{onCommand:p.handleWidgetAction},{dropdown:i(()=>[a(C,null,{default:i(()=>[a(w,{command:`refresh-${r.id}`},{default:i(()=>[...u[12]||(u[12]=[s("刷新",-1)])]),_:2,__:[12]},1032,["command"]),a(w,{command:`settings-${r.id}`},{default:i(()=>[...u[13]||(u[13]=[s("设置",-1)])]),_:2,__:[13]},1032,["command"]),a(w,{command:`remove-${r.id}`,divided:""},{default:i(()=>[...u[14]||(u[14]=[s("移除",-1)])]),_:2,__:[14]},1032,["command"])]),_:2},1024)]),default:i(()=>[a(y,{class:"widget-menu"},{default:i(()=>[a(p.MoreFilled)]),_:1})]),_:2},1024)]),n("div",Gt,[(t(),c(v(r.component),{config:r.config,data:r.data,onUpdate:p.handleWidgetUpdate},null,40,["config","data"]))])],2))),128)),n("div",{class:"add-widget-card",onClick:u[1]||(u[1]=e=>p.showAddWidgetDialog=!0)},[a(y,{class:"add-icon"},{default:i(()=>[a(p.Plus)]),_:1}),u[15]||(u[15]=n("p",null,"添加组件",-1))])],512)]),n("div",Wt,[a(R,{gutter:24},{default:i(()=>[a(A,{span:12},{default:i(()=>[n("div",qt,[n("div",Ft,[u[17]||(u[17]=n("h3",null,"🕒 最近活动",-1)),a(k,{text:"",size:"small",onClick:p.viewAllActivities},{default:i(()=>u[16]||(u[16]=[s("查看全部",-1)])),_:1,__:[16]})]),n("div",Qt,[(t(!0),e(o,null,l(p.recentActivities,o=>(t(),e("div",{key:o.id,class:"activity-item"},[n("div",Bt,[a(y,{class:P(o.iconClass)},{default:i(()=>[(t(),c(v(o.icon)))]),_:2},1032,["class"])]),n("div",Jt,[n("p",Kt,D(o.title),1),n("p",Yt,D(p.formatTime(o.time)),1)]),o.actionable?(t(),e("div",Xt,[a(k,{size:"small",text:"",onClick:e=>p.handleActivityAction(o)},{default:i(()=>[s(D(o.actionText),1)]),_:2},1032,["onClick"])])):r("",!0)]))),128))])])]),_:1}),a(A,{span:12},{default:i(()=>[n("div",Zt,[n("div",ea,[u[19]||(u[19]=n("h3",null,"💡 智能提醒",-1)),a(k,{text:"",size:"small",onClick:p.customizeReminders},{default:i(()=>u[18]||(u[18]=[s("设置",-1)])),_:1,__:[18]})]),n("div",ta,[(t(!0),e(o,null,l(p.intelligentReminders,r=>(t(),e("div",{key:r.id,class:P(["reminder-item",`reminder-${r.priority}`])},[n("div",aa,[a(y,null,{default:i(()=>[a(p.Bell)]),_:1})]),n("div",ia,[n("p",sa,D(r.title),1),n("p",na,D(r.description),1)]),n("div",ra,[a(k,{size:"small",onClick:e=>p.handleReminder(r)},{default:i(()=>[...u[20]||(u[20]=[s(" 处理 ",-1)])]),_:2,__:[20]},1032,["onClick"]),a(k,{size:"small",text:"",onClick:e=>p.dismissReminder(r.id)},{default:i(()=>[...u[21]||(u[21]=[s(" 忽略 ",-1)])]),_:2,__:[21]},1032,["onClick"])])],2))),128))])])]),_:1})]),_:1})]),a(M,{modelValue:p.showAddWidgetDialog,"onUpdate:modelValue":u[2]||(u[2]=e=>p.showAddWidgetDialog=e),title:"添加仪表板组件",width:"600px",class:"add-widget-dialog"},{default:i(()=>[n("div",oa,[(t(!0),e(o,null,l(p.availableWidgets,s=>(t(),e("div",{key:s.id,class:"widget-option",onClick:e=>p.addWidget(s)},[n("div",ca,[a(y,null,{default:i(()=>[(t(),c(v(s.icon)))]),_:2},1024)]),n("div",da,[n("h4",null,D(s.title),1),n("p",null,D(s.description),1)])],8,la))),128))])]),_:1},8,["modelValue"]),a(M,{modelValue:p.showCustomizeDialog,"onUpdate:modelValue":u[4]||(u[4]=e=>p.showCustomizeDialog=e),title:"自定义快速操作",width:"700px",class:"customize-dialog"},{footer:i(()=>[a(k,{onClick:u[3]||(u[3]=e=>p.showCustomizeDialog=!1)},{default:i(()=>u[24]||(u[24]=[s("取消",-1)])),_:1,__:[24]}),a(k,{type:"primary",onClick:p.saveCustomization},{default:i(()=>u[25]||(u[25]=[s("保存设置",-1)])),_:1,__:[25]})]),default:i(()=>[n("div",ua,[n("div",ma,[u[22]||(u[22]=n("h4",null,"可用操作",-1)),n("div",pa,[(t(!0),e(o,null,l(p.availableActions,s=>(t(),e("div",{key:s.id,class:"action-item",onClick:e=>p.addActionToSelected(s)},[a(y,null,{default:i(()=>[(t(),c(v(s.icon)))]),_:2},1024),n("span",null,D(s.title),1)],8,ga))),128))])]),n("div",va,[u[23]||(u[23]=n("h4",null,"已选操作",-1)),n("div",ha,[(t(!0),e(o,null,l(p.personalizedQuickActions,s=>(t(),e("div",{key:s.id,class:"action-item selected"},[a(y,null,{default:i(()=>[(t(),c(v(s.icon)))]),_:2},1024),n("span",null,D(s.title),1),a(y,{class:"remove-icon",onClick:e=>p.removeAction(s.id)},{default:i(()=>[a(p.Close)]),_:2},1032,["onClick"])]))),128))])])])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-a9d55574"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/IntelligentWorkbench.vue"]]);class ya{static debounce(e,t,a=!1){let i;return function(...s){const n=a&&!i;clearTimeout(i),i=setTimeout(()=>{i=null,a||e.apply(this,s)},t),n&&e.apply(this,s)}}static throttle(e,t){let a;return function(...i){a||(e.apply(this,i),a=!0,setTimeout(()=>a=!1,t))}}static rafThrottle(e){let t;return function(...a){t||(t=requestAnimationFrame(()=>{e.apply(this,a),t=null}))}}static delay(e){return new Promise(t=>setTimeout(t,e))}static idleCallback(e,t={}){return"function"==typeof requestIdleCallback?requestIdleCallback(e,t):setTimeout(e,0)}static timeSlicing(e,t,a=100){return new Promise((i,s)=>{let n=0;const r=[];!function o(){const l=Math.min(n+a,e.length);try{for(let a=n;a<l;a++)r.push(t(e[a],a));n=l,n<e.length?ya.idleCallback(o):i(r)}catch(c){s(c)}}()})}static preloadImages(e){return Promise.all(e.map(e=>new Promise((t,a)=>{const i=new Image;i.onload=()=>t(e),i.onerror=()=>a(new Error(`Failed to load image: ${e}`)),i.src=e})))}static createLazyImageObserver(e,t={}){const a={root:null,rootMargin:"50px",threshold:.1,...t};return"IntersectionObserver"in window?new IntersectionObserver(t=>{t.forEach(t=>{t.isIntersecting&&e(t.target)})},a):{observe:t=>e(t),disconnect:()=>{}}}static createWeakCache(){const e=new WeakMap;return{get:t=>e.get(t),set:(t,a)=>(e.set(t,a),a),has:t=>e.has(t),delete:t=>e.delete(t),clear:()=>ya.createWeakCache()}}static createLRUCache(e=100){const t=new Map;return{get(e){if(t.has(e)){const a=t.get(e);return t.delete(e),t.set(e,a),a}},set(a,i){if(t.has(a))t.delete(a);else if(t.size>=e){const e=t.keys().next().value;t.delete(e)}return t.set(a,i),i},has:e=>t.has(e),delete:e=>t.delete(e),clear(){t.clear()},get size(){return t.size},entries:()=>Array.from(t.entries())}}static batchDOMUpdates(e){return new Promise(t=>{const a=document.createDocumentFragment();e.forEach(e=>{"function"==typeof e&&e(a)}),requestAnimationFrame(()=>{t(a)})})}static measurePerformance(e,t){return async function(...a){const i=performance.now();try{const i=await t.apply(this,a);performance.now();return performance.mark&&performance.measure&&(performance.mark(`${e}-start`),performance.mark(`${e}-end`),performance.measure(e,`${e}-start`,`${e}-end`)),i}catch(s){performance.now();throw s}}}static lazyLoadComponent(e){return()=>({component:e(),loading:{template:'<div class="lazy-loading">Loading...</div>'},error:{template:'<div class="lazy-error">Failed to load component</div>'},delay:200,timeout:3e3})}static lazyLoadRoute(e){return()=>e().catch(e=>A(()=>import("./ErrorPage-5JCWG06-.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10])))}}const ba=ya.debounce,ka=ya.throttle,_a={__name:"VirtualScrollList",props:{items:{type:Array,required:!0},itemHeight:{type:[Number,Function],default:50},containerHeight:{type:Number,default:400},bufferSize:{type:Number,default:5},threshold:{type:Number,default:100},loading:{type:Boolean,default:!1},keyField:{type:String,default:"id"}},emits:["scroll","reach-bottom","item-rendered"],setup(e,{expose:t,emit:a}){const i=e,s=a,n=u(null),r=u(0),o=u(i.containerHeight),l=u(new Map),c=u(50),d=m(()=>{if("function"==typeof i.itemHeight){let e=0;for(let t=0;t<i.items.length;t++)e+=C(t);return e}return i.items.length*i.itemHeight}),g=m(()=>Math.ceil(o.value/c.value)+2*i.bufferSize),v=m(()=>{if("function"==typeof i.itemHeight)return A();const e=Math.floor(r.value/i.itemHeight);return Math.max(0,e-i.bufferSize)}),f=m(()=>Math.min(i.items.length-1,v.value+g.value)),k=m(()=>i.items.slice(v.value,f.value+1)),_=m(()=>{if("function"==typeof i.itemHeight){return{position:"absolute",top:`${D(v.value)}px`,left:"0",right:"0"}}return{position:"absolute",top:v.value*i.itemHeight+"px",left:"0",right:"0"}}),w=m(()=>({height:`${o.value}px`,overflow:"auto",position:"relative"})),C=e=>{if("function"==typeof i.itemHeight){const t=i.items[e],a=l.value.get(S(t,e));if(void 0!==a)return a;const s=i.itemHeight(t,e);return l.value.set(S(t,e),s),s}return i.itemHeight},S=(e,t)=>e&&"object"==typeof e&&i.keyField in e?e[i.keyField]:t,A=()=>{let e=0,t=0;for(let a=0;a<i.items.length;a++){const s=C(a);if(e+s>=r.value){t=Math.max(0,a-i.bufferSize);break}e+=s}return t},D=e=>{let t=0;for(let a=0;a<e;a++)t+=C(a);return t},x=ka(e=>{const{scrollTop:t,scrollHeight:a,clientHeight:n}=e.target;r.value=t;a-n-t<i.threshold&&s("reach-bottom"),s("scroll",{scrollTop:t,scrollHeight:a,clientHeight:n,startIndex:v.value,endIndex:f.value})},16),R=ba(()=>{if(n.value){const e=n.value.getBoundingClientRect();o.value=e.height}},100),T=(e,t="smooth")=>{if(!n.value||e<0||e>=i.items.length)return;let a=0;a="function"==typeof i.itemHeight?D(e):e*i.itemHeight,n.value.scrollTo({top:a,behavior:t})},I=(e,t="smooth")=>{n.value&&n.value.scrollTo({top:e,behavior:t})},z=()=>{l.value.clear(),h(()=>{const e=n.value?.querySelectorAll(".virtual-scroll-item");e&&e.forEach((e,t)=>{const a=v.value+t,s=i.items[a],n=e.getBoundingClientRect().height;n>0&&l.value.set(S(s,a),n)})})},U=()=>({cachedCount:l.value.size,totalCount:i.items.length,cacheRatio:l.value.size/i.items.length});y(()=>i.items.length,()=>{if("function"==typeof i.itemHeight){const e=new Set;i.items.forEach((t,a)=>{e.add(S(t,a))});for(const t of l.value.keys())e.has(t)||l.value.delete(t)}});const M=u({renderCount:0,lastRenderTime:0,averageRenderTime:0}),P=()=>{const e=performance.now();h(()=>{const t=performance.now()-e;M.value.renderCount++,M.value.lastRenderTime=t;const{renderCount:a,averageRenderTime:i}=M.value;M.value.averageRenderTime=(i*(a-1)+t)/a,s("item-rendered",{renderTime:t,visibleCount:k.value.length,performance:M.value})})};y([v,f],P);let O=null;p(()=>{n.value&&(o.value=n.value.clientHeight,window.ResizeObserver&&(O=new ResizeObserver(e=>{for(const t of e)o.value=t.contentRect.height}),O.observe(n.value)),"function"==typeof i.itemHeight&&h(z))}),b(()=>{O&&O.disconnect()}),t({scrollToIndex:T,scrollTo:I,refreshItemHeights:z,getCacheStats:U,getPerformanceStats:()=>M.value});const $={props:i,emit:s,containerRef:n,scrollTop:r,containerHeight:o,itemHeights:l,estimatedItemHeight:c,totalHeight:d,visibleCount:g,startIndex:v,endIndex:f,visibleItems:k,visibleAreaStyle:_,containerStyle:w,getItemHeight:C,getItemKey:S,getItemStyle:e=>({position:"absolute",top:"0",left:"0",right:"0",height:`${C(e)}px`}),getStartIndexForDynamicHeight:A,getOffsetTopForDynamicHeight:D,handleScroll:x,updateContainerSize:R,scrollToIndex:T,scrollTo:I,refreshItemHeights:z,getCacheStats:U,performanceMonitor:M,trackRenderPerformance:P,get resizeObserver(){return O},set resizeObserver(e){O=e},ref:u,computed:m,watch:y,onMounted:p,onUnmounted:b,nextTick:h,get Loading(){return oe},get throttle(){return ka},get debounce(){return ba}};return Object.defineProperty($,"__isScriptSetup",{enumerable:!1,value:!0}),$}},wa={key:0,class:"virtual-scroll-loading"};const Ca=C(_a,[["render",function(s,c,d,u,m,p){const g=U;return t(),e("div",{ref:"containerRef",class:"virtual-scroll-list",style:re(u.containerStyle),onScroll:c[0]||(c[0]=(...e)=>u.handleScroll&&u.handleScroll(...e))},[n("div",{style:re({height:u.totalHeight+"px",position:"relative"})},[n("div",{style:re(u.visibleAreaStyle)},[(t(!0),e(o,null,l(u.visibleItems,(a,i)=>(t(),e("div",{key:u.getItemKey(a,u.startIndex+i),style:re(u.getItemStyle(u.startIndex+i)),class:"virtual-scroll-item"},[f(s.$slots,"default",{item:a,index:u.startIndex+i},void 0,!0)],4))),128))],4)],4),d.loading?(t(),e("div",wa,[a(g,{class:"is-loading"},{default:i(()=>[a(u.Loading)]),_:1}),c[1]||(c[1]=n("span",null,"加载中...",-1))])):r("",!0)],36)}],["__scopeId","data-v-07bdc2a6"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/common/VirtualScrollList.vue"]]),Sa={class:"ai-enhanced-search"},Aa={class:"dialog-header"},Da={class:"header-title"},xa={class:"header-actions"},Ra={class:"search-content"},Ta={class:"search-input-container"},Ia={class:"input-actions"},za={key:0,class:"search-suggestions"},Ua={key:0,class:"ai-analysis"},Ma={class:"analysis-indicator"},Pa={class:"search-results-container"},Oa={key:0,class:"recommendations"},$a={key:0,class:"recommendation-section"},ja={class:"section-header"},Na=["onClick"],Va={class:"item-icon ai-icon"},La={class:"item-content"},Ha={class:"item-title"},Ea={class:"item-desc"},Ga={key:0,class:"ai-confidence"},Wa={class:"item-badge"},qa={class:"recommendation-section"},Fa={class:"section-header"},Qa=["onClick"],Ba={class:"item-content"},Ja={class:"item-title"},Ka={class:"item-desc"},Ya={key:0,class:"item-badge"},Xa={key:1,class:"recommendation-section"},Za={class:"section-header"},ei=["onClick"],ti={class:"item-icon"},ai={class:"item-content"},ii={class:"item-title"},si={class:"item-time"},ni={key:1,class:"search-results"},ri={class:"results-header"},oi={class:"search-time"},li={key:0,class:"ai-explanation"},ci={class:"explanation-header"},di=["onClick"],ui={class:"result-content"},mi=["innerHTML"],pi={class:"result-path"},gi={key:0,class:"result-description"},vi={class:"result-badges"},hi={key:2,class:"ai-score"},fi={key:2,class:"no-results"},yi={class:"no-results-icon"},bi={class:"search-suggestions"},ki={key:0,class:"voice-indicator"};const _i=C({__name:"AIEnhancedSearch",setup(e,{expose:t}){t();const a=d(),i=S(),s=ht(),n=u(!1),r=u(""),o=u(null),l=u(0),c=u(!1),g=u(!1),v=u(0),f=u(""),k=u([{id:"ai_group_creation",title:"智能群组创建助手",description:"基于您的历史数据，AI建议创建产品交流群",icon:"ChatDotRound",confidence:.92,action:()=>a.push("/groups/create?ai=true")},{id:"ai_user_analysis",title:"用户行为分析",description:"AI发现用户活跃度异常，建议查看详细分析",icon:"DataAnalysis",confidence:.87,action:()=>a.push("/analytics/users?ai=true")}]),_=u(["创建群组","用户管理","数据分析","订单处理","系统设置"]),w=u([]),C=m(()=>i.currentUser),A=m(()=>[{id:"create_group",title:"创建群组",description:"快速创建新的微信群组",icon:"ChatDotRound",iconClass:"text-blue-500",protected:!0,path:"/groups/create",action:()=>a.push("/groups/create")},{id:"user_management",title:"用户管理",description:"管理系统用户和权限",icon:"User",iconClass:"text-green-500",path:"/users",action:()=>a.push("/users")},{id:"data_analysis",title:"数据分析",description:"查看业务数据和统计报表",icon:"DataAnalysis",iconClass:"text-purple-500",path:"/analytics",action:()=>a.push("/analytics")}].filter(e=>{if("create_group"===e.id)return!0;const t=C.value?.role;return"admin"===t||!("substation"!==t||!["user_management","data_analysis"].includes(e.id))})),D=m(()=>{const e=s.behaviorData.featureUsage||{};return Object.entries(e).sort(([,e],[,t])=>t-e).slice(0,5).map(([e,t])=>({id:e,title:j(e),icon:N(e),lastUsed:new Date(Date.now()-864e5*Math.random())}))}),x=()=>{n.value=!0,h(()=>{o.value?.focus()})},R=()=>{n.value=!1,r.value="",l.value=0,f.value=""},T=async e=>{if(!e.trim())return w.value=[],void(f.value="");c.value=!0;const t=Date.now();try{await new Promise(e=>setTimeout(e,300));const a=await I(e);w.value=a,v.value=Date.now()-t,f.value=U(e,a),s.recordSearch(e,a)}catch(a){}finally{c.value=!1}},I=async e=>{const t=e.toLowerCase(),a=[];return[{id:"create_group",title:"创建群组",description:"快速创建新的微信群组",icon:"ChatDotRound",iconClass:"text-blue-500",path:"/groups/create",protected:!0,keywords:["创建","群组","微信","新建","group","create"],category:"core"},{id:"manage_users",title:"用户管理",description:"管理系统用户和权限设置",icon:"User",iconClass:"text-green-500",path:"/users",keywords:["用户","管理","权限","user","manage"],category:"management"},{id:"analytics",title:"数据分析",description:"查看业务数据和统计报表",icon:"DataAnalysis",iconClass:"text-purple-500",path:"/analytics",keywords:["数据","分析","统计","报表","analytics","data"],category:"analytics"}].forEach(i=>{let s=0;i.title.toLowerCase().includes(t)&&(s+=10),i.description.toLowerCase().includes(t)&&(s+=5),i.keywords.forEach(e=>{e.toLowerCase().includes(t)&&(s+=3)}),i.path.toLowerCase().includes(t)&&(s+=2),i.protected&&(s+=5);const n=z(e,i);s+=n,s>0&&a.push({...i,aiScore:Math.min(s/20,1),matchScore:s})}),a.sort((e,t)=>t.matchScore-e.matchScore)},z=(e,t)=>{let a=0;return Object.entries({"创建":["群组","新建","添加"],"管理":["用户","设置","配置"],"分析":["数据","统计","报表"],"群组":["创建","管理","设置"]}).forEach(([i,s])=>{e.includes(i)&&s.forEach(e=>{(t.title.includes(e)||t.description.includes(e))&&(a+=2)})}),a},U=(e,t)=>{if(0===t.length)return`AI未能理解查询"${e}"，请尝试使用更具体的关键词。`;const a=t[0],i=Math.round(100*a.aiScore);return`AI理解您想要"${e}"，推荐使用"${a.title}"功能（匹配度${i}%）。`},M=()=>{if(r.value&&w.value.length>0){const e=w.value[l.value];e&&P(e)}else if(!r.value){const e=[...k.value,...A.value][l.value];e&&(e.action?e.action():P(e))}},P=e=>{s.recordFeatureUsage(e.id),e.action?e.action():e.path&&a.push(e.path),R()},O=()=>{if("webkitSpeechRecognition"in window||"SpeechRecognition"in window){g.value=!0;const e=new(window.SpeechRecognition||window.webkitSpeechRecognition);e.lang="zh-CN",e.continuous=!1,e.interimResults=!1,e.onresult=e=>{const t=e.results[0][0].transcript;r.value=t,T(t),g.value=!1},e.onerror=()=>{g.value=!1},e.onend=()=>{g.value=!1},e.start()}else H.warning("您的浏览器不支持语音识别功能")},$=()=>{g.value=!1},j=e=>({create_group:"创建群组",manage_users:"用户管理",analytics:"数据分析",orders:"订单管理",settings:"系统设置"}[e]||e),N=e=>({create_group:"ChatDotRound",manage_users:"User",analytics:"DataAnalysis",orders:"ShoppingCart",settings:"Setting"}[e]||"Document"),V=e=>{(e.ctrlKey||e.metaKey)&&"k"===e.key&&(e.preventDefault(),x())};p(()=>{document.addEventListener("keydown",V)}),b(()=>{document.removeEventListener("keydown",V)}),y(n,e=>{e&&(l.value=0)});const E={router:a,userStore:i,preferencesStore:s,showDialog:n,searchQuery:r,searchInputRef:o,selectedIndex:l,isAnalyzing:c,voiceSearchActive:g,searchTime:v,aiExplanation:f,aiRecommendations:k,searchSuggestions:_,searchResults:w,currentUser:C,roleBasedRecommendations:A,recentItems:D,openSearch:x,closeSearch:R,clearSearch:()=>{r.value="",l.value=0,w.value=[],f.value=""},handleSearchInput:T,performAISearch:I,calculateSemanticScore:z,generateAIExplanation:U,handleKeydown:e=>{const t=r.value?w.value.length:k.value.length+A.value.length;switch(e.key){case"ArrowDown":e.preventDefault(),l.value=Math.min(l.value+1,t-1);break;case"ArrowUp":e.preventDefault(),l.value=Math.max(l.value-1,0);break;case"Enter":e.preventDefault(),M();break;case"Escape":e.preventDefault(),R()}},handleEnterKey:M,handleItemClick:P,handleRecommendationClick:e=>{e.action&&e.action(),R()},toggleVoiceSearch:()=>{g.value?$():O()},startVoiceSearch:O,stopVoiceSearch:$,highlightText:(e,t)=>{if(!t)return e;const a=new RegExp(`(${t})`,"gi");return e.replace(a,"<mark>$1</mark>")},getFeatureTitle:j,getFeatureIcon:N,formatTime:e=>{const t=new Date-e,a=Math.floor(t/6e4),i=Math.floor(t/36e5);return a<60?`${a}分钟前`:i<24?`${i}小时前`:e.toLocaleDateString()},handleGlobalKeydown:V,ref:u,computed:m,onMounted:p,onUnmounted:b,nextTick:h,watch:y,get useRouter(){return d},get useUserStore(){return S},get usePreferencesStore(){return ht},get ElMessage(){return H},VirtualScrollList:Ca,get Search(){return ue},get MagicStick(){return se},get Microphone(){return de},get Close(){return Z},get Loading(){return oe},get User(){return L},get Clock(){return ce}};return Object.defineProperty(E,"__isScriptSetup",{enumerable:!1,value:!0}),E}},[["render",function(d,u,m,p,g,h){const f=U,y=x,b=le,k=z,_=Q;return t(),e("div",Sa,[n("div",{class:"search-trigger",onClick:p.openSearch},[a(f,{class:"search-icon"},{default:i(()=>[a(p.Search)]),_:1}),u[5]||(u[5]=n("span",{class:"search-placeholder"},"AI智能搜索...",-1)),u[6]||(u[6]=n("div",{class:"search-shortcuts"},[n("kbd",null,"Ctrl"),s(" + "),n("kbd",null,"K")],-1))]),a(_,{modelValue:p.showDialog,"onUpdate:modelValue":u[4]||(u[4]=e=>p.showDialog=e),"show-close":!1,"close-on-click-modal":!1,width:"700px",class:"search-dialog","append-to-body":""},{header:i(()=>[n("div",Aa,[n("div",Da,[a(f,null,{default:i(()=>[a(p.MagicStick)]),_:1}),u[7]||(u[7]=n("span",null,"AI智能搜索",-1))]),n("div",xa,[a(y,{text:"",onClick:p.toggleVoiceSearch,class:P({active:p.voiceSearchActive})},{default:i(()=>[a(f,null,{default:i(()=>[a(p.Microphone)]),_:1})]),_:1},8,["class"]),a(y,{text:"",onClick:p.closeSearch},{default:i(()=>[a(f,null,{default:i(()=>[a(p.Close)]),_:1})]),_:1})])])]),default:i(()=>[n("div",Ra,[n("div",Ta,[a(b,{modelValue:p.searchQuery,"onUpdate:modelValue":u[0]||(u[0]=e=>p.searchQuery=e),ref:"searchInputRef",placeholder:"输入搜索内容，支持自然语言查询...",size:"large",class:"search-input",onInput:p.handleSearchInput,onKeydown:p.handleKeydown},{prefix:i(()=>[a(f,null,{default:i(()=>[a(p.Search)]),_:1})]),suffix:i(()=>[n("div",Ia,[p.searchQuery?(t(),c(y,{key:0,text:"",onClick:p.clearSearch,class:"clear-btn"},{default:i(()=>[a(f,null,{default:i(()=>[a(p.Close)]),_:1})]),_:1})):r("",!0),a(y,{text:"",onClick:p.toggleVoiceSearch,class:P([{active:p.voiceSearchActive},"voice-btn"])},{default:i(()=>[a(f,null,{default:i(()=>[a(p.Microphone)]),_:1})]),_:1},8,["class"])])]),_:1},8,["modelValue"]),!p.searchQuery&&p.searchSuggestions.length>0?(t(),e("div",za,[(t(!0),e(o,null,l(p.searchSuggestions,e=>(t(),c(k,{key:e,size:"small",class:"suggestion-tag",onClick:t=>p.searchQuery=e},{default:i(()=>[s(D(e),1)]),_:2},1032,["onClick"]))),128))])):r("",!0)]),p.isAnalyzing?(t(),e("div",Ua,[n("div",Ma,[a(f,{class:"is-loading"},{default:i(()=>[a(p.Loading)]),_:1}),u[8]||(u[8]=n("span",null,"AI正在分析您的查询...",-1))])])):r("",!0),n("div",Pa,[p.searchQuery||p.isAnalyzing?p.searchQuery&&!p.isAnalyzing?(t(),e("div",ni,[n("div",ri,[n("span",null,"找到 "+D(p.searchResults.length)+" 个结果",1),n("span",oi,D(p.searchTime)+"ms",1)]),p.aiExplanation?(t(),e("div",li,[n("div",ci,[a(f,null,{default:i(()=>[a(p.MagicStick)]),_:1}),u[14]||(u[14]=n("span",null,"AI理解",-1))]),n("p",null,D(p.aiExplanation),1)])):r("",!0),a(p.VirtualScrollList,{items:p.searchResults,"item-height":80,"container-height":300,class:"results-list"},{default:i(({item:o,index:l})=>[n("div",{class:P(["result-item",{active:p.selectedIndex===l,protected:o.protected,"ai-recommended":o.aiScore>.8}]),onClick:e=>p.handleItemClick(o)},[n("div",{class:P(["result-icon",o.iconClass])},[a(f,null,{default:i(()=>[(t(),c(v(o.icon)))]),_:2},1024)],2),n("div",ui,[n("div",{class:"result-title",innerHTML:p.highlightText(o.title,p.searchQuery)},null,8,mi),n("div",pi,D(o.path),1),o.description?(t(),e("div",gi,D(o.description),1)):r("",!0)]),n("div",vi,[o.protected?(t(),c(k,{key:0,type:"warning",size:"small"},{default:i(()=>u[15]||(u[15]=[s("核心",-1)])),_:1,__:[15]})):r("",!0),o.aiScore>.8?(t(),c(k,{key:1,type:"success",size:"small"},{default:i(()=>u[16]||(u[16]=[s("AI推荐",-1)])),_:1,__:[16]})):r("",!0),o.aiScore?(t(),e("div",hi,D(Math.round(100*o.aiScore))+"%匹配 ",1)):r("",!0)])],10,di)]),_:1},8,["items"])])):p.searchQuery&&0===p.searchResults.length&&!p.isAnalyzing?(t(),e("div",fi,[n("div",yi,[a(f,null,{default:i(()=>[a(p.Search)]),_:1})]),u[21]||(u[21]=n("div",{class:"no-results-text"},"未找到相关结果",-1)),n("div",bi,[u[20]||(u[20]=n("p",null,"您可以尝试：",-1)),a(y,{size:"small",onClick:u[1]||(u[1]=e=>p.searchQuery="创建群组")},{default:i(()=>u[17]||(u[17]=[s("创建群组",-1)])),_:1,__:[17]}),a(y,{size:"small",onClick:u[2]||(u[2]=e=>p.searchQuery="用户管理")},{default:i(()=>u[18]||(u[18]=[s("用户管理",-1)])),_:1,__:[18]}),a(y,{size:"small",onClick:u[3]||(u[3]=e=>p.searchQuery="数据分析")},{default:i(()=>u[19]||(u[19]=[s("数据分析",-1)])),_:1,__:[19]})])])):r("",!0):(t(),e("div",Oa,[p.aiRecommendations.length>0?(t(),e("div",$a,[n("div",ja,[a(f,null,{default:i(()=>[a(p.MagicStick)]),_:1}),u[9]||(u[9]=n("span",null,"AI为您推荐",-1))]),(t(!0),e(o,null,l(p.aiRecommendations,(o,l)=>(t(),e("div",{key:o.id,class:P(["recommendation-item",{active:p.selectedIndex===l}]),onClick:e=>p.handleRecommendationClick(o)},[n("div",Va,[a(f,null,{default:i(()=>[(t(),c(v(o.icon||"MagicStick")))]),_:2},1024)]),n("div",La,[n("div",Ha,D(o.title),1),n("div",Ea,D(o.description),1),o.confidence?(t(),e("div",Ga,[n("span",null,"AI置信度: "+D(Math.round(100*o.confidence))+"%",1)])):r("",!0)]),n("div",Wa,[a(k,{type:"info",size:"small"},{default:i(()=>[...u[10]||(u[10]=[s("AI推荐",-1)])]),_:1,__:[10]})])],10,Na))),128))])):r("",!0),n("div",qa,[n("div",Fa,[a(f,null,{default:i(()=>[a(p.User)]),_:1}),u[11]||(u[11]=n("span",null,"为您推荐",-1))]),(t(!0),e(o,null,l(p.roleBasedRecommendations,(o,l)=>(t(),e("div",{key:o.id,class:P(["recommendation-item",{active:p.selectedIndex===p.aiRecommendations.length+l,protected:o.protected}]),onClick:e=>p.handleItemClick(o)},[n("div",{class:P(["item-icon",o.iconClass])},[a(f,null,{default:i(()=>[(t(),c(v(o.icon)))]),_:2},1024)],2),n("div",Ba,[n("div",Ja,D(o.title),1),n("div",Ka,D(o.description),1)]),o.protected?(t(),e("div",Ya,[a(k,{type:"warning",size:"small"},{default:i(()=>[...u[12]||(u[12]=[s("核心功能",-1)])]),_:1,__:[12]})])):r("",!0)],10,Qa))),128))]),p.recentItems.length>0?(t(),e("div",Xa,[n("div",Za,[a(f,null,{default:i(()=>[a(p.Clock)]),_:1}),u[13]||(u[13]=n("span",null,"最近使用",-1))]),(t(!0),e(o,null,l(p.recentItems,(s,r)=>(t(),e("div",{key:s.id,class:"recommendation-item recent-item",onClick:e=>p.handleItemClick(s)},[n("div",ti,[a(f,null,{default:i(()=>[(t(),c(v(s.icon)))]),_:2},1024)]),n("div",ai,[n("div",ii,D(s.title),1),n("div",si,D(p.formatTime(s.lastUsed)),1)])],8,ei))),128))])):r("",!0)]))])])]),_:1},8,["modelValue"]),p.voiceSearchActive?(t(),e("div",ki,[u[23]||(u[23]=n("div",{class:"voice-animation"},[n("div",{class:"wave"}),n("div",{class:"wave"}),n("div",{class:"wave"})],-1)),u[24]||(u[24]=n("p",null,"正在听取您的语音...",-1)),a(y,{onClick:p.stopVoiceSearch},{default:i(()=>u[22]||(u[22]=[s("停止",-1)])),_:1,__:[22]})])):r("",!0)])}],["__scopeId","data-v-de36c3ff"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/AIEnhancedSearch.vue"]]),wi={class:"metrics-grid"},Ci={class:"metric-icon"},Si={class:"metric-content"},Ai={class:"metric-value"},Di={class:"metric-label"};const xi=C({__name:"DashboardMetrics",props:{data:{type:Object,default:()=>({})}},setup(e,{expose:t}){t();const a=e,i=m(()=>[{key:"users",label:"总用户数",value:a.data.totalUsers||"12,345",change:"+12.5%",changeType:"increase",icon:L,color:"#409eff"},{key:"links",label:"短链接数",value:a.data.totalLinks||"8,976",change:"+8.2%",changeType:"increase",icon:ve,color:"#67c23a"},{key:"revenue",label:"今日收入",value:a.data.todayRevenue||"¥23,456",change:"+15.3%",changeType:"increase",icon:ge,color:"#e6a23c"},{key:"conversion",label:"转化率",value:a.data.conversionRate||"3.2%",change:"-0.5%",changeType:"decrease",icon:ee,color:"#f56c6c"}]),s={props:a,metrics:i,computed:m,get User(){return L},get Link(){return ve},get Money(){return ge},get TrendCharts(){return ee},get ArrowUp(){return pe},get ArrowDown(){return me}};return Object.defineProperty(s,"__isScriptSetup",{enumerable:!1,value:!0}),s}},[["render",function(r,d,u,m,p,g){const h=U;return t(),e("div",wi,[(t(!0),e(o,null,l(m.metrics,r=>(t(),e("div",{class:"metric-card",key:r.key},[n("div",Ci,[a(h,{size:32,color:r.color},{default:i(()=>[(t(),c(v(r.icon)))]),_:2},1032,["color"])]),n("div",Si,[n("div",Ai,D(r.value),1),n("div",Di,D(r.label),1),n("div",{class:P(["metric-change",r.changeType])},[a(h,{size:12},{default:i(()=>[(t(),c(v("increase"===r.changeType?"ArrowUp":"ArrowDown")))]),_:2},1024),s(" "+D(r.change),1)],2)])]))),128))])}],["__scopeId","data-v-747552cb"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/dashboard/DashboardMetrics.vue"]]),Ri={class:"dashboard-charts"},Ti={class:"chart-card"},Ii={class:"card-header"},zi={class:"chart-controls"},Ui={class:"chart-container"},Mi={class:"activity-card"},Pi={class:"card-header"},Oi={class:"heatmap-container"};const $i=C({__name:"DashboardCharts",props:{data:{type:Object,default:()=>({})}},emits:["chart-click"],setup(e,{expose:t,emit:a}){t();const i=e,s=a,n=u("24h"),r=m(()=>({tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{data:["访问量","转化量","收入"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:l()},yAxis:{type:"value"},series:[{name:"访问量",type:"line",stack:"Total",smooth:!0,data:c(24)},{name:"转化量",type:"line",stack:"Total",smooth:!0,data:c(24,.3)},{name:"收入",type:"line",stack:"Total",smooth:!0,data:c(24,.1)}]})),o=m(()=>({tooltip:{position:"top"},grid:{height:"50%",top:"10%"},xAxis:{type:"category",data:["00","02","04","06","08","10","12","14","16","18","20","22"],splitArea:{show:!0}},yAxis:{type:"category",data:["周日","周一","周二","周三","周四","周五","周六"],splitArea:{show:!0}},visualMap:{min:0,max:100,calculable:!0,orient:"horizontal",left:"center",bottom:"15%"},series:[{name:"活跃度",type:"heatmap",data:d(),label:{show:!0},emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}));function l(){const e=[];for(let t=0;t<24;t++)e.push(`${t.toString().padStart(2,"0")}:00`);return e}function c(e,t=1){const a=[];for(let i=0;i<e;i++)a.push(Math.floor(1e3*Math.random()*t));return a}function d(){const e=[];for(let t=0;t<7;t++)for(let a=0;a<12;a++)e.push([a,t,Math.floor(100*Math.random())]);return e}p(()=>{});const g={props:i,emit:s,chartTimeRange:n,chartOption:r,heatmapOption:o,generateTimeLabels:l,generateRandomData:c,generateHeatmapData:d,handleChartClick:e=>{s("chart-click",e)},ref:u,computed:m,onMounted:p,get VChart(){return Pe},get InfoFilled(){return be}};return Object.defineProperty(g,"__isScriptSetup",{enumerable:!1,value:!0}),g}},[["render",function(r,o,l,c,d,u){const m=he,p=fe,g=U,v=ye;return t(),e("div",Ri,[n("div",Ti,[n("div",Ii,[o[4]||(o[4]=n("h3",null,"实时数据趋势",-1)),n("div",zi,[a(p,{modelValue:c.chartTimeRange,"onUpdate:modelValue":o[0]||(o[0]=e=>c.chartTimeRange=e),size:"small"},{default:i(()=>[a(m,{label:"24h"},{default:i(()=>o[1]||(o[1]=[s("24小时",-1)])),_:1,__:[1]}),a(m,{label:"7d"},{default:i(()=>o[2]||(o[2]=[s("7天",-1)])),_:1,__:[2]}),a(m,{label:"30d"},{default:i(()=>o[3]||(o[3]=[s("30天",-1)])),_:1,__:[3]})]),_:1},8,["modelValue"])])]),n("div",Ui,[a(c.VChart,{class:"chart",option:c.chartOption,autoresize:"",onClick:c.handleChartClick},null,8,["option"])])]),n("div",Mi,[n("div",Pi,[o[5]||(o[5]=n("h3",null,"用户活动热力图",-1)),a(v,{content:"显示用户在不同时间段的活跃度"},{default:i(()=>[a(g,null,{default:i(()=>[a(c.InfoFilled)]),_:1})]),_:1})]),n("div",Oi,[a(c.VChart,{class:"heatmap",option:c.heatmapOption,autoresize:""},null,8,["option"])])])])}],["__scopeId","data-v-c3ea06e3"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/dashboard/DashboardCharts.vue"]]),ji={class:"dashboard-activities"},Ni={class:"orders-card"},Vi={class:"card-header"},Li={class:"orders-list"},Hi=["onClick"],Ei={class:"order-info"},Gi={class:"order-id"},Wi={class:"order-user"},qi={class:"order-time"},Fi={class:"order-status"},Qi={class:"popular-groups-card"},Bi={class:"card-header"},Ji={class:"groups-list"},Ki=["onClick"],Yi={class:"group-rank"},Xi={class:"group-avatar"},Zi=["src","alt"],es={class:"group-info"},ts={class:"group-name"},as={class:"group-members"},is={class:"group-activity"},ss={class:"activity-score"},ns={class:"recent-activities-card"},rs={class:"card-header"},os={class:"activities-list"},ls={class:"activity-icon"},cs={class:"activity-content"},ds={class:"activity-text"},us={class:"activity-time"};const ms=C({__name:"DashboardActivities",emits:["view-order","view-group","view-all-orders","view-all-groups"],setup(e,{expose:t,emit:a}){t();const i=a,s=u([{id:"20241201001",user:"张三",amount:"299.00",status:"paid",time:new Date},{id:"20241201002",user:"李四",amount:"199.00",status:"pending",time:new Date(Date.now()-3e5)},{id:"20241201003",user:"王五",amount:"399.00",status:"paid",time:new Date(Date.now()-6e5)},{id:"20241201004",user:"赵六",amount:"99.00",status:"failed",time:new Date(Date.now()-9e5)},{id:"20241201005",user:"钱七",amount:"599.00",status:"paid",time:new Date(Date.now()-12e5)}]),n=u([{id:1,name:"创业交流群",members:1234,activity:98,avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:2,name:"技术分享群",members:987,activity:95,avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:3,name:"投资理财群",members:756,activity:92,avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:4,name:"生活分享群",members:543,activity:88,avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:5,name:"学习交流群",members:432,activity:85,avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"}]),r=u([{id:1,text:'用户"张三"完成了订单支付',time:new Date,icon:ge,color:"#67c23a"},{id:2,text:'新用户"李四"注册成功',time:new Date(Date.now()-18e4),icon:L,color:"#409eff"},{id:3,text:'群组"创业交流群"新增50个成员',time:new Date(Date.now()-36e4),icon:K,color:"#e6a23c"},{id:4,text:"系统完成了定时备份任务",time:new Date(Date.now()-54e4),icon:ie,color:"#909399"},{id:5,text:'短链接"abc123"被访问1000次',time:new Date(Date.now()-72e4),icon:ve,color:"#f56c6c"}]);p(()=>{});const o={emit:i,recentOrders:s,popularGroups:n,recentActivities:r,formatTime:e=>{const t=new Date-e,a=Math.floor(t/6e4);if(a<1)return"刚刚";if(a<60)return`${a}分钟前`;const i=Math.floor(a/60);if(i<24)return`${i}小时前`;return`${Math.floor(i/24)}天前`},getStatusType:e=>({paid:"success",pending:"warning",failed:"danger"}[e]||"info"),getStatusText:e=>({paid:"已支付",pending:"待支付",failed:"支付失败"}[e]||"未知"),viewOrderDetail:e=>{i("view-order",e)},viewGroupDetail:e=>{i("view-group",e)},viewAllOrders:()=>{i("view-all-orders")},viewAllGroups:()=>{i("view-all-groups")},refreshActivities:()=>{},ref:u,computed:m,onMounted:p,get User(){return L},get ChatDotRound(){return K},get Money(){return ge},get Setting(){return ie},get Bell(){return ne},get Link(){return ve}};return Object.defineProperty(o,"__isScriptSetup",{enumerable:!1,value:!0}),o}},[["render",function(r,d,u,m,p,g){const h=x,f=z,y=U;return t(),e("div",ji,[n("div",Ni,[n("div",Vi,[d[1]||(d[1]=n("h3",null,"最新订单",-1)),a(h,{type:"text",onClick:m.viewAllOrders},{default:i(()=>d[0]||(d[0]=[s("查看全部",-1)])),_:1,__:[0]})]),n("div",Li,[(t(!0),e(o,null,l(m.recentOrders,r=>(t(),e("div",{key:r.id,class:"order-item",onClick:e=>m.viewOrderDetail(r)},[n("div",Ei,[n("div",Gi,"#"+D(r.id),1),n("div",Wi,D(r.user),1),n("div",qi,D(m.formatTime(r.time)),1)]),n("div",{class:P(["order-amount",r.status])}," ¥"+D(r.amount),3),n("div",Fi,[a(f,{type:m.getStatusType(r.status),size:"small"},{default:i(()=>[s(D(m.getStatusText(r.status)),1)]),_:2},1032,["type"])])],8,Hi))),128))])]),n("div",Qi,[n("div",Bi,[d[3]||(d[3]=n("h3",null,"热门群组排行",-1)),a(h,{type:"text",onClick:m.viewAllGroups},{default:i(()=>d[2]||(d[2]=[s("查看全部",-1)])),_:1,__:[2]})]),n("div",Ji,[(t(!0),e(o,null,l(m.popularGroups,(a,i)=>(t(),e("div",{key:a.id,class:"group-item",onClick:e=>m.viewGroupDetail(a)},[n("div",Yi,D(i+1),1),n("div",Xi,[n("img",{src:a.avatar,alt:a.name},null,8,Zi)]),n("div",es,[n("div",ts,D(a.name),1),n("div",as,D(a.members)+"人",1)]),n("div",is,[n("div",ss,D(a.activity),1),d[4]||(d[4]=n("div",{class:"activity-label"},"活跃度",-1))])],8,Ki))),128))])]),n("div",ns,[n("div",rs,[d[6]||(d[6]=n("h3",null,"最新动态",-1)),a(h,{type:"text",onClick:m.refreshActivities},{default:i(()=>d[5]||(d[5]=[s("刷新",-1)])),_:1,__:[5]})]),n("div",os,[(t(!0),e(o,null,l(m.recentActivities,s=>(t(),e("div",{key:s.id,class:"activity-item"},[n("div",ls,[a(y,{color:s.color},{default:i(()=>[(t(),c(v(s.icon)))]),_:2},1032,["color"])]),n("div",cs,[n("div",ds,D(s.text),1),n("div",us,D(m.formatTime(s.time)),1)])]))),128))])])])}],["__scopeId","data-v-dbb168ac"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/dashboard/DashboardActivities.vue"]]),ps={class:"modern-dashboard"},gs={class:"dashboard-welcome"},vs={class:"welcome-banner"},hs={class:"banner-content"},fs={class:"welcome-text"},ys={class:"welcome-title"},bs={class:"welcome-subtitle"},ks={class:"banner-actions"},_s={class:"dashboard-content"},ws={class:"content-left"},Cs={class:"chart-card"},Ss={class:"card-header"},As={class:"card-actions"},Ds={class:"chart-container"},xs={ref:"mainChart",class:"main-chart"},Rs={class:"activity-card"},Ts={class:"activity-heatmap"},Is={class:"heatmap-grid"},zs=["title"],Us={class:"orders-card"},Ms={class:"card-header"},Ps={class:"orders-list"},Os=["onClick"],$s={class:"order-avatar"},js={class:"order-info"},Ns={class:"order-title"},Vs={class:"order-meta"},Ls={class:"order-amount"},Hs={class:"popular-groups-card"},Es={class:"card-header"},Gs={class:"groups-ranking"},Ws=["onClick"],qs={class:"group-info"},Fs={class:"group-name"},Qs={class:"group-stats"},Bs={class:"member-count"},Js={class:"revenue"},Ks={class:"group-trend"},Ys={class:"trend-value"},Xs={class:"recent-activities-card"},Zs={class:"card-header"},en={class:"activity-filters"},tn={class:"activities-timeline"},an={class:"timeline-content"},sn={class:"activity-header"},nn={class:"activity-user"},rn={class:"activity-action"},on={class:"activity-target"},ln={class:"activity-time"},cn={class:"content-right"},dn={class:"quick-stats-card"},un={class:"stats-grid"},mn={class:"stat-content"},pn={class:"stat-value"},gn={class:"stat-label"},vn={class:"notifications-card"},hn={class:"card-header"},fn={class:"notifications-list"},yn={class:"notification-content"},bn={class:"notification-title"},kn={class:"notification-time"},_n={class:"notification-actions"},wn={class:"system-status-card"},Cn={class:"status-metrics"},Sn={class:"status-item"},An={class:"status-progress"},Dn={class:"status-value"},xn={class:"status-item"},Rn={class:"status-progress"},Tn={class:"status-value"},In={class:"status-item"},zn={class:"status-progress"},Un={class:"status-value"},Mn={class:"status-item"},Pn={class:"status-progress"},On={class:"status-value"},$n={class:"quick-actions-card"},jn={class:"actions-grid"},Nn=["onClick"],Vn={class:"action-label"},Ln={class:"quick-actions-content"},Hn={class:"category-title"},En={class:"category-actions"},Gn=["onClick"],Wn={class:"action-info"},qn={class:"action-name"},Fn={class:"action-desc"};const Qn=C({__name:"ModernDashboard",setup(e,{expose:t}){t();const a=d(),i=S(),s=u(!1),n=u("today"),r=u(null),o=u(!1),l=w({totalUsers:"12,345",totalLinks:"8,976",todayRevenue:"23,456",conversionRate:"3.2"}),c=m(()=>(new Date).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"})),g=w([{key:"users",label:"总用户数",value:12580,change:"+12.5%",changeType:"positive",icon:xe,iconClass:"user-icon",chartGradient:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"},{key:"orders",label:"总订单数",value:8964,change:"+8.2%",changeType:"positive",icon:De,iconClass:"order-icon",chartGradient:"linear-gradient(135deg, #10b981 0%, #059669 100%)"},{key:"revenue",label:"总收入",value:256780,change:"+15.3%",changeType:"positive",icon:ge,iconClass:"revenue-icon",chartGradient:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"},{key:"conversion",label:"转化率",value:68.5,change:"-2.1%",changeType:"negative",icon:ee,iconClass:"conversion-icon",chartGradient:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)"}]),v=w({cpu:45,memory:62,disk:78,latency:23}),f=w([{key:"todayUsers",label:"今日新增用户",value:156,icon:L,color:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"},{key:"todayOrders",label:"今日订单",value:89,icon:De,color:"linear-gradient(135deg, #10b981 0%, #059669 100%)"},{key:"todayRevenue",label:"今日收入",value:"¥12,580",icon:ge,color:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"},{key:"activeUsers",label:"在线用户",value:1,icon:xe,color:"linear-gradient(135deg, #ef4444 0%, #dc2626 100%)"}]),y=w([{id:1,groupName:"高端投资理财群",userName:"张三",userAvatar:"/avatars/user1.jpg",amount:299,status:"paid",createdAt:new Date(Date.now()-3e5)},{id:2,groupName:"股票交流群",userName:"李四",userAvatar:"/avatars/user2.jpg",amount:199,status:"pending",createdAt:new Date(Date.now()-9e5)},{id:3,groupName:"创业交流群",userName:"王五",userAvatar:"/avatars/user3.jpg",amount:99,status:"paid",createdAt:new Date(Date.now()-18e5)}]),b=w([{id:1,title:"系统维护通知",type:"warning",read:!1,createdAt:new Date(Date.now()-6e5)},{id:2,title:"新用户注册",type:"info",read:!1,createdAt:new Date(Date.now()-12e5)},{id:3,title:"订单支付成功",type:"success",read:!0,createdAt:new Date(Date.now()-21e5)}]),k=w([{key:"addUser",label:"用户管理",icon:L,color:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",action:()=>a.push("/admin/users/list")},{key:"createGroup",label:"群组管理",icon:Ce,color:"linear-gradient(135deg, #10b981 0%, #059669 100%)",action:()=>a.push("/admin/community/groups")},{key:"viewReports",label:"数据分析",icon:ee,color:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",action:()=>a.push("/admin/analytics")},{key:"systemSettings",label:"系统设置",icon:ie,color:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",action:()=>a.push("/admin/system/settings")}]),_=w([{name:"用户管理",actions:[{key:"userList",label:"用户列表",description:"查看和管理系统用户",icon:L,color:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",action:()=>a.push("/admin/users/list")},{key:"userAnalytics",label:"用户分析",description:"查看用户行为和统计数据",icon:Y,color:"linear-gradient(135deg, #10b981 0%, #059669 100%)",action:()=>a.push("/admin/users/analytics")}]},{name:"社群管理",actions:[{key:"groupManage",label:"群组管理",description:"管理微信群组",icon:Ce,color:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",action:()=>a.push("/admin/community/groups")},{key:"templateManage",label:"模板管理",description:"管理群组模板",icon:$,color:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",action:()=>a.push("/admin/community/templates")}]}]),C=w([]),A=w([{id:1,name:"高端投资理财群",memberCount:2580,revenue:125600,trendType:"up",trendValue:"+15.2%"},{id:2,name:"股票交流群",memberCount:1890,revenue:89400,trendType:"up",trendValue:"+8.7%"},{id:3,name:"创业交流群",memberCount:1456,revenue:67200,trendType:"down",trendValue:"-2.3%"},{id:4,name:"技术分享群",memberCount:1234,revenue:45600,trendType:"up",trendValue:"+5.1%"},{id:5,name:"职场发展群",memberCount:987,revenue:32100,trendType:"flat",trendValue:"0%"}]),D=u("all"),x=w([{key:"all",label:"全部"},{key:"user",label:"用户"},{key:"order",label:"订单"},{key:"system",label:"系统"}]),R=w([{id:1,userName:"张三",action:"加入了",target:"高端投资理财群",type:"user",createdAt:new Date(Date.now()-12e4)},{id:2,userName:"李四",action:"完成了订单支付",target:"¥299",type:"order",createdAt:new Date(Date.now()-48e4)},{id:3,userName:"系统",action:"自动备份了",target:"数据库",type:"system",createdAt:new Date(Date.now()-9e5)},{id:4,userName:"王五",action:"创建了",target:"新的群组",type:"user",createdAt:new Date(Date.now()-15e5)},{id:5,userName:"赵六",action:"申请了",target:"提现 ¥1200",type:"order",createdAt:new Date(Date.now()-21e5)}]),T=m(()=>"all"===D.value?R:R.filter(e=>e.type===D.value)),I=()=>{const e=[],t=new Date;for(let a=364;a>=0;a--){const i=new Date(t.getTime()-24*a*60*60*1e3),s=Math.floor(20*Math.random());let n="none";s>15?n="high":s>8?n="medium":s>3&&(n="low"),e.push({date:i.toLocaleDateString(),count:s,level:n})}C.splice(0,C.length,...e)};p(()=>{I(),h(()=>{})});const z={router:a,userStore:i,showQuickActions:s,chartTimeRange:n,mainChart:r,showIntelligentWorkbench:o,metricsData:l,currentDate:c,getGreeting:()=>{const e=(new Date).getHours();return e<6?"夜深了，注意休息":e<12?"早上好":e<18?"下午好":"晚上好"},coreMetrics:g,systemStatus:v,quickStats:f,recentOrders:y,notifications:b,quickActions:k,actionCategories:_,heatmapData:C,popularGroups:A,activeActivityFilter:D,activityFilters:x,recentActivities:R,filteredActivities:T,generateHeatmapData:I,formatNumber:e=>e>=1e4?(e/1e4).toFixed(1)+"w":e.toLocaleString(),formatTime:e=>{const t=new Date-e,a=Math.floor(t/6e4),i=Math.floor(t/36e5),s=Math.floor(t/864e5);return a<1?"刚刚":a<60?`${a}分钟前`:i<24?`${i}小时前`:`${s}天前`},getOrderStatusText:e=>({paid:"已支付",pending:"待支付",cancelled:"已取消",refunded:"已退款"}[e]||"未知"),getNotificationIcon:e=>({warning:Ae,info:be,success:Se,error:Ae}[e]||be),refreshDashboard:()=>{H.success("数据已刷新")},viewOrderDetail:e=>{a.push("/admin/orders")},markAsRead:e=>{const t=b.find(t=>t.id===e);t&&(t.read=!0,H.success("已标记为已读"))},showAllNotifications:()=>{a.push("/admin/system/notifications")},handleQuickAction:e=>{e.action&&(e.action(),s.value=!1)},viewGroupDetail:e=>{a.push("/admin/community/groups")},generateAvatarColor:e=>{const t=["linear-gradient(135deg, #667eea 0%, #764ba2 100%)","linear-gradient(135deg, #f093fb 0%, #f5576c 100%)","linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)","linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)","linear-gradient(135deg, #fa709a 0%, #fee140 100%)","linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)","linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)","linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)","linear-gradient(135deg, #ff8a80 0%, #ea6100 100%)","linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%)"];let a=0;for(let i=0;i<e.length;i++)a=e.charCodeAt(i)+((a<<5)-a);return t[Math.abs(a)%t.length]},getAvatarText:e=>e?/[\u4e00-\u9fa5]/.test(e)?e.slice(-1):e.charAt(0).toUpperCase():"?",initChart:()=>{if(!r.value)return;chart.setOption({tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{data:["用户数","订单数","收入"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:["00:00","04:00","08:00","12:00","16:00","20:00","24:00"]},yAxis:{type:"value"},series:[{name:"用户数",type:"line",stack:"Total",smooth:!0,lineStyle:{color:"#3b82f6"},areaStyle:{color:"rgba(59, 130, 246, 0.2)"},data:[120,132,101,134,90,230,210]},{name:"订单数",type:"line",stack:"Total",smooth:!0,lineStyle:{color:"#10b981"},areaStyle:{color:"rgba(16, 185, 129, 0.2)"},data:[220,182,191,234,290,330,310]},{name:"收入",type:"line",stack:"Total",smooth:!0,lineStyle:{color:"#f59e0b"},areaStyle:{color:"rgba(245, 158, 11, 0.2)"},data:[150,232,201,154,190,330,410]}]}),window.addEventListener("resize",()=>{chart.resize()})},ref:u,reactive:w,computed:m,onMounted:p,nextTick:h,get useRouter(){return d},get useUserStore(){return S},get ElMessage(){return H},get Plus(){return N},get Refresh(){return Te},get ArrowUp(){return pe},get ArrowDown(){return me},get ArrowRight(){return Re},get User(){return L},get UserFilled(){return xe},get Tickets(){return De},get Money(){return ge},get TrendCharts(){return ee},get Bell(){return ne},get Warning(){return Ae},get InfoFilled(){return be},get SuccessFilled(){return Se},get Setting(){return ie},get Document(){return $},get DataAnalysis(){return Y},get Connection(){return Ce},get Grid(){return ae},NavigationTestPanel:vt,IntelligentWorkbench:fa,AIEnhancedSearch:_i,DashboardMetrics:xi,DashboardCharts:$i,DashboardActivities:ms};return Object.defineProperty(z,"__isScriptSetup",{enumerable:!1,value:!0}),z}},[["render",function(r,d,u,m,p,g){const h=R,f=U,y=x,b=Ie,w=ke,C=E,S=k("Minus"),A=_e,T=G,I=we,z=Q;return t(),e("div",ps,[n("div",gs,[a(h,{class:"welcome-card",shadow:"never"},{default:i(()=>d[5]||(d[5]=[n("div",{class:"welcome-content"},[n("h2",null,"欢迎回来！"),n("p",null,"这里是您的管理控制台，您可以查看最新的业务数据和进行快捷操作。")],-1)])),_:1,__:[5]})]),n("div",vs,[n("div",hs,[n("div",fs,[n("h1",ys," 欢迎回来，"+D(m.userStore.nickname||"管理员")+"！ ",1),n("p",bs," 今天是 "+D(m.currentDate)+"，"+D(m.getGreeting()),1)]),n("div",ks,[a(y,{type:"primary",class:"modern-btn primary",onClick:d[0]||(d[0]=e=>m.showQuickActions=!0)},{default:i(()=>[a(f,null,{default:i(()=>[a(m.Plus)]),_:1}),d[6]||(d[6]=s(" 快速操作 ",-1))]),_:1,__:[6]}),a(y,{class:"modern-btn secondary",onClick:m.refreshDashboard},{default:i(()=>[a(f,null,{default:i(()=>[a(m.Refresh)]),_:1}),d[7]||(d[7]=s(" 刷新数据 ",-1))]),_:1,__:[7]})])]),d[8]||(d[8]=n("div",{class:"banner-decoration"},[n("div",{class:"decoration-circle circle-1"}),n("div",{class:"decoration-circle circle-2"}),n("div",{class:"decoration-circle circle-3"})],-1))]),a(m.DashboardMetrics,{data:m.metricsData},null,8,["data"]),n("div",_s,[n("div",ws,[n("div",Cs,[n("div",Ss,[d[9]||(d[9]=n("h3",{class:"card-title"},"实时数据概览",-1)),n("div",As,[a(w,{modelValue:m.chartTimeRange,"onUpdate:modelValue":d[1]||(d[1]=e=>m.chartTimeRange=e),size:"small",class:"time-selector"},{default:i(()=>[a(b,{label:"今日",value:"today"}),a(b,{label:"本周",value:"week"}),a(b,{label:"本月",value:"month"}),a(b,{label:"本年",value:"year"})]),_:1},8,["modelValue"])])]),n("div",Ds,[n("div",xs,null,512)])]),n("div",Rs,[d[10]||(d[10]=_('<div class="card-header" data-v-275f41e0><h3 class="card-title" data-v-275f41e0>用户活动热力图</h3><div class="activity-legend" data-v-275f41e0><span class="legend-item" data-v-275f41e0><div class="legend-color low" data-v-275f41e0></div> 低活跃 </span><span class="legend-item" data-v-275f41e0><div class="legend-color medium" data-v-275f41e0></div> 中活跃 </span><span class="legend-item" data-v-275f41e0><div class="legend-color high" data-v-275f41e0></div> 高活跃 </span></div></div>',1)),n("div",Ts,[n("div",Is,[(t(!0),e(o,null,l(m.heatmapData,(a,i)=>(t(),e("div",{key:i,class:P(["heatmap-cell",a.level]),title:`${a.date}: ${a.count} 活跃用户`},null,10,zs))),128))])])]),n("div",Us,[n("div",Ms,[d[12]||(d[12]=n("h3",{class:"card-title"},"最新订单",-1)),a(y,{text:"",onClick:d[2]||(d[2]=e=>r.$router.push("/admin/orders"))},{default:i(()=>[d[11]||(d[11]=s(" 查看全部 ",-1)),a(f,null,{default:i(()=>[a(m.ArrowRight)]),_:1})]),_:1,__:[11]})]),n("div",Ps,[(t(!0),e(o,null,l(m.recentOrders,r=>(t(),e("div",{key:r.id,class:"order-item",onClick:e=>m.viewOrderDetail(r.id)},[n("div",$s,[a(C,{size:40,src:r.userAvatar,style:re({background:m.generateAvatarColor(r.userName),color:"white",fontWeight:"600"})},{default:i(()=>[s(D(m.getAvatarText(r.userName)),1)]),_:2},1032,["src","style"])]),n("div",js,[n("div",Ns,D(r.groupName),1),n("div",Vs,D(r.userName)+" · "+D(m.formatTime(r.createdAt)),1)]),n("div",Ls," ¥"+D(r.amount),1),n("div",{class:P(["order-status",r.status])},D(m.getOrderStatusText(r.status)),3)],8,Os))),128))])]),n("div",Hs,[n("div",Es,[d[14]||(d[14]=n("h3",{class:"card-title"},"热门群组排行",-1)),a(y,{text:"",onClick:d[3]||(d[3]=e=>r.$router.push("/admin/community/groups"))},{default:i(()=>[d[13]||(d[13]=s(" 查看全部 ",-1)),a(f,null,{default:i(()=>[a(m.ArrowRight)]),_:1})]),_:1,__:[13]})]),n("div",Gs,[(t(!0),e(o,null,l(m.popularGroups,(s,r)=>(t(),e("div",{key:s.id,class:"ranking-item",onClick:e=>m.viewGroupDetail(s.id)},[n("div",{class:P(["ranking-number",{"top-three":r<3}])},D(r+1),3),n("div",qs,[n("div",Fs,D(s.name),1),n("div",Qs,[n("span",Bs,D(s.memberCount)+"人",1),d[15]||(d[15]=n("span",{class:"separator"},"·",-1)),n("span",Js,"¥"+D(s.revenue),1)])]),n("div",Ks,[n("div",{class:P(["trend-icon",s.trendType])},[a(f,null,{default:i(()=>["up"===s.trendType?(t(),c(m.ArrowUp,{key:0})):"down"===s.trendType?(t(),c(m.ArrowDown,{key:1})):(t(),c(S,{key:2}))]),_:2},1024)],2),n("span",Ys,D(s.trendValue),1)])],8,Ws))),128))])]),n("div",Xs,[n("div",Zs,[d[16]||(d[16]=n("h3",{class:"card-title"},"最新动态",-1)),n("div",en,[a(A,{size:"small"},{default:i(()=>[(t(!0),e(o,null,l(m.activityFilters,e=>(t(),c(y,{key:e.key,type:m.activeActivityFilter===e.key?"primary":"",onClick:t=>m.activeActivityFilter=e.key},{default:i(()=>[s(D(e.label),1)]),_:2},1032,["type","onClick"]))),128))]),_:1})])]),n("div",tn,[(t(!0),e(o,null,l(m.filteredActivities,a=>(t(),e("div",{key:a.id,class:"timeline-item"},[n("div",{class:P(["timeline-dot",a.type])},null,2),n("div",an,[n("div",sn,[n("span",nn,D(a.userName),1),n("span",rn,D(a.action),1),n("span",on,D(a.target),1)]),n("div",ln,D(m.formatTime(a.createdAt)),1)])]))),128))])])]),n("div",cn,[n("div",dn,[d[17]||(d[17]=n("div",{class:"card-header"},[n("h3",{class:"card-title"},"今日统计")],-1)),n("div",un,[(t(!0),e(o,null,l(m.quickStats,s=>(t(),e("div",{class:"stat-item",key:s.key},[n("div",{class:"stat-icon",style:re({background:s.color})},[a(f,null,{default:i(()=>[(t(),c(v(s.icon)))]),_:2},1024)],4),n("div",mn,[n("div",pn,D(s.value),1),n("div",gn,D(s.label),1)])]))),128))])]),n("div",vn,[n("div",hn,[d[19]||(d[19]=n("h3",{class:"card-title"},"通知中心",-1)),a(T,{value:m.notifications.length,hidden:0===m.notifications.length},{default:i(()=>[a(y,{text:"",onClick:m.showAllNotifications},{default:i(()=>d[18]||(d[18]=[s(" 查看全部 ",-1)])),_:1,__:[18]})]),_:1},8,["value","hidden"])]),n("div",fn,[(t(!0),e(o,null,l(m.notifications.slice(0,5),r=>(t(),e("div",{key:r.id,class:P(["notification-item",{unread:!r.read}])},[n("div",{class:P(["notification-icon",r.type])},[a(f,null,{default:i(()=>[(t(),c(v(m.getNotificationIcon(r.type))))]),_:2},1024)],2),n("div",yn,[n("div",bn,D(r.title),1),n("div",kn,D(m.formatTime(r.createdAt)),1)]),n("div",_n,[a(y,{text:"",size:"small",onClick:e=>m.markAsRead(r.id)},{default:i(()=>[...d[20]||(d[20]=[s(" 标记已读 ",-1)])]),_:2,__:[20]},1032,["onClick"])])],2))),128))])]),n("div",wn,[d[25]||(d[25]=n("div",{class:"card-header"},[n("h3",{class:"card-title"},"系统状态"),n("div",{class:"status-indicator online"},[n("div",{class:"status-dot"}),s(" 运行正常 ")])],-1)),n("div",Cn,[n("div",Sn,[d[21]||(d[21]=n("div",{class:"status-label"},"CPU 使用率",-1)),n("div",An,[a(I,{percentage:m.systemStatus.cpu,"show-text":!1},null,8,["percentage"]),n("span",Dn,D(m.systemStatus.cpu)+"%",1)])]),n("div",xn,[d[22]||(d[22]=n("div",{class:"status-label"},"内存使用率",-1)),n("div",Rn,[a(I,{percentage:m.systemStatus.memory,"show-text":!1,color:"#10b981"},null,8,["percentage"]),n("span",Tn,D(m.systemStatus.memory)+"%",1)])]),n("div",In,[d[23]||(d[23]=n("div",{class:"status-label"},"磁盘使用率",-1)),n("div",zn,[a(I,{percentage:m.systemStatus.disk,"show-text":!1,color:"#f59e0b"},null,8,["percentage"]),n("span",Un,D(m.systemStatus.disk)+"%",1)])]),n("div",Mn,[d[24]||(d[24]=n("div",{class:"status-label"},"网络延迟",-1)),n("div",Pn,[n("span",On,D(m.systemStatus.latency)+"ms",1)])])])]),n("div",$n,[d[26]||(d[26]=n("div",{class:"card-header"},[n("h3",{class:"card-title"},"快捷操作")],-1)),n("div",jn,[(t(!0),e(o,null,l(m.quickActions,s=>(t(),e("div",{key:s.key,class:"action-item",onClick:e=>m.handleQuickAction(s)},[n("div",{class:"action-icon",style:re({background:s.color})},[a(f,null,{default:i(()=>[(t(),c(v(s.icon)))]),_:2},1024)],4),n("div",Vn,D(s.label),1)],8,Nn))),128))])])])]),a(z,{modelValue:m.showQuickActions,"onUpdate:modelValue":d[4]||(d[4]=e=>m.showQuickActions=e),title:"快速操作",width:"600px",class:"quick-actions-dialog"},{default:i(()=>[n("div",Ln,[(t(!0),e(o,null,l(m.actionCategories,s=>(t(),e("div",{class:"action-category",key:s.name},[n("h4",Hn,D(s.name),1),n("div",En,[(t(!0),e(o,null,l(s.actions,s=>(t(),e("div",{key:s.key,class:"category-action-item",onClick:e=>m.handleQuickAction(s)},[n("div",{class:"action-icon",style:re({background:s.color})},[a(f,null,{default:i(()=>[(t(),c(v(s.icon)))]),_:2},1024)],4),n("div",Wn,[n("div",qn,D(s.label),1),n("div",Fn,D(s.description),1)])],8,Gn))),128))])]))),128))])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-275f41e0"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/dashboard/ModernDashboard.vue"]]);export{Qn as default};
