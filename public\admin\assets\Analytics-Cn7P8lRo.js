/* empty css             *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                  *//* empty css                   *//* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css               *//* empty css                    *//* empty css                *//* empty css                       *//* empty css                        *//* empty css               *//* empty css                  *//* empty css                       */import{f as e,o as a,p as t}from"./anti-block-CJ1NNk3N.js";import{l as s,q as l,G as i,A as n,m as o,E as c,F as r,Y as d,B as m,z as u}from"./vue-vendor-BcnDv-68.js";import{_ as p}from"./index-eUTsTR3J.js";import{a0 as _,a1 as h,U as g,V as v,W as b,Y as f,Z as y,s as w,_ as S,$ as k,bg as D,u as V,ai as j,aj as C,ak as x,a7 as M,a2 as T,ap as A,a3 as U,al as R,aq as I,a4 as H}from"./element-plus-C2UshkXo.js";import"./index-D4AyIzGN.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const $={class:"analytics-page"},q={class:"page-header"},N={class:"page-actions"},P={class:"metrics-overview"},z={class:"metric-card"},L={class:"metric-content"},W={class:"metric-number"},B={class:"metric-trend"},O={class:"metric-card"},Q={class:"metric-content"},F={class:"metric-number"},Y={class:"metric-trend"},E={class:"metric-card"},G={class:"metric-content"},Z={class:"metric-number"},J={class:"metric-trend"},K={class:"metric-card"},X={class:"metric-content"},ee={class:"metric-number"},ae={class:"metric-trend"},te={class:"charts-section"},se={slot:"header",class:"card-header"},le={class:"chart-container"},ie={key:0,class:"chart-placeholder"},ne={key:1,class:"trend-chart"},oe={class:"chart-demo"},ce={slot:"header",class:"card-header"},re={class:"chart-container"},de={class:"domain-stats"},me={class:"domain-info"},ue={class:"domain-name"},pe={class:"domain-type"},_e={class:"domain-metrics"},he={class:"metric-item"},ge={class:"metric-value"},ve={class:"metric-item"},be={class:"metric-value"},fe={class:"metric-item"},ye={class:"details-section"},we={class:"stats-content"},Se={class:"stat-info"},ke={class:"stat-name"},De={class:"stat-percent"},Ve={class:"stat-bar"},je={class:"stat-count"},Ce={class:"stats-content"},xe={class:"stat-info"},Me={class:"stat-name"},Te={class:"stat-percent"},Ae={class:"stat-bar"},Ue={class:"stat-count"},Re={class:"stats-content"},Ie={class:"time-stats"},He={class:"time-label"},$e={class:"time-bar"},qe={class:"time-count"},Ne={class:"alerts-section"},Pe={slot:"header",class:"card-header"},ze={class:"alerts-content"},Le={class:"alert-summary"},We={class:"summary-item"},Be={class:"summary-content"},Oe={class:"summary-number"},Qe={class:"summary-item"},Fe={class:"summary-content"},Ye={class:"summary-number"},Ee={class:"summary-item"},Ge={class:"summary-content"},Ze={class:"summary-number"},Je={class:"alert-list"},Ke={class:"alert-icon"},Xe={class:"alert-content"},ea={class:"alert-title"},aa={class:"alert-desc"},ta={class:"alert-time"},sa={class:"alert-actions"},la={class:"domain-detail-content"},ia={"slot-scope":"scope"},na={class:"domain-text"},oa={"slot-scope":"scope"},ca={"slot-scope":"scope"},ra={"slot-scope":"scope"},da={"slot-scope":"scope"},ma={class:"alert-settings-content"},ua={slot:"footer",class:"dialog-footer"};const pa=p({name:"AntiBlockAnalytics",data:()=>({loading:!1,domainDetailVisible:!1,domainDetailLoading:!1,dateRange:[],trendPeriod:"7d",analytics:{total_clicks:0,unique_visitors:0,success_rate:0,avg_response_time:0,click_trend:0,visitor_trend:0,success_trend:0,response_trend:0},trendData:[],domainStats:[],domainDetailStats:[],sourceStats:[{name:"微信",count:1250,percentage:45},{name:"QQ",count:890,percentage:32},{name:"直接访问",count:456,percentage:16},{name:"其他",count:189,percentage:7}],regionStats:[{name:"广东",count:890,percentage:32},{name:"浙江",count:678,percentage:24},{name:"江苏",count:456,percentage:16},{name:"上海",count:234,percentage:8},{name:"其他",count:527,percentage:20}],hourlyStats:[],alerts:{warning_count:3,error_count:1,blocked_count:2},recentAlerts:[{id:1,type:"warning",title:"域名响应时间过长",description:"short1.example.com 响应时间超过5秒",created_at:new Date},{id:2,type:"error",title:"域名访问失败",description:"short2.example.com 返回404错误",created_at:new Date},{id:3,type:"blocked",title:"域名被封禁",description:"short3.example.com 被微信封禁",created_at:new Date}],alertSettingsVisible:!1,alertSettings:{domain_check_enabled:!0,domain_check_interval:5,block_threshold:3,response_time_threshold:5e3,success_rate_threshold:90,notification_enabled:!0,notification_email:"<EMAIL>",notification_webhook:"",auto_switch_enabled:!0,auto_switch_threshold:3}}),computed:{maxHourlyCount(){return Math.max(...this.hourlyStats.map(e=>e.count))}},mounted(){this.initDateRange(),this.loadAnalytics(),this.initHourlyStats()},methods:{initDateRange(){const e=new Date,a=new Date;a.setDate(e.getDate()-7),this.dateRange=[a.toISOString().split("T")[0],e.toISOString().split("T")[0]]},initHourlyStats(){this.hourlyStats=[];for(let e=0;e<24;e++)this.hourlyStats.push({hour:e,count:Math.floor(100*Math.random())+10})},async loadAnalytics(){this.loading=!0;try{const e={start_date:this.dateRange[0],end_date:this.dateRange[1]},{data:a}=await t(e);this.analytics=a,await this.loadDomainStats(),await this.loadTrendData()}catch(e){this.$message.error("加载统计数据失败")}finally{this.loading=!1}},async loadDomainStats(){try{const{data:a}=await e({per_page:10});this.domainStats=a.data.map(e=>({...e,usage_count:Math.floor(1e3*Math.random())+100,success_rate:Math.floor(20*Math.random())+80}))}catch(a){}},async loadTrendData(){try{const e={period:this.trendPeriod,start_date:this.dateRange[0],end_date:this.dateRange[1]},{data:t}=await a(e);this.trendData=t.data||[]}catch(e){}},showDomainDetail(){this.domainDetailVisible=!0,this.loadDomainDetailStats()},async loadDomainDetailStats(){this.domainDetailLoading=!0;try{const{data:a}=await e({per_page:50});this.domainDetailStats=a.data.map(e=>({...e,usage_count:Math.floor(1e3*Math.random())+100,success_count:Math.floor(800*Math.random())+80,success_rate:Math.floor(20*Math.random())+80,avg_response_time:Math.floor(2e3*Math.random())+200}))}catch(a){this.$message.error("加载域名详情失败")}finally{this.domainDetailLoading=!1}},refreshData(){this.loadAnalytics()},exportReport(){this.$message.success("报告导出中...")},showAlertSettings(){this.alertSettingsVisible=!0,this.loadAlertSettings()},loadAlertSettings(){this.alertSettings={domain_check_enabled:!0,domain_check_interval:5,block_threshold:3,response_time_threshold:5e3,success_rate_threshold:90,notification_enabled:!0,notification_email:"<EMAIL>",notification_webhook:"",auto_switch_enabled:!0,auto_switch_threshold:3}},saveAlertSettings(){this.$message.loading("正在保存设置..."),setTimeout(()=>{this.$message.success("告警设置保存成功"),this.alertSettingsVisible=!1},1e3)},handleAlert(e){this.$message.success("告警处理成功")},formatNumber:e=>e>=1e4?(e/1e4).toFixed(1)+"W":e.toString(),getTrendClass:e=>e>0?"trend-up":e<0?"trend-down":"trend-stable",getTrendIcon:e=>e>0?"el-icon-top":e<0?"el-icon-bottom":"el-icon-minus",getDomainTypeName:e=>({redirect:"短链接",landing:"中转页",api:"API服务"}[e]||e),getDomainTypeColor:e=>({redirect:"primary",landing:"success",api:"warning"}[e]||""),getHealthColor:e=>e>=90?"#67c23a":e>=80?"#409eff":e>=60?"#e6a23c":"#f56c6c",getAlertIcon:e=>({warning:"el-icon-warning",error:"el-icon-error",blocked:"el-icon-remove-outline"}[e]||"el-icon-info"),formatTime(e){return e?this.$dayjs(e).format("MM-DD HH:mm"):"-"}}},[["render",function(e,a,t,p,pa,_a){const ha=_,ga=h,va=f,ba=g,fa=k,ya=S,wa=v,Sa=D,ka=C,Da=x,Va=j,ja=b,Ca=A,xa=R,Ma=U,Ta=I,Aa=H,Ua=T,Ra=M;return o(),s("div",$,[l("div",q,[a[15]||(a[15]=l("div",{class:"page-title"},[l("h1",null,"📊 统计分析"),l("p",{class:"page-desc"},"防红系统的访问统计、域名分析和性能监控")],-1)),l("div",N,[i(ha,{modelValue:pa.dateRange,"onUpdate:modelValue":a[0]||(a[0]=e=>pa.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",onChange:_a.loadAnalytics},null,8,["modelValue","onChange"]),i(ga,{type:"primary",onClick:_a.exportReport},{default:n(()=>a[13]||(a[13]=[l("i",{class:"el-icon-download"},null,-1),c(" 导出报告 ",-1)])),_:1,__:[13]},8,["onClick"]),i(ga,{type:"success",onClick:_a.refreshData},{default:n(()=>a[14]||(a[14]=[l("i",{class:"el-icon-refresh"},null,-1),c(" 刷新 ",-1)])),_:1,__:[14]},8,["onClick"])])]),l("div",P,[i(ba,{gutter:20},{default:n(()=>[i(va,{span:6},{default:n(()=>[l("div",z,[a[18]||(a[18]=l("div",{class:"metric-icon total-clicks"},[l("i",{class:"el-icon-view"})],-1)),l("div",L,[l("div",W,y(_a.formatNumber(pa.analytics.total_clicks)),1),a[17]||(a[17]=l("div",{class:"metric-label"},"总访问量",-1)),l("div",B,[l("span",{class:w(_a.getTrendClass(pa.analytics.click_trend))},[l("i",{class:w(_a.getTrendIcon(pa.analytics.click_trend))},null,2),c(" "+y(Math.abs(pa.analytics.click_trend))+"% ",1)],2),a[16]||(a[16]=l("span",{class:"trend-desc"},"较昨日",-1))])])])]),_:1}),i(va,{span:6},{default:n(()=>[l("div",O,[a[21]||(a[21]=l("div",{class:"metric-icon unique-visitors"},[l("i",{class:"el-icon-user"})],-1)),l("div",Q,[l("div",F,y(_a.formatNumber(pa.analytics.unique_visitors)),1),a[20]||(a[20]=l("div",{class:"metric-label"},"独立访客",-1)),l("div",Y,[l("span",{class:w(_a.getTrendClass(pa.analytics.visitor_trend))},[l("i",{class:w(_a.getTrendIcon(pa.analytics.visitor_trend))},null,2),c(" "+y(Math.abs(pa.analytics.visitor_trend))+"% ",1)],2),a[19]||(a[19]=l("span",{class:"trend-desc"},"较昨日",-1))])])])]),_:1}),i(va,{span:6},{default:n(()=>[l("div",E,[a[24]||(a[24]=l("div",{class:"metric-icon success-rate"},[l("i",{class:"el-icon-success"})],-1)),l("div",G,[l("div",Z,y(pa.analytics.success_rate)+"%",1),a[23]||(a[23]=l("div",{class:"metric-label"},"访问成功率",-1)),l("div",J,[l("span",{class:w(_a.getTrendClass(pa.analytics.success_trend))},[l("i",{class:w(_a.getTrendIcon(pa.analytics.success_trend))},null,2),c(" "+y(Math.abs(pa.analytics.success_trend))+"% ",1)],2),a[22]||(a[22]=l("span",{class:"trend-desc"},"较昨日",-1))])])])]),_:1}),i(va,{span:6},{default:n(()=>[l("div",K,[a[27]||(a[27]=l("div",{class:"metric-icon response-time"},[l("i",{class:"el-icon-time"})],-1)),l("div",X,[l("div",ee,y(pa.analytics.avg_response_time)+"ms",1),a[26]||(a[26]=l("div",{class:"metric-label"},"平均响应时间",-1)),l("div",ae,[l("span",{class:w(_a.getTrendClass(-pa.analytics.response_trend))},[l("i",{class:w(_a.getTrendIcon(-pa.analytics.response_trend))},null,2),c(" "+y(Math.abs(pa.analytics.response_trend))+"ms ",1)],2),a[25]||(a[25]=l("span",{class:"trend-desc"},"较昨日",-1))])])])]),_:1})]),_:1})]),l("div",te,[i(ba,{gutter:20},{default:n(()=>[i(va,{span:12},{default:n(()=>[i(wa,{class:"chart-card"},{default:n(()=>[l("div",se,[a[31]||(a[31]=l("span",null,"📈 访问趋势",-1)),i(ya,{modelValue:pa.trendPeriod,"onUpdate:modelValue":a[1]||(a[1]=e=>pa.trendPeriod=e),size:"small",onChange:_a.loadTrendData},{default:n(()=>[i(fa,{label:"7d"},{default:n(()=>a[28]||(a[28]=[c("7天",-1)])),_:1,__:[28]}),i(fa,{label:"30d"},{default:n(()=>a[29]||(a[29]=[c("30天",-1)])),_:1,__:[29]}),i(fa,{label:"90d"},{default:n(()=>a[30]||(a[30]=[c("90天",-1)])),_:1,__:[30]})]),_:1},8,["modelValue","onChange"])]),l("div",le,[pa.trendData.length?(o(),s("div",ne,[l("div",oe,[a[33]||(a[33]=l("h4",null,"访问趋势图",-1)),l("p",null,"显示过去"+y(pa.trendPeriod)+"的访问统计",1)])])):(o(),s("div",ie,a[32]||(a[32]=[l("i",{class:"el-icon-loading"},null,-1),l("p",null,"加载中...",-1)])))])]),_:1})]),_:1}),i(va,{span:12},{default:n(()=>[i(wa,{class:"chart-card"},{default:n(()=>[l("div",ce,[a[35]||(a[35]=l("span",null,"🌐 域名使用分布",-1)),i(ga,{type:"text",onClick:_a.showDomainDetail},{default:n(()=>a[34]||(a[34]=[c("查看详情",-1)])),_:1,__:[34]},8,["onClick"])]),l("div",re,[l("div",de,[(o(!0),s(r,null,d(pa.domainStats,e=>(o(),s("div",{class:"domain-item",key:e.id},[l("div",me,[l("span",ue,y(e.domain),1),l("span",pe,y(_a.getDomainTypeName(e.type)),1)]),l("div",_e,[l("div",he,[a[36]||(a[36]=l("span",{class:"metric-label"},"使用次数",-1)),l("span",ge,y(e.usage_count),1)]),l("div",ve,[a[37]||(a[37]=l("span",{class:"metric-label"},"成功率",-1)),l("span",be,y(e.success_rate)+"%",1)]),l("div",fe,[a[38]||(a[38]=l("span",{class:"metric-label"},"健康度",-1)),i(Sa,{percentage:e.health_score,color:_a.getHealthColor(e.health_score),"stroke-width":6,"show-text":!1},null,8,["percentage","color"])])])]))),128))])])]),_:1})]),_:1})]),_:1})]),l("div",ye,[i(ba,{gutter:20},{default:n(()=>[i(va,{span:8},{default:n(()=>[i(wa,{class:"stats-card"},{default:n(()=>[a[39]||(a[39]=l("div",{slot:"header",class:"card-header"},[l("span",null,"📱 访问来源")],-1)),l("div",we,[(o(!0),s(r,null,d(pa.sourceStats,e=>(o(),s("div",{class:"stat-item",key:e.name},[l("div",Se,[l("div",ke,y(e.name),1),l("div",De,y(e.percentage)+"%",1)]),l("div",Ve,[l("div",{class:"stat-fill",style:V({width:e.percentage+"%"})},null,4)]),l("div",je,y(e.count),1)]))),128))])]),_:1,__:[39]})]),_:1}),i(va,{span:8},{default:n(()=>[i(wa,{class:"stats-card"},{default:n(()=>[a[40]||(a[40]=l("div",{slot:"header",class:"card-header"},[l("span",null,"🌍 地区分布")],-1)),l("div",Ce,[(o(!0),s(r,null,d(pa.regionStats,e=>(o(),s("div",{class:"stat-item",key:e.name},[l("div",xe,[l("div",Me,y(e.name),1),l("div",Te,y(e.percentage)+"%",1)]),l("div",Ae,[l("div",{class:"stat-fill",style:V({width:e.percentage+"%"})},null,4)]),l("div",Ue,y(e.count),1)]))),128))])]),_:1,__:[40]})]),_:1}),i(va,{span:8},{default:n(()=>[i(wa,{class:"stats-card"},{default:n(()=>[a[41]||(a[41]=l("div",{slot:"header",class:"card-header"},[l("span",null,"🕐 时间分布")],-1)),l("div",Re,[l("div",Ie,[(o(!0),s(r,null,d(pa.hourlyStats,e=>(o(),s("div",{class:"time-item",key:e.hour},[l("div",He,y(e.hour)+":00",1),l("div",$e,[l("div",{class:"time-fill",style:V({height:e.count/_a.maxHourlyCount*100+"%"})},null,4)]),l("div",qe,y(e.count),1)]))),128))])])]),_:1,__:[41]})]),_:1})]),_:1})]),l("div",Ne,[i(wa,{class:"alerts-card"},{default:n(()=>[l("div",Pe,[a[43]||(a[43]=l("span",null,"⚠️ 异常监控",-1)),i(ga,{type:"text",onClick:_a.showAlertSettings},{default:n(()=>a[42]||(a[42]=[c("告警设置",-1)])),_:1,__:[42]},8,["onClick"])]),l("div",ze,[l("div",Le,[l("div",We,[a[45]||(a[45]=l("div",{class:"summary-icon warning"},[l("i",{class:"el-icon-warning"})],-1)),l("div",Be,[l("div",Oe,y(pa.alerts.warning_count),1),a[44]||(a[44]=l("div",{class:"summary-label"},"警告",-1))])]),l("div",Qe,[a[47]||(a[47]=l("div",{class:"summary-icon error"},[l("i",{class:"el-icon-error"})],-1)),l("div",Fe,[l("div",Ye,y(pa.alerts.error_count),1),a[46]||(a[46]=l("div",{class:"summary-label"},"错误",-1))])]),l("div",Ee,[a[49]||(a[49]=l("div",{class:"summary-icon blocked"},[l("i",{class:"el-icon-remove-outline"})],-1)),l("div",Ge,[l("div",Ze,y(pa.alerts.blocked_count),1),a[48]||(a[48]=l("div",{class:"summary-label"},"封禁",-1))])])]),l("div",Je,[(o(!0),s(r,null,d(pa.recentAlerts,e=>(o(),s("div",{class:"alert-item",key:e.id},[l("div",Ke,[l("i",{class:w(_a.getAlertIcon(e.type))},null,2)]),l("div",Xe,[l("div",ea,y(e.title),1),l("div",aa,y(e.description),1),l("div",ta,y(_a.formatTime(e.created_at)),1)]),l("div",sa,[i(ga,{type:"text",size:"small",onClick:a=>_a.handleAlert(e)},{default:n(()=>[...a[50]||(a[50]=[c(" 处理 ",-1)])]),_:2,__:[50]},1032,["onClick"])])]))),128))])])]),_:1})]),i(ja,{title:"域名详细统计",visible:pa.domainDetailVisible,width:"800px"},{default:n(()=>[l("div",la,[m((o(),u(Va,{data:pa.domainDetailStats},{default:n(()=>[i(ka,{prop:"domain",label:"域名",width:"180"},{default:n(()=>[l("template",ia,[l("span",na,y(e.scope.row.domain),1)])]),_:1}),i(ka,{prop:"type",label:"类型",width:"100"},{default:n(()=>[l("template",oa,[i(Da,{size:"small",type:_a.getDomainTypeColor(e.scope.row.type)},{default:n(()=>[c(y(_a.getDomainTypeName(e.scope.row.type)),1)]),_:1},8,["type"])])]),_:1}),i(ka,{prop:"usage_count",label:"使用次数",width:"100"}),i(ka,{prop:"success_count",label:"成功次数",width:"100"}),i(ka,{prop:"success_rate",label:"成功率",width:"100"},{default:n(()=>[l("template",ca,[l("span",null,y(e.scope.row.success_rate)+"%",1)])]),_:1}),i(ka,{prop:"health_score",label:"健康度",width:"120"},{default:n(()=>[l("template",ra,[i(Sa,{percentage:e.scope.row.health_score,color:_a.getHealthColor(e.scope.row.health_score),"stroke-width":8},null,8,["percentage","color"])])]),_:1}),i(ka,{prop:"avg_response_time",label:"平均响应时间",width:"120"},{default:n(()=>[l("template",da,[l("span",null,y(e.scope.row.avg_response_time)+"ms",1)])]),_:1})]),_:1},8,["data"])),[[Ra,pa.domainDetailLoading]])])]),_:1},8,["visible"]),i(ja,{title:"告警设置",visible:pa.alertSettingsVisible,width:"600px"},{default:n(()=>[l("div",ma,[i(Ua,{model:pa.alertSettings,"label-width":"150px"},{default:n(()=>[i(Ca,{"content-position":"left"},{default:n(()=>a[51]||(a[51]=[c("域名检测设置",-1)])),_:1,__:[51]}),i(Ma,{label:"启用域名检测"},{default:n(()=>[i(xa,{modelValue:pa.alertSettings.domain_check_enabled,"onUpdate:modelValue":a[2]||(a[2]=e=>pa.alertSettings.domain_check_enabled=e)},null,8,["modelValue"])]),_:1}),i(Ma,{label:"检测间隔（分钟）"},{default:n(()=>[i(Ta,{modelValue:pa.alertSettings.domain_check_interval,"onUpdate:modelValue":a[3]||(a[3]=e=>pa.alertSettings.domain_check_interval=e),min:1,max:60,disabled:!pa.alertSettings.domain_check_enabled},null,8,["modelValue","disabled"])]),_:1}),i(Ma,{label:"封禁阈值"},{default:n(()=>[i(Ta,{modelValue:pa.alertSettings.block_threshold,"onUpdate:modelValue":a[4]||(a[4]=e=>pa.alertSettings.block_threshold=e),min:1,max:10,disabled:!pa.alertSettings.domain_check_enabled},null,8,["modelValue","disabled"]),a[52]||(a[52]=l("span",{class:"form-help"},"连续失败次数超过此值时触发告警",-1))]),_:1,__:[52]}),i(Ca,{"content-position":"left"},{default:n(()=>a[53]||(a[53]=[c("性能监控设置",-1)])),_:1,__:[53]}),i(Ma,{label:"响应时间阈值（毫秒）"},{default:n(()=>[i(Ta,{modelValue:pa.alertSettings.response_time_threshold,"onUpdate:modelValue":a[5]||(a[5]=e=>pa.alertSettings.response_time_threshold=e),min:1e3,max:1e4,step:100},null,8,["modelValue"])]),_:1}),i(Ma,{label:"成功率阈值（%）"},{default:n(()=>[i(Ta,{modelValue:pa.alertSettings.success_rate_threshold,"onUpdate:modelValue":a[6]||(a[6]=e=>pa.alertSettings.success_rate_threshold=e),min:50,max:100,step:1},null,8,["modelValue"])]),_:1}),i(Ca,{"content-position":"left"},{default:n(()=>a[54]||(a[54]=[c("通知设置",-1)])),_:1,__:[54]}),i(Ma,{label:"启用通知"},{default:n(()=>[i(xa,{modelValue:pa.alertSettings.notification_enabled,"onUpdate:modelValue":a[7]||(a[7]=e=>pa.alertSettings.notification_enabled=e)},null,8,["modelValue"])]),_:1}),i(Ma,{label:"邮件通知"},{default:n(()=>[i(Aa,{modelValue:pa.alertSettings.notification_email,"onUpdate:modelValue":a[8]||(a[8]=e=>pa.alertSettings.notification_email=e),placeholder:"请输入邮箱地址",disabled:!pa.alertSettings.notification_enabled},null,8,["modelValue","disabled"])]),_:1}),i(Ma,{label:"Webhook通知"},{default:n(()=>[i(Aa,{modelValue:pa.alertSettings.notification_webhook,"onUpdate:modelValue":a[9]||(a[9]=e=>pa.alertSettings.notification_webhook=e),placeholder:"请输入Webhook地址",disabled:!pa.alertSettings.notification_enabled},null,8,["modelValue","disabled"])]),_:1}),i(Ca,{"content-position":"left"},{default:n(()=>a[55]||(a[55]=[c("自动切换设置",-1)])),_:1,__:[55]}),i(Ma,{label:"启用自动切换"},{default:n(()=>[i(xa,{modelValue:pa.alertSettings.auto_switch_enabled,"onUpdate:modelValue":a[10]||(a[10]=e=>pa.alertSettings.auto_switch_enabled=e)},null,8,["modelValue"])]),_:1}),i(Ma,{label:"自动切换阈值"},{default:n(()=>[i(Ta,{modelValue:pa.alertSettings.auto_switch_threshold,"onUpdate:modelValue":a[11]||(a[11]=e=>pa.alertSettings.auto_switch_threshold=e),min:1,max:10,disabled:!pa.alertSettings.auto_switch_enabled},null,8,["modelValue","disabled"]),a[56]||(a[56]=l("span",{class:"form-help"},"域名异常次数超过此值时自动切换",-1))]),_:1,__:[56]})]),_:1},8,["model"])]),l("div",ua,[i(ga,{onClick:a[12]||(a[12]=e=>pa.alertSettingsVisible=!1)},{default:n(()=>a[57]||(a[57]=[c("取消",-1)])),_:1,__:[57]}),i(ga,{type:"primary",onClick:_a.saveAlertSettings},{default:n(()=>a[58]||(a[58]=[c("保存设置",-1)])),_:1,__:[58]},8,["onClick"])])]),_:1},8,["visible"])])}],["__scopeId","data-v-1573b57b"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/anti-block/Analytics.vue"]]);export{pa as default};
