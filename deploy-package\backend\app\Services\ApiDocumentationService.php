<?php

namespace App\Services;

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\File;
use ReflectionClass;
use ReflectionMethod;

/**
 * API文档生成服务
 * 自动生成和维护API文档
 */
class ApiDocumentationService
{
    private array $apiRoutes = [];
    private array $controllers = [];
    private string $docsPath;

    public function __construct()
    {
        $this->docsPath = storage_path('api-docs');
        if (!File::exists($this->docsPath)) {
            File::makeDirectory($this->docsPath, 0755, true);
        }
    }

    /**
     * 生成完整的API文档
     */
    public function generateDocumentation(): array
    {
        $this->collectApiRoutes();
        $this->analyzeControllers();
        
        $documentation = [
            'info' => $this->getApiInfo(),
            'servers' => $this->getServers(),
            'paths' => $this->generatePaths(),
            'components' => $this->generateComponents(),
            'security' => $this->getSecuritySchemes(),
            'tags' => $this->getTags(),
        ];

        // 保存文档到文件
        $this->saveDocumentation($documentation);

        return $documentation;
    }

    /**
     * 收集API路由信息
     */
    private function collectApiRoutes(): void
    {
        $routes = Route::getRoutes();
        
        foreach ($routes as $route) {
            $uri = $route->uri();
            
            // 只处理API路由
            if (str_starts_with($uri, 'api/')) {
                $this->apiRoutes[] = [
                    'uri' => $uri,
                    'methods' => $route->methods(),
                    'name' => $route->getName(),
                    'action' => $route->getAction(),
                    'middleware' => $route->middleware(),
                    'parameters' => $this->extractRouteParameters($uri),
                ];
            }
        }
    }

    /**
     * 分析控制器方法
     */
    private function analyzeControllers(): void
    {
        $controllerPath = app_path('Http/Controllers/Api');
        $files = File::allFiles($controllerPath);

        foreach ($files as $file) {
            $className = $this->getClassNameFromFile($file);
            if ($className && class_exists($className)) {
                $this->controllers[$className] = $this->analyzeController($className);
            }
        }
    }

    /**
     * 分析单个控制器
     */
    private function analyzeController(string $className): array
    {
        $reflection = new ReflectionClass($className);
        $methods = [];

        foreach ($reflection->getMethods(ReflectionMethod::IS_PUBLIC) as $method) {
            if ($method->getDeclaringClass()->getName() === $className) {
                $methods[$method->getName()] = [
                    'name' => $method->getName(),
                    'parameters' => $this->getMethodParameters($method),
                    'return_type' => $method->getReturnType()?->getName(),
                    'doc_comment' => $method->getDocComment(),
                    'annotations' => $this->parseAnnotations($method->getDocComment()),
                ];
            }
        }

        return [
            'class' => $className,
            'methods' => $methods,
            'doc_comment' => $reflection->getDocComment(),
        ];
    }

    /**
     * 生成API路径文档
     */
    private function generatePaths(): array
    {
        $paths = [];

        foreach ($this->apiRoutes as $route) {
            $path = '/' . $route['uri'];
            
            foreach ($route['methods'] as $method) {
                $method = strtolower($method);
                if ($method === 'head') continue;

                $paths[$path][$method] = $this->generatePathOperation($route, $method);
            }
        }

        return $paths;
    }

    /**
     * 生成单个路径操作文档
     */
    private function generatePathOperation(array $route, string $method): array
    {
        $operation = [
            'summary' => $this->generateSummary($route, $method),
            'description' => $this->generateDescription($route, $method),
            'tags' => $this->getOperationTags($route),
            'parameters' => $this->generateParameters($route),
            'responses' => $this->generateResponses($route, $method),
        ];

        // 添加请求体（对于POST、PUT、PATCH方法）
        if (in_array($method, ['post', 'put', 'patch'])) {
            $operation['requestBody'] = $this->generateRequestBody($route, $method);
        }

        // 添加安全要求
        if ($this->requiresAuthentication($route)) {
            $operation['security'] = [['jwt' => []]];
        }

        return $operation;
    }

    /**
     * 生成组件定义
     */
    private function generateComponents(): array
    {
        return [
            'schemas' => $this->generateSchemas(),
            'responses' => $this->generateResponseComponents(),
            'parameters' => $this->generateParameterComponents(),
            'requestBodies' => $this->generateRequestBodyComponents(),
        ];
    }

    /**
     * 生成数据模型Schema
     */
    private function generateSchemas(): array
    {
        $schemas = [];

        // 用户模型
        $schemas['User'] = [
            'type' => 'object',
            'properties' => [
                'id' => ['type' => 'integer', 'example' => 1],
                'username' => ['type' => 'string', 'example' => 'admin'],
                'email' => ['type' => 'string', 'format' => 'email', 'example' => '<EMAIL>'],
                'phone' => ['type' => 'string', 'example' => '13800138000'],
                'nickname' => ['type' => 'string', 'example' => '管理员'],
                'avatar' => ['type' => 'string', 'example' => '/uploads/avatars/default.jpg'],
                'role' => ['type' => 'string', 'enum' => ['admin', 'substation', 'distributor', 'user']],
                'status' => ['type' => 'integer', 'enum' => [0, 1], 'description' => '0:禁用 1:启用'],
                'balance' => ['type' => 'number', 'format' => 'float', 'example' => 100.50],
                'total_earnings' => ['type' => 'number', 'format' => 'float', 'example' => 1000.00],
                'created_at' => ['type' => 'string', 'format' => 'date-time'],
                'updated_at' => ['type' => 'string', 'format' => 'date-time'],
            ],
            'required' => ['id', 'username', 'role', 'status']
        ];

        // 微信群组模型
        $schemas['WechatGroup'] = [
            'type' => 'object',
            'properties' => [
                'id' => ['type' => 'integer', 'example' => 1],
                'name' => ['type' => 'string', 'example' => '学习交流群'],
                'description' => ['type' => 'string', 'example' => '这是一个学习交流群'],
                'category' => ['type' => 'string', 'example' => '学习'],
                'price' => ['type' => 'number', 'format' => 'float', 'example' => 99.00],
                'qr_code' => ['type' => 'string', 'example' => '/uploads/qrcodes/group1.jpg'],
                'cover_image' => ['type' => 'string', 'example' => '/uploads/covers/group1.jpg'],
                'status' => ['type' => 'integer', 'enum' => [0, 1], 'description' => '0:禁用 1:启用'],
                'owner_id' => ['type' => 'integer', 'example' => 1],
                'current_members' => ['type' => 'integer', 'example' => 150],
                'max_members' => ['type' => 'integer', 'example' => 500],
                'created_at' => ['type' => 'string', 'format' => 'date-time'],
                'updated_at' => ['type' => 'string', 'format' => 'date-time'],
            ],
            'required' => ['id', 'name', 'price', 'status', 'owner_id']
        ];

        // 订单模型
        $schemas['Order'] = [
            'type' => 'object',
            'properties' => [
                'id' => ['type' => 'integer', 'example' => 1],
                'order_no' => ['type' => 'string', 'example' => 'ORD202501240001'],
                'user_id' => ['type' => 'integer', 'example' => 1],
                'group_id' => ['type' => 'integer', 'example' => 1],
                'amount' => ['type' => 'number', 'format' => 'float', 'example' => 99.00],
                'status' => ['type' => 'string', 'enum' => ['pending', 'paid', 'cancelled', 'refunded']],
                'payment_method' => ['type' => 'string', 'example' => 'wechat'],
                'payment_status' => ['type' => 'string', 'enum' => ['pending', 'success', 'failed']],
                'paid_at' => ['type' => 'string', 'format' => 'date-time', 'nullable' => true],
                'created_at' => ['type' => 'string', 'format' => 'date-time'],
                'updated_at' => ['type' => 'string', 'format' => 'date-time'],
            ],
            'required' => ['id', 'order_no', 'user_id', 'group_id', 'amount', 'status']
        ];

        // 通用响应模型
        $schemas['ApiResponse'] = [
            'type' => 'object',
            'properties' => [
                'success' => ['type' => 'boolean', 'example' => true],
                'message' => ['type' => 'string', 'example' => '操作成功'],
                'data' => ['type' => 'object', 'nullable' => true],
                'timestamp' => ['type' => 'string', 'format' => 'date-time'],
            ],
            'required' => ['success', 'message']
        ];

        // 分页响应模型
        $schemas['PaginatedResponse'] = [
            'type' => 'object',
            'properties' => [
                'success' => ['type' => 'boolean', 'example' => true],
                'message' => ['type' => 'string', 'example' => '获取成功'],
                'data' => [
                    'type' => 'object',
                    'properties' => [
                        'data' => ['type' => 'array', 'items' => ['type' => 'object']],
                        'current_page' => ['type' => 'integer', 'example' => 1],
                        'last_page' => ['type' => 'integer', 'example' => 10],
                        'per_page' => ['type' => 'integer', 'example' => 20],
                        'total' => ['type' => 'integer', 'example' => 200],
                        'from' => ['type' => 'integer', 'example' => 1],
                        'to' => ['type' => 'integer', 'example' => 20],
                    ]
                ]
            ]
        ];

        return $schemas;
    }

    /**
     * 获取API基本信息
     */
    private function getApiInfo(): array
    {
        return [
            'title' => '晨鑫流量变现系统 API',
            'version' => '2.0.0',
            'description' => '晨鑫流量变现系统 - 智能社群营销与多级分销平台 API 文档',
            'contact' => [
                'name' => '晨鑫流量变现系统 Team',
                'email' => '<EMAIL>',
            ],
            'license' => [
                'name' => 'MIT',
                'url' => 'https://opensource.org/licenses/MIT',
            ],
        ];
    }

    /**
     * 获取服务器信息
     */
    private function getServers(): array
    {
        return [
            [
                'url' => config('app.url'),
                'description' => 'Production Server',
            ],
            [
                'url' => 'http://localhost:8000',
                'description' => 'Development Server',
            ],
        ];
    }

    /**
     * 获取安全方案
     */
    private function getSecuritySchemes(): array
    {
        return [
            'jwt' => [
                'type' => 'http',
                'scheme' => 'bearer',
                'bearerFormat' => 'JWT',
                'description' => 'JWT Token authentication',
            ],
        ];
    }

    /**
     * 获取标签定义
     */
    private function getTags(): array
    {
        return [
            ['name' => 'Authentication', 'description' => '用户认证相关接口'],
            ['name' => 'Users', 'description' => '用户管理相关接口'],
            ['name' => 'Groups', 'description' => '微信群组管理相关接口'],
            ['name' => 'Orders', 'description' => '订单管理相关接口'],
            ['name' => 'Distribution', 'description' => '分销系统相关接口'],
            ['name' => 'Payment', 'description' => '支付系统相关接口'],
            ['name' => 'Admin', 'description' => '管理员功能相关接口'],
            ['name' => 'System', 'description' => '系统功能相关接口'],
        ];
    }

    /**
     * 保存文档到文件
     */
    private function saveDocumentation(array $documentation): void
    {
        // 保存为JSON格式
        File::put(
            $this->docsPath . '/api-docs.json',
            json_encode($documentation, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );

        // 保存为YAML格式
        $yaml = $this->arrayToYaml($documentation);
        File::put($this->docsPath . '/api-docs.yaml', $yaml);

        // 生成HTML文档
        $this->generateHtmlDocumentation($documentation);
    }

    /**
     * 生成HTML文档
     */
    private function generateHtmlDocumentation(array $documentation): void
    {
        $html = view('api-docs.template', compact('documentation'))->render();
        File::put($this->docsPath . '/api-docs.html', $html);
    }

    /**
     * 辅助方法
     */
    private function extractRouteParameters(string $uri): array
    {
        preg_match_all('/\{([^}]+)\}/', $uri, $matches);
        return $matches[1] ?? [];
    }

    private function getClassNameFromFile($file): ?string
    {
        $content = File::get($file->getPathname());
        
        if (preg_match('/namespace\s+([^;]+);/', $content, $namespaceMatch) &&
            preg_match('/class\s+(\w+)/', $content, $classMatch)) {
            return $namespaceMatch[1] . '\\' . $classMatch[1];
        }

        return null;
    }

    private function getMethodParameters(ReflectionMethod $method): array
    {
        $parameters = [];
        foreach ($method->getParameters() as $param) {
            $parameters[] = [
                'name' => $param->getName(),
                'type' => $param->getType()?->getName(),
                'optional' => $param->isOptional(),
                'default' => $param->isDefaultValueAvailable() ? $param->getDefaultValue() : null,
            ];
        }
        return $parameters;
    }

    private function parseAnnotations(?string $docComment): array
    {
        if (!$docComment) return [];

        $annotations = [];
        if (preg_match_all('/@(\w+)(?:\s+(.+))?/', $docComment, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $annotations[$match[1]] = $match[2] ?? true;
            }
        }

        return $annotations;
    }

    private function generateSummary(array $route, string $method): string
    {
        $action = $route['action']['uses'] ?? '';
        if (is_string($action) && str_contains($action, '@')) {
            [$controller, $methodName] = explode('@', $action);
            $controller = class_basename($controller);
            return "{$controller}::{$methodName}";
        }

        return ucfirst($method) . ' ' . $route['uri'];
    }

    private function generateDescription(array $route, string $method): string
    {
        return "API endpoint for {$method} {$route['uri']}";
    }

    private function getOperationTags(array $route): array
    {
        $uri = $route['uri'];
        
        if (str_contains($uri, 'auth')) return ['Authentication'];
        if (str_contains($uri, 'user')) return ['Users'];
        if (str_contains($uri, 'group')) return ['Groups'];
        if (str_contains($uri, 'order')) return ['Orders'];
        if (str_contains($uri, 'distribution')) return ['Distribution'];
        if (str_contains($uri, 'payment')) return ['Payment'];
        if (str_contains($uri, 'admin')) return ['Admin'];
        
        return ['System'];
    }

    private function generateParameters(array $route): array
    {
        $parameters = [];
        
        foreach ($route['parameters'] as $param) {
            $parameters[] = [
                'name' => $param,
                'in' => 'path',
                'required' => true,
                'schema' => ['type' => 'string'],
                'description' => "The {$param} parameter",
            ];
        }

        return $parameters;
    }

    private function generateResponses(array $route, string $method): array
    {
        return [
            '200' => [
                'description' => 'Success',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/ApiResponse']
                    ]
                ]
            ],
            '401' => [
                'description' => 'Unauthorized',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/ApiResponse']
                    ]
                ]
            ],
            '422' => [
                'description' => 'Validation Error',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/ApiResponse']
                    ]
                ]
            ],
            '500' => [
                'description' => 'Server Error',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/ApiResponse']
                    ]
                ]
            ],
        ];
    }

    private function generateRequestBody(array $route, string $method): array
    {
        return [
            'required' => true,
            'content' => [
                'application/json' => [
                    'schema' => [
                        'type' => 'object',
                        'properties' => [
                            'example' => ['type' => 'string', 'example' => 'value']
                        ]
                    ]
                ]
            ]
        ];
    }

    private function requiresAuthentication(array $route): bool
    {
        return in_array('auth:sanctum', $route['middleware']) || 
               in_array('jwt.auth', $route['middleware']);
    }

    private function generateResponseComponents(): array
    {
        return [];
    }

    private function generateParameterComponents(): array
    {
        return [];
    }

    private function generateRequestBodyComponents(): array
    {
        return [];
    }

    private function arrayToYaml(array $array, int $indent = 0): string
    {
        $yaml = '';
        $spaces = str_repeat('  ', $indent);

        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $yaml .= $spaces . $key . ":\n";
                $yaml .= $this->arrayToYaml($value, $indent + 1);
            } else {
                $yaml .= $spaces . $key . ': ' . $this->yamlValue($value) . "\n";
            }
        }

        return $yaml;
    }

    private function yamlValue($value): string
    {
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }
        if (is_null($value)) {
            return 'null';
        }
        if (is_string($value) && (str_contains($value, ':') || str_contains($value, ' '))) {
            return '"' . addslashes($value) . '"';
        }
        return (string) $value;
    }
}