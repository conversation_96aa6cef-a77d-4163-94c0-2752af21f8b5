// 侧边栏菜单配置 - 优化后的导航结构
export const sidebarMenuConfig = [
  // === 核心功能 ===
  {
    title: '核心功能',
    group: true
  },
  {
    path: '/admin/dashboard',
    title: '仪表板',
    icon: 'TrendCharts',
    name: 'Dashboard'
  },
  {
    path: '/admin/analytics',
    title: '数据分析',
    icon: 'DataLine',
    name: 'DashboardAnalytics'
  },

  // === 用户管理 ===
  {
    title: '用户管理',
    group: true
  },
  {
    path: '/admin/users',
    title: '用户管理',
    icon: 'User',
    name: 'UserManagement',
    children: [
      {
        path: '/admin/users/list',
        title: '用户列表',
        icon: 'List',
        name: 'UserList'
      },
      {
        path: '/admin/users/analytics',
        title: '用户分析',
        icon: 'TrendCharts',
        name: 'UserAnalytics'
      },
      {
        path: '/admin/users/profile',
        title: '个人资料',
        icon: 'Avatar',
        name: 'UserProfile'
      }
    ]
  },

  // === 社群功能 ===
  {
    title: '社群功能',
    group: true
  },
  {
    path: '/admin/community',
    title: '社群管理',
    icon: 'ChatDotRound',
    name: 'CommunityManagement',
    children: [
      {
        path: '/admin/community/groups',
        title: '群组管理',
        icon: 'ChatDotRound',
        name: 'GroupManagement'
      },
      {
        path: '/admin/community/templates',
        title: '模板管理',
        icon: 'Document',
        name: 'TemplateManagement'
      }
    ]
  },

  // === 业务管理 ===
  {
    title: '业务管理',
    group: true
  },
  {
    path: '/admin/links',
    title: '链接管理',
    icon: 'Link',
    name: 'LinkManagement'
  },
  {
    path: '/admin/agents',
    title: '代理商管理',
    icon: 'Avatar',
    name: 'AgentManagement',
    roles: ['admin'],
    children: [
      {
        path: '/admin/agents/list',
        title: '代理商列表',
        icon: 'List',
        name: 'AgentList',
        roles: ['admin']
      },
      {
        path: '/admin/agents/hierarchy',
        title: '层级结构',
        icon: 'Grid',
        name: 'AgentHierarchy',
        roles: ['admin']
      },
      {
        path: '/admin/agents/commission',
        title: '佣金管理',
        icon: 'Money',
        name: 'AgentCommission',
        roles: ['admin']
      },
      {
        path: '/admin/agents/performance',
        title: '业绩统计',
        icon: 'TrendCharts',
        name: 'AgentPerformance',
        roles: ['admin']
      }
    ]
  },
  {
    path: '/admin/orders',
    title: '订单管理',
    icon: 'ShoppingCart',
    name: 'OrderManagement'
  },

  // === 财务管理 ===
  {
    title: '财务管理',
    group: true
  },
  {
    path: '/admin/finance',
    title: '财务管理',
    icon: 'Money',
    name: 'FinanceManagement',
    roles: ['admin'],
    children: [
      {
        path: '/admin/finance/dashboard',
        title: '财务概览',
        icon: 'DataBoard',
        name: 'FinanceDashboard',
        roles: ['admin']
      },
      {
        path: '/admin/finance/transactions',
        title: '交易记录',
        icon: 'CreditCard',
        name: 'TransactionList',
        roles: ['admin']
      },
      {
        path: '/admin/finance/commission',
        title: '佣金日志',
        icon: 'Coin',
        name: 'CommissionLog',
        roles: ['admin']
      },
      {
        path: '/admin/finance/withdraw',
        title: '提现管理',
        icon: 'Upload',
        name: 'WithdrawManage',
        roles: ['admin']
      }
    ]
  },

  // === 支付系统 ===
  {
    title: '支付系统',
    group: true
  },
  {
    path: '/admin/payment',
    title: '支付管理',
    icon: 'CreditCard',
    name: 'PaymentManagement',
    roles: ['admin'],
    children: [
      {
        path: '/admin/payment/settings',
        title: '支付设置',
        icon: 'Setting',
        name: 'PaymentSettings',
        roles: ['admin']
      },
      {
        path: '/admin/payment/channels',
        title: '支付渠道',
        icon: 'Connection',
        name: 'PaymentChannelManagement',
        roles: ['admin']
      },
      {
        path: '/admin/payment/orders',
        title: '支付订单',
        icon: 'Tickets',
        name: 'PaymentOrders',
        roles: ['admin']
      },
      {
        path: '/admin/payment/logs',
        title: '支付日志',
        icon: 'Document',
        name: 'PaymentLogs',
        roles: ['admin']
      }
    ]
  },

  // === 推广营销 ===
  {
    title: '推广营销',
    group: true
  },
  {
    path: '/admin/promotion',
    title: '分销推广',
    icon: 'Share',
    name: 'PromotionManagement',
    roles: ['admin'],
    children: [
      {
        path: '/admin/promotion/distributors',
        title: '分销商管理',
        icon: 'User',
        name: 'DistributorManagement',
        roles: ['admin']
      },
      {
        path: '/admin/promotion/links',
        title: '推广链接',
        icon: 'Link',
        name: 'PromotionLinks',
        roles: ['admin']
      }
    ]
  },

  // === 安全防护 ===
  {
    title: '安全防护',
    group: true
  },
  {
    path: '/admin/anti-block',
    title: '防红系统',
    icon: 'Lock',
    name: 'AntiBlockSystem',
    roles: ['admin'],
    children: [
      {
        path: '/admin/anti-block/dashboard',
        title: '防红概览',
        icon: 'DataBoard',
        name: 'AntiBlockDashboard',
        roles: ['admin']
      },
      {
        path: '/admin/anti-block/domains',
        title: '域名管理',
        icon: 'Connection',
        name: 'DomainManagement',
        roles: ['admin']
      },
      {
        path: '/admin/anti-block/links',
        title: '短链管理',
        icon: 'Link',
        name: 'ShortLinkManagement',
        roles: ['admin']
      },
      {
        path: '/admin/anti-block/analytics',
        title: '防红分析',
        icon: 'TrendCharts',
        name: 'AntiBlockAnalytics',
        roles: ['admin']
      },
      {
        path: '/admin/anti-block/enhanced',
        title: '增强防护',
        icon: 'Star',
        name: 'AntiBlockEnhanced',
        roles: ['admin'],
        isNew: true
      }
    ]
  },

  // === 系统管理 ===
  {
    title: '系统管理',
    group: true
  },
  {
    path: '/admin/permissions',
    title: '权限管理',
    icon: 'Lock',
    name: 'PermissionSystem',
    roles: ['admin'],
    children: [
      {
        path: '/admin/permissions/roles',
        title: '角色管理',
        icon: 'UserFilled',
        name: 'RoleManagement',
        roles: ['admin']
      },
      {
        path: '/admin/permissions/permissions',
        title: '权限配置',
        icon: 'Key',
        name: 'PermissionManagement',
        roles: ['admin']
      }
    ]
  },
  {
    path: '/admin/system',
    title: '系统管理',
    icon: 'Setting',
    name: 'SystemManagement',
    roles: ['admin'],
    children: [
      {
        path: '/admin/system/settings',
        title: '系统设置',
        icon: 'Setting',
        name: 'SystemSettings',
        roles: ['admin']
      },
      {
        path: '/admin/system/monitor',
        title: '系统监控',
        icon: 'Monitor',
        name: 'SystemMonitor',
        roles: ['admin'],
        isHot: true
      },
      {
        path: '/admin/system/logs',
        title: '操作日志',
        icon: 'Document',
        name: 'OperationLogs',
        roles: ['admin']
      },
      {
        path: '/admin/system/notifications',
        title: '通知管理',
        icon: 'Bell',
        name: 'NotificationManagement',
        roles: ['admin']
      },
      {
        path: '/admin/system/data-export',
        title: '数据导出',
        icon: 'Download',
        name: 'DataExport',
        roles: ['admin']
      },
      {
        path: '/admin/system/file-management',
        title: '文件管理',
        icon: 'Folder',
        name: 'FileManagement',
        roles: ['admin']
      },
      {
        path: '/admin/system/function-test',
        title: '功能测试',
        icon: 'Tools',
        name: 'FunctionTest',
        roles: ['admin']
      },
      {
        path: '/admin/system/user-guide',
        title: '使用指南',
        icon: 'QuestionFilled',
        name: 'UserGuide'
      }
    ]
  }
]

// 权限过滤函数
export const filterMenuByRole = (menu, userRoles = []) => {
  return menu.filter(item => {
    // 如果是分组标题，直接返回
    if (item.group) return true
    
    // 检查当前项权限
    if (item.roles && item.roles.length > 0) {
      const hasPermission = item.roles.some(role => userRoles.includes(role))
      if (!hasPermission) return false
    }
    
    // 递归过滤子菜单
    if (item.children) {
      item.children = filterMenuByRole(item.children, userRoles)
      // 如果子菜单全部被过滤掉，则隐藏父菜单
      return item.children.length > 0
    }
    
    return true
  })
}

// 扁平化菜单（用于搜索等功能）
export const flattenMenu = (menu, result = []) => {
  menu.forEach(item => {
    if (!item.group) {
      result.push(item)
      if (item.children) {
        flattenMenu(item.children, result)
      }
    }
  })
  return result
}

// 查找菜单项
export const findMenuItem = (menu, path) => {
  for (const item of menu) {
    if (item.path === path) {
      return item
    }
    if (item.children) {
      const found = findMenuItem(item.children, path)
      if (found) return found
    }
  }
  return null
}

// 获取面包屑导航
export const getBreadcrumb = (menu, path) => {
  const breadcrumb = []
  
  const findPath = (menu, targetPath, currentPath = []) => {
    for (const item of menu) {
      if (item.group) continue
      
      const newPath = [...currentPath, item]
      
      if (item.path === targetPath) {
        breadcrumb.push(...newPath)
        return true
      }
      
      if (item.children && findPath(item.children, targetPath, newPath)) {
        return true
      }
    }
    return false
  }
  
  findPath(menu, path)
  return breadcrumb
}

export default sidebarMenuConfig