/* empty css             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                  *//* empty css               *//* empty css                    *//* empty css                */import{l as e,G as a,A as s,M as t,r as l,o as n,I as r,m as i,q as o,E as u,F as c,Y as d,z as g,C as p}from"./vue-vendor-BcnDv-68.js";import{H as m}from"./echarts-D6CUuNS9.js";import{c as v,g as f}from"./system-Co5QGav0.js";import{_ as y}from"./index-eUTsTR3J.js";import{U as _,V as h,ag as C,X as k,Y as x,s as S,Z as w,bg as b,a1 as j,ak as z,an as L,a5 as P,a6 as M}from"./element-plus-C2UshkXo.js";import"./utils-SdQ7DxjY.js";const T={class:"app-container"},A={class:"status-item"},U={class:"status-content"},B={class:"status-item"},V={class:"status-content"},E={class:"status-value"},$={class:"status-item"},I={class:"status-content"},O={class:"status-value"},R={class:"status-item"},F={class:"status-content"},Q={class:"status-value"},D={class:"card-header"},q={class:"card-header"},Y={class:"service-item"},G={class:"service-header"},J={class:"service-name"},N={class:"service-info"},X={class:"info-item"},Z={class:"info-value"},H={class:"info-item"},K={class:"info-value"},W={class:"info-item"},ee={class:"info-value"},ae={class:"service-actions"},se={class:"card-header"},te={class:"log-container"},le={class:"log-time"},ne={class:"log-level"},re={class:"log-message"},ie={class:"log-pagination"},oe={class:"tool-item"},ue={class:"tool-item"},ce={class:"tool-item"};const de=y({__name:"Monitor",setup(e,{expose:a}){a();const s=t({server_status:!0,cpu_usage:35,memory_usage:68,disk_usage:42}),i=l([{name:"Laravel应用",status:"running",cpu:15,memory:256,uptime:"2天3小时"},{name:"MySQL数据库",status:"running",cpu:8,memory:512,uptime:"5天12小时"},{name:"Redis缓存",status:"running",cpu:3,memory:128,uptime:"5天12小时"},{name:"Nginx服务",status:"running",cpu:2,memory:64,uptime:"5天12小时"},{name:"队列处理器",status:"running",cpu:12,memory:192,uptime:"1天6小时"},{name:"定时任务",status:"running",cpu:1,memory:32,uptime:"5天12小时"}]),o=l([]),u=l(""),c=l(1),d=l(20),g=l(0),p=l(!1),y=l(!1),_=l(!1),h=l({}),x=l({});let S=null;const w=()=>{h.value={tooltip:{trigger:"axis"},legend:{data:["CPU使用率","内存使用率"]},xAxis:{type:"category",data:["00:00","04:00","08:00","12:00","16:00","20:00","24:00"]},yAxis:{type:"value",max:100},series:[{name:"CPU使用率",type:"line",data:[20,25,30,45,35,28,32]},{name:"内存使用率",type:"line",data:[50,55,60,75,68,62,65]}]},x.value={tooltip:{trigger:"axis"},legend:{data:["入站流量","出站流量"]},xAxis:{type:"category",data:["00:00","04:00","08:00","12:00","16:00","20:00","24:00"]},yAxis:{type:"value"},series:[{name:"入站流量",type:"bar",data:[120,200,150,800,700,550,400]},{name:"出站流量",type:"bar",data:[80,150,100,600,500,400,300]}]}},b=()=>{o.value=[{id:1,level:"info",message:"系统启动完成",created_at:"2024-01-01 12:00:00"},{id:2,level:"warning",message:"CPU使用率较高",created_at:"2024-01-01 12:05:00"},{id:3,level:"error",message:"数据库连接失败",created_at:"2024-01-01 12:10:00"}],g.value=o.value.length},j=()=>{S=setInterval(()=>{s.cpu_usage=Math.floor(100*Math.random()),s.memory_usage=Math.floor(100*Math.random()),s.disk_usage=Math.floor(100*Math.random())},5e3)};n(()=>{w(),b(),j()}),r(()=>{S&&clearInterval(S)});const z={systemStatus:s,services:i,logs:o,logLevel:u,logPage:c,logPageSize:d,logTotal:g,clearing:p,restarting:y,generating:_,performanceChartOptions:h,networkChartOptions:x,get refreshTimer(){return S},set refreshTimer(e){S=e},getCpuColor:e=>e<50?"#67C23A":e<80?"#E6A23C":"#F56C6C",getMemoryColor:e=>e<60?"#67C23A":e<85?"#E6A23C":"#F56C6C",getDiskColor:e=>e<70?"#67C23A":e<90?"#E6A23C":"#F56C6C",getLogLevelType:e=>({error:"danger",warning:"warning",info:"info",debug:"info"}[e]||"info"),refreshCharts:w,refreshServices:async()=>{try{k.success("服务状态已刷新")}catch(e){k.error("刷新服务状态失败")}},startService:e=>{C.confirm(`确定要启动 ${e.name} 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{e.status="running",k.success(`${e.name} 已启动`)})},restartService:e=>{C.confirm(`确定要重启 ${e.name} 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{k.success(`${e.name} 已重启`)})},stopService:e=>{C.confirm(`确定要停止 ${e.name} 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{e.status="stopped",k.success(`${e.name} 已停止`)})},refreshLogs:b,clearLogs:()=>{C.confirm("确定要清空所有日志吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{o.value=[],g.value=0,k.success("日志已清空")})},handleLogSizeChange:e=>{d.value=e,b()},handleLogCurrentChange:e=>{c.value=e,b()},clearSystemCache:async()=>{p.value=!0;try{await v(),k.success("缓存清理成功")}catch(e){k.error("缓存清理失败")}finally{p.value=!1}},restartQueue:async()=>{y.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),k.success("队列重启成功")}catch(e){k.error("队列重启失败")}finally{y.value=!1}},generateReport:async()=>{_.value=!0;try{await new Promise(e=>setTimeout(e,2e3)),k.success("报表生成成功")}catch(e){k.error("报表生成失败")}finally{_.value=!1}},startAutoRefresh:j,ref:l,reactive:t,onMounted:n,onUnmounted:r,get ElMessage(){return k},get ElMessageBox(){return C},get VChart(){return m},get getSystemInfo(){return f},get clearCache(){return v}};return Object.defineProperty(z,"__isScriptSetup",{enumerable:!1,value:!0}),z}},[["render",function(t,l,n,r,m,v){const f=h,y=x,C=b,k=_,de=j,ge=z,pe=M,me=P,ve=L;return i(),e("div",T,[a(k,{gutter:20},{default:s(()=>[a(y,{span:6},{default:s(()=>[a(f,{class:"status-card"},{default:s(()=>[o("div",A,[l[4]||(l[4]=o("div",{class:"status-icon server-icon"},[o("i",{class:"el-icon-monitor"})],-1)),o("div",U,[l[3]||(l[3]=o("div",{class:"status-title"},"服务器状态",-1)),o("div",{class:S(["status-value",r.systemStatus.server_status?"text-success":"text-danger"])},w(r.systemStatus.server_status?"运行正常":"异常"),3)])])]),_:1})]),_:1}),a(y,{span:6},{default:s(()=>[a(f,{class:"status-card"},{default:s(()=>[o("div",B,[l[6]||(l[6]=o("div",{class:"status-icon cpu-icon"},[o("i",{class:"el-icon-cpu"})],-1)),o("div",V,[l[5]||(l[5]=o("div",{class:"status-title"},"CPU使用率",-1)),o("div",E,w(r.systemStatus.cpu_usage)+"%",1),a(C,{percentage:r.systemStatus.cpu_usage,color:r.getCpuColor(r.systemStatus.cpu_usage)},null,8,["percentage","color"])])])]),_:1})]),_:1}),a(y,{span:6},{default:s(()=>[a(f,{class:"status-card"},{default:s(()=>[o("div",$,[l[8]||(l[8]=o("div",{class:"status-icon memory-icon"},[o("i",{class:"el-icon-pie-chart"})],-1)),o("div",I,[l[7]||(l[7]=o("div",{class:"status-title"},"内存使用",-1)),o("div",O,w(r.systemStatus.memory_usage)+"%",1),a(C,{percentage:r.systemStatus.memory_usage,color:r.getMemoryColor(r.systemStatus.memory_usage)},null,8,["percentage","color"])])])]),_:1})]),_:1}),a(y,{span:6},{default:s(()=>[a(f,{class:"status-card"},{default:s(()=>[o("div",R,[l[10]||(l[10]=o("div",{class:"status-icon disk-icon"},[o("i",{class:"el-icon-folder"})],-1)),o("div",F,[l[9]||(l[9]=o("div",{class:"status-title"},"磁盘空间",-1)),o("div",Q,w(r.systemStatus.disk_usage)+"%",1),a(C,{percentage:r.systemStatus.disk_usage,color:r.getDiskColor(r.systemStatus.disk_usage)},null,8,["percentage","color"])])])]),_:1})]),_:1})]),_:1}),a(k,{gutter:20,style:{"margin-top":"20px"}},{default:s(()=>[a(y,{span:12},{default:s(()=>[a(f,null,{header:s(()=>[o("div",D,[l[12]||(l[12]=o("span",null,"📊 CPU & 内存监控",-1)),a(de,{type:"text",onClick:r.refreshCharts},{default:s(()=>l[11]||(l[11]=[u("刷新",-1)])),_:1,__:[11]})])]),default:s(()=>[a(r.VChart,{class:"chart",option:r.performanceChartOptions,autoresize:""},null,8,["option"])]),_:1})]),_:1}),a(y,{span:12},{default:s(()=>[a(f,null,{header:s(()=>l[13]||(l[13]=[o("div",{class:"card-header"},[o("span",null,"🌐 网络流量监控")],-1)])),default:s(()=>[a(r.VChart,{class:"chart",option:r.networkChartOptions,autoresize:""},null,8,["option"])]),_:1})]),_:1})]),_:1}),a(f,{style:{"margin-top":"20px"}},{header:s(()=>[o("div",q,[l[15]||(l[15]=o("span",null,"⚙️ 服务状态监控",-1)),a(de,{type:"primary",onClick:r.refreshServices},{default:s(()=>l[14]||(l[14]=[u("刷新状态",-1)])),_:1,__:[14]})])]),default:s(()=>[a(k,{gutter:20},{default:s(()=>[(i(!0),e(c,null,d(r.services,e=>(i(),g(y,{span:8,key:e.name},{default:s(()=>[o("div",Y,[o("div",G,[o("div",J,w(e.name),1),a(ge,{type:"running"===e.status?"success":"danger"},{default:s(()=>[u(w("running"===e.status?"运行中":"已停止"),1)]),_:2},1032,["type"])]),o("div",N,[o("div",X,[l[16]||(l[16]=o("span",{class:"info-label"},"CPU:",-1)),o("span",Z,w(e.cpu)+"%",1)]),o("div",H,[l[17]||(l[17]=o("span",{class:"info-label"},"内存:",-1)),o("span",K,w(e.memory)+"MB",1)]),o("div",W,[l[18]||(l[18]=o("span",{class:"info-label"},"运行时间:",-1)),o("span",ee,w(e.uptime),1)])]),o("div",ae,["running"!==e.status?(i(),g(de,{key:0,type:"success",size:"small",onClick:a=>r.startService(e)},{default:s(()=>[...l[19]||(l[19]=[u(" 启动 ",-1)])]),_:2,__:[19]},1032,["onClick"])):p("",!0),"running"===e.status?(i(),g(de,{key:1,type:"warning",size:"small",onClick:a=>r.restartService(e)},{default:s(()=>[...l[20]||(l[20]=[u(" 重启 ",-1)])]),_:2,__:[20]},1032,["onClick"])):p("",!0),"running"===e.status?(i(),g(de,{key:2,type:"danger",size:"small",onClick:a=>r.stopService(e)},{default:s(()=>[...l[21]||(l[21]=[u(" 停止 ",-1)])]),_:2,__:[21]},1032,["onClick"])):p("",!0)])])]),_:2},1024))),128))]),_:1})]),_:1}),a(f,{style:{"margin-top":"20px"}},{header:s(()=>[o("div",se,[l[24]||(l[24]=o("span",null,"📋 系统日志",-1)),o("div",null,[a(me,{modelValue:r.logLevel,"onUpdate:modelValue":l[0]||(l[0]=e=>r.logLevel=e),placeholder:"选择日志级别",style:{width:"120px","margin-right":"10px"}},{default:s(()=>[a(pe,{label:"全部",value:""}),a(pe,{label:"错误",value:"error"}),a(pe,{label:"警告",value:"warning"}),a(pe,{label:"信息",value:"info"}),a(pe,{label:"调试",value:"debug"})]),_:1},8,["modelValue"]),a(de,{type:"primary",onClick:r.refreshLogs},{default:s(()=>l[22]||(l[22]=[u("刷新日志",-1)])),_:1,__:[22]}),a(de,{type:"warning",onClick:r.clearLogs},{default:s(()=>l[23]||(l[23]=[u("清空日志",-1)])),_:1,__:[23]})])])]),default:s(()=>[o("div",te,[(i(!0),e(c,null,d(r.logs,t=>(i(),e("div",{class:S(["log-item","log-"+t.level]),key:t.id},[o("div",le,w(t.created_at),1),o("div",ne,[a(ge,{type:r.getLogLevelType(t.level),size:"small"},{default:s(()=>[u(w(t.level.toUpperCase()),1)]),_:2},1032,["type"])]),o("div",re,w(t.message),1)],2))),128))]),o("div",ie,[a(ve,{"current-page":r.logPage,"onUpdate:currentPage":l[1]||(l[1]=e=>r.logPage=e),"page-size":r.logPageSize,"onUpdate:pageSize":l[2]||(l[2]=e=>r.logPageSize=e),"page-sizes":[20,50,100,200],small:!1,disabled:!1,background:!1,layout:"total, sizes, prev, pager, next, jumper",total:r.logTotal,onSizeChange:r.handleLogSizeChange,onCurrentChange:r.handleLogCurrentChange},null,8,["current-page","page-size","total"])])]),_:1}),a(f,{style:{"margin-top":"20px"}},{header:s(()=>l[25]||(l[25]=[o("div",{class:"card-header"},[o("span",null,"🔧 系统工具")],-1)])),default:s(()=>[a(k,{gutter:20},{default:s(()=>[a(y,{span:8},{default:s(()=>[o("div",oe,[l[27]||(l[27]=o("h4",null,"🗑️ 清理缓存",-1)),l[28]||(l[28]=o("p",null,"清理系统缓存文件，释放存储空间",-1)),a(de,{type:"warning",onClick:r.clearCache,loading:r.clearing},{default:s(()=>l[26]||(l[26]=[u("清理缓存",-1)])),_:1,__:[26]},8,["onClick","loading"])])]),_:1}),a(y,{span:8},{default:s(()=>[o("div",ue,[l[30]||(l[30]=o("h4",null,"🔄 重启队列",-1)),l[31]||(l[31]=o("p",null,"重启后台任务队列处理器",-1)),a(de,{type:"info",onClick:r.restartQueue,loading:r.restarting},{default:s(()=>l[29]||(l[29]=[u("重启队列",-1)])),_:1,__:[29]},8,["loading"])])]),_:1}),a(y,{span:8},{default:s(()=>[o("div",ce,[l[33]||(l[33]=o("h4",null,"📊 生成报表",-1)),l[34]||(l[34]=o("p",null,"生成系统运行状况报表",-1)),a(de,{type:"success",onClick:r.generateReport,loading:r.generating},{default:s(()=>l[32]||(l[32]=[u("生成报表",-1)])),_:1,__:[32]},8,["loading"])])]),_:1})]),_:1})]),_:1})])}],["__scopeId","data-v-01e972d5"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/Monitor.vue"]]);export{de as default};
