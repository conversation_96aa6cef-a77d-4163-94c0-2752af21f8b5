const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/permission-CLkZa8df.js","assets/index-eUTsTR3J.js","assets/vue-vendor-BcnDv-68.js","assets/element-plus-C2UshkXo.js","assets/utils-SdQ7DxjY.js","assets/echarts-D6CUuNS9.js","assets/index-XVEiIDg_.css","assets/navigation-DbqezFjv.js"])))=>i.map(i=>d[i]);
import{r as e,_ as a,u as s,g as r,a as t}from"./index-eUTsTR3J.js";import{l as o,ai as i,q as n,K as l,C as d,B as c,a6 as u,W as v,ak as g,a2 as m,ag as p,r as h,M as w,c as f,o as y,n as b,I as x,m as k}from"./vue-vendor-BcnDv-68.js";import{ag as S,Z as E,s as _,bz as I,b1 as L,bb as C,aO as T,bA as A,bB as M,bC as D,bD as R,ac as P,b3 as F,a9 as B,X as O}from"./element-plus-C2UshkXo.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const U=9e5;const j=new class{constructor(){this.lastActivity=Date.now(),this.sessionTimer=null,this.idleTimer=null,this.isActive=!0,this.initSessionMonitoring()}initSessionMonitoring(){["mousedown","mousemove","keypress","scroll","touchstart"].forEach(e=>{document.addEventListener(e,this.updateActivity.bind(this),!0)}),this.startSessionCheck(),this.startIdleCheck()}updateActivity(){this.lastActivity=Date.now(),this.isActive=!0}startSessionCheck(){this.sessionTimer=setInterval(()=>{const e=localStorage.getItem("admin_token");if(e)try{1e3*JSON.parse(atob(e.split(".")[1])).exp<Date.now()&&this.handleSessionExpired()}catch(a){this.handleSessionExpired()}else this.handleSessionExpired()},6e4)}startIdleCheck(){this.idleTimer=setInterval(()=>{Date.now()-this.lastActivity>U&&this.isActive&&this.handleIdleTimeout()},3e4)}handleSessionExpired(){this.cleanup(),S.alert("您的登录会话已过期，请重新登录","会话过期",{confirmButtonText:"重新登录",type:"warning",showClose:!1,closeOnClickModal:!1,closeOnPressEscape:!1}).then(()=>{this.logout()})}handleIdleTimeout(){this.isActive=!1,S.confirm("检测到您已长时间未操作，是否继续保持登录？","空闲提醒",{confirmButtonText:"继续使用",cancelButtonText:"退出登录",type:"warning"}).then(()=>{this.updateActivity()}).catch(()=>{this.logout()})}logout(){localStorage.removeItem("admin_token"),localStorage.removeItem("admin_user"),localStorage.removeItem("admin_permissions"),localStorage.removeItem("admin_roles"),e.push("/login")}getCurrentSession(){const e=localStorage.getItem("admin_token");if(!e)return null;try{const a=JSON.parse(atob(e.split(".")[1]));return{id:a.jti||"session_"+Date.now(),userId:a.sub,username:a.username,exp:a.exp,iat:a.iat,isValid:1e3*a.exp>Date.now()}}catch(a){return null}}cleanup(){this.sessionTimer&&(clearInterval(this.sessionTimer),this.sessionTimer=null),this.idleTimer&&(clearInterval(this.idleTimer),this.idleTimer=null)}destroy(){this.cleanup();["mousedown","mousemove","keypress","scroll","touchstart"].forEach(e=>{document.removeEventListener(e,this.updateActivity.bind(this),!0)})}};const z=new class{constructor(){this.logs=[],this.maxLogs=1e3}addLog(e,a){const s={id:Date.now()+Math.random(),type:e,...a,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent};this.logs.unshift(s),this.logs.length>this.maxLogs&&(this.logs=this.logs.slice(0,this.maxLogs))}getLogs(e=null,a=100){let s=this.logs;return e&&(s=this.logs.filter(a=>a.type===e)),s.slice(0,a)}clearLogs(){this.logs=[]}sendToServer(e){}},V=(e,a,s,r={})=>{(new Date).toISOString(),navigator.userAgent},q=(e,a,s={})=>{(new Date).toISOString(),navigator.userAgent},$={__name:"Login",setup(e,{expose:a}){a();const o=p(),i=s(),n=h(),l=h(),d=h(),c=h(!1),u=h(!1),v=h(!1),g=h(!1),m=h(!1),k=h(!0),S=()=>{const e=window.innerHeight,a=window.innerWidth;m.value=e<800||a<480},E=()=>{S()},_=w({username:"",password:""}),U=w({username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度在 3 到 50 个字符",trigger:"blur"},{validator:(e,a,s)=>{if(!a)return void s();const r=a.trim();if(r.includes("@")){if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r))return void s(new Error("邮箱格式不正确"))}else{if(!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(r))return void s(new Error("用户名只能包含字母、数字、下划线或中文"))}s()},trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:128,message:"密码长度在 6 到 128 个字符",trigger:"blur"},{validator:(e,a,s)=>{if(!a)return void s();const r=/[a-zA-Z]/.test(a),t=/[0-9]/.test(a);if(!r||!t)return void s(new Error("密码必须包含字母和数字"));/[<>'"&]/.test(a)?s(new Error("密码包含不安全字符")):s()},trigger:"blur"}]}),$=f(()=>u.value?"text":"password"),Z=async e=>{try{const{getUserDefaultRoute:a}=await t(async()=>{const{getUserDefaultRoute:e}=await import("./navigation-DbqezFjv.js");return{getUserDefaultRoute:e}},[]),s=o.currentRoute.value.query.redirect;if(s){const{checkRoutePermission:a}=await t(async()=>{const{checkRoutePermission:e}=await import("./permission-CLkZa8df.js");return{checkRoutePermission:e}},__vite__mapDeps([0,1,2,3,4,5,6,7]));if(a({path:s,meta:{}},e))return void o.push(s);O.warning({message:"您没有权限访问请求的页面，已跳转到默认页面",duration:4e3})}const r=a(e),n=j.createSession({id:i.userInfo?.id,username:_.username,role:e});q(_.username,0,`重定向到: ${r}`),V(_.username,0,n.id,`角色: ${e}, 重定向: ${r}`),o.push(r)}catch(a){o.push("/admin/dashboard")}};y(()=>{S(),window.addEventListener("resize",E),""===_.username?b(()=>{l.value.focus()}):b(()=>{d.value.focus()})}),x(()=>{window.removeEventListener("resize",E)});const H=h({username:"",password:""}),K=h(""),N={router:o,userStore:i,loginFormRef:n,usernameRef:l,passwordRef:d,loading:c,showPassword:u,capsTooltip:v,rememberMe:g,isCompactMode:m,isDev:k,checkScreenSize:S,handleResize:E,loginForm:_,loginRules:U,passwordType:$,checkCapslock:e=>{const{key:a}=e;v.value=a&&1===a.length&&a>="A"&&a<="Z"},handlePreviewLogin:async()=>{c.value=!0,O.info("正在进入预览模式...");try{i.enterPreviewMode();if(!r())throw new Error("Token 设置失败");setTimeout(async()=>{O.success("欢迎来到预览模式！");try{await o.push("/admin/dashboard")}catch(e){O.error("页面跳转失败，请手动刷新页面")}c.value=!1},500)}catch(e){O.error("无法进入预览模式，请稍后重试。"),c.value=!1}},handleLogin:async()=>{try{await n.value.validate(),c.value=!0;const e=await i.login(_),a=e.data?.user?.role||i.userInfo?.role,s=e.data?.user?.nickname||e.data?.user?.username||"用户",r={admin:"欢迎回来，超级管理员！",substation:"欢迎回来，分站管理员！",agent:"欢迎回来，代理商！",distributor:"欢迎回来，分销员！",group_owner:"欢迎回来，群主！",user:"欢迎回来！"};O.success({message:`${r[a]||"登录成功！"} ${s}`,type:"success",duration:3e3}),await Z(a)}catch(e){const a={"Network Error":"网络连接失败，请检查网络设置",timeout:"请求超时，请稍后重试",Unauthorized:"用户名或密码错误",Forbidden:"账户已被禁用，请联系管理员"},s=a[e.code]||a[e.message]||e.message||"登录失败，请检查用户名和密码";O.error({message:s,type:"error",duration:5e3}),q(_.username,0,s)}finally{c.value=!1}},handleSmartRedirect:Z,handleForgotPassword:()=>{O.info("请联系系统管理员重置密码")},errors:H,globalError:K,clearFieldError:e=>{H.value[e]&&(H.value[e]=""),K.value&&(K.value="")},ref:h,reactive:w,nextTick:b,onMounted:y,onUnmounted:x,computed:f,get useRouter(){return p},get useUserStore(){return s},get ElMessage(){return O},get User(){return B},get Lock(){return F},get View(){return P},get Hide(){return R},get Right(){return D},get Loading(){return M},get InfoFilled(){return A},get QuestionFilled(){return T},get CircleCheckFilled(){return C},get TrendCharts(){return L},get Share(){return I},get securityLogger(){return z},get sessionManager(){return j},get logLogin(){return q},get logSession(){return V},get getToken(){return r}};return Object.defineProperty(N,"__isScriptSetup",{enumerable:!1,value:!0}),N}},Z={class:"login-page"},H={class:"login-container"},K={class:"login-card"},N={key:0,class:"error-alert"},J={class:"form-fields"},W={class:"form-group"},Q={class:"input-wrapper"},X={key:0,class:"field-error"},G={class:"form-group"},Y={class:"input-wrapper"},ee=["type"],ae={key:0,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor"},se={key:1,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor"},re={key:0,class:"field-error"},te={class:"form-options"},oe={class:"remember-me"},ie=["disabled"],ne={key:0,class:"loading-spinner"},le={key:1,class:"dev-tools"};const de=a($,[["render",function(e,a,s,r,t,p){return k(),o("div",Z,[a[16]||(a[16]=i('<div class="background-decoration" data-v-08aaad4e><div class="floating-shapes" data-v-08aaad4e><div class="shape shape-1" data-v-08aaad4e></div><div class="shape shape-2" data-v-08aaad4e></div><div class="shape shape-3" data-v-08aaad4e></div><div class="shape shape-4" data-v-08aaad4e></div><div class="shape shape-5" data-v-08aaad4e></div></div><div class="gradient-overlay" data-v-08aaad4e></div></div>',1)),n("div",H,[n("div",K,[a[15]||(a[15]=i('<div class="login-header" data-v-08aaad4e><div class="logo-section" data-v-08aaad4e><div class="logo-icon" data-v-08aaad4e><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-08aaad4e><path d="M13 10V3L4 14h7v7l9-11h-7z" fill="currentColor" data-v-08aaad4e></path></svg></div><div class="logo-text" data-v-08aaad4e><h1 data-v-08aaad4e>晨鑫流量变现系统</h1><p data-v-08aaad4e>智能社群营销与多级分销平台</p></div></div><div class="welcome-section" data-v-08aaad4e><h2 data-v-08aaad4e>管理员登录</h2><p data-v-08aaad4e>欢迎回来，请登录您的管理员账户</p><div class="status-indicator" data-v-08aaad4e><div class="status-dot" data-v-08aaad4e></div><span data-v-08aaad4e>系统运行正常</span></div></div></div>',1)),n("form",{class:"login-form",onSubmit:l(r.handleLogin,["prevent"])},[r.globalError?(k(),o("div",N,[a[6]||(a[6]=i('<div class="error-icon" data-v-08aaad4e><svg viewBox="0 0 24 24" fill="none" stroke="currentColor" data-v-08aaad4e><circle cx="12" cy="12" r="10" data-v-08aaad4e></circle><line x1="15" y1="9" x2="9" y2="15" data-v-08aaad4e></line><line x1="9" y1="9" x2="15" y2="15" data-v-08aaad4e></line></svg></div>',1)),n("span",null,E(r.globalError),1)])):d("",!0),n("div",J,[n("div",W,[a[8]||(a[8]=n("label",{for:"username",class:"form-label"},"用户名或邮箱",-1)),n("div",Q,[a[7]||(a[7]=n("div",{class:"input-icon"},[n("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor"},[n("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),n("circle",{cx:"12",cy:"7",r:"4"})])],-1)),c(n("input",{id:"username",ref:"usernameRef","onUpdate:modelValue":a[0]||(a[0]=e=>r.loginForm.username=e),type:"text",required:"",class:_(["modern-input",{"input-error":r.errors.username}]),placeholder:"请输入用户名或邮箱",autofocus:"",onInput:a[1]||(a[1]=e=>r.clearFieldError("username")),onKeyup:v(r.handleLogin,["enter"])},null,34),[[u,r.loginForm.username]])]),r.errors.username?(k(),o("p",X,E(r.errors.username),1)):d("",!0)]),n("div",G,[a[12]||(a[12]=n("label",{for:"password",class:"form-label"},"密码",-1)),n("div",Y,[a[11]||(a[11]=i('<div class="input-icon" data-v-08aaad4e><svg viewBox="0 0 24 24" fill="none" stroke="currentColor" data-v-08aaad4e><rect x="3" y="11" width="18" height="11" rx="2" ry="2" data-v-08aaad4e></rect><circle cx="12" cy="16" r="1" data-v-08aaad4e></circle><path d="M7 11V7a5 5 0 0 1 10 0v4" data-v-08aaad4e></path></svg></div>',1)),c(n("input",{id:"password",ref:"passwordRef","onUpdate:modelValue":a[2]||(a[2]=e=>r.loginForm.password=e),type:r.showPassword?"text":"password",required:"",class:_(["modern-input",{"input-error":r.errors.password}]),placeholder:"请输入密码",onInput:a[3]||(a[3]=e=>r.clearFieldError("password")),onKeyup:v(r.handleLogin,["enter"])},null,42,ee),[[g,r.loginForm.password]]),n("button",{type:"button",onClick:a[4]||(a[4]=e=>r.showPassword=!r.showPassword),class:"password-toggle"},[r.showPassword?(k(),o("svg",se,a[10]||(a[10]=[n("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"},null,-1),n("line",{x1:"1",y1:"1",x2:"23",y2:"23"},null,-1)]))):(k(),o("svg",ae,a[9]||(a[9]=[n("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"},null,-1),n("circle",{cx:"12",cy:"12",r:"3"},null,-1)])))])]),r.errors.password?(k(),o("p",re,E(r.errors.password),1)):d("",!0)])]),n("div",te,[n("div",oe,[c(n("input",{id:"remember","onUpdate:modelValue":a[5]||(a[5]=e=>r.rememberMe=e),type:"checkbox",class:"modern-checkbox"},null,512),[[m,r.rememberMe]]),a[13]||(a[13]=n("label",{for:"remember",class:"checkbox-label"},"记住我",-1))]),n("a",{href:"#",class:"forgot-password",onClick:l(r.handleForgotPassword,["prevent"])}," 忘记密码？ ")]),n("button",{type:"submit",disabled:r.loading,class:"modern-login-button"},[r.loading?(k(),o("div",ne,a[14]||(a[14]=[n("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor"},[n("path",{d:"M21 12a9 9 0 11-6.219-8.56"})],-1)]))):d("",!0),n("span",null,E(r.loading?"登录中...":"立即登录管理后台"),1)],8,ie),r.isDev?(k(),o("div",le,[n("button",{type:"button",onClick:r.handlePreviewLogin,class:"preview-button"}," 🚀 开发预览模式 ")])):d("",!0)],32)])])])}],["__scopeId","data-v-08aaad4e"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/Login.vue"]]);export{de as default};
