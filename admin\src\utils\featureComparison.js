/**
 * 功能对比分析工具
 * 检查新路由配置是否有功能缺失
 */

// 从原始路由配置提取的所有功能页面
const originalFeatures = [
  // 核心功能
  { path: '/admin/dashboard', name: 'Dashboard', title: '控制台' },
  { path: '/admin/analytics', name: 'DashboardAnalytics', title: '数据分析' },

  // 用户管理
  { path: '/admin/users', name: 'UserManagement', title: '用户管理' },
  { path: '/admin/user-analytics', name: 'UserAnalytics', title: '用户分析' },

  // 权限管理
  { path: '/admin/permissions', name: 'PermissionManagement', title: '权限管理' },
  { path: '/admin/roles', name: 'RoleManagement', title: '角色管理' },

  // 社群管理
  { path: '/admin/groups', name: 'GroupManagement', title: '群组管理' },
  { path: '/admin/templates', name: 'TemplateManagement', title: '模板管理' },

  // 链接管理
  { path: '/admin/links', name: 'LinkManagement', title: '链接管理' },

  // 系统设置
  { path: '/admin/settings', name: 'SystemSettings', title: '系统设置' },
  { path: '/admin/operation-logs', name: 'OperationLogs', title: '操作日志' },
  { path: '/admin/system-monitor', name: 'SystemMonitor', title: '系统监控' },
  { path: '/admin/notifications', name: 'NotificationManagement', title: '通知管理' },
  { path: '/admin/data-export', name: 'DataExport', title: '数据导出' },
  { path: '/admin/file-management', name: 'FileManagement', title: '文件管理' },

  // 代理商管理
  { path: '/admin/agents', name: 'AgentList', title: '代理商管理' },
  { path: '/admin/agent-hierarchy', name: 'AgentHierarchy', title: '代理商层级' },
  { path: '/admin/agent-commission', name: 'AgentCommission', title: '佣金管理' },
  { path: '/admin/agent-performance', name: 'AgentPerformance', title: '业绩统计' },

  // 财务管理
  { path: '/admin/finance', name: 'FinanceDashboard', title: '财务管理' },
  { path: '/admin/transactions', name: 'TransactionList', title: '交易记录' },
  { path: '/admin/commission-logs', name: 'CommissionLog', title: '佣金日志' },
  { path: '/admin/withdraw-manage', name: 'WithdrawManage', title: '提现管理' },

  // 支付管理
  { path: '/admin/payment-settings', name: 'PaymentSettings', title: '支付设置' },
  { path: '/admin/payment-channels', name: 'PaymentChannelManagement', title: '支付渠道' },
  { path: '/admin/payment-orders', name: 'PaymentOrders', title: '支付订单' },
  { path: '/admin/payment-logs', name: 'PaymentLogs', title: '支付日志' },

  // 订单管理
  { path: '/admin/orders', name: 'OrderManagement', title: '订单管理' },

  // 分销管理
  { path: '/admin/distributors', name: 'DistributorManagement', title: '分销管理' },
  { path: '/admin/promotion', name: 'PromotionManagement', title: '推广管理' },

  // 防红系统
  { path: '/admin/anti-block', name: 'AntiBlockDashboard', title: '防红系统' },
  { path: '/admin/domains', name: 'DomainManagement', title: '域名管理' },
  { path: '/admin/short-links', name: 'ShortLinkManagement', title: '短链管理' },
  { path: '/admin/anti-block-analytics', name: 'AntiBlockAnalytics', title: '防红分析' },
  { path: '/admin/anti-block-enhanced', name: 'AntiBlockEnhanced', title: '增强防红' }
]

// 从新路由配置提取的所有功能页面
const newFeatures = [
  // 核心功能
  { path: '/admin/dashboard', name: 'Dashboard', title: '仪表板' },

  // 用户管理
  { path: '/admin/users/list', name: 'UserList', title: '用户列表' },
  { path: '/admin/users/analytics', name: 'UserAnalytics', title: '用户分析' },
  { path: '/admin/users/profile', name: 'UserProfile', title: '个人资料' },

  // 社群管理
  { path: '/admin/community/groups', name: 'GroupManagement', title: '群组管理' },
  { path: '/admin/community/templates', name: 'TemplateManagement', title: '模板管理' },

  // 代理商管理
  { path: '/admin/agents/list', name: 'AgentList', title: '代理商列表' },
  { path: '/admin/agents/hierarchy', name: 'AgentHierarchy', title: '层级结构' },
  { path: '/admin/agents/commission', name: 'AgentCommission', title: '佣金管理' },
  { path: '/admin/agents/performance', name: 'AgentPerformance', title: '业绩统计' },

  // 财务管理
  { path: '/admin/finance/dashboard', name: 'FinanceDashboard', title: '财务概览' },
  { path: '/admin/finance/transactions', name: 'TransactionList', title: '交易记录' },
  { path: '/admin/finance/commission', name: 'CommissionLog', title: '佣金日志' },
  { path: '/admin/finance/withdraw', name: 'WithdrawManage', title: '提现管理' },

  // 支付管理
  { path: '/admin/payment/settings', name: 'PaymentSettings', title: '支付设置' },
  { path: '/admin/payment/channels', name: 'PaymentChannelManagement', title: '支付渠道' },
  { path: '/admin/payment/orders', name: 'PaymentOrders', title: '支付订单' },
  { path: '/admin/payment/logs', name: 'PaymentLogs', title: '支付日志' },

  // 订单管理
  { path: '/admin/orders', name: 'OrderManagement', title: '订单管理' },

  // 分销推广
  { path: '/admin/promotion/distributors', name: 'DistributorManagement', title: '分销商管理' },
  { path: '/admin/promotion/links', name: 'PromotionLinks', title: '推广链接' },

  // 防红系统
  { path: '/admin/anti-block/dashboard', name: 'AntiBlockDashboard', title: '防红概览' },
  { path: '/admin/anti-block/domains', name: 'DomainManagement', title: '域名管理' },
  { path: '/admin/anti-block/links', name: 'ShortLinkManagement', title: '短链管理' },
  { path: '/admin/anti-block/analytics', name: 'AntiBlockAnalytics', title: '防红分析' },
  { path: '/admin/anti-block/enhanced', name: 'AntiBlockEnhanced', title: '增强防护' },

  // 权限管理
  { path: '/admin/permissions/roles', name: 'RoleManagement', title: '角色管理' },
  { path: '/admin/permissions/permissions', name: 'PermissionManagement', title: '权限配置' },

  // 系统管理
  { path: '/admin/system/settings', name: 'SystemSettings', title: '系统设置' },
  { path: '/admin/system/monitor', name: 'SystemMonitor', title: '系统监控' },
  { path: '/admin/system/logs', name: 'OperationLogs', title: '操作日志' },
  { path: '/admin/system/notifications', name: 'NotificationManagement', title: '通知管理' },
  { path: '/admin/system/data-export', name: 'DataExport', title: '数据导出' },
  { path: '/admin/system/file-management', name: 'FileManagement', title: '文件管理' },
  { path: '/admin/system/function-test', name: 'FunctionTest', title: '功能测试' },
  { path: '/admin/system/user-guide', name: 'UserGuide', title: '使用指南' }
]

/**
 * 分析功能差异
 */
export function analyzeFeatureDifference() {
  const analysis = {
    missing: [],        // 原有功能但新配置中缺失
    added: [],          // 新增功能
    pathChanged: [],    // 路径发生变化的功能
    identical: []       // 完全相同的功能
  }

  // 检查缺失的功能
  originalFeatures.forEach(original => {
    const found = newFeatures.find(newFeature => 
      newFeature.name === original.name || 
      newFeature.title === original.title ||
      newFeature.path === original.path
    )
    
    if (!found) {
      analysis.missing.push(original)
    } else if (found.path !== original.path) {
      analysis.pathChanged.push({
        original: original,
        new: found
      })
    } else {
      analysis.identical.push(original)
    }
  })

  // 检查新增的功能
  newFeatures.forEach(newFeature => {
    const found = originalFeatures.find(original => 
      original.name === newFeature.name || 
      original.title === newFeature.title ||
      original.path === newFeature.path
    )
    
    if (!found) {
      analysis.added.push(newFeature)
    }
  })

  return analysis
}

/**
 * 生成详细的对比报告
 */
export function generateComparisonReport() {
  const analysis = analyzeFeatureDifference()
  
  let report = `
🔍 路由功能对比分析报告
========================

📊 统计摘要:
- 原有功能总数: ${originalFeatures.length}
- 新配置功能数: ${newFeatures.length}
- 完全相同: ${analysis.identical.length}
- 路径变更: ${analysis.pathChanged.length}
- 缺失功能: ${analysis.missing.length}
- 新增功能: ${analysis.added.length}

`

  // 缺失功能分析
  if (analysis.missing.length > 0) {
    report += `\n❌ 缺失的功能 (${analysis.missing.length}个):\n`
    analysis.missing.forEach((feature, index) => {
      report += `${index + 1}. ${feature.title} (${feature.path})\n`
    })
  }

  // 路径变更分析
  if (analysis.pathChanged.length > 0) {
    report += `\n🔄 路径变更的功能 (${analysis.pathChanged.length}个):\n`
    analysis.pathChanged.forEach((change, index) => {
      report += `${index + 1}. ${change.original.title}\n`
      report += `   原路径: ${change.original.path}\n`
      report += `   新路径: ${change.new.path}\n`
    })
  }

  // 新增功能分析
  if (analysis.added.length > 0) {
    report += `\n✨ 新增的功能 (${analysis.added.length}个):\n`
    analysis.added.forEach((feature, index) => {
      report += `${index + 1}. ${feature.title} (${feature.path})\n`
    })
  }

  // 重要发现
  report += `\n🎯 重要发现:\n`
  
  // 检查关键功能
  const keyFeatures = [
    { title: '短链管理', original: 'short-links', new: 'anti-block/links' },
    { title: '数据分析', original: 'analytics', new: 'users/analytics' },
    { title: '链接管理', original: 'links', new: null }
  ]
  
  keyFeatures.forEach(feature => {
    const originalExists = originalFeatures.some(f => f.path.includes(feature.original))
    const newExists = feature.new ? newFeatures.some(f => f.path.includes(feature.new)) : false
    
    if (originalExists && !newExists && !feature.new) {
      report += `⚠️ ${feature.title}: 可能缺失\n`
    } else if (originalExists && newExists && feature.new) {
      report += `✅ ${feature.title}: 已迁移 (路径变更)\n`
    } else if (originalExists && !newExists) {
      report += `❌ ${feature.title}: 缺失\n`
    }
  })

  return report
}

/**
 * 检查关键功能是否缺失
 */
export function checkCriticalFeatures() {
  const criticalFeatures = [
    'ShortLinkManagement', // 短链管理
    'DomainManagement',    // 域名管理
    'LinkManagement',      // 链接管理
    'UserAnalytics',       // 用户分析
    'FinanceDashboard',    // 财务管理
    'SystemSettings'       // 系统设置
  ]

  const results = {
    missing: [],
    found: []
  }

  criticalFeatures.forEach(featureName => {
    const foundInNew = newFeatures.some(f => f.name === featureName)
    const foundInOriginal = originalFeatures.some(f => f.name === featureName)

    if (foundInOriginal && !foundInNew) {
      results.missing.push(featureName)
    } else if (foundInNew) {
      results.found.push(featureName)
    }
  })

  return results
}

export default {
  analyzeFeatureDifference,
  generateComparisonReport,
  checkCriticalFeatures
}