{"name": "linkhub/pro", "type": "project", "description": "晨鑫流量变现系统 - 新一代智能社群营销与多级分销平台", "keywords": ["framework", "laravel", "wechat", "distribution", "marketing"], "license": "MIT", "require": {"php": "^8.1|^8.2|^8.3", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.10", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "tymon/jwt-auth": "^2.0", "spatie/laravel-permission": "^5.10", "intervention/image": "^2.7", "maatwebsite/excel": "^3.1.48", "predis/predis": "^2.0", "league/flysystem-aws-s3-v3": "^3.0", "doctrine/dbal": "^3.6", "pusher/pusher-php-server": "^7.2", "rap2hpoutre/laravel-log-viewer": "^2.3", "sentry/sentry-laravel": "^3.8", "laravel/scout": "^10.2", "elasticsearch/elasticsearch": "^8.8", "rap2hpoutre/fast-excel": "^5.2", "paragonie/sodium_compat": "^2.1", "openspout/openspout": "^4.23", "maennchen/zipstream-php": "^2.4"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0", "phpstan/phpstan": "^1.10", "larastan/larastan": "^2.6"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\"", "@php artisan key:generate --ansi"], "check-security": ["@php artisan security:check"], "check-code": ["./vendor/bin/phpstan analyse", "./vendor/bin/pint --test"], "fix-code": ["./vendor/bin/pint"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}, "platform": {"php": "8.2.9"}, "platform-check": false}, "minimum-stability": "stable", "prefer-stable": true}