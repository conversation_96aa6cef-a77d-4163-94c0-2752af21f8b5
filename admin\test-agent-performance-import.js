#!/usr/bin/env node

/**
 * 测试代理商绩效分析页面导入是否正常的脚本
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🧪 开始测试代理商绩效分析页面模块导入...\n')

const checks = []

// 检查1: Vue组件文件是否存在且语法正确
const performancePath = path.join(__dirname, 'src/views/agent/AgentPerformance.vue')
if (fs.existsSync(performancePath)) {
  try {
    const content = fs.readFileSync(performancePath, 'utf8')
    
    // 检查基本的Vue文件结构
    const hasTemplate = content.includes('<template>')
    const hasScript = content.includes('<script setup>')
    const hasStyle = content.includes('<style')
    
    if (hasTemplate && hasScript && hasStyle) {
      checks.push({ 
        name: 'Vue组件结构', 
        status: '✅ 正常', 
        details: 'template, script, style 区块都存在' 
      })
    } else {
      checks.push({ 
        name: 'Vue组件结构', 
        status: '❌ 异常', 
        details: `缺少必要区块 - template:${hasTemplate}, script:${hasScript}, style:${hasStyle}` 
      })
    }
    
    // 检查语法问题
    let syntaxIssues = []
    
    // 检查是否有不匹配的括号或引号
    const openBraces = (content.match(/\{/g) || []).length
    const closeBraces = (content.match(/\}/g) || []).length
    if (openBraces !== closeBraces) {
      syntaxIssues.push(`括号不匹配 ({${openBraces} vs }${closeBraces})`)
    }
    
    // 检查是否有未闭合的标签
    const templateMatch = content.match(/<template>([\s\S]*)<\/template>/)
    if (templateMatch) {
      const templateContent = templateMatch[1]
      const divTags = (templateContent.match(/<div[^>]*>/g) || []).length
      const closeDivTags = (templateContent.match(/<\/div>/g) || []).length
      if (divTags !== closeDivTags) {
        syntaxIssues.push(`div标签不匹配 (<div>${divTags} vs </div>${closeDivTags})`)
      }
    }
    
    // 检查SCSS语法
    const styleMatch = content.match(/<style[^>]*scoped[^>]*>([\s\S]*)<\/style>/)
    if (styleMatch) {
      const styleContent = styleMatch[1]
      const openBracesStyle = (styleContent.match(/\{/g) || []).length
      const closeBracesStyle = (styleContent.match(/\}/g) || []).length
      if (openBracesStyle !== closeBracesStyle) {
        syntaxIssues.push(`SCSS括号不匹配 ({${openBracesStyle} vs }${closeBracesStyle})`)
      }
    }
    
    if (syntaxIssues.length === 0) {
      checks.push({ 
        name: '语法检查', 
        status: '✅ 通过', 
        details: '未发现明显的语法错误' 
      })
    } else {
      checks.push({ 
        name: '语法检查', 
        status: '⚠️ 发现问题', 
        details: syntaxIssues.join(', ') 
      })
    }
    
  } catch (error) {
    checks.push({ 
      name: '文件读取', 
      status: '❌ 失败', 
      details: `读取文件时出错: ${error.message}` 
    })
  }
} else {
  checks.push({ 
    name: 'Vue组件文件', 
    status: '❌ 不存在', 
    details: 'AgentPerformance.vue文件未找到' 
  })
}

// 检查2: 依赖模块是否存在
const dependencies = [
  { path: 'src/api/agent.js', name: 'Agent API' },
  { path: 'src/components/Charts/LineChart.vue', name: 'LineChart组件' },
  { path: 'src/components/Charts/DoughnutChart.vue', name: 'DoughnutChart组件' }
]

dependencies.forEach(dep => {
  const depPath = path.join(__dirname, dep.path)
  if (fs.existsSync(depPath)) {
    checks.push({ 
      name: dep.name, 
      status: '✅ 存在', 
      details: `依赖文件存在: ${dep.path}` 
    })
  } else {
    checks.push({ 
      name: dep.name, 
      status: '❌ 缺失', 
      details: `依赖文件不存在: ${dep.path}` 
    })
  }
})

// 检查3: 路由配置
const routerPath = path.join(__dirname, 'src/router/index.js')
if (fs.existsSync(routerPath)) {
  try {
    const routerContent = fs.readFileSync(routerPath, 'utf8')
    const hasAgentPerformanceRoute = routerContent.includes('AgentPerformance') && 
                                    routerContent.includes('agent/AgentPerformance.vue')
    
    if (hasAgentPerformanceRoute) {
      checks.push({ 
        name: '路由配置', 
        status: '✅ 正确', 
        details: 'AgentPerformance路由配置存在' 
      })
    } else {
      checks.push({ 
        name: '路由配置', 
        status: '⚠️ 可能有问题', 
        details: '未找到AgentPerformance相关路由配置' 
      })
    }
  } catch (error) {
    checks.push({ 
      name: '路由配置检查', 
      status: '❌ 失败', 
      details: `读取路由文件失败: ${error.message}` 
    })
  }
}

// 输出检查结果
console.log('📋 代理商绩效分析页面模块导入测试报告\n')
console.log(''.padEnd(70, '='))

let passCount = 0
let warnCount = 0
let failCount = 0

checks.forEach((check, index) => {
  console.log(`${index + 1}. ${check.name.padEnd(20)} ${check.status}`)
  console.log(`   ${check.details}\n`)
  
  if (check.status.includes('✅')) passCount++
  else if (check.status.includes('⚠️')) warnCount++
  else failCount++
})

console.log(''.padEnd(70, '='))
console.log('📊 总结:')
console.log(`   ✅ 通过: ${passCount} 项`)
console.log(`   ⚠️ 警告: ${warnCount} 项`)
console.log(`   ❌ 失败: ${failCount} 项`)

if (failCount === 0 && warnCount === 0) {
  console.log('\n🎉 所有检查通过！模块应该能够正常导入。')
  console.log('\n💡 如果仍然出现导入错误，可能的解决方案：')
  console.log('1. 清除浏览器缓存和本地存储')
  console.log('2. 重启开发服务器: npm run dev')
  console.log('3. 删除 node_modules/.vite 缓存目录')
  console.log('4. 检查网络连接和代理设置')
} else if (failCount === 0) {
  console.log('\n⚠️ 基本检查通过，但有一些警告需要注意。')
} else {
  console.log('\n❌ 发现关键问题，需要修复后才能正常导入模块。')
}

console.log('\n🔧 常见导入失败解决方法：')
console.log('1. 硬刷新页面 (Ctrl+F5 或 Ctrl+Shift+R)')
console.log('2. 检查开发服务器控制台是否有错误信息')
console.log('3. 确认文件保存正确且没有格式问题')
console.log('4. 重新启动开发服务器')

process.exit(failCount > 0 ? 1 : 0)