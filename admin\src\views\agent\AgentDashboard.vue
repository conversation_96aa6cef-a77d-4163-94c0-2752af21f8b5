<template>
  <div class="modern-agent-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><UserFilled /></el-icon>
          </div>
          <div class="header-text">
            <h1>代理商工作台</h1>
            <p>管理您的推广业务，跟踪团队业绩，提升佣金收入</p>
          </div>
        </div>
        <div class="header-actions">
          <div class="agent-badges">
            <el-tag type="primary" class="agent-tag">{{ agentInfo.agent_level_text || '平台代理' }}</el-tag>
            <el-tag type="success" class="agent-tag">{{ agentInfo.agent_type_text || '个人代理' }}</el-tag>
            <span class="agent-code">编码: {{ agentInfo.agent_code || 'AG001' }}</span>
          </div>
          <div class="action-buttons">
            <el-button @click="showHelpDialog = true" class="action-btn secondary">
              <el-icon><QuestionFilled /></el-icon>
              功能说明
            </el-button>
            <el-button type="primary" @click="refreshAllData" class="action-btn primary">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-container" v-loading="statsLoading">
        <div class="stat-card" v-for="stat in agentStatCards" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <el-card class="quick-actions-card">
      <template #header>
        <span>快捷操作</span>
      </template>
      <el-row :gutter="15">
        <el-col :span="4">
          <div class="action-item" @click="goToPromotionTools">
            <el-icon class="action-icon"><Share /></el-icon>
            <span>推广工具</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToTeamManagement">
            <el-icon class="action-icon"><Connection /></el-icon>
            <span>团队管理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToCommissionCenter">
            <el-icon class="action-icon"><Money /></el-icon>
            <span>佣金中心</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToTrainingCenter">
            <el-icon class="action-icon"><Reading /></el-icon>
            <span>培训中心</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToPerformanceAnalysis">
            <el-icon class="action-icon"><TrendCharts /></el-icon>
            <span>绩效分析</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToSettings">
            <el-icon class="action-icon"><Setting /></el-icon>
            <span>账户设置</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>佣金收入趋势</span>
              <el-radio-group v-model="commissionPeriod" size="small" @change="loadCommissionTrend">
                <el-radio-button label="week">近7天</el-radio-button>
                <el-radio-button label="month">近30天</el-radio-button>
                <el-radio-button label="quarter">近3个月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <LineChart
            :data="commissionTrendData"
            :options="chartOptions"
            height="300px"
          />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户来源分析</span>
          </template>
          <DoughnutChart
            :data="userSourceData"
            :options="doughnutOptions"
            height="300px"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 团队概览和最新动态 -->
    <el-row :gutter="20" class="info-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>我的团队</span>
              <el-button size="small" @click="goToTeamManagement">查看全部</el-button>
            </div>
          </template>
          <div class="team-overview">
            <div class="team-stats">
              <div class="team-stat-item">
                <span class="label">直属下级:</span>
                <span class="value">{{ teamStats.direct_children || 0 }}</span>
              </div>
              <div class="team-stat-item">
                <span class="label">团队总人数:</span>
                <span class="value">{{ teamStats.total_children || 0 }}</span>
              </div>
              <div class="team-stat-item">
                <span class="label">活跃成员:</span>
                <span class="value">{{ teamStats.active_members || 0 }}</span>
              </div>
            </div>
            <div class="recent-members">
              <h4>最新加入成员</h4>
              <div v-for="member in recentMembers" :key="member.id" class="member-item">
                <el-avatar :size="32" :src="member.avatar" />
                <div class="member-info">
                  <div class="member-name">{{ member.name }}</div>
                  <div class="member-time">{{ formatDate(member.created_at) }}</div>
                </div>
                <el-tag size="small" :type="getMemberTypeColor(member.agent_type)">
                  {{ member.agent_type_text }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最新动态</span>
              <el-button size="small" @click="refreshActivities">刷新</el-button>
            </div>
          </template>
          <div class="activities-list">
            <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
              <div class="activity-icon">
                <el-icon :color="getActivityColor(activity.type)">
                  <component :is="getActivityIcon(activity.type)" />
                </el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ formatDate(activity.created_at) }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 推广链接快速生成 -->
    <el-card class="promotion-card">
      <template #header>
        <span>推广链接快速生成</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-input
            v-model="promotionLink"
            placeholder="您的专属推广链接"
            readonly
          >
            <template #prepend>推广链接</template>
            <template #append>
              <el-button @click="copyPromotionLink">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="generateQRCode">
            <el-icon><Setting /></el-icon>
            生成二维码
          </el-button>
          <el-button @click="goToPromotionTools">
            <el-icon><Setting /></el-icon>
            更多工具
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 二维码对话框 -->
    <el-dialog v-model="qrCodeDialogVisible" title="推广二维码" width="400px">
      <div class="qr-code-container">
        <div ref="qrCodeRef" class="qr-code"></div>
        <div class="qr-code-tips">
          <p>扫描二维码或分享链接进行推广</p>
          <el-button type="primary" @click="downloadQRCode">下载二维码</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 功能说明对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="代理商工作台功能说明"
      width="1000px"
      class="help-dialog"
    >
      <div class="help-content">
        <!-- 功能概述 -->
        <div class="help-section">
          <h3>🎯 功能概述</h3>
          <p>代理商工作台是您管理推广业务的核心平台，提供全面的数据统计、团队管理、佣金跟踪等功能，帮助您高效开展推广业务，最大化收益。</p>
        </div>

        <!-- 核心功能 -->
        <div class="help-section">
          <h3>🚀 核心功能模块</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Share /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>推广工具</h4>
                  <p>专属推广链接、二维码生成、推广素材管理</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Connection /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>团队管理</h4>
                  <p>下级代理商管理、团队业绩统计、层级关系维护</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>佣金中心</h4>
                  <p>佣金收入统计、提现申请、收益明细查询</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>绩效分析</h4>
                  <p>推广数据分析、转化率统计、业绩趋势图表</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Reading /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>培训中心</h4>
                  <p>推广技巧学习、产品知识培训、营销资料下载</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Setting /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>账户设置</h4>
                  <p>个人信息管理、收款账户设置、通知偏好配置</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 代理商等级说明 -->
        <div class="help-section">
          <h3>🏆 代理商等级体系</h3>
          <el-table :data="agentLevels" style="width: 100%">
            <el-table-column prop="level" label="等级" width="100">
              <template #default="{ row }">
                <el-tag :type="row.color">{{ row.level }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="等级名称" width="120" />
            <el-table-column prop="requirements" label="升级条件" />
            <el-table-column prop="commission_rate" label="佣金比例" width="100" />
            <el-table-column prop="benefits" label="专属权益" />
          </el-table>
        </div>

        <!-- 佣金计算规则 -->
        <div class="help-section">
          <h3>💰 佣金计算规则</h3>
          <div class="commission-rules">
            <div class="rule-item">
              <h4>🔸 直推佣金</h4>
              <p>直接推广用户产生的订单，您可获得 <strong>{{ commissionRates.direct }}%</strong> 的佣金</p>
              <div class="example">
                <span class="example-label">示例：</span>
                用户通过您的链接购买100元产品，您获得{{ commissionRates.direct }}元佣金
              </div>
            </div>
            <div class="rule-item">
              <h4>🔸 团队佣金</h4>
              <p>下级代理商推广产生的订单，您可获得 <strong>{{ commissionRates.team }}%</strong> 的团队佣金</p>
              <div class="example">
                <span class="example-label">示例：</span>
                下级代理推广100元订单，您获得{{ commissionRates.team }}元团队佣金
              </div>
            </div>
            <div class="rule-item">
              <h4>🔸 层级佣金</h4>
              <p>支持多层级佣金分配，最多支持 <strong>{{ commissionRules.max_levels }}</strong> 级分佣</p>
              <el-table :data="levelCommissions" size="small" style="margin-top: 10px;">
                <el-table-column prop="level" label="层级" width="80" />
                <el-table-column prop="rate" label="佣金比例" width="100" />
                <el-table-column prop="description" label="说明" />
              </el-table>
            </div>
          </div>
        </div>

        <!-- 推广技巧 -->
        <div class="help-section">
          <h3>📈 推广技巧与建议</h3>
          <el-tabs v-model="activeTab" type="card">
            <el-tab-pane label="推广渠道" name="channels">
              <div class="tips-content">
                <h4>🌟 推荐推广渠道</h4>
                <ul>
                  <li><strong>社交媒体</strong>：微信朋友圈、QQ空间、微博等社交平台分享</li>
                  <li><strong>社群营销</strong>：微信群、QQ群、论坛等社群推广</li>
                  <li><strong>内容营销</strong>：撰写产品评测、使用心得等优质内容</li>
                  <li><strong>线下推广</strong>：朋友推荐、活动宣传等线下渠道</li>
                  <li><strong>短视频平台</strong>：抖音、快手等短视频平台推广</li>
                </ul>
                <el-alert type="success" :closable="false" style="margin-top: 15px;">
                  💡 建议：多渠道组合推广，提高覆盖面和转化率
                </el-alert>
              </div>
            </el-tab-pane>
            <el-tab-pane label="推广话术" name="scripts">
              <div class="tips-content">
                <h4>💬 推广话术模板</h4>
                <div class="script-item">
                  <h5>朋友圈推广</h5>
                  <div class="script-text">
                    "发现一个不错的平台，可以通过推广赚取佣金💰<br>
                    产品质量有保障，佣金结算及时✅<br>
                    感兴趣的朋友可以了解一下👇<br>
                    [推广链接]"
                  </div>
                </div>
                <div class="script-item">
                  <h5>私聊推广</h5>
                  <div class="script-text">
                    "Hi，最近在做一个项目，产品不错，佣金也挺可观的。<br>
                    如果你有兴趣了解或者想要产品的话，可以通过我的链接购买，<br>
                    这样我也能获得一些佣金收入😊<br>
                    链接：[推广链接]"
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="注意事项" name="notes">
              <div class="tips-content">
                <h4>⚠️ 推广注意事项</h4>
                <ul>
                  <li><strong>诚信推广</strong>：如实介绍产品特点，不夸大宣传</li>
                  <li><strong>合规操作</strong>：遵守平台规则，不进行违规推广</li>
                  <li><strong>用户体验</strong>：关注用户反馈，提供优质服务</li>
                  <li><strong>持续学习</strong>：关注产品更新，学习推广技巧</li>
                  <li><strong>数据分析</strong>：定期分析推广数据，优化推广策略</li>
                </ul>
                <el-alert type="warning" :closable="false" style="margin-top: 15px;">
                  ⚠️ 警告：严禁虚假宣传、恶意刷单等违规行为，一经发现将取消代理资格
                </el-alert>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 操作指南 -->
        <div class="help-section">
          <h3>📝 操作指南</h3>
          <el-collapse v-model="activeGuides">
            <el-collapse-item title="如何生成推广链接？" name="link-generation">
              <div class="guide-content">
                <ol>
                  <li>在工作台下方找到"推广链接快速生成"区域</li>
                  <li>系统会自动生成您的专属推广链接</li>
                  <li>点击"复制"按钮复制链接到剪贴板</li>
                  <li>也可以点击"生成二维码"创建推广二维码</li>
                  <li>将链接或二维码分享给潜在用户</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 提示：推广链接包含您的专属代理编码，用户通过此链接注册购买，您将获得相应佣金
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何查看佣金收入？" name="commission-check">
              <div class="guide-content">
                <ol>
                  <li>在统计卡片中查看总佣金收入和本月佣金</li>
                  <li>点击"佣金中心"查看详细的佣金明细</li>
                  <li>在佣金收入趋势图中查看收入变化</li>
                  <li>可以按时间段筛选查看不同期间的收入</li>
                </ol>
                <el-alert type="success" :closable="false">
                  ✅ 说明：佣金每日结算，T+1到账，可在佣金中心申请提现
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何管理我的团队？" name="team-management">
              <div class="guide-content">
                <ol>
                  <li>在"我的团队"卡片中查看团队概况</li>
                  <li>点击"团队管理"进入详细的团队管理页面</li>
                  <li>可以查看下级代理商的业绩和状态</li>
                  <li>为团队成员提供培训和指导</li>
                  <li>关注团队成员的推广数据和收益情况</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 建议：定期与团队成员沟通，分享推广经验，共同提升业绩
                </el-alert>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 常见问题 -->
        <div class="help-section">
          <h3>❓ 常见问题</h3>
          <el-collapse v-model="activeFAQ">
            <el-collapse-item title="佣金什么时候到账？" name="faq1">
              <p>佣金采用T+1结算模式，即今日产生的佣金将在明日到账。您可以在佣金中心查看详细的结算记录。</p>
            </el-collapse-item>
            <el-collapse-item title="如何提升代理商等级？" name="faq2">
              <p>代理商等级根据您的推广业绩自动评定，包括推广用户数、团队规模、佣金收入等指标。持续推广并发展团队即可提升等级。</p>
            </el-collapse-item>
            <el-collapse-item title="推广链接有有效期吗？" name="faq3">
              <p>推广链接长期有效，但建议定期更新推广素材。如果您的代理资格发生变化，系统会自动更新链接状态。</p>
            </el-collapse-item>
            <el-collapse-item title="可以同时推广多个产品吗？" name="faq4">
              <p>可以的。您可以推广平台上的所有产品，每个产品的佣金比例可能不同，具体以产品页面显示为准。</p>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Share, Connection, Money, Reading, TrendCharts, Setting, 
  User, Coin, DocumentCopy, QuestionFilled, UserFilled, Refresh, ArrowUp
} from '@element-plus/icons-vue'
import StatCard from '@/components/dashboard/StatCard.vue'
import LineChart from '@/components/Charts/LineChart.vue'
import DoughnutChart from '@/components/Charts/DoughnutChart.vue'
import { agentApi } from '@/api/agent'
import QRCode from 'qrcode'

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const statsLoading = ref(true)
const commissionPeriod = ref('month')
const qrCodeDialogVisible = ref(false)
const showHelpDialog = ref(false)
const qrCodeRef = ref()

const agentInfo = ref({})
const stats = ref({})
const teamStats = ref({})
const recentMembers = ref([])
const recentActivities = ref([])
const promotionLink = ref('')

// 代理商统计卡片数据 - 与其他页面保持一致的设计
const agentStatCards = ref([
  {
    key: 'total_users',
    label: '推广用户数',
    value: '0',
    icon: 'User',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15.2%'
  },
  {
    key: 'total_commission',
    label: '总佣金收入',
    value: '¥0',
    icon: 'Money',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+28.5%'
  },
  {
    key: 'monthly_commission',
    label: '本月佣金',
    value: '¥0',
    icon: 'Coin',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+18.2%'
  },
  {
    key: 'child_agents',
    label: '下级代理商',
    value: '0',
    icon: 'Connection',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+5'
  }
])

// 帮助对话框相关数据
const activeTab = ref('channels')
const activeGuides = ref(['link-generation'])
const activeFAQ = ref([])

// 代理商等级数据
const agentLevels = ref([
  {
    level: '初级代理',
    color: 'info',
    name: '新手代理',
    requirements: '注册成功，完成实名认证',
    commission_rate: '5%',
    benefits: '基础推广工具、新手培训'
  },
  {
    level: '中级代理',
    color: 'primary',
    name: '进阶代理',
    requirements: '推广用户≥10人，月佣金≥500元',
    commission_rate: '8%',
    benefits: '专属客服、营销素材、数据分析'
  },
  {
    level: '高级代理',
    color: 'warning',
    name: '资深代理',
    requirements: '推广用户≥50人，团队≥5人，月佣金≥2000元',
    commission_rate: '12%',
    benefits: '优先结算、专属培训、活动优先权'
  },
  {
    level: '金牌代理',
    color: 'danger',
    name: '顶级代理',
    requirements: '推广用户≥200人，团队≥20人，月佣金≥10000元',
    commission_rate: '15%',
    benefits: '专属经理、定制服务、年度奖励'
  }
])

// 佣金比例数据
const commissionRates = ref({
  direct: 10,
  team: 3
})

// 佣金规则数据
const commissionRules = ref({
  max_levels: 5
})

// 层级佣金数据
const levelCommissions = ref([
  { level: '1级', rate: '10%', description: '直接推广用户' },
  { level: '2级', rate: '3%', description: '下级代理推广用户' },
  { level: '3级', rate: '1%', description: '三级代理推广用户' },
  { level: '4级', rate: '0.5%', description: '四级代理推广用户' },
  { level: '5级', rate: '0.2%', description: '五级代理推广用户' }
])

// 图表数据
const commissionTrendData = ref({
  labels: [],
  datasets: [{
    label: '佣金收入',
    data: [],
    borderColor: '#409EFF',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    tension: 0.4
  }]
})

const userSourceData = ref({
  labels: ['直接推广', '团队推广', '活动推广', '其他'],
  datasets: [{
    data: [0, 0, 0, 0],
    backgroundColor: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C']
  }]
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
}

// 方法
const loadAgentInfo = async () => {
  try {
    const response = await agentApi.getMy()
    agentInfo.value = response.data
    generatePromotionLink()
  } catch (error) {
    ElMessage.error('加载代理商信息失败')
  }
}

const loadStats = async () => {
  try {
    const response = await agentApi.getMyStats()
    stats.value = response.data
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  }
}

const loadCommissionTrend = async () => {
  try {
    // 模拟数据，实际应该调用API
    const mockData = {
      week: {
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        data: [120, 190, 300, 500, 200, 300, 450]
      },
      month: {
        labels: Array.from({length: 30}, (_, i) => `${i+1}日`),
        data: Array.from({length: 30}, () => Math.floor(Math.random() * 1000))
      },
      quarter: {
        labels: ['1月', '2月', '3月'],
        data: [8000, 12000, 15000]
      }
    }
    
    const data = mockData[commissionPeriod.value]
    commissionTrendData.value = {
      labels: data.labels,
      datasets: [{
        label: '佣金收入',
        data: data.data,
        borderColor: '#409EFF',
        backgroundColor: 'rgba(64, 158, 255, 0.1)',
        tension: 0.4
      }]
    }
  } catch (error) {
    ElMessage.error('加载佣金趋势失败')
  }
}

const loadTeamData = async () => {
  try {
    // 模拟团队数据
    teamStats.value = {
      direct_children: 12,
      total_children: 45,
      active_members: 38
    }
    
    recentMembers.value = [
      {
        id: 1,
        name: '张三',
        avatar: '',
        agent_type: 'individual',
        agent_type_text: '个人代理',
        created_at: new Date()
      },
      {
        id: 2,
        name: '李四',
        avatar: '',
        agent_type: 'enterprise',
        agent_type_text: '企业代理',
        created_at: new Date(Date.now() - 86400000)
      }
    ]
  } catch (error) {
    ElMessage.error('加载团队数据失败')
  }
}

const loadRecentActivities = async () => {
  try {
    // 模拟活动数据
    recentActivities.value = [
      {
        id: 1,
        type: 'commission',
        title: '佣金到账',
        description: '您获得了 ¥150.00 的推广佣金',
        created_at: new Date()
      },
      {
        id: 2,
        type: 'user',
        title: '新用户注册',
        description: '通过您的推广链接，新增用户"王五"',
        created_at: new Date(Date.now() - 3600000)
      },
      {
        id: 3,
        type: 'team',
        title: '团队成员升级',
        description: '下级代理商"赵六"升级为二级代理',
        created_at: new Date(Date.now() - 7200000)
      }
    ]
  } catch (error) {
    ElMessage.error('加载活动数据失败')
  }
}

const generatePromotionLink = () => {
  if (agentInfo.value.agent_code) {
    promotionLink.value = `${window.location.origin}/register?agent=${agentInfo.value.agent_code}`
  }
}

const copyPromotionLink = async () => {
  try {
    await navigator.clipboard.writeText(promotionLink.value)
    ElMessage.success('推广链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const generateQRCode = async () => {
  try {
    qrCodeDialogVisible.value = true
    await nextTick()
    
    const canvas = await QRCode.toCanvas(qrCodeRef.value, promotionLink.value, {
      width: 200,
      margin: 2
    })
  } catch (error) {
    ElMessage.error('生成二维码失败')
  }
}

const downloadQRCode = () => {
  const canvas = qrCodeRef.value.querySelector('canvas')
  if (canvas) {
    const link = document.createElement('a')
    link.download = `推广二维码-${agentInfo.value.agent_code}.png`
    link.href = canvas.toDataURL()
    link.click()
  }
}

const refreshActivities = () => {
  loadRecentActivities()
  ElMessage.success('动态已刷新')
}

// 导航方法
const goToPromotionTools = () => {
  // 跳转到推广工具页面
  router.push('/admin/promotion/links')
}

const goToTeamManagement = () => {
  // 跳转到团队管理页面
  router.push('/admin/agents/hierarchy')
}

const goToCommissionCenter = () => {
  // 跳转到佣金中心
  router.push('/admin/agents/commission')
}

const goToTrainingCenter = () => {
  // 跳转到培训中心
  router.push('/admin/agents/list')
}

const goToPerformanceAnalysis = () => {
  // 跳转到绩效分析
  router.push('/admin/agents/performance')
}

const goToSettings = () => {
  // 跳转到设置页面
  router.push('/admin/system/settings')
}

// 工具方法
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getMemberTypeColor = (type) => {
  const colors = {
    'individual': 'primary',
    'enterprise': 'success',
    'channel': 'warning'
  }
  return colors[type] || 'info'
}

const getActivityColor = (type) => {
  const colors = {
    'commission': '#67C23A',
    'user': '#409EFF',
    'team': '#E6A23C'
  }
  return colors[type] || '#909399'
}

const getActivityIcon = (type) => {
  const icons = {
    'commission': 'Money',
    'user': 'User',
    'team': 'Connection'
  }
  return icons[type] || 'InfoFilled'
}

// 刷新所有数据
const refreshAllData = async () => {
  try {
    await Promise.all([
      loadAgentInfo(),
      loadStats(),
      loadCommissionTrend(),
      loadTeamData(),
      loadRecentActivities()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

// 模拟加载统计数据
const loadMockStats = async () => {
  try {
    statsLoading.value = true
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟统计数据
    const mockStats = {
      total_users: 234,
      total_commission: 45670.89,
      monthly_commission: 8950.50,
      child_agents_count: 12
    }
    
    stats.value = mockStats
    
    // 更新统计卡片数据
    agentStatCards.value[0].value = mockStats.total_users.toString()
    agentStatCards.value[1].value = '¥' + mockStats.total_commission.toLocaleString()
    agentStatCards.value[2].value = '¥' + mockStats.monthly_commission.toLocaleString()
    agentStatCards.value[3].value = mockStats.child_agents_count.toString()
    
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadMockStats() // 使用模拟数据
  loadAgentInfo()
  loadCommissionTrend()
  loadTeamData()
  loadRecentActivities()
})
</script>

<style lang="scss" scoped>
.modern-agent-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  // 页面头部样式
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 12px;
        
        .agent-badges {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .agent-tag {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
          }
          
          .agent-code {
            color: #909399;
            font-size: 13px;
            font-weight: 500;
          }
        }
        
        .action-buttons {
          display: flex;
          gap: 12px;
          
          .action-btn {
            height: 36px;
            padding: 0 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &.secondary {
              background: #f5f7fa;
              border-color: #dcdfe6;
              color: #606266;
              
              &:hover {
                background: #ecf5ff;
                border-color: #409eff;
                color: #409eff;
              }
            }
            
            &.primary {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border: none;
              
              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
              }
            }
          }
        }
      }
    }
  }

  // 统计卡片区域
  .stats-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 快捷操作卡片
  .quick-actions-card {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;
      font-weight: 600;
      color: #303133;
    }
    
    :deep(.el-card__body) {
      padding: 24px;
    }
    
    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px 16px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #e4e7ed;
      background: white;
      
      &:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
        border-color: #667eea;
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
      }
      
      .action-icon {
        font-size: 28px;
        color: #667eea;
        margin-bottom: 12px;
        transition: all 0.3s ease;
      }
      
      span {
        font-weight: 500;
        color: #303133;
        font-size: 14px;
      }
    }
  }

  // 其他卡片样式
  :deep(.el-card) {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .el-card__header {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        span {
          font-weight: 600;
          color: #303133;
          font-size: 16px;
        }
      }
    }
    
    .el-card__body {
      padding: 24px;
    }
  }

  // 图表行样式
  .charts-row {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
  }

  // 信息行样式
  .info-row {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
  }

  // 推广卡片样式
  .promotion-card {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
  }

  // 团队概览样式
  .team-overview {
    .team-stats {
      display: flex;
      justify-content: space-around;
      margin-bottom: 20px;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-radius: 12px;
      border: 1px solid #e4e7ed;
      
      .team-stat-item {
        text-align: center;
        
        .label {
          display: block;
          color: #909399;
          font-size: 13px;
          margin-bottom: 8px;
          font-weight: 500;
        }
        
        .value {
          display: block;
          color: #303133;
          font-size: 24px;
          font-weight: 700;
        }
      }
    }
    
    .recent-members {
      h4 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
      
      .member-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f2f5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .member-info {
          flex: 1;
          margin-left: 12px;
          
          .member-name {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .member-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }

  // 活动列表样式
  .activities-list {
    max-height: 350px;
    overflow-y: auto;
    
    .activity-item {
      display: flex;
      align-items: flex-start;
      padding: 16px 0;
      border-bottom: 1px solid #f0f2f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .activity-icon {
        margin-right: 12px;
        margin-top: 2px;
      }
      
      .activity-content {
        flex: 1;
        
        .activity-title {
          font-weight: 600;
          color: #303133;
          margin-bottom: 6px;
        }
        
        .activity-desc {
          color: #606266;
          font-size: 14px;
          margin-bottom: 6px;
          line-height: 1.4;
        }
        
        .activity-time {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }

  // 二维码对话框
  .qr-code-container {
    text-align: center;
    
    .qr-code {
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
    }
    
    .qr-code-tips {
      p {
        color: #606266;
        margin-bottom: 15px;
        font-size: 14px;
      }
    }
  }
}

  /* 帮助对话框样式 */
  :deep(.help-dialog) {
    .el-dialog__body {
      padding: 20px;
      max-height: 70vh;
      overflow-y: auto;
    }
  }

  .help-content {
    .help-section {
      margin-bottom: 30px;
      
      h3 {
        color: #303133;
        margin-bottom: 15px;
        font-size: 18px;
        border-bottom: 2px solid #667eea;
        padding-bottom: 8px;
      }
      
      p {
        color: #606266;
        line-height: 1.6;
        margin-bottom: 15px;
      }
    }
    
    .feature-item {
      display: flex;
      align-items: flex-start;
      padding: 20px;
      border: 1px solid #e4e7ed;
      border-radius: 12px;
      margin-bottom: 16px;
      transition: all 0.3s ease;
      background: white;
      
      &:hover {
        border-color: #667eea;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
        transform: translateY(-2px);
      }
      
      .feature-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        flex-shrink: 0;
        
        .el-icon {
          color: white;
          font-size: 20px;
        }
      }
      
      .feature-content {
        flex: 1;
        
        h4 {
          margin: 0 0 8px 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
        }
        
        p {
          margin: 0;
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
    
    .commission-rules {
      .rule-item {
        margin-bottom: 25px;
        padding: 24px;
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
        border-radius: 12px;
        border-left: 4px solid #667eea;
        
        h4 {
          margin: 0 0 12px 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
        }
        
        p {
          margin: 0 0 12px 0;
          color: #606266;
          line-height: 1.6;
        }
        
        .example {
          padding: 12px 16px;
          background: rgba(102, 126, 234, 0.1);
          border-radius: 8px;
          font-size: 14px;
          
          .example-label {
            font-weight: 600;
            color: #667eea;
          }
        }
      }
    }
    
    .tips-content {
      h4 {
        color: #303133;
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
      }
      
      ul {
        margin: 0 0 16px 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          color: #606266;
          line-height: 1.6;
          
          strong {
            color: #303133;
            font-weight: 600;
          }
        }
      }
      
      .script-item {
        margin-bottom: 20px;
        
        h5 {
          margin: 0 0 10px 0;
          color: #667eea;
          font-size: 14px;
          font-weight: 600;
        }
        
        .script-text {
          padding: 16px;
          background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
          border-radius: 8px;
          border-left: 3px solid #667eea;
          font-size: 14px;
          line-height: 1.6;
          color: #606266;
        }
      }
    }
    
    .guide-content {
      padding: 20px;
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-radius: 12px;
      
      ol, ul {
        margin: 0 0 16px 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          color: #606266;
          line-height: 1.5;
        }
      }
      
      :deep(.el-alert) {
        margin-top: 16px;
        border-radius: 8px;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .modern-agent-dashboard {
    .stats-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .modern-agent-dashboard {
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        align-items: center;
        
        .agent-badges {
          justify-content: center;
          flex-wrap: wrap;
        }
        
        .action-buttons {
          justify-content: center;
        }
      }
    }
    
    .stats-container {
      grid-template-columns: 1fr;
    }
    
    .help-content .feature-item {
      flex-direction: column;
      text-align: center;
      
      .feature-icon {
        margin: 0 0 15px 0;
      }
    }
  }
}
</style>
</style>