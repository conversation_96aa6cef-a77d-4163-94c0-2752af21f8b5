const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ModernRichTextEditor-yvw2xTms.js","assets/vue-vendor-BcnDv-68.js","assets/element-plus-C2UshkXo.js","assets/index-eUTsTR3J.js","assets/utils-SdQ7DxjY.js","assets/echarts-D6CUuNS9.js","assets/index-XVEiIDg_.css","assets/ModernRichTextEditor-DMFQXIpc.css","assets/MediaUploader-CWV5P0gW.js","assets/MediaUploader-ByO6pya-.css","assets/base-pYMXRPpM.css","assets/el-overlay-CcMgTIy5.css","assets/el-upload-q8uObtwj.css","assets/el-progress-Dw9yTa91.css","assets/el-button-CDqfIFiK.css"])))=>i.map(i=>d[i]);
import{_ as e,a as t}from"./index-eUTsTR3J.js";/* empty css             *//* empty css                  *//* empty css                        *//* empty css                  *//* empty css                         *//* empty css                    *//* empty css                     *//* empty css                 */import{l,m as o,q as a,F as d,Y as n,G as i,A as u,z as p,D as s,C as c,E as r,r as m,w as V,aj as _,c as v}from"./vue-vendor-BcnDv-68.js";import{ah as g,Z as k,a1 as f,a4 as y,a3 as h,aV as U,aU as C,aI as b,aJ as x,aH as j,av as w,bt as I,by as B,bx as D,ad as P,b2 as T,aa as A,at as M,aE as E,af as q}from"./element-plus-C2UshkXo.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const R={class:"paid-content-editor"},z={class:"content-blocks"},F={class:"block-header"},L={class:"block-type-info"},O={class:"block-type-name"},S={class:"block-actions"},G={class:"block-content"},N={key:0,class:"richtext-editor"},Y={key:1,class:"images-editor"},H={key:2,class:"documents-editor"},J={key:3,class:"video-editor"},Q={key:4,class:"links-editor"},W={key:5,class:"qrcode-editor"},X={class:"add-block-section"};const Z=e({__name:"PaidContentEditor",props:{modelValue:{type:Array,default:()=>[]}},emits:["update:modelValue","change"],setup(e,{expose:l,emit:o}){l();const a=_({loader:()=>t(()=>import("./ModernRichTextEditor-yvw2xTms.js"),__vite__mapDeps([0,1,2,3,4,5,6,7])),errorComponent:{template:'<div class="component-error">富文本编辑器加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),d=_({loader:()=>t(()=>import("./MediaUploader-CWV5P0gW.js"),__vite__mapDeps([8,1,2,3,4,5,6,9,10,11,12,13,14])),errorComponent:{template:'<div class="component-error">媒体上传器加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),n=e,i=o,u=m([...n.modelValue]);V(()=>n.modelValue,e=>{u.value=[...e]},{deep:!0}),V(u,e=>{i("update:modelValue",e),i("change",e)},{deep:!0});const p=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),s={ModernRichTextEditor:a,MediaUploader:d,props:n,emit:i,contentBlocks:u,generateId:p,addBlock:e=>{const t={id:p(),type:e,title:"",content:"",description:"",videoType:"upload"};u.value.push(t)},removeBlock:e=>{u.value.splice(e,1)},moveBlock:(e,t)=>{const l=e+t;if(l>=0&&l<u.value.length){const t=u.value.splice(e,1)[0];u.value.splice(l,0,t)}},updateContent:()=>{i("update:modelValue",[...u.value]),i("change",[...u.value])},getBlockIcon:e=>({richtext:T,images:P,documents:D,video:B,links:I,qrcode:w}[e]||T),getBlockTypeName:e=>({richtext:"富文本内容",images:"图片内容",documents:"文档内容",video:"视频内容",links:"网站链接",qrcode:"二维码"}[e]||"未知类型"),ref:m,computed:v,watch:V,defineAsyncComponent:_,get Plus(){return q},get ArrowDown(){return E},get ArrowUp(){return M},get Delete(){return A},get Document(){return T},get Picture(){return P},get Folder(){return D},get VideoPlay(){return B},get Link(){return I},get Grid(){return w}};return Object.defineProperty(s,"__isScriptSetup",{enumerable:!1,value:!0}),s}},[["render",function(e,t,m,V,_,v){const w=g,I=f,B=y,D=h,P=U,T=C,A=x,M=b,E=j;return o(),l("div",R,[a("div",z,[(o(!0),l(d,null,n(V.contentBlocks,(e,d)=>(o(),l("div",{key:e.id,class:"content-block"},[a("div",F,[a("div",L,[i(w,null,{default:u(()=>[(o(),p(s(V.getBlockIcon(e.type))))]),_:2},1024),a("span",O,k(V.getBlockTypeName(e.type)),1)]),a("div",S,[d>0?(o(),p(I,{key:0,onClick:e=>V.moveBlock(d,-1),size:"small",type:"text",icon:V.ArrowUp},null,8,["onClick","icon"])):c("",!0),d<V.contentBlocks.length-1?(o(),p(I,{key:1,onClick:e=>V.moveBlock(d,1),size:"small",type:"text",icon:V.ArrowDown},null,8,["onClick","icon"])):c("",!0),i(I,{onClick:e=>V.removeBlock(d),size:"small",type:"text",icon:V.Delete,class:"delete-btn"},null,8,["onClick","icon"])])]),a("div",G,["richtext"===e.type?(o(),l("div",N,[i(V.ModernRichTextEditor,{modelValue:e.content,"onUpdate:modelValue":t=>e.content=t,height:200,placeholder:"请输入富文本内容...",onChange:V.updateContent},null,8,["modelValue","onUpdate:modelValue"])])):"images"===e.type?(o(),l("div",Y,[i(V.MediaUploader,{modelValue:e.content,"onUpdate:modelValue":t=>e.content=t,type:"image",limit:9,accept:"image/*","list-type":"picture-card",onChange:V.updateContent},{tip:u(()=>[...t[0]||(t[0]=[a("div",{class:"upload-tip"},"支持上传多张图片，最多9张",-1)])]),_:2},1032,["modelValue","onUpdate:modelValue"])])):"documents"===e.type?(o(),l("div",H,[i(D,{label:"文档标题"},{default:u(()=>[i(B,{modelValue:e.title,"onUpdate:modelValue":t=>e.title=t,placeholder:"请输入文档标题",onInput:V.updateContent},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(V.MediaUploader,{modelValue:e.content,"onUpdate:modelValue":t=>e.content=t,type:"file",limit:5,accept:".pdf,.doc,.docx,.txt",onChange:V.updateContent},{tip:u(()=>[...t[1]||(t[1]=[a("div",{class:"upload-tip"},"支持PDF、Word、TXT格式，最多5个文件",-1)])]),_:2},1032,["modelValue","onUpdate:modelValue"])])):"video"===e.type?(o(),l("div",J,[i(D,{label:"视频标题"},{default:u(()=>[i(B,{modelValue:e.title,"onUpdate:modelValue":t=>e.title=t,placeholder:"请输入视频标题",onInput:V.updateContent},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(T,{modelValue:e.videoType,"onUpdate:modelValue":t=>e.videoType=t,onTabChange:V.updateContent},{default:u(()=>[i(P,{label:"上传视频",name:"upload"},{default:u(()=>[i(V.MediaUploader,{modelValue:e.content,"onUpdate:modelValue":t=>e.content=t,type:"video",limit:1,accept:"video/*",onChange:V.updateContent},{tip:u(()=>[...t[2]||(t[2]=[a("div",{class:"upload-tip"},"支持MP4、AVI等格式，建议大小不超过100MB",-1)])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(P,{label:"嵌入链接",name:"embed"},{default:u(()=>[i(B,{modelValue:e.content,"onUpdate:modelValue":t=>e.content=t,type:"textarea",rows:3,placeholder:"请输入视频嵌入代码或链接",onInput:V.updateContent},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1032,["modelValue","onUpdate:modelValue"])])):"links"===e.type?(o(),l("div",Q,[i(D,{label:"链接标题"},{default:u(()=>[i(B,{modelValue:e.title,"onUpdate:modelValue":t=>e.title=t,placeholder:"请输入链接标题",onInput:V.updateContent},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(D,{label:"链接地址"},{default:u(()=>[i(B,{modelValue:e.content,"onUpdate:modelValue":t=>e.content=t,placeholder:"https://",onInput:V.updateContent},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(D,{label:"链接描述"},{default:u(()=>[i(B,{modelValue:e.description,"onUpdate:modelValue":t=>e.description=t,type:"textarea",rows:2,placeholder:"请输入链接描述（可选）",onInput:V.updateContent},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)])):"qrcode"===e.type?(o(),l("div",W,[i(D,{label:"二维码标题"},{default:u(()=>[i(B,{modelValue:e.title,"onUpdate:modelValue":t=>e.title=t,placeholder:"请输入二维码标题",onInput:V.updateContent},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(D,{label:"二维码描述"},{default:u(()=>[i(B,{modelValue:e.description,"onUpdate:modelValue":t=>e.description=t,type:"textarea",rows:2,placeholder:"请输入二维码描述",onInput:V.updateContent},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(V.MediaUploader,{modelValue:e.content,"onUpdate:modelValue":t=>e.content=t,type:"image",limit:1,accept:"image/*","list-type":"picture-card",onChange:V.updateContent},{tip:u(()=>[...t[3]||(t[3]=[a("div",{class:"upload-tip"},"上传二维码图片",-1)])]),_:2},1032,["modelValue","onUpdate:modelValue"])])):c("",!0)])]))),128))]),a("div",X,[i(E,{onCommand:V.addBlock,trigger:"click"},{dropdown:u(()=>[i(M,null,{default:u(()=>[i(A,{command:"richtext"},{default:u(()=>[i(w,null,{default:u(()=>[i(V.Document)]),_:1}),t[5]||(t[5]=r(" 富文本内容 ",-1))]),_:1,__:[5]}),i(A,{command:"images"},{default:u(()=>[i(w,null,{default:u(()=>[i(V.Picture)]),_:1}),t[6]||(t[6]=r(" 图片内容 ",-1))]),_:1,__:[6]}),i(A,{command:"documents"},{default:u(()=>[i(w,null,{default:u(()=>[i(V.Folder)]),_:1}),t[7]||(t[7]=r(" 文档内容 ",-1))]),_:1,__:[7]}),i(A,{command:"video"},{default:u(()=>[i(w,null,{default:u(()=>[i(V.VideoPlay)]),_:1}),t[8]||(t[8]=r(" 视频内容 ",-1))]),_:1,__:[8]}),i(A,{command:"links"},{default:u(()=>[i(w,null,{default:u(()=>[i(V.Link)]),_:1}),t[9]||(t[9]=r(" 网站链接 ",-1))]),_:1,__:[9]}),i(A,{command:"qrcode"},{default:u(()=>[i(w,null,{default:u(()=>[i(V.Grid)]),_:1}),t[10]||(t[10]=r(" 二维码 ",-1))]),_:1,__:[10]})]),_:1})]),default:u(()=>[i(I,{type:"primary",icon:V.Plus},{default:u(()=>[t[4]||(t[4]=r(" 添加内容块 ",-1)),i(w,{class:"el-icon--right"},{default:u(()=>[i(V.ArrowDown)]),_:1})]),_:1,__:[4]},8,["icon"])]),_:1})])])}],["__scopeId","data-v-6f940d23"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/PaidContentEditor.vue"]]);export{Z as default};
