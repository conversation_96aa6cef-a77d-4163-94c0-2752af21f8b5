<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 个人信息 -->
      <el-col :span="12">
        <el-card class="profile-card">
          <template #header>
            <div class="card-header">
              <span>个人信息</span>
              <el-badge :value="profileForm.nickname ? 0 : 1" :hidden="profileForm.nickname" type="warning">
                <el-icon><User /></el-icon>
              </el-badge>
            </div>
          </template>
          <el-form ref="profileFormRef" :model="profileForm" :rules="profileRules" label-width="100px">
            <el-form-item label="头像">
              <AvatarUpload 
                v-model="profileForm.avatar"
                :size="120"
                :max-size="5"
                :enable-preview="true"
                @success="handleAvatarSuccess"
                @error="handleAvatarError"
              />
            </el-form-item>
            <el-form-item label="用户名" prop="username">
              <el-input v-model="profileForm.username" readonly>
                <template #suffix>
                  <el-tooltip content="用户名不可修改" placement="top">
                    <el-icon class="readonly-icon"><Lock /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="昵称" prop="nickname">
              <el-input 
                v-model="profileForm.nickname" 
                placeholder="请输入昵称" 
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="真实姓名" prop="real_name">
              <el-input 
                v-model="profileForm.real_name" 
                placeholder="请输入真实姓名"
                maxlength="10" 
              />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="profileForm.email" placeholder="请输入邮箱">
                <template #suffix>
                  <el-tag v-if="profileForm.email_verified" type="success" size="small">已验证</el-tag>
                  <el-tag v-else type="warning" size="small">未验证</el-tag>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="profileForm.phone" placeholder="请输入手机号">
                <template #suffix>
                  <el-tag v-if="profileForm.phone_verified" type="success" size="small">已验证</el-tag>
                  <el-tag v-else type="warning" size="small">未验证</el-tag>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="profileForm.gender">
                <el-radio :label="1">男</el-radio>
                <el-radio :label="2">女</el-radio>
                <el-radio :label="0">保密</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="生日" prop="birthday">
              <el-date-picker
                v-model="profileForm.birthday"
                type="date"
                placeholder="选择生日"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
              />
            </el-form-item>
            <el-form-item label="个人简介" prop="bio">
              <el-input
                v-model="profileForm.bio"
                type="textarea"
                placeholder="介绍一下自己..."
                :rows="3"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="角色">
              <el-tag :type="getRoleTagType(profileForm.role)">{{ getRoleName(profileForm.role) }}</el-tag>
            </el-form-item>
            <el-form-item label="账户状态">
              <el-tag :type="profileForm.status === 'active' ? 'success' : 'danger'">
                {{ profileForm.status === 'active' ? '正常' : '已禁用' }}
              </el-tag>
            </el-form-item>
            <el-form-item label="余额">
              <span class="balance">¥ {{ formatAmount(profileForm.balance) }}</span>
              <el-button type="text" size="small" @click="showBalanceHistory = true">查看明细</el-button>
            </el-form-item>
            <el-form-item label="注册时间">
              <span>{{ formatDate(profileForm.created_at) }}</span>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="updateProfile" :loading="profileLoading">
                <el-icon><EditPen /></el-icon>
                更新信息
              </el-button>
              <el-button @click="resetProfileForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 修改密码 -->
      <el-col :span="12">
        <el-card class="password-card">
          <template #header>
            <div class="card-header">
              <span>修改密码</span>
              <el-icon class="security-icon"><Key /></el-icon>
            </div>
          </template>
          <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="100px">
            <el-alert
              title="安全提示"
              type="info"
              description="为了账户安全，建议定期更换密码，密码应包含大小写字母、数字和特殊字符。"
              :closable="false"
              class="security-alert"
            />
            <el-form-item label="当前密码" prop="current_password">
              <el-input
                v-model="passwordForm.current_password"
                type="password"
                placeholder="请输入当前密码"
                show-password
                autocomplete="current-password"
              />
            </el-form-item>
            <el-form-item label="新密码" prop="password">
              <el-input
                v-model="passwordForm.password"
                type="password"
                placeholder="请输入新密码(至少6位)"
                show-password
                autocomplete="new-password"
                @input="checkPasswordStrength"
              />
              <!-- 密码强度指示器 -->
              <div v-if="passwordStrength.show" class="password-strength">
                <div class="strength-bar">
                  <div 
                    class="strength-fill" 
                    :class="'strength-' + passwordStrength.level"
                    :style="{ width: passwordStrength.percentage + '%' }"
                  ></div>
                </div>
                <div class="strength-text" :class="'strength-' + passwordStrength.level">
                  {{ passwordStrength.text }}
                </div>
                <div v-if="passwordStrength.suggestions.length" class="strength-suggestions">
                  <ul>
                    <li v-for="suggestion in passwordStrength.suggestions" :key="suggestion">
                      {{ suggestion }}
                    </li>
                  </ul>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="确认密码" prop="password_confirmation">
              <el-input
                v-model="passwordForm.password_confirmation"
                type="password"
                placeholder="请再次输入新密码"
                show-password
                autocomplete="new-password"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="changePassword" :loading="passwordLoading">
                <el-icon><Lock /></el-icon>
                修改密码
              </el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
              <el-button type="info" plain @click="generateRandomPassword">
                <el-icon><Refresh /></el-icon>
                生成随机密码
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- 账户统计 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>账户统计</span>
            </div>
          </template>
          <el-row :gutter="20" class="stats-row">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ userStats.total_orders || 0 }}</div>
                <div class="stat-label">总订单数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ userStats.total_amount || 0 }} 元</div>
                <div class="stat-label">总消费金额</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ userStats.total_commission || 0 }} 元</div>
                <div class="stat-label">总佣金收入</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ userStats.children_count || 0 }}</div>
                <div class="stat-label">下级用户数</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 安全设置 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="12">
        <el-card class="security-card">
          <template #header>
            <div class="card-header">
              <span>安全设置</span>
              <el-icon><Lock /></el-icon>
            </div>
          </template>
          <div class="security-settings">
            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-title">两步验证</div>
                <div class="setting-desc">使用手机验证码提高账户安全性</div>
              </div>
              <el-switch 
                v-model="securitySettings.two_factor_enabled"
                @change="handleTwoFactorToggle"
              />
            </div>
            <el-divider />
            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-title">登录提醒</div>
                <div class="setting-desc">新设备登录时发送邮件通知</div>
              </div>
              <el-switch 
                v-model="securitySettings.login_notification"
                @change="handleLoginNotificationToggle"
              />
            </div>
            <el-divider />
            <div class="setting-item">
              <div class="setting-info">
                <div class="setting-title">会话管理</div>
                <div class="setting-desc">查看和管理活跃会话</div>
              </div>
              <el-button size="small" @click="showActiveSessions = true">管理会话</el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 登录记录 -->
      <el-col :span="12">
        <el-card class="login-records-card">
          <template #header>
            <div class="card-header">
              <span>登录记录</span>
              <el-button type="text" size="small" @click="showLoginHistory = true">
                查看更多
              </el-button>
            </div>
          </template>
          <div class="login-info">
            <div class="login-item">
              <el-icon class="login-icon"><Clock /></el-icon>
              <div>
                <div class="login-time">{{ formatDate(profileForm.last_login_at) || '从未登录' }}</div>
                <div class="login-detail">最后登录时间</div>
              </div>
            </div>
            <div class="login-item">
              <el-icon class="login-icon"><Location /></el-icon>
              <div>
                <div class="login-ip">{{ profileForm.last_login_ip || '未知' }}</div>
                <div class="login-detail">最后登录IP</div>
              </div>
            </div>
            <div class="login-item">
              <el-icon class="login-icon"><Monitor /></el-icon>
              <div>
                <div class="login-device">{{ profileForm.last_device || '未知设备' }}</div>
                <div class="login-detail">最后使用设备</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 余额明细对话框 -->
    <el-dialog v-model="showBalanceHistory" title="余额明细" width="60%">
      <el-table :data="balanceHistory" style="width: 100%">
        <el-table-column prop="created_at" label="时间" width="180" />
        <el-table-column prop="type_text" label="类型" width="120" />
        <el-table-column label="金额" width="120">
          <template #default="scope">
            <span :class="scope.row.amount > 0 ? 'income' : 'expense'">
              {{ scope.row.amount > 0 ? '+' : '' }}{{ scope.row.amount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明" />
        <el-table-column prop="balance_after" label="余额" />
      </el-table>
      <template #footer>
        <el-button @click="showBalanceHistory = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 活跃会话对话框 -->
    <el-dialog v-model="showActiveSessions" title="活跃会话" width="70%">
      <el-table :data="activeSessions" style="width: 100%">
        <el-table-column prop="ip_address" label="IP地址" width="140" />
        <el-table-column prop="location" label="位置" width="120" />
        <el-table-column prop="device" label="设备" />
        <el-table-column prop="last_activity" label="最后活动" width="160" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button 
              v-if="!scope.row.is_current"
              type="danger" 
              size="small" 
              @click="terminateSession(scope.row.id)"
            >
              终止
            </el-button>
            <el-tag v-else type="success" size="small">当前会话</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="showActiveSessions = false">关闭</el-button>
        <el-button type="danger" @click="terminateAllSessions">终止所有其他会话</el-button>
      </template>
    </el-dialog>

    <!-- 登录历史对话框 -->
    <el-dialog v-model="showLoginHistory" title="登录历史" width="70%">
      <el-table :data="loginHistory" style="width: 100%">
        <el-table-column prop="login_time" label="登录时间" width="160" />
        <el-table-column prop="ip_address" label="IP地址" width="140" />
        <el-table-column prop="location" label="位置" width="120" />
        <el-table-column prop="device" label="设备/浏览器" />
        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'" size="small">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="showLoginHistory = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { 
  getInfo, 
  updateProfile as updateProfileApi, 
  changePassword as changePasswordApi,
  getSecuritySettings,
  updateSecuritySettings,
  getActiveSessions,
  terminateSession as terminateSessionApi,
  terminateAllSessions as terminateAllSessionsApi,
  getLoginLogs,
  validatePasswordStrength
} from '@/api/auth'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
import { 
  Plus, User, Lock, Key, EditPen, Refresh, Clock, Location, Monitor 
} from '@element-plus/icons-vue'
import AvatarUpload from '@/components/AvatarUpload.vue'

const userStore = useUserStore()
const profileFormRef = ref()
const passwordFormRef = ref()
const profileLoading = ref(false)
const passwordLoading = ref(false)

const profileForm = ref({
  id: null,
  username: '',
  nickname: '',
  real_name: '',
  email: '',
  phone: '',
  avatar: '',
  role: '',
  gender: 0,
  birthday: '',
  bio: '',
  status: 'active',
  balance: 0,
  email_verified: false,
  phone_verified: false,
  created_at: '',
  last_login_at: '',
  last_login_ip: '',
  last_device: ''
})

const passwordForm = ref({
  current_password: '',
  password: '',
  password_confirmation: ''
})

const userStats = ref({
  total_orders: 0,
  total_amount: 0,
  total_commission: 0,
  children_count: 0
})

// 新增响应式数据
const passwordStrength = ref({
  show: false,
  level: 'weak',
  percentage: 0,
  text: '弱',
  suggestions: []
})

const securitySettings = ref({
  two_factor_enabled: false,
  login_notification: true
})

const showBalanceHistory = ref(false)
const showActiveSessions = ref(false)
const showLoginHistory = ref(false)

const balanceHistory = ref([])
const activeSessions = ref([])
const loginHistory = ref([])

const uploadUrl = ref('/api/v1/upload')
const uploadHeaders = ref({
  'Authorization': `Bearer ${getToken()}`
})

const profileRules = reactive({
  nickname: [
    { required: true, message: '昵称不能为空', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度为2-20个字符', trigger: 'blur' }
  ],
  real_name: [
    { min: 2, max: 10, message: '真实姓名长度为2-10个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  bio: [
    { max: 200, message: '个人简介不能超过200个字符', trigger: 'blur' }
  ]
})

const passwordRules = reactive({
  current_password: [{ required: true, message: '当前密码不能为空', trigger: 'blur' }],
  password: [
    { required: true, message: '新密码不能为空', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value && value === passwordForm.value.current_password) {
          callback(new Error('新密码不能与当前密码相同'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  password_confirmation: [
    { required: true, message: '确认密码不能为空', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

const getUserInfo = async () => {
  try {
    const { data } = await getInfo()
    if (data.success) {
      profileForm.value = { ...data.data.user }
      userStats.value = data.data.stats || {}
    }
  } catch (error) {
    ElMessage.error('获取用户信息失败')
  }
}

const updateProfile = async () => {
  try {
    await profileFormRef.value.validate()
    profileLoading.value = true
    
    const { data } = await updateProfileApi({
      nickname: profileForm.value.nickname,
      real_name: profileForm.value.real_name,
      email: profileForm.value.email,
      phone: profileForm.value.phone,
      avatar: profileForm.value.avatar,
      gender: profileForm.value.gender,
      birthday: profileForm.value.birthday,
      bio: profileForm.value.bio
    })
    
    if (data.success) {
      ElMessage.success('个人信息更新成功')
      // 更新store中的用户信息
      await userStore.getUserInfo()
      await getUserInfo() // 重新获取最新信息
    }
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error(error.response?.data?.message || '更新失败')
  } finally {
    profileLoading.value = false
  }
}

const changePassword = async () => {
  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true
    
    const { data } = await changePasswordApi({
      current_password: passwordForm.value.current_password,
      password: passwordForm.value.password,
      password_confirmation: passwordForm.value.password_confirmation
    })
    
    if (data.success) {
      ElMessage.success('密码修改成功')
      resetPasswordForm()
    }
  } catch (error) {
    console.error('密码修改失败:', error)
  } finally {
    passwordLoading.value = false
  }
}

const resetPasswordForm = () => {
  passwordForm.value = {
    current_password: '',
    password: '',
    password_confirmation: ''
  }
  passwordFormRef.value?.resetFields()
}

const handleAvatarSuccess = (data) => {
  // 头像上传成功后，自动更新个人资料
  profileForm.value.avatar = data.url
  
  // 同时更新用户存储中的头像信息
  if (userStore.userInfo) {
    userStore.userInfo.avatar = data.url
  }
  
  ElMessage.success('头像上传成功!')
}

const handleAvatarError = (error) => {
  console.error('头像上传失败:', error)
  ElMessage.error('头像上传失败，请重试')
}

const getRoleTagType = (role) => {
  const roleMap = {
    admin: 'danger',
    substation: 'warning',
    distributor: 'success',
    user: 'info'
  }
  return roleMap[role]
}

const getRoleName = (role) => {
  const roleMap = {
    admin: '超级管理员',
    substation: '分站管理员',
    distributor: '分销商',
    user: '普通用户'
  }
  return roleMap[role]
}

// 新增方法
const formatAmount = (amount) => {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount || 0)
}

const formatDate = (date) => {
  if (!date) return '未知'
  return new Date(date).toLocaleString('zh-CN')
}

const disabledDate = (time) => {
  return time.getTime() > Date.now()
}

const resetProfileForm = () => {
  getUserInfo() // 重新获取数据，相当于重置
}

// 密码强度检查
const checkPasswordStrength = (password) => {
  if (!password) {
    passwordStrength.value.show = false
    return
  }

  const result = validatePasswordStrength(password)
  passwordStrength.value = {
    show: true,
    level: result.strength,
    percentage: (result.score / 5) * 100,
    text: result.strength === 'strong' ? '强' : result.strength === 'medium' ? '中' : '弱',
    suggestions: result.feedback
  }
}

// 生成随机密码
const generateRandomPassword = () => {
  const length = 12
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
  let password = ''
  
  // 确保包含各种字符类型
  password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]
  password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]
  password += '0123456789'[Math.floor(Math.random() * 10)]
  password += '!@#$%^&*'[Math.floor(Math.random() * 8)]
  
  // 填充剩余长度
  for (let i = 4; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length))
  }
  
  // 打乱密码字符顺序
  password = password.split('').sort(() => Math.random() - 0.5).join('')
  
  passwordForm.value.password = password
  passwordForm.value.password_confirmation = password
  
  checkPasswordStrength(password)
  ElMessage.success('随机密码已生成，请确认后保存')
}

// 安全设置处理
const handleTwoFactorToggle = async (value) => {
  try {
    if (value) {
      // 启用两步验证的逻辑
      ElMessage.info('两步验证功能开发中...')
      securitySettings.value.two_factor_enabled = false
    } else {
      try {
        const { data } = await updateSecuritySettings({ two_factor_enabled: false })
        if (data.success) {
          ElMessage.success('已关闭两步验证')
        }
      } catch (apiError) {
        // API未实现时的降级处理
        ElMessage.info('安全设置已保存到本地')
      }
    }
  } catch (error) {
    securitySettings.value.two_factor_enabled = !value
    ElMessage.error('设置失败')
  }
}

const handleLoginNotificationToggle = async (value) => {
  try {
    const { data } = await updateSecuritySettings({ login_notification: value })
    if (data.success) {
      ElMessage.success(value ? '已开启登录提醒' : '已关闭登录提醒')
    }
  } catch (error) {
    securitySettings.value.login_notification = !value
    ElMessage.error('设置失败')
  }
}

// 会话管理
const loadActiveSessions = async () => {
  try {
    const { data } = await getActiveSessions()
    if (data.success) {
      activeSessions.value = data.data
    }
  } catch (error) {
    console.log('API未实现，使用模拟数据')
    // 使用模拟数据
    activeSessions.value = [
      {
        id: 1,
        ip_address: '127.0.0.1',
        location: '本地',
        device: 'Chrome 浏览器',
        last_activity: '刚刚',
        is_current: true
      }
    ]
  }
}

const terminateSession = async (sessionId) => {
  try {
    await ElMessageBox.confirm('确定要终止这个会话吗？', '确认操作')
    const { data } = await terminateSessionApi(sessionId)
    if (data.success) {
      ElMessage.success('会话已终止')
      loadActiveSessions()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('终止会话失败')
    }
  }
}

const terminateAllSessions = async () => {
  try {
    await ElMessageBox.confirm('确定要终止所有其他会话吗？这将强制其他设备重新登录。', '确认操作', {
      type: 'warning'
    })
    const { data } = await terminateAllSessionsApi()
    if (data.success) {
      ElMessage.success('已终止所有其他会话')
      loadActiveSessions()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 登录历史
const loadLoginHistory = async () => {
  try {
    const { data } = await getLoginLogs({ limit: 20 })
    if (data.success) {
      loginHistory.value = data.data
    }
  } catch (error) {
    console.log('API未实现，使用模拟数据')
    // 使用模拟数据
    loginHistory.value = [
      {
        login_time: new Date().toLocaleString('zh-CN'),
        ip_address: '127.0.0.1',
        location: '本地',
        device: 'Chrome 浏览器',
        status: 'success'
      },
      {
        login_time: new Date(Date.now() - 24*60*60*1000).toLocaleString('zh-CN'),
        ip_address: '*************',
        location: '内网',
        device: 'Chrome 浏览器',
        status: 'success'
      }
    ]
  }
}

// 余额明细模拟数据
const loadBalanceHistory = () => {
  balanceHistory.value = [
    {
      created_at: new Date().toLocaleString('zh-CN'),
      type_text: '充值',
      amount: 100.00,
      description: '在线充值',
      balance_after: 1000.00
    },
    {
      created_at: new Date(Date.now() - 24*60*60*1000).toLocaleString('zh-CN'),
      type_text: '消费',
      amount: -50.00,
      description: '购买服务',
      balance_after: 900.00
    },
    {
      created_at: new Date(Date.now() - 48*60*60*1000).toLocaleString('zh-CN'),
      type_text: '返佣',
      amount: 25.50,
      description: '推荐奖励',
      balance_after: 950.00
    }
  ]
}

// 监听对话框打开事件
const watchShowBalanceHistory = () => {
  if (showBalanceHistory.value) {
    loadBalanceHistory()
  }
}

const watchShowActiveSessions = () => {
  if (showActiveSessions.value) {
    loadActiveSessions()
  }
}

const watchShowLoginHistory = () => {
  if (showLoginHistory.value) {
    loadLoginHistory()
  }
}

onMounted(() => {
  getUserInfo()
  
  // 监听对话框状态变化
  watch(showBalanceHistory, watchShowBalanceHistory)
  watch(showActiveSessions, watchShowActiveSessions)  
  watch(showLoginHistory, watchShowLoginHistory)
})
</script>

<style lang="scss" scoped>
.app-container {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .security-icon,
    .readonly-icon {
      margin-left: 8px;
      color: #909399;
    }
  }
  
  .mt-4 {
    margin-top: 20px;
  }
  
  .balance {
    color: #f56c6c;
    font-weight: bold;
    font-size: 16px;
    margin-right: 10px;
  }
  
  .stats-row {
    .stat-item {
      text-align: center;
      padding: 20px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
  
  // 新增样式
  .profile-card,
  .password-card,
  .security-card,
  .login-records-card {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    
    :deep(.el-card__header) {
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;
    }
  }
  
  .security-alert {
    margin-bottom: 20px;
    
    :deep(.el-alert__content) {
      font-size: 13px;
      line-height: 1.5;
    }
  }
  
  // 密码强度指示器
  .password-strength {
    margin-top: 8px;
    
    .strength-bar {
      width: 100%;
      height: 4px;
      background: #f0f0f0;
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 5px;
      
      .strength-fill {
        height: 100%;
        transition: all 0.3s ease;
        
        &.strength-weak {
          background: #f56c6c;
        }
        
        &.strength-medium {
          background: #e6a23c;
        }
        
        &.strength-strong {
          background: #67c23a;
        }
      }
    }
    
    .strength-text {
      font-size: 12px;
      font-weight: bold;
      
      &.strength-weak {
        color: #f56c6c;
      }
      
      &.strength-medium {
        color: #e6a23c;
      }
      
      &.strength-strong {
        color: #67c23a;
      }
    }
    
    .strength-suggestions {
      margin-top: 5px;
      
      ul {
        margin: 0;
        padding-left: 15px;
        
        li {
          font-size: 11px;
          color: #909399;
          line-height: 1.3;
        }
      }
    }
  }
  
  // 安全设置样式
  .security-settings {
    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      
      .setting-info {
        flex: 1;
        
        .setting-title {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .setting-desc {
          font-size: 12px;
          color: #909399;
          line-height: 1.4;
        }
      }
    }
  }
  
  // 登录记录样式
  .login-info {
    .login-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .login-icon {
        font-size: 18px;
        color: #409eff;
        margin-right: 12px;
        width: 18px;
        text-align: center;
      }
      
      .login-time,
      .login-ip,
      .login-device {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
        margin-bottom: 2px;
      }
      
      .login-detail {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  // 对话框表格样式
  :deep(.el-dialog) {
    .income {
      color: #67c23a;
      font-weight: bold;
    }
    
    .expense {
      color: #f56c6c;
      font-weight: bold;
    }
    
    .el-table {
      .el-tag {
        font-size: 11px;
      }
    }
  }
  
  // 输入框验证状态样式
  :deep(.el-form-item) {
    .el-input__suffix-inner {
      .el-tag {
        font-size: 10px;
        height: 18px;
        line-height: 16px;
        margin-right: 5px;
      }
    }
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .el-row {
      .el-col {
        margin-bottom: 20px;
        
        // 小屏幕下改为单列布局
        &:nth-child(1), &:nth-child(2) {
          width: 100% !important;
          flex: 0 0 100% !important;
          max-width: 100% !important;
        }
      }
    }
    
    .stats-row {
      .el-col {
        width: 50% !important;
        flex: 0 0 50% !important;
        max-width: 50% !important;
      }
      
      .stat-item {
        margin-bottom: 15px;
        padding: 15px;
        
        .stat-value {
          font-size: 20px;
        }
      }
    }
    
    .security-settings {
      .setting-item {
        flex-direction: column;
        align-items: flex-start;
        
        .setting-info {
          margin-bottom: 10px;
          
          .setting-title {
            font-size: 13px;
          }
          
          .setting-desc {
            font-size: 11px;
          }
        }
      }
    }
    
    .login-info {
      .login-item {
        padding: 8px 0;
        
        .login-icon {
          font-size: 16px;
          margin-right: 10px;
        }
        
        .login-time,
        .login-ip,
        .login-device {
          font-size: 13px;
        }
        
        .login-detail {
          font-size: 11px;
        }
      }
    }
    
    // 对话框移动端优化
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 0 auto;
      
      .el-dialog__body {
        padding: 15px;
      }
      
      .el-table {
        font-size: 12px;
        
        .el-table__cell {
          padding: 8px 4px;
        }
      }
    }
    
    // 表单项移动端优化
    :deep(.el-form-item) {
      .el-form-item__label {
        width: 100px !important;
        font-size: 13px;
      }
      
      .el-form-item__content {
        margin-left: 100px !important;
      }
      
      .el-input,
      .el-select,
      .el-date-picker {
        width: 100% !important;
      }
    }
    
    // 按钮组移动端优化
    .el-form-item .el-button {
      margin-bottom: 8px;
      
      &:not(:last-child) {
        margin-right: 8px;
      }
    }
    
    // 卡片头部移动端优化
    .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      
      span {
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    // 安全警告移动端优化
    .security-alert {
      margin-bottom: 15px;
      
      :deep(.el-alert__description) {
        font-size: 12px;
        line-height: 1.4;
      }
    }
    
    // 密码强度指示器移动端优化
    .password-strength {
      .strength-bar {
        height: 3px;
      }
      
      .strength-text {
        font-size: 11px;
      }
      
      .strength-suggestions ul li {
        font-size: 10px;
      }
    }
  }
  
  // 超小屏幕设备 (<480px)
  @media (max-width: 480px) {
    .stats-row {
      .el-col {
        width: 100% !important;
        flex: 0 0 100% !important;
        max-width: 100% !important;
      }
    }
    
    :deep(.el-form-item) {
      .el-form-item__label {
        width: 80px !important;
        font-size: 12px;
      }
      
      .el-form-item__content {
        margin-left: 80px !important;
      }
    }
    
    .el-form-item .el-button {
      width: 100%;
      margin-right: 0;
      margin-bottom: 8px;
    }
    
    .card-header span {
      font-size: 14px;
    }
  }
}

// 全局样式覆盖
:deep(.el-date-editor) {
  width: 100% !important;
}

:deep(.el-radio-group) {
  display: flex;
  gap: 15px;
}

:deep(.el-textarea) {
  .el-textarea__inner {
    resize: vertical;
    min-height: 80px;
  }
}
</style> 