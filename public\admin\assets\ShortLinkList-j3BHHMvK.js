/* empty css             *//* empty css                   *//* empty css                 *//* empty css                    *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css                       *//* empty css                  *//* empty css                */import{l as e,q as l,G as a,A as t,r as i,c as s,o,m as n,E as r,F as d,Y as u,z as c,B as p,C as m}from"./vue-vendor-BcnDv-68.js";import{e as _,f as h,h as g,i as f,j as y,k as v,s as k,l as b,m as w,n as C}from"./anti-block-CJ1NNk3N.js";import{_ as L,u as V}from"./index-eUTsTR3J.js";import{X as x,a1 as S,V as D,W as U,ag as j,a2 as z,a3 as T,a5 as F,a6 as R,a0 as q,a4 as M,Z as A,ai as B,aj as I,bf as $,ak as H,a7 as O,an as Q,aU as E,aV as N,aZ as P}from"./element-plus-C2UshkXo.js";import"./index-D4AyIzGN.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const Z={class:"short-link-list"},G={class:"page-header"},W={class:"page-actions"},X={slot:"header",class:"card-header"},Y={slot:"header",class:"card-header"},J={class:"short-code-info"},K={class:"short-code"},ee={class:"full-url-info"},le={class:"full-url"},ae={class:"original-url"},te={class:"domain-info"},ie={class:"domain-text"},se={class:"click-count"},oe={class:"pagination-wrapper"},ne={slot:"footer"},re={class:"switch-domain-content"},de={slot:"footer"},ue={class:"stats-content"},ce={class:"stats-header"},pe={class:"stats-summary"},me={class:"summary-item"},_e={class:"summary-value"},he={class:"summary-item"},ge={class:"summary-value"},fe={class:"summary-item"},ye={class:"summary-value"},ve={class:"qrcode-content"},ke={key:0,class:"qrcode-image"},be=["src"],we={class:"qrcode-info"},Ce={slot:"footer"},Le={class:"help-detail"};const Ve=L({__name:"ShortLinkList",setup(e,{expose:l}){l();const a=V(),t=i(!1),n=i(!1),r=i(!1),d=i(!1),u=i(!1),c=i(!1),p=i(!1),m=i(!1),L=i(!1),S=i(!1),D=i([]),U=i([]),z=i([]),T=i([]),F=i({current:1,pageSize:20,total:0}),R=i({link_type:"",status:"",domain_id:"",date_range:"",keyword:""}),q=i({original_url:"",link_type:"recruit",domain_id:"",custom_code:"",expires_at:"",remarks:""}),M=i({}),A=i(""),B=i({total_clicks:0,today_clicks:0,unique_visitors:0}),I=i([]),$=i("trend"),H=i(""),O=s(()=>"admin"===a.userInfo?.role);o(()=>{Q(),E()});const Q=async()=>{t.value=!0;try{const e={page:F.value.current,per_page:F.value.pageSize,...R.value},{data:l}=await _(e);D.value=l.data||[],F.value.total=l.total||0}catch(e){x.error("加载短链接列表失败")}finally{t.value=!1}},E=async()=>{try{const{data:e}=await h({per_page:100,status:1});z.value=e.data||[],T.value=e.data||[]}catch(e){}},N=async e=>{S.value=!0;try{B.value={total_clicks:1250,today_clicks:45,unique_visitors:890};const{data:l}=await v({link_id:e,limit:50});I.value=l.data||[]}catch(l){x.error("加载统计数据失败")}finally{S.value=!1}},P=e=>{navigator.clipboard.writeText(e).then(()=>{x.success("复制成功")}).catch(()=>{x.error("复制失败")})},Z=()=>{q.value={original_url:"",link_type:"recruit",domain_id:"",custom_code:"",expires_at:"",remarks:""}},G={userStore:a,loading:t,dialogVisible:n,switchDomainVisible:r,statsVisible:d,qrCodeVisible:u,helpVisible:c,editMode:p,submitting:m,switching:L,statsLoading:S,shortLinks:D,selectedLinks:U,domainOptions:z,availableDomains:T,pagination:F,filters:R,linkForm:q,linkRules:{original_url:[{required:!0,message:"请输入原始URL",trigger:"blur"},{type:"url",message:"请输入有效的URL地址",trigger:"blur"}],link_type:[{required:!0,message:"请选择链接类型",trigger:"change"}]},currentLink:M,selectedDomainId:A,linkStats:B,accessLogs:I,statsTab:$,qrCodeUrl:H,linkTypeDescriptions:[{type:"recruit",name:"推广链接",description:"用于分销员推广的链接"},{type:"payment",name:"支付链接",description:"用于支付页面的链接"},{type:"other",name:"其他链接",description:"其他用途的链接"}],isAdmin:O,loadShortLinks:Q,loadDomainOptions:E,showAddDialog:()=>{p.value=!1,n.value=!0,Z()},editShortLink:e=>{p.value=!0,n.value=!0,q.value={...e,domain_id:e.domain.id}},submitForm:async()=>{try{m.value=!0,p.value?(await w(q.value.id,q.value),x.success("短链接更新成功")):(await C(q.value),x.success("短链接创建成功")),n.value=!1,Q()}catch(e){if(e.fields)return;x.error(p.value?"更新失败":"创建失败")}finally{m.value=!1}},deleteShortLinkAction:e=>{j.confirm(`确定删除短链接 ${e.short_code} 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await b(e.id),x.success("删除成功"),Q()}catch(l){x.error("删除失败")}})},switchDomain:e=>{M.value=e,A.value=e.domain.id,r.value=!0},confirmSwitchDomain:async()=>{if(A.value){L.value=!0;try{await k(M.value.id,A.value),x.success("域名切换成功"),r.value=!1,Q()}catch(e){x.error("域名切换失败")}finally{L.value=!1}}else x.warning("请选择新域名")},viewStats:async e=>{M.value=e,d.value=!0,await N(e.id)},loadLinkStats:N,generateQRCodeAction:async()=>{if(1===U.value.length)try{const e=U.value[0],{data:l}=await y({url:e.full_url});H.value=l.qr_code_url,M.value=e,u.value=!0}catch(e){x.error("生成二维码失败")}else x.warning("请选择一个短链接")},downloadQRCode:()=>{if(!H.value)return;const e=document.createElement("a");e.href=H.value,e.download=`qrcode_${M.value.short_code}.png`,e.click()},handleSelectionChange:e=>{U.value=e},batchSwitchDomain:async()=>{0!==U.value.length&&j.prompt("请输入新域名ID","批量切换域名",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^\d+$/,inputErrorMessage:"请输入有效的域名ID"}).then(async({value:e})=>{try{U.value.map(e=>e.id);x.success("批量切换成功"),Q()}catch(l){x.error("批量切换失败")}})},batchDeleteAction:async()=>{0!==U.value.length&&j.confirm(`确定删除选中的 ${U.value.length} 个短链接吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=U.value.map(e=>e.id);await f(e),x.success("批量删除成功"),Q()}catch(e){x.error("批量删除失败")}})},copyShortLink:e=>{P(e.full_url)},copyFullUrl:e=>{P(e.full_url)},copyToClipboard:P,exportLinks:async()=>{try{const e=await g(R.value),l=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),a=window.URL.createObjectURL(l),t=document.createElement("a");t.href=a,t.download=`short_links_${(new Date).toISOString().split("T")[0]}.xlsx`,t.click(),window.URL.revokeObjectURL(a)}catch(e){x.error("导出失败")}},resetFilters:()=>{R.value={link_type:"",status:"",domain_id:"",date_range:"",keyword:""},Q()},handleSizeChange:e=>{F.value.pageSize=e,Q()},handleCurrentChange:e=>{F.value.current=e,Q()},resetForm:Z,showHelpDialog:()=>{c.value=!0},getLinkTypeName:e=>({recruit:"推广链接",payment:"支付链接",other:"其他链接"}[e]||e),getLinkTypeColor:e=>({recruit:"primary",payment:"success",other:"info"}[e]||""),getStatusName:e=>({1:"正常",2:"异常",3:"禁用",4:"过期"}[e]||"未知"),getStatusColor:e=>({1:"success",2:"warning",3:"danger",4:"info"}[e]||""),getDomainHealthColor:e=>e>=90?"success":e>=80?"primary":e>=60?"warning":"danger",truncateUrl:(e,l)=>e?e.length>l?e.substring(0,l)+"...":e:"",truncateText:(e,l)=>e?e.length>l?e.substring(0,l)+"...":e:"",formatTime:e=>e?new Date(e).toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-",ref:i,computed:s,onMounted:o,get ElMessage(){return x},get ElMessageBox(){return j},get getShortLinks(){return _},get createShortLink(){return C},get updateShortLink(){return w},get deleteShortLink(){return b},get switchShortLinkDomain(){return k},get getAccessLogs(){return v},get generateQRCode(){return y},get batchDeleteShortLinks(){return f},get exportShortLinks(){return g},get getDomainList(){return h},get useUserStore(){return V}};return Object.defineProperty(G,"__isScriptSetup",{enumerable:!1,value:!0}),G}},[["render",function(i,s,o,_,h,g){const f=S,y=D,v=R,k=F,b=T,w=q,C=M,L=z,V=I,x=$,j=H,Ve=B,xe=Q,Se=U,De=N,Ue=E,je=P,ze=O;return n(),e("div",Z,[l("div",G,[s[19]||(s[19]=l("div",{class:"page-title"},[l("h1",null,"🔗 短链接管理"),l("p",{class:"page-desc"},"管理系统生成的防红短链接，监控访问情况和域名使用")],-1)),l("div",W,[a(f,{type:"primary",onClick:_.showAddDialog},{default:t(()=>s[16]||(s[16]=[l("i",{class:"el-icon-plus"},null,-1),r(" 创建短链接 ",-1)])),_:1,__:[16]}),a(f,{type:"success",onClick:_.generateQRCode,disabled:1!==_.selectedLinks.length},{default:t(()=>s[17]||(s[17]=[l("i",{class:"el-icon-s-grid"},null,-1),r(" 生成二维码 ",-1)])),_:1,__:[17]},8,["onClick","disabled"]),a(f,{type:"info",onClick:_.exportLinks},{default:t(()=>s[18]||(s[18]=[l("i",{class:"el-icon-download"},null,-1),r(" 导出数据 ",-1)])),_:1,__:[18]})])]),a(y,{class:"help-card",style:{"margin-bottom":"20px"}},{default:t(()=>[l("div",X,[s[21]||(s[21]=l("span",null,"💡 短链接使用说明",-1)),a(f,{type:"text",onClick:_.showHelpDialog},{default:t(()=>s[20]||(s[20]=[r("查看详情",-1)])),_:1,__:[20]})]),s[22]||(s[22]=l("div",{class:"help-content"},[l("div",{class:"help-tips"},[l("div",{class:"tip-item"},[l("i",{class:"el-icon-info",style:{color:"#409eff"}}),l("span",null,[l("strong",null,"自动生成："),r("分销员推广链接会自动生成防红短链接")])]),l("div",{class:"tip-item"},[l("i",{class:"el-icon-success",style:{color:"#67c23a"}}),l("span",null,[l("strong",null,"智能切换："),r("域名异常时自动切换到备用域名")])]),l("div",{class:"tip-item"},[l("i",{class:"el-icon-view",style:{color:"#e6a23c"}}),l("span",null,[l("strong",null,"实时统计："),r("详细记录每次访问的数据和来源")])])])],-1))]),_:1,__:[22]}),a(y,{class:"filter-card"},{default:t(()=>[a(L,{inline:!0,model:_.filters,class:"filter-form"},{default:t(()=>[a(b,{label:"链接类型"},{default:t(()=>[a(k,{modelValue:_.filters.link_type,"onUpdate:modelValue":s[0]||(s[0]=e=>_.filters.link_type=e),placeholder:"全部类型",clearable:""},{default:t(()=>[a(v,{label:"推广链接",value:"recruit"}),a(v,{label:"支付链接",value:"payment"}),a(v,{label:"其他链接",value:"other"})]),_:1},8,["modelValue"])]),_:1}),a(b,{label:"链接状态"},{default:t(()=>[a(k,{modelValue:_.filters.status,"onUpdate:modelValue":s[1]||(s[1]=e=>_.filters.status=e),placeholder:"全部状态",clearable:""},{default:t(()=>[a(v,{label:"正常",value:1}),a(v,{label:"异常",value:2}),a(v,{label:"禁用",value:3}),a(v,{label:"过期",value:4})]),_:1},8,["modelValue"])]),_:1}),a(b,{label:"域名"},{default:t(()=>[a(k,{modelValue:_.filters.domain_id,"onUpdate:modelValue":s[2]||(s[2]=e=>_.filters.domain_id=e),placeholder:"全部域名",clearable:""},{default:t(()=>[(n(!0),e(d,null,u(_.domainOptions,e=>(n(),c(v,{key:e.id,label:e.domain,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(b,{label:"创建时间"},{default:t(()=>[a(w,{modelValue:_.filters.date_range,"onUpdate:modelValue":s[3]||(s[3]=e=>_.filters.date_range=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},null,8,["modelValue"])]),_:1}),a(b,{label:"关键词"},{default:t(()=>[a(C,{modelValue:_.filters.keyword,"onUpdate:modelValue":s[4]||(s[4]=e=>_.filters.keyword=e),placeholder:"短链接代码或原始URL",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(b,null,{default:t(()=>[a(f,{type:"primary",onClick:_.loadShortLinks},{default:t(()=>s[23]||(s[23]=[r("查询",-1)])),_:1,__:[23]}),a(f,{onClick:_.resetFilters},{default:t(()=>s[24]||(s[24]=[r("重置",-1)])),_:1,__:[24]})]),_:1})]),_:1},8,["model"])]),_:1}),a(y,null,{default:t(()=>[l("div",Y,[l("span",null,"短链接列表 ("+A(_.pagination.total)+")",1),l("div",null,[_.isAdmin?(n(),c(f,{key:0,type:"warning",size:"small",onClick:_.batchSwitchDomain,disabled:0===_.selectedLinks.length},{default:t(()=>s[25]||(s[25]=[r(" 批量切换域名 ",-1)])),_:1,__:[25]},8,["disabled"])):m("",!0),_.isAdmin?(n(),c(f,{key:1,type:"danger",size:"small",onClick:i.batchDelete,disabled:0===_.selectedLinks.length},{default:t(()=>s[26]||(s[26]=[r(" 批量删除 ",-1)])),_:1,__:[26]},8,["onClick","disabled"])):m("",!0)])]),p((n(),c(Ve,{data:_.shortLinks,onSelectionChange:_.handleSelectionChange,stripe:""},{default:t(()=>[a(V,{type:"selection",width:"55"}),a(V,{prop:"short_code",label:"短链接",width:"120"},{default:t(e=>[l("div",J,[l("span",K,A(e.row.short_code),1),a(f,{type:"text",size:"mini",onClick:l=>_.copyShortLink(e.row),style:{"margin-left":"5px"}},{default:t(()=>s[27]||(s[27]=[l("i",{class:"el-icon-copy-document"},null,-1)])),_:2,__:[27]},1032,["onClick"])])]),_:1}),a(V,{prop:"full_url",label:"完整链接",width:"200"},{default:t(e=>[l("div",ee,[l("span",le,A(e.row.full_url),1),a(f,{type:"text",size:"mini",onClick:l=>_.copyFullUrl(e.row),style:{"margin-left":"5px"}},{default:t(()=>s[28]||(s[28]=[l("i",{class:"el-icon-copy-document"},null,-1)])),_:2,__:[28]},1032,["onClick"])])]),_:1}),a(V,{prop:"original_url",label:"原始URL","min-width":"250"},{default:t(e=>[l("div",ae,[a(x,{content:e.row.original_url,placement:"top"},{default:t(()=>[l("span",null,A(_.truncateUrl(e.row.original_url,40)),1)]),_:2},1032,["content"])])]),_:1}),a(V,{prop:"domain",label:"使用域名",width:"150"},{default:t(e=>[l("div",te,[l("span",ie,A(e.row.domain.domain),1),a(j,{type:_.getDomainHealthColor(e.row.domain.health_score),size:"mini",style:{"margin-left":"5px"}},{default:t(()=>[r(A(e.row.domain.health_score)+"% ",1)]),_:2},1032,["type"])])]),_:1}),a(V,{prop:"link_type",label:"类型",width:"100"},{default:t(e=>[a(j,{size:"small",type:_.getLinkTypeColor(e.row.link_type)},{default:t(()=>[r(A(_.getLinkTypeName(e.row.link_type)),1)]),_:2},1032,["type"])]),_:1}),a(V,{prop:"status",label:"状态",width:"80"},{default:t(e=>[a(j,{type:_.getStatusColor(e.row.status),size:"small"},{default:t(()=>[r(A(_.getStatusName(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),a(V,{prop:"click_count",label:"点击量",width:"80"},{default:t(e=>[l("span",se,A(e.row.click_count),1)]),_:1}),a(V,{prop:"created_at",label:"创建时间",width:"140"},{default:t(e=>[l("span",null,A(_.formatTime(e.row.created_at)),1)]),_:1}),a(V,{prop:"last_click_at",label:"最后访问",width:"140"},{default:t(e=>[l("span",null,A(_.formatTime(e.row.last_click_at)),1)]),_:1}),a(V,{label:"操作",width:"180",fixed:"right"},{default:t(e=>[a(f,{type:"text",size:"small",onClick:l=>_.viewStats(e.row)},{default:t(()=>s[29]||(s[29]=[r(" 统计 ",-1)])),_:2,__:[29]},1032,["onClick"]),_.isAdmin?(n(),c(f,{key:0,type:"text",size:"small",onClick:l=>_.switchDomain(e.row)},{default:t(()=>s[30]||(s[30]=[r(" 切换域名 ",-1)])),_:2,__:[30]},1032,["onClick"])):m("",!0),_.isAdmin?(n(),c(f,{key:1,type:"text",size:"small",onClick:l=>_.editShortLink(e.row)},{default:t(()=>s[31]||(s[31]=[r(" 编辑 ",-1)])),_:2,__:[31]},1032,["onClick"])):m("",!0),_.isAdmin?(n(),c(f,{key:2,type:"text",size:"small",onClick:l=>_.deleteShortLink(e.row),style:{color:"#f56c6c"}},{default:t(()=>s[32]||(s[32]=[r(" 删除 ",-1)])),_:2,__:[32]},1032,["onClick"])):m("",!0)]),_:1})]),_:1},8,["data"])),[[ze,_.loading]]),l("div",oe,[a(xe,{onSizeChange:_.handleSizeChange,onCurrentChange:_.handleCurrentChange,"current-page":_.pagination.current,"page-sizes":[10,20,50,100],"page-size":_.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:_.pagination.total},null,8,["current-page","page-size","total"])])]),_:1}),a(Se,{title:_.editMode?"编辑短链接":"创建短链接",visible:_.dialogVisible,width:"500px",onClose:_.resetForm},{default:t(()=>[a(L,{model:_.linkForm,rules:_.linkRules,ref:"linkForm","label-width":"100px"},{default:t(()=>[a(b,{label:"原始URL",prop:"original_url"},{default:t(()=>[a(C,{modelValue:_.linkForm.original_url,"onUpdate:modelValue":s[5]||(s[5]=e=>_.linkForm.original_url=e),placeholder:"请输入完整的URL地址",type:"textarea",rows:"2"},null,8,["modelValue"]),s[33]||(s[33]=l("div",{class:"form-tip"}," 🔗 请输入完整的URL地址，包含 http:// 或 https:// ",-1))]),_:1,__:[33]}),a(b,{label:"链接类型",prop:"link_type"},{default:t(()=>[a(k,{modelValue:_.linkForm.link_type,"onUpdate:modelValue":s[6]||(s[6]=e=>_.linkForm.link_type=e),placeholder:"选择链接类型",style:{width:"100%"}},{default:t(()=>[a(v,{label:"推广链接",value:"recruit"}),a(v,{label:"支付链接",value:"payment"}),a(v,{label:"其他链接",value:"other"})]),_:1},8,["modelValue"])]),_:1}),a(b,{label:"选择域名",prop:"domain_id"},{default:t(()=>[a(k,{modelValue:_.linkForm.domain_id,"onUpdate:modelValue":s[7]||(s[7]=e=>_.linkForm.domain_id=e),placeholder:"选择域名",style:{width:"100%"}},{default:t(()=>[(n(!0),e(d,null,u(_.availableDomains,e=>(n(),c(v,{key:e.id,label:`${e.domain} (健康度: ${e.health_score}%)`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),s[34]||(s[34]=l("div",{class:"form-tip"}," 💡 系统会自动选择最佳域名，也可手动指定 ",-1))]),_:1,__:[34]}),a(b,{label:"自定义代码"},{default:t(()=>[a(C,{modelValue:_.linkForm.custom_code,"onUpdate:modelValue":s[8]||(s[8]=e=>_.linkForm.custom_code=e),placeholder:"留空则自动生成",maxlength:"20"},null,8,["modelValue"]),s[35]||(s[35]=l("div",{class:"form-tip"}," 🎯 自定义短链接代码，仅支持字母数字，留空则自动生成 ",-1))]),_:1,__:[35]}),a(b,{label:"有效期"},{default:t(()=>[a(w,{modelValue:_.linkForm.expires_at,"onUpdate:modelValue":s[9]||(s[9]=e=>_.linkForm.expires_at=e),type:"datetime",placeholder:"选择过期时间",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"]),s[36]||(s[36]=l("div",{class:"form-tip"}," ⏰ 留空则永久有效，过期后链接将无法访问 ",-1))]),_:1,__:[36]}),a(b,{label:"备注"},{default:t(()=>[a(C,{modelValue:_.linkForm.remarks,"onUpdate:modelValue":s[10]||(s[10]=e=>_.linkForm.remarks=e),type:"textarea",rows:"2",placeholder:"链接用途说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),l("div",ne,[a(f,{onClick:s[11]||(s[11]=e=>_.dialogVisible=!1)},{default:t(()=>s[37]||(s[37]=[r("取消",-1)])),_:1,__:[37]}),a(f,{type:"primary",onClick:_.submitForm,loading:_.submitting},{default:t(()=>[r(A(_.editMode?"更新":"创建"),1)]),_:1},8,["loading"])])]),_:1},8,["title","visible"]),a(Se,{title:"切换域名",visible:_.switchDomainVisible,width:"400px"},{default:t(()=>[l("div",re,[l("p",null,[s[38]||(s[38]=r("为短链接 ",-1)),l("strong",null,A(_.currentLink.short_code),1),s[39]||(s[39]=r(" 切换域名：",-1))]),a(k,{modelValue:_.selectedDomainId,"onUpdate:modelValue":s[12]||(s[12]=e=>_.selectedDomainId=e),placeholder:"选择新域名",style:{width:"100%"}},{default:t(()=>[(n(!0),e(d,null,u(_.availableDomains,e=>(n(),c(v,{key:e.id,label:`${e.domain} (健康度: ${e.health_score}%)`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),s[40]||(s[40]=l("div",{class:"form-tip",style:{"margin-top":"10px"}}," 💡 切换域名后，原链接将重定向到新域名 ",-1))]),l("div",de,[a(f,{onClick:s[13]||(s[13]=e=>_.switchDomainVisible=!1)},{default:t(()=>s[41]||(s[41]=[r("取消",-1)])),_:1,__:[41]}),a(f,{type:"primary",onClick:_.confirmSwitchDomain,loading:_.switching},{default:t(()=>s[42]||(s[42]=[r("切换",-1)])),_:1,__:[42]},8,["loading"])])]),_:1},8,["visible"]),a(Se,{title:"访问统计",visible:_.statsVisible,width:"800px"},{default:t(()=>[l("div",ue,[l("div",ce,[l("h4",null,A(_.currentLink.short_code)+" 的访问统计",1),l("div",pe,[l("div",me,[s[43]||(s[43]=l("span",{class:"summary-label"},"总访问量",-1)),l("span",_e,A(_.linkStats.total_clicks),1)]),l("div",he,[s[44]||(s[44]=l("span",{class:"summary-label"},"今日访问",-1)),l("span",ge,A(_.linkStats.today_clicks),1)]),l("div",fe,[s[45]||(s[45]=l("span",{class:"summary-label"},"独立访客",-1)),l("span",ye,A(_.linkStats.unique_visitors),1)])])]),a(Ue,{modelValue:_.statsTab,"onUpdate:modelValue":s[14]||(s[14]=e=>_.statsTab=e)},{default:t(()=>[a(De,{label:"访问趋势",name:"trend"},{default:t(()=>s[46]||(s[46]=[l("div",{class:"chart-container"},[l("p",null,"访问趋势图表")],-1)])),_:1,__:[46]}),a(De,{label:"访问记录",name:"logs"},{default:t(()=>[p((n(),c(Ve,{data:_.accessLogs,size:"small"},{default:t(()=>[a(V,{prop:"access_time",label:"访问时间",width:"140"},{default:t(e=>[l("span",null,A(_.formatTime(e.row.access_time)),1)]),_:1}),a(V,{prop:"ip_address",label:"IP地址",width:"120"},{default:t(e=>[l("span",null,A(e.row.ip_address),1)]),_:1}),a(V,{prop:"user_agent",label:"设备信息","min-width":"200"},{default:t(e=>[a(x,{content:e.row.user_agent,placement:"top"},{default:t(()=>[l("span",null,A(_.truncateText(e.row.user_agent,30)),1)]),_:2},1032,["content"])]),_:1}),a(V,{prop:"referer",label:"来源","min-width":"150"},{default:t(e=>[l("span",null,A(e.row.referer||"直接访问"),1)]),_:1}),a(V,{prop:"region",label:"地区",width:"100"},{default:t(e=>[l("span",null,A(e.row.region||"-"),1)]),_:1})]),_:1},8,["data"])),[[ze,_.statsLoading]])]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["visible"]),a(Se,{title:"二维码",visible:_.qrCodeVisible,width:"400px"},{default:t(()=>[l("div",ve,[_.qrCodeUrl?(n(),e("div",ke,[l("img",{src:_.qrCodeUrl,alt:"二维码"},null,8,be)])):m("",!0),l("div",we,[l("p",null,[s[47]||(s[47]=l("strong",null,"短链接：",-1)),r(A(_.currentLink.full_url),1)]),l("p",null,[s[48]||(s[48]=l("strong",null,"创建时间：",-1)),r(A(_.formatTime(new Date)),1)])])]),l("div",Ce,[a(f,{onClick:s[15]||(s[15]=e=>_.qrCodeVisible=!1)},{default:t(()=>s[49]||(s[49]=[r("关闭",-1)])),_:1,__:[49]}),a(f,{type:"primary",onClick:_.downloadQRCode},{default:t(()=>s[50]||(s[50]=[r("下载二维码",-1)])),_:1,__:[50]})])]),_:1},8,["visible"]),a(Se,{title:"短链接使用说明",visible:_.helpVisible,width:"700px"},{default:t(()=>[l("div",Le,[s[52]||(s[52]=l("h3",null,"🔗 什么是防红短链接？",-1)),s[53]||(s[53]=l("p",null,"防红短链接是经过特殊处理的短链接，能够有效避免被微信、QQ等平台检测和封禁。",-1)),s[54]||(s[54]=l("h3",null,"🚀 主要功能",-1)),s[55]||(s[55]=l("ul",null,[l("li",null,[l("strong",null,"智能域名选择："),r("系统自动选择最健康的域名生成短链接")]),l("li",null,[l("strong",null,"自动域名切换："),r("当域名异常时自动切换到备用域名")]),l("li",null,[l("strong",null,"访问统计分析："),r("详细记录每次访问的数据和来源")]),l("li",null,[l("strong",null,"二维码生成："),r("一键生成短链接二维码")]),l("li",null,[l("strong",null,"批量管理："),r("支持批量操作和数据导出")])],-1)),s[56]||(s[56]=l("h3",null,"📊 链接类型说明",-1)),a(Ve,{data:_.linkTypeDescriptions,size:"small"},{default:t(()=>[a(V,{prop:"type",label:"类型",width:"100"}),a(V,{prop:"name",label:"名称",width:"100"}),a(V,{prop:"description",label:"说明"})]),_:1}),s[57]||(s[57]=l("h3",null,"⚠️ 使用注意事项",-1)),a(je,{type:"warning",closable:!1},{default:t(()=>s[51]||(s[51]=[l("ul",{style:{margin:"0","padding-left":"20px"}},[l("li",null,"原始URL必须是完整的地址，包含协议头（http://或https://）"),l("li",null,"自定义代码仅支持字母和数字，建议使用有意义的代码"),l("li",null,"设置合理的过期时间，避免链接长期有效造成安全风险"),l("li",null,"定期检查链接状态，及时处理异常链接")],-1)])),_:1,__:[51]})])]),_:1},8,["visible"])])}],["__scopeId","data-v-406611f5"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/anti-block/ShortLinkList.vue"]]);export{Ve as default};
