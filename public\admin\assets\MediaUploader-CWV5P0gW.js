/* empty css             *//* empty css                   *//* empty css                  *//* empty css                    *//* empty css                  */import{l as e,m as a,G as l,C as t,A as s,z as r,q as d,E as i,t as o,r as u,c as p,w as n}from"./vue-vendor-BcnDv-68.js";import{ah as m,Z as c,a1 as y,s as f,ao as v,W as g,bo as h,b4 as V,af as b,X as x}from"./element-plus-C2UshkXo.js";import{_ as w,g as S}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const _={__name:"MediaUploader",props:{modelValue:{type:[String,Array],default:()=>[]},type:{type:String,default:"image",validator:e=>["image","video","file"].includes(e)},multiple:{type:Boolean,default:!1},limit:{type:Number,default:1},accept:{type:String,default:"image/*"},listType:{type:String,default:"text",validator:e=>["text","picture","picture-card"].includes(e)},drag:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},maxSize:{type:Number,default:10}},emits:["update:modelValue","change","success","error"],setup(e,{expose:a,emit:l}){a();const t=e,s=l,r=u(),d=u(!1),i=u(!1),o=u(""),m=p(()=>({image:"图片",video:"视频",file:"文件"}[t.type]||"文件")),c=p(()=>["media-uploader-component",`media-uploader-${t.type}`,{"is-drag":t.drag,"is-disabled":t.disabled}]),y=p(()=>"#mock-upload"),f=p(()=>({Authorization:`Bearer ${S()}`})),v=p(()=>({type:t.type})),g=p(()=>{const e=t.maxSize>1?`${t.maxSize}MB`:1024*t.maxSize+"KB";return`支持 ${t.accept}，单个文件不超过 ${e}`}),w=p(()=>t.modelValue?Array.isArray(t.modelValue)?t.modelValue.map((e,a)=>({uid:a,name:`${m.value}${a+1}`,status:"success",url:e})):t.modelValue?[{uid:0,name:m.value,status:"success",url:t.modelValue}]:[]:[]),_=e=>{const{type:a}=t,l=e.type;switch(a){case"image":return l.startsWith("image/");case"video":return l.startsWith("video/");default:return!0}},$=(e,a)=>{if(d.value=!1,e.success){const l=e.data.url;U(l),s("success",e,a),s("change",t.modelValue),x.success("上传成功")}else x.error(e.message||"上传失败"),s("error",e,a)},U=e=>{if(t.multiple){const a=Array.isArray(t.modelValue)?[...t.modelValue]:[];a.push(e),s("update:modelValue",a)}else s("update:modelValue",e)},j=e=>{if(t.multiple){const a=Array.isArray(t.modelValue)?[...t.modelValue]:[],l=a.indexOf(e);l>-1&&(a.splice(l,1),s("update:modelValue",a))}else s("update:modelValue","")};n(()=>t.modelValue,e=>{},{deep:!0});const z={props:t,emit:s,uploadRef:r,uploading:d,previewVisible:i,previewUrl:o,typeText:m,uploaderClass:c,uploadUrl:y,uploadHeaders:f,uploadData:v,uploadHint:g,fileList:w,beforeUpload:e=>{if(!_(e))return x.error(`请上传正确的${m.value}格式`),!1;return e.size/1024/1024<t.maxSize?(d.value=!0,"#mock-upload"!==y.value||(setTimeout(()=>{const a={success:!0,message:"上传成功",data:{url:`https://picsum.photos/400/300?random=${Math.random()}`,filename:e.name,size:e.size,type:e.type,upload_time:(new Date).toISOString()}};$(a,e)},1e3+2e3*Math.random()),!1)):(x.error(`${m.value}大小不能超过 ${t.maxSize}MB`),!1)},checkFileType:_,handleSuccess:$,handleError:(e,a)=>{d.value=!1,x.error("上传失败，请重试"),s("error",e,a)},handleRemove:e=>{const a=e.url;j(a),s("change",t.modelValue)},handleExceed:e=>{x.warning(`最多只能上传 ${t.limit} 个${m.value}`)},handlePreview:e=>{"image"===t.type?(o.value=e.url,i.value=!0):window.open(e.url,"_blank")},updateValue:U,removeValue:j,ref:u,computed:p,watch:n,get ElMessage(){return x},get Plus(){return b},get Upload(){return V},get UploadFilled(){return h},get getToken(){return S}};return Object.defineProperty(z,"__isScriptSetup",{enumerable:!1,value:!0}),z}},$={class:"media-uploader"},U={key:0,class:"upload-card"},j={class:"upload-text"},z={key:1,class:"upload-dragger"},k={class:"upload-text"},A={class:"upload-hint"},T={key:0,class:"upload-tip"},B=["src"];const M=w(_,[["render",function(u,p,n,h,V,b){const x=m,w=y,S=v,_=g;return a(),e("div",$,[l(S,{ref:"uploadRef",class:f(h.uploaderClass),action:h.uploadUrl,headers:h.uploadHeaders,data:h.uploadData,multiple:n.multiple,limit:n.limit,accept:n.accept,"list-type":n.listType,"file-list":h.fileList,"before-upload":h.beforeUpload,"on-success":h.handleSuccess,"on-error":h.handleError,"on-remove":h.handleRemove,"on-exceed":h.handleExceed,"on-preview":h.handlePreview,drag:n.drag,disabled:n.disabled},{default:s(()=>["picture-card"===n.listType?(a(),e("div",U,[l(x,{class:"upload-icon"},{default:s(()=>[l(h.Plus)]),_:1}),d("div",j,"上传"+c(h.typeText),1)])):n.drag?(a(),e("div",z,[l(x,{class:"upload-icon"},{default:s(()=>[l(h.UploadFilled)]),_:1}),d("div",k,[i("将"+c(h.typeText)+"拖到此处，或",1),p[1]||(p[1]=d("em",null,"点击上传",-1))]),d("div",A,c(h.uploadHint),1)])):(a(),r(w,{key:2,icon:h.Upload,type:"primary",loading:h.uploading},{default:s(()=>[i(" 上传"+c(h.typeText),1)]),_:1},8,["icon","loading"]))]),_:1},8,["class","action","headers","data","multiple","limit","accept","list-type","file-list","drag","disabled"]),u.$slots.tip?(a(),e("div",T,[o(u.$slots,"tip",{},void 0,!0)])):t("",!0),l(_,{modelValue:h.previewVisible,"onUpdate:modelValue":p[0]||(p[0]=e=>h.previewVisible=e),title:"图片预览",width:"60%","append-to-body":""},{default:s(()=>[d("img",{src:h.previewUrl,alt:"预览图片",style:{width:"100%"}},null,8,B)]),_:1},8,["modelValue"])])}],["__scopeId","data-v-4bd3427d"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/MediaUploader.vue"]]);export{M as default};
