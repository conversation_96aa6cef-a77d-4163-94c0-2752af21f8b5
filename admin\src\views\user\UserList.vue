<template>
  <div class="modern-user-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><UserFilled /></el-icon>
          </div>
          <div class="header-text">
            <h1>用户管理</h1>
            <p>管理平台所有用户信息，包括用户权限、状态和基本资料</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleExport" class="action-btn secondary">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button type="primary" @click="handleCreate" class="action-btn primary">
            <el-icon><Plus /></el-icon>
            添加用户
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-container">
        <div class="stat-card" v-for="stat in userStats" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <el-card class="filter-card" shadow="never">
        <div class="filter-content">
          <div class="filter-left">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索用户名、邮箱、手机号"
              prefix-icon="Search"
              clearable
              class="search-input"
              @keyup.enter="handleSearch"
            />
            <el-select
              v-model="searchForm.role"
              placeholder="用户角色"
              clearable
              class="filter-select"
            >
              <el-option label="全部角色" value="" />
              <el-option label="管理员" value="admin" />
              <el-option label="分站管理员" value="substation" />
              <el-option label="代理商" value="agent" />
              <el-option label="分销员" value="distributor" />
              <el-option label="群主" value="group_owner" />
              <el-option label="普通用户" value="user" />
            </el-select>
            <el-select
              v-model="searchForm.status"
              placeholder="用户状态"
              clearable
              class="filter-select"
            >
              <el-option label="全部状态" value="" />
              <el-option label="正常" value="active" />
              <el-option label="禁用" value="inactive" />
            </el-select>
          </div>
          <div class="filter-right">
            <el-button @click="handleSearch" type="primary" class="search-btn">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset" class="reset-btn">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 用户列表 -->
    <div class="table-section">
      <el-card class="table-card" shadow="never">
        <template #header>
          <div class="table-header">
            <div class="table-title">
              <span>用户列表</span>
              <el-tag size="small" type="info">共 {{ total }} 条记录</el-tag>
            </div>
            <div class="table-actions">
              <el-button 
                v-if="selectedUsers.length > 0" 
                @click="handleBatchDelete" 
                type="danger" 
                size="small"
                plain
              >
                <el-icon><Delete /></el-icon>
                批量删除 ({{ selectedUsers.length }})
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="userList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          class="modern-table"
          stripe
          border
        >
          <el-table-column type="selection" width="55" align="center" />
          
          <el-table-column label="用户信息" min-width="200">
            <template #default="{ row }">
              <div class="user-info">
                <el-avatar :size="40" :src="row.avatar" class="user-avatar">
                  <el-icon><UserFilled /></el-icon>
                </el-avatar>
                <div class="user-details">
                  <div class="user-name">{{ row.username }}</div>
                  <div class="user-email">{{ row.email }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="真实姓名" prop="realName" width="100" />
          
          <el-table-column label="手机号码" prop="phone" width="120" />
          
          <el-table-column label="用户角色" width="120">
            <template #default="{ row }">
              <el-tag :type="getRoleTagType(row.role)" size="small">
                {{ getRoleLabel(row.role) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                {{ row.status === 'active' ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="注册时间" width="160">
            <template #default="{ row }">
              <div class="time-info">
                <div>{{ formatDate(row.created_at) }}</div>
                <small>{{ formatTime(row.created_at) }}</small>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="最后登录" width="160">
            <template #default="{ row }">
              <div class="time-info" v-if="row.last_login_at">
                <div>{{ formatDate(row.last_login_at) }}</div>
                <small>{{ formatTime(row.last_login_at) }}</small>
              </div>
              <span v-else class="text-muted">从未登录</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button @click="handleEdit(row)" type="primary" size="small" plain>
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button 
                  @click="handleToggleStatus(row)" 
                  :type="row.status === 'active' ? 'warning' : 'success'" 
                  size="small" 
                  plain
                >
                  <el-icon><component :is="row.status === 'active' ? 'Lock' : 'Unlock'" /></el-icon>
                  {{ row.status === 'active' ? '禁用' : '启用' }}
                </el-button>
                <el-button @click="handleDelete(row)" type="danger" size="small" plain>
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
            class="modern-pagination"
          />
        </div>
      </el-card>
    </div>

    <!-- 用户编辑对话框 -->
    <UserDialog
      v-model="editDialogVisible"
      :user-data="currentUser"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  UserFilled, Download, Plus, Search, RefreshLeft, Edit, Delete, 
  Lock, Unlock, TrendCharts, ArrowUp, ArrowDown, User, Avatar, 
  Management, Share, Star
} from '@element-plus/icons-vue'
import UserDialog from './components/UserDialog.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const userList = ref([])
const selectedUsers = ref([])
const editDialogVisible = ref(false)
const currentUser = ref({})
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  role: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20
})

// 统计数据
const userStats = ref([
  {
    key: 'total',
    label: '总用户数',
    value: 1234,
    icon: 'UserFilled',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+12.5%'
  },
  {
    key: 'active',
    label: '活跃用户',
    value: 1156,
    icon: 'User',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+8.3%'
  },
  {
    key: 'distributors',
    label: '分销员',
    value: 89,
    icon: 'Share',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15.2%'
  },
  {
    key: 'agents',
    label: '代理商',
    value: 23,
    icon: 'Avatar',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '****%'
  }
])

// 模拟用户数据
const mockUserList = [
  {
    id: 1,
    username: 'admin',
    realName: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    role: 'admin',
    status: 'active',
    avatar: '',
    created_at: '2024-01-01 10:00:00',
    last_login_at: '2024-08-06 15:30:00'
  },
  {
    id: 2,
    username: 'zhangsan',
    realName: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    role: 'agent',
    status: 'active',
    avatar: '',
    created_at: '2024-02-15 14:20:00',
    last_login_at: '2024-08-06 12:15:00'
  },
  {
    id: 3,
    username: 'lisi',
    realName: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    role: 'distributor',
    status: 'active',
    avatar: '',
    created_at: '2024-03-10 09:30:00',
    last_login_at: '2024-08-05 18:45:00'
  },
  {
    id: 4,
    username: 'wangwu',
    realName: '王五',
    email: '<EMAIL>',
    phone: '13800138003',
    role: 'user',
    status: 'inactive',
    avatar: '',
    created_at: '2024-04-20 16:10:00',
    last_login_at: null
  }
]

// 工具函数
const getRoleLabel = (role) => {
  const roleMap = {
    admin: '管理员',
    substation: '分站管理员',
    agent: '代理商',
    distributor: '分销员',
    group_owner: '群主',
    user: '普通用户'
  }
  return roleMap[role] || '未知角色'
}

const getRoleTagType = (role) => {
  const typeMap = {
    admin: 'danger',
    substation: 'warning',
    agent: 'primary',
    distributor: 'success',
    group_owner: 'info',
    user: ''
  }
  return typeMap[role] || ''
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatTime = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 事件处理函数
const handleCreate = () => {
  currentUser.value = {}
  editDialogVisible.value = true
}

const handleEdit = (row) => {
  currentUser.value = { ...row }
  editDialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该用户吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })

    // 模拟删除操作
    const index = userList.value.findIndex(user => user.id === row.id)
    if (index > -1) {
      userList.value.splice(index, 1)
      total.value--
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleToggleStatus = async (row) => {
  const newStatus = row.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'inactive' ? '禁用' : '启用'

  try {
    await ElMessageBox.confirm(`确定要${action}该用户吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 模拟状态切换
    row.status = newStatus
    ElMessage.success(`${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

const handleBatchDelete = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要删除的用户')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要批量删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可恢复！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })

    // 模拟批量删除
    selectedUsers.value.forEach(user => {
      const index = userList.value.findIndex(u => u.id === user.id)
      if (index > -1) {
        userList.value.splice(index, 1)
        total.value--
      }
    })
    
    selectedUsers.value = []
    ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const handleSearch = () => {
  loading.value = true
  // 模拟搜索
  setTimeout(() => {
    let filteredList = [...mockUserList]
    
    if (searchForm.keyword) {
      filteredList = filteredList.filter(user => 
        user.username.includes(searchForm.keyword) ||
        user.email.includes(searchForm.keyword) ||
        user.phone.includes(searchForm.keyword)
      )
    }
    
    if (searchForm.role) {
      filteredList = filteredList.filter(user => user.role === searchForm.role)
    }
    
    if (searchForm.status) {
      filteredList = filteredList.filter(user => user.status === searchForm.status)
    }
    
    userList.value = filteredList
    total.value = filteredList.length
    loading.value = false
  }, 500)
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.role = ''
  searchForm.status = ''
  userList.value = [...mockUserList]
  total.value = mockUserList.length
  ElMessage.info('搜索条件已重置')
}

const handlePageChange = (page) => {
  pagination.page = page
  // 这里可以添加分页逻辑
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  // 这里可以添加分页逻辑
}

const handleExport = async () => {
  try {
    ElMessage.success('导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const handleEditSuccess = () => {
  // 刷新列表
  userList.value = [...mockUserList]
  ElMessage.success('用户信息更新成功')
}

// 初始化数据
const initData = () => {
  userList.value = [...mockUserList]
  total.value = mockUserList.length
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.modern-user-list {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  // 页面头部样式
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          height: 40px;
          padding: 0 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }
  
  // 统计卡片区域
  .stats-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }
  
  // 筛选区域
  .filter-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .filter-card {
      border-radius: 16px;
      border: none;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      
      :deep(.el-card__body) {
        padding: 24px;
      }
      
      .filter-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 16px;
        
        .filter-left {
          display: flex;
          gap: 16px;
          flex: 1;
          
          .search-input {
            width: 300px;
            
            :deep(.el-input__wrapper) {
              border-radius: 8px;
              border: 1px solid #dcdfe6;
              transition: all 0.3s ease;
              
              &:hover {
                border-color: #c0c4cc;
              }
              
              &.is-focus {
                border-color: #667eea;
                box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
              }
            }
          }
          
          .filter-select {
            width: 150px;
            
            :deep(.el-input__wrapper) {
              border-radius: 8px;
            }
          }
        }
        
        .filter-right {
          display: flex;
          gap: 12px;
          
          .search-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 0 20px;
            height: 36px;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }
          }
          
          .reset-btn {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            border-radius: 8px;
            padding: 0 20px;
            height: 36px;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
        }
      }
    }
  }
  
  // 表格区域
  .table-section {
    max-width: 1400px;
    margin: 0 auto 40px;
    padding: 0 24px;
    
    .table-card {
      border-radius: 16px;
      border: none;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      
      :deep(.el-card__header) {
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
        border-bottom: 1px solid #e4e7ed;
        padding: 20px 24px;
        
        .table-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .table-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            
            .el-tag {
              background: rgba(102, 126, 234, 0.1);
              color: #667eea;
              border: none;
            }
          }
          
          .table-actions {
            .el-button {
              border-radius: 8px;
              font-weight: 500;
            }
          }
        }
      }
      
      :deep(.el-card__body) {
        padding: 0;
      }
      
      .modern-table {
        :deep(.el-table__header) {
          background: #fafbfc;
          
          th {
            background: #fafbfc !important;
            border-bottom: 1px solid #e4e7ed;
            font-weight: 600;
            color: #606266;
            font-size: 13px;
            padding: 16px 12px;
          }
        }
        
        :deep(.el-table__body) {
          tr {
            transition: all 0.3s ease;
            
            &:hover {
              background: #f8f9ff !important;
            }
            
            td {
              border-bottom: 1px solid #f0f2f5;
              padding: 16px 12px;
            }
          }
        }
        
        .user-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .user-avatar {
            border: 2px solid #f0f2ff;
            transition: all 0.3s ease;
            
            &:hover {
              transform: scale(1.1);
            }
          }
          
          .user-details {
            .user-name {
              font-weight: 600;
              color: #303133;
              font-size: 14px;
              margin-bottom: 2px;
            }
            
            .user-email {
              font-size: 12px;
              color: #909399;
            }
          }
        }
        
        .time-info {
          div {
            font-size: 13px;
            color: #303133;
            margin-bottom: 2px;
          }
          
          small {
            font-size: 11px;
            color: #909399;
          }
        }
        
        .text-muted {
          color: #c0c4cc;
          font-size: 12px;
          font-style: italic;
        }
        
        .action-buttons {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
          
          .el-button {
            border-radius: 6px;
            font-size: 12px;
            padding: 4px 8px;
            height: auto;
            min-height: 28px;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &:hover {
              transform: translateY(-1px);
            }
          }
        }
      }
      
      .pagination-wrapper {
        padding: 24px;
        display: flex;
        justify-content: center;
        background: #fafbfc;
        border-top: 1px solid #e4e7ed;
        
        .modern-pagination {
          :deep(.el-pagination__total) {
            color: #606266;
            font-weight: 500;
          }
          
          :deep(.el-pagination__sizes) {
            .el-select .el-input__wrapper {
              border-radius: 6px;
            }
          }
          
          :deep(.btn-prev),
          :deep(.btn-next) {
            border-radius: 6px;
            border: 1px solid #dcdfe6;
            background: white;
            
            &:hover {
              background: #667eea;
              border-color: #667eea;
              color: white;
            }
          }
          
          :deep(.el-pager) {
            li {
              border-radius: 6px;
              margin: 0 2px;
              border: 1px solid transparent;
              transition: all 0.3s ease;
              
              &:hover {
                background: #f0f2ff;
                border-color: #667eea;
                color: #667eea;
              }
              
              &.is-active {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-color: #667eea;
                color: white;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .modern-user-list {
    .stats-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .modern-user-list {
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .stats-container {
      grid-template-columns: 1fr;
    }
    
    .filter-content {
      flex-direction: column;
      gap: 16px;
      
      .filter-left {
        flex-direction: column;
        width: 100%;
        
        .search-input,
        .filter-select {
          width: 100%;
        }
      }
      
      .filter-right {
        width: 100%;
        justify-content: center;
      }
    }
    
    .modern-table {
      :deep(.el-table__body) {
        .action-buttons {
          flex-direction: column;
          
          .el-button {
            width: 100%;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: fadeInUp 0.6s ease-out;
  
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
}

.filter-card,
.table-card {
  animation: fadeInUp 0.6s ease-out;
}

.filter-card {
  animation-delay: 0.5s;
}

.table-card {
  animation-delay: 0.6s;
}
</style>