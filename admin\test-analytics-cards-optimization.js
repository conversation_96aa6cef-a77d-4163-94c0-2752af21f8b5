#!/usr/bin/env node

/**
 * 数据分析页面卡片配色优化验证脚本
 * 检查卡片配色和视觉效果优化是否完成
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🎨 开始验证数据分析页面卡片配色优化...\n')

const checks = []

// 检查1: Analytics.vue文件是否存在
const analyticsPath = path.join(__dirname, 'src/views/dashboard/Analytics.vue')
if (fs.existsSync(analyticsPath)) {
  checks.push({ name: 'Analytics页面文件', status: '✅ 存在', details: '数据分析页面文件已找到' })
} else {
  checks.push({ name: 'Analytics页面文件', status: '❌ 未找到', details: '页面文件缺失' })
}

// 检查2: 新的渐变配色方案是否已应用
if (fs.existsSync(analyticsPath)) {
  const content = fs.readFileSync(analyticsPath, 'utf8')
  
  const hasGradientColors = content.includes('linear-gradient(135deg, #667eea 0%, #764ba2 100%)') &&
                           content.includes('linear-gradient(135deg, #f093fb 0%, #f5576c 100%)') &&
                           content.includes('linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)') &&
                           content.includes('linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)')
  
  if (hasGradientColors) {
    checks.push({ name: '渐变配色方案', status: '✅ 已应用', details: '四个卡片使用了不同的美观渐变色' })
  } else {
    checks.push({ name: '渐变配色方案', status: '⚠️ 可能有问题', details: '未检测到完整的渐变配色' })
  }
}

// 检查3: 阴影色彩配置是否完整
if (fs.existsSync(analyticsPath)) {
  const content = fs.readFileSync(analyticsPath, 'utf8')
  
  const hasShadowColors = content.includes('shadowColor') &&
                         content.includes('rgba(102, 126, 234, 0.3)') &&
                         content.includes('rgba(240, 147, 251, 0.3)') &&
                         content.includes('rgba(79, 172, 254, 0.3)') &&
                         content.includes('rgba(67, 233, 123, 0.3)')
  
  if (hasShadowColors) {
    checks.push({ name: '阴影色彩配置', status: '✅ 已完善', details: '每个卡片都有对应的阴影颜色' })
  } else {
    checks.push({ name: '阴影色彩配置', status: '⚠️ 需要改进', details: '阴影颜色配置不完整' })
  }
}

// 检查4: 模板中是否正确使用新配色
if (fs.existsSync(analyticsPath)) {
  const content = fs.readFileSync(analyticsPath, 'utf8')
  
  const hasCorrectTemplate = content.includes('background: metric.gradient') &&
                            content.includes('boxShadow: `0 8px 25px ${metric.shadowColor}`') &&
                            content.includes('color: \'white\'')
  
  if (hasCorrectTemplate) {
    checks.push({ name: '模板配色应用', status: '✅ 正确应用', details: '模板中正确使用了渐变和阴影' })
  } else {
    checks.push({ name: '模板配色应用', status: '⚠️ 可能有问题', details: '模板中的配色应用不完整' })
  }
}

// 检查5: CSS样式优化是否完整
if (fs.existsSync(analyticsPath)) {
  const content = fs.readFileSync(analyticsPath, 'utf8')
  
  const hasOptimizedStyles = content.includes('border-radius: 16px') &&
                            content.includes('cubic-bezier(0.4, 0, 0.2, 1)') &&
                            content.includes('transform: translateY(-4px) scale(1.02)') &&
                            content.includes('transform: scale(1.1) rotate(5deg)')
  
  if (hasOptimizedStyles) {
    checks.push({ name: 'CSS样式优化', status: '✅ 已完善', details: '包含现代化的动画和过渡效果' })
  } else {
    checks.push({ name: 'CSS样式优化', status: '⚠️ 部分优化', details: 'CSS优化可能不完整' })
  }
}

// 检查6: 趋势指示器美化是否完成
if (fs.existsSync(analyticsPath)) {
  const content = fs.readFileSync(analyticsPath, 'utf8')
  
  const hasTrendStyling = content.includes('backdrop-filter: blur(8px)') &&
                         content.includes('rgba(16, 185, 129, 0.1)') &&
                         content.includes('rgba(239, 68, 68, 0.1)')
  
  if (hasTrendStyling) {
    checks.push({ name: '趋势指示器美化', status: '✅ 已完成', details: '趋势指示器有毛玻璃效果和颜色区分' })
  } else {
    checks.push({ name: '趋势指示器美化', status: '⚠️ 需要改进', details: '趋势指示器样式不完整' })
  }
}

// 检查7: 动画效果是否添加
if (fs.existsSync(analyticsPath)) {
  const content = fs.readFileSync(analyticsPath, 'utf8')
  
  const hasAnimations = content.includes('@keyframes cardGlow') &&
                       content.includes('animation: cardGlow')
  
  if (hasAnimations) {
    checks.push({ name: '动画效果', status: '✅ 已添加', details: '包含卡片发光动画和悬停效果' })
  } else {
    checks.push({ name: '动画效果', status: '⚠️ 可能缺失', details: '动画效果配置不完整' })
  }
}

// 输出检查结果
console.log('📋 数据分析页面卡片配色优化验证报告\n')
console.log(''.padEnd(70, '='))

let passCount = 0
let warnCount = 0
let failCount = 0

checks.forEach((check, index) => {
  console.log(`${index + 1}. ${check.name.padEnd(20)} ${check.status}`)
  console.log(`   ${check.details}\n`)
  
  if (check.status.includes('✅')) passCount++
  else if (check.status.includes('⚠️')) warnCount++
  else failCount++
})

console.log(''.padEnd(70, '='))
console.log('📊 总结:')
console.log(`   ✅ 通过: ${passCount} 项`)
console.log(`   ⚠️ 警告: ${warnCount} 项`)
console.log(`   ❌ 失败: ${failCount} 项`)

if (failCount === 0 && warnCount === 0) {
  console.log('\n🎉 所有检查通过！卡片配色优化完成。')
} else if (failCount === 0) {
  console.log('\n⚠️ 基本优化完成，但可能需要进一步调整。')
} else {
  console.log('\n❌ 发现关键问题，需要进一步修复。')
}

console.log('\n🎨 配色优化包括:')
console.log('1. ✅ 四种不同的渐变配色方案:')
console.log('   - 紫蓝渐变 (#667eea → #764ba2) - 总访问量')
console.log('   - 粉红渐变 (#f093fb → #f5576c) - 活跃用户') 
console.log('   - 蓝青渐变 (#4facfe → #00f2fe) - 收入')
console.log('   - 绿青渐变 (#43e97b → #38f9d7) - 转化率')
console.log('2. ✅ 对应的阴影色彩，增强立体感')
console.log('3. ✅ 现代化的卡片样式和动画效果')
console.log('4. ✅ 优化的悬停效果和过渡动画')
console.log('5. ✅ 美化的趋势指示器样式')
console.log('6. ✅ 微妙的发光动画效果')

console.log('\n🔍 访问路径: /admin/analytics 或 /admin/dashboard/analytics')
console.log('💡 现在统计卡片应该展现出丰富的渐变色彩和现代化的视觉效果')
console.log('🎭 每个卡片都有独特的配色主题，提升了整体页面的视觉层次')

process.exit(failCount > 0 ? 1 : 0)