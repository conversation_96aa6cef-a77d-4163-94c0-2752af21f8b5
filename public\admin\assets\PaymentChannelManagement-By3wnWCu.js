/* empty css             *//* empty css                   *//* empty css                    *//* empty css                   *//* empty css                     *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                  *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css               *//* empty css                */import{l as e,q as a,G as l,A as s,r as t,M as n,o as r,m as i,E as o,B as d,z as u,F as c,Y as p}from"./vue-vendor-BcnDv-68.js";import{X as m,U as _,a1 as g,V as h,W as f,ae as v,af as y,b8 as b,b9 as w,aC as U,aS as C,ag as P,Y as V,ah as j,Z as k,ai as D,aj as x,al as F,ak as T,a7 as M,a2 as Y,a3 as z,a5 as S,a6 as N,a4 as I,b6 as G,b7 as O,a0 as B,aU as H,aV as L}from"./element-plus-C2UshkXo.js";import{p as $}from"./payment-CejeSQd2.js";import{_ as A}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const E={class:"payment-channel-management"},R={class:"overview-cards"},q={class:"card-content"},K={class:"card-icon total"},Q={class:"card-info"},W={class:"card-content"},X={class:"card-icon active"},Z={class:"card-info"},J={class:"card-content"},ee={class:"card-icon configs"},ae={class:"card-info"},le={class:"card-content"},se={class:"card-icon permissions"},te={class:"card-info"},ne={class:"toolbar"},re={class:"channel-info"},ie=["src","alt"];const oe=A({__name:"PaymentChannelManagement",setup(e,{expose:a}){a();const l=t(!1),s=t([]),i=t({total_channels:0,active_channels:0,inactive_channels:0,total_configs:0,active_configs:0,tested_configs:0,total_permissions:0,active_permissions:0,expiring_permissions:0}),o=n({visible:!1,loading:!1}),d=n({user_type:"",user_id:"",channel_codes:[],permission_types:[],expires_at:"",remark:""}),u=n({visible:!1,channelName:"",activeTab:"configured",configuredUsers:[],authorizedUsers:[]});r(()=>{c()});const c=async()=>{await Promise.all([p(),_()])},p=async()=>{try{l.value=!0;const e=await $.getChannels();s.value=e.data.map(e=>({...e,statusLoading:!1}))}catch(e){m.error("加载支付通道列表失败")}finally{l.value=!1}},_=async()=>{try{const e=await $.getOverview();i.value=e.data}catch(e){m.error("加载概览数据失败")}},g=()=>{Object.assign(d,{user_type:"",user_id:"",channel_codes:[],permission_types:[],expires_at:"",remark:""}),o.visible=!0},h={loading:l,channels:s,overview:i,grantPermissionDialog:o,grantPermissionForm:d,channelUsersDialog:u,loadData:c,loadChannels:p,loadOverview:_,toggleChannelStatus:async e=>{try{e.statusLoading=!0;const a=e.status?"启用":"禁用";await P.confirm(`确定要${a}支付通道"${e.channel_name}"吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await $.toggleStatus({channel_code:e.channel_code,status:e.status}),m.success(`${a}成功`),await _()}catch(a){e.status=!e.status,"cancel"!==a&&m.error("操作失败")}finally{e.statusLoading=!1}},showGrantPermissionDialog:g,grantChannelPermission:e=>{d.channel_codes=[e.channel_code],g()},submitGrantPermission:async()=>{try{if(!d.user_type||!d.user_id||0===d.channel_codes.length||0===d.permission_types.length)return void m.warning("请填写完整的授权信息");o.loading=!0;const e=[];for(const a of d.channel_codes)for(const l of d.permission_types)e.push({user_type:d.user_type,user_id:parseInt(d.user_id),channel_code:a,permission_type:l,expires_at:d.expires_at||null,remark:d.remark||null});await $.grantPermissions({permissions:e}),m.success("权限授权成功"),o.visible=!1,await _()}catch(e){m.error("权限授权失败")}finally{o.loading=!1}},viewChannelUsers:async e=>{try{u.channelName=e.channel_name,u.visible=!0;const a=await $.getChannelUsers(e.channel_code);u.configuredUsers=a.data.configured_users,u.authorizedUsers=a.data.authorized_users}catch(a){m.error("加载用户列表失败")}},editChannel:e=>{m.info("编辑功能开发中...")},refreshData:()=>{c()},getUserTypeTagType:e=>({user:"",distributor:"success",substation:"warning"}[e]||""),getUserTypeName:e=>({user:"普通用户",distributor:"分销商",substation:"分站管理员"}[e]||e),ref:t,reactive:n,onMounted:r,get ElMessage(){return m},get ElMessageBox(){return P},get CreditCard(){return C},get Check(){return U},get Setting(){return w},get Key(){return b},get Plus(){return y},get Refresh(){return v},get paymentChannelManagementApi(){return $}};return Object.defineProperty(h,"__isScriptSetup",{enumerable:!1,value:!0}),h}},[["render",function(t,n,r,m,v,y){const b=j,w=h,U=V,C=_,P=g,$=x,A=F,oe=T,de=D,ue=N,ce=S,pe=z,me=I,_e=O,ge=G,he=B,fe=Y,ve=f,ye=L,be=H,we=M;return i(),e("div",E,[n[25]||(n[25]=a("div",{class:"page-header"},[a("h1",null,"支付通道管理"),a("p",{class:"page-description"},"系统管理员可以控制支付通道的启用状态和用户权限")],-1)),a("div",R,[l(C,{gutter:20},{default:s(()=>[l(U,{span:6},{default:s(()=>[l(w,{class:"overview-card"},{default:s(()=>[a("div",q,[a("div",K,[l(b,null,{default:s(()=>[l(m.CreditCard)]),_:1})]),a("div",Q,[a("h3",null,k(m.overview.total_channels),1),n[10]||(n[10]=a("p",null,"总通道数",-1))])])]),_:1})]),_:1}),l(U,{span:6},{default:s(()=>[l(w,{class:"overview-card"},{default:s(()=>[a("div",W,[a("div",X,[l(b,null,{default:s(()=>[l(m.Check)]),_:1})]),a("div",Z,[a("h3",null,k(m.overview.active_channels),1),n[11]||(n[11]=a("p",null,"启用通道",-1))])])]),_:1})]),_:1}),l(U,{span:6},{default:s(()=>[l(w,{class:"overview-card"},{default:s(()=>[a("div",J,[a("div",ee,[l(b,null,{default:s(()=>[l(m.Setting)]),_:1})]),a("div",ae,[a("h3",null,k(m.overview.active_configs),1),n[12]||(n[12]=a("p",null,"活跃配置",-1))])])]),_:1})]),_:1}),l(U,{span:6},{default:s(()=>[l(w,{class:"overview-card"},{default:s(()=>[a("div",le,[a("div",se,[l(b,null,{default:s(()=>[l(m.Key)]),_:1})]),a("div",te,[a("h3",null,k(m.overview.active_permissions),1),n[13]||(n[13]=a("p",null,"有效权限",-1))])])]),_:1})]),_:1})]),_:1})]),a("div",ne,[l(P,{type:"primary",onClick:m.showGrantPermissionDialog},{default:s(()=>[l(b,null,{default:s(()=>[l(m.Plus)]),_:1}),n[14]||(n[14]=o(" 批量授权 ",-1))]),_:1,__:[14]}),l(P,{onClick:m.refreshData},{default:s(()=>[l(b,null,{default:s(()=>[l(m.Refresh)]),_:1}),n[15]||(n[15]=o(" 刷新 ",-1))]),_:1,__:[15]})]),l(w,{class:"channel-list-card"},{header:s(()=>n[16]||(n[16]=[a("div",{class:"card-header"},[a("span",null,"支付通道列表")],-1)])),default:s(()=>[d((i(),u(de,{data:m.channels,stripe:""},{default:s(()=>[l($,{prop:"channel_name",label:"通道名称","min-width":"120"},{default:s(({row:e})=>[a("div",re,[a("img",{src:e.channel_icon,alt:e.channel_name,class:"channel-icon"},null,8,ie),a("span",null,k(e.channel_name),1)])]),_:1}),l($,{prop:"channel_code",label:"通道代码",width:"120"}),l($,{prop:"status",label:"状态",width:"100"},{default:s(({row:e})=>[l(A,{modelValue:e.status,"onUpdate:modelValue":a=>e.status=a,onChange:a=>m.toggleChannelStatus(e),loading:e.statusLoading,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])]),_:1}),l($,{prop:"fee_rate",label:"手续费率",width:"100"},{default:s(({row:e})=>[o(k((100*e.fee_rate).toFixed(2))+"% ",1)]),_:1}),l($,{prop:"config_count",label:"配置数量",width:"100"},{default:s(({row:e})=>[l(oe,{type:"info"},{default:s(()=>[o(k(e.config_count),1)]),_:2},1024)]),_:1}),l($,{prop:"active_config_count",label:"活跃配置",width:"100"},{default:s(({row:e})=>[l(oe,{type:"success"},{default:s(()=>[o(k(e.active_config_count),1)]),_:2},1024)]),_:1}),l($,{label:"操作",width:"200",fixed:"right"},{default:s(({row:e})=>[l(P,{size:"small",onClick:a=>m.viewChannelUsers(e)},{default:s(()=>n[17]||(n[17]=[o(" 查看用户 ",-1)])),_:2,__:[17]},1032,["onClick"]),l(P,{size:"small",type:"primary",onClick:a=>m.grantChannelPermission(e)},{default:s(()=>n[18]||(n[18]=[o(" 授权 ",-1)])),_:2,__:[18]},1032,["onClick"]),l(P,{size:"small",onClick:a=>m.editChannel(e)},{default:s(()=>n[19]||(n[19]=[o(" 编辑 ",-1)])),_:2,__:[19]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[we,m.loading]])]),_:1}),l(ve,{modelValue:m.grantPermissionDialog.visible,"onUpdate:modelValue":n[7]||(n[7]=e=>m.grantPermissionDialog.visible=e),title:"批量授权支付通道权限",width:"800px","close-on-click-modal":!1},{footer:s(()=>[l(P,{onClick:n[6]||(n[6]=e=>m.grantPermissionDialog.visible=!1)},{default:s(()=>n[23]||(n[23]=[o("取消",-1)])),_:1,__:[23]}),l(P,{type:"primary",onClick:m.submitGrantPermission,loading:m.grantPermissionDialog.loading},{default:s(()=>n[24]||(n[24]=[o(" 确认授权 ",-1)])),_:1,__:[24]},8,["loading"])]),default:s(()=>[l(fe,{model:m.grantPermissionForm,"label-width":"120px"},{default:s(()=>[l(pe,{label:"用户类型"},{default:s(()=>[l(ce,{modelValue:m.grantPermissionForm.user_type,"onUpdate:modelValue":n[0]||(n[0]=e=>m.grantPermissionForm.user_type=e),placeholder:"请选择用户类型"},{default:s(()=>[l(ue,{label:"普通用户",value:"user"}),l(ue,{label:"分销商",value:"distributor"}),l(ue,{label:"分站管理员",value:"substation"})]),_:1},8,["modelValue"])]),_:1}),l(pe,{label:"用户ID"},{default:s(()=>[l(me,{modelValue:m.grantPermissionForm.user_id,"onUpdate:modelValue":n[1]||(n[1]=e=>m.grantPermissionForm.user_id=e),placeholder:"请输入用户ID",type:"number"},null,8,["modelValue"])]),_:1}),l(pe,{label:"支付通道"},{default:s(()=>[l(ce,{modelValue:m.grantPermissionForm.channel_codes,"onUpdate:modelValue":n[2]||(n[2]=e=>m.grantPermissionForm.channel_codes=e),multiple:"",placeholder:"请选择支付通道"},{default:s(()=>[(i(!0),e(c,null,p(m.channels,e=>(i(),u(ue,{key:e.channel_code,label:e.channel_name,value:e.channel_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(pe,{label:"权限类型"},{default:s(()=>[l(ge,{modelValue:m.grantPermissionForm.permission_types,"onUpdate:modelValue":n[3]||(n[3]=e=>m.grantPermissionForm.permission_types=e)},{default:s(()=>[l(_e,{label:"use"},{default:s(()=>n[20]||(n[20]=[o("使用权限",-1)])),_:1,__:[20]}),l(_e,{label:"config"},{default:s(()=>n[21]||(n[21]=[o("配置权限",-1)])),_:1,__:[21]}),l(_e,{label:"manage"},{default:s(()=>n[22]||(n[22]=[o("管理权限",-1)])),_:1,__:[22]})]),_:1},8,["modelValue"])]),_:1}),l(pe,{label:"过期时间"},{default:s(()=>[l(he,{modelValue:m.grantPermissionForm.expires_at,"onUpdate:modelValue":n[4]||(n[4]=e=>m.grantPermissionForm.expires_at=e),type:"datetime",placeholder:"选择过期时间（可选）",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(pe,{label:"备注"},{default:s(()=>[l(me,{modelValue:m.grantPermissionForm.remark,"onUpdate:modelValue":n[5]||(n[5]=e=>m.grantPermissionForm.remark=e),type:"textarea",placeholder:"请输入备注信息",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(ve,{modelValue:m.channelUsersDialog.visible,"onUpdate:modelValue":n[9]||(n[9]=e=>m.channelUsersDialog.visible=e),title:`${m.channelUsersDialog.channelName} - 用户列表`,width:"1000px"},{default:s(()=>[l(be,{modelValue:m.channelUsersDialog.activeTab,"onUpdate:modelValue":n[8]||(n[8]=e=>m.channelUsersDialog.activeTab=e)},{default:s(()=>[l(ye,{label:"已配置用户",name:"configured"},{default:s(()=>[l(de,{data:m.channelUsersDialog.configuredUsers,stripe:""},{default:s(()=>[l($,{prop:"owner_type",label:"用户类型",width:"120"},{default:s(({row:e})=>[l(oe,{type:m.getUserTypeTagType(e.owner_type)},{default:s(()=>[o(k(m.getUserTypeName(e.owner_type)),1)]),_:2},1032,["type"])]),_:1}),l($,{prop:"owner_id",label:"用户ID",width:"100"}),l($,{prop:"config_name",label:"配置名称"}),l($,{prop:"status",label:"状态",width:"100"},{default:s(({row:e})=>[l(oe,{type:e.status?"success":"danger"},{default:s(()=>[o(k(e.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),l($,{prop:"test_status",label:"测试状态",width:"100"},{default:s(({row:e})=>[l(oe,{type:e.test_status?"success":"warning"},{default:s(()=>[o(k(e.test_status?"已测试":"未测试"),1)]),_:2},1032,["type"])]),_:1}),l($,{prop:"created_at",label:"创建时间",width:"180"})]),_:1},8,["data"])]),_:1}),l(ye,{label:"已授权用户",name:"authorized"},{default:s(()=>[l(de,{data:m.channelUsersDialog.authorizedUsers,stripe:""},{default:s(()=>[l($,{prop:"user_type",label:"用户类型",width:"120"},{default:s(({row:e})=>[l(oe,{type:m.getUserTypeTagType(e.user_type)},{default:s(()=>[o(k(m.getUserTypeName(e.user_type)),1)]),_:2},1032,["type"])]),_:1}),l($,{prop:"user_id",label:"用户ID",width:"100"}),l($,{prop:"permission_type_name",label:"权限类型",width:"120"}),l($,{prop:"status",label:"状态",width:"100"},{default:s(({row:e})=>[l(oe,{type:e.status?"success":"danger"},{default:s(()=>[o(k(e.status?"有效":"无效"),1)]),_:2},1032,["type"])]),_:1}),l($,{prop:"granted_at",label:"授权时间",width:"180"}),l($,{prop:"expires_at",label:"过期时间",width:"180"},{default:s(({row:e})=>[o(k(e.expires_at||"永不过期"),1)]),_:1}),l($,{prop:"remark",label:"备注"})]),_:1},8,["data"])]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue","title"])])}],["__scopeId","data-v-86fc7970"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/payment/PaymentChannelManagement.vue"]]);export{oe as default};
