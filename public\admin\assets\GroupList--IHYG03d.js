const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/MediaUploader-CWV5P0gW.js","assets/vue-vendor-BcnDv-68.js","assets/element-plus-C2UshkXo.js","assets/index-eUTsTR3J.js","assets/utils-SdQ7DxjY.js","assets/echarts-D6CUuNS9.js","assets/index-XVEiIDg_.css","assets/MediaUploader-ByO6pya-.css","assets/base-pYMXRPpM.css","assets/el-overlay-CcMgTIy5.css","assets/el-upload-q8uObtwj.css","assets/el-progress-Dw9yTa91.css","assets/el-button-CDqfIFiK.css","assets/VideoUploader-D-S-VTCK.js","assets/VideoUploader-DWdy6Use.css","assets/el-form-item-DCFsf57O.css","assets/el-switch-B5lTGWdM.css","assets/el-input-Cz--kClu.css","assets/el-radio-group-BzMpJalG.css","assets/el-radio-button-CSkroacn.css","assets/AIContentGenerator-EZZ9SntM.js","assets/AIContentGenerator-D7ohkLzz.css","assets/el-card-fwQOLwdi.css","assets/el-timeline-item-BvbJTz1y.css","assets/el-rate-CTwMIh-J.css","assets/el-divider-BUtF_RGI.css","assets/el-input-number-DUUPPWGj.css","assets/el-col-Ds2mGN2S.css","assets/el-tag-DljBBxJR.css","assets/el-select-CvzM3W2w.css","assets/el-popper-Ba7_ER_z.css","assets/ContentTemplateLibrary-GmCYiGD-.js","assets/ContentTemplateLibrary-BnVI6Pq8.css","assets/AvatarLibrarySelector-DytNoyC1.js","assets/AvatarLibrarySelector-51RdxTM7.css","assets/el-checkbox-DIj50LEB.css","assets/ModernRichTextEditor-yvw2xTms.js","assets/ModernRichTextEditor-DMFQXIpc.css","assets/LayoutDesigner-DFCwJBl5.js","assets/LayoutDesigner-DvOgGUIV.css","assets/el-button-group-ePhtJS9H.css","assets/LandingPagePreview-DIzIwpum.js","assets/LandingPagePreview-8JJ9wi1R.css","assets/PreviewDialog-CyFBEQBQ.js","assets/PreviewDialog-B72M24X1.css","assets/el-collapse-item-BqS7tZDP.css","assets/GroupLandingPreview-D2ZziMrY.js","assets/GroupLandingPreview-CxIGTrhR.css","assets/PaidContentEditor-DAwoU7E0.js","assets/PaidContentEditor-825PZ7Zz.css","assets/el-dropdown-item-h8USmNax.css","assets/el-tab-pane-DTGC0oAx.css","assets/GroupTemplateSelector-B-glm6hi.js","assets/GroupTemplateSelector-CWsthD2h.css","assets/el-alert-G57rL0jl.css"])))=>i.map(i=>d[i]);
/* empty css             *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css               *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                        *//* empty css                         *//* empty css                    *//* empty css                  *//* empty css                */import{z as e,m as a,A as t,B as l,G as r,l as n,C as o,q as s,E as i,F as d,Y as u,W as c,r as m,c as p,M as _,w as g,aj as f,n as v,o as h,ag as y,D as b}from"./vue-vendor-BcnDv-68.js";import{a7 as w,aZ as C,V as x,U as V,Y as D,a3 as k,a4 as P,a5 as U,a6 as T,aq as S,ah as I,a1 as A,Z as R,_ as j,aL as M,al as z,ak as E,a2 as $,W as G,aE as q,at as L,b1 as O,bs as B,ct as F,aB as Q,ac as N,bu as J,bA as Z,ch as K,a9 as H,bT as W,av as Y,b2 as X,ad as ee,b9 as ae,af as te,ag as le,X as re,b5 as ne,aC as oe,bl as se,aG as ie,ap as de,bq as ue,br as ce,cj as me,bz as pe,cv as _e,cb as ge,aT as fe,aJ as ve,aI as he,aH as ye,aj as be,ai as we,an as Ce,cw as xe,cx as Ve,ax as De,cy as ke,au as Pe,$ as Ue,cz as Te,ab as Se,bt as Ie,ae as Ae,a_ as Re,aa as je,b4 as Me,ci as ze,aF as Ee,be as $e,bG as Ge,u as qe,s as Le,bg as Oe,aK as Be}from"./element-plus-C2UshkXo.js";import{s as Fe,_ as Qe,u as Ne,a as Je,g as Ze}from"./index-eUTsTR3J.js";/* empty css                   *//* empty css                     *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                 */import{e as Ke,f as He,h as We,i as Ye,j as Xe,k as ea,l as aa,m as ta}from"./community-CUcF7leP.js";/* empty css                  *//* empty css                         *//* empty css                   */import{f as la}from"./format-3eU4VJ9V.js";/* empty css                        */import{r as ra}from"./anti-block-CJ1NNk3N.js";import{f as na}from"./chunk-KZPPZA2C-C8HwxGb3.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";import"./index-D4AyIzGN.js";function oa(e,a){return Fe({url:`/advanced-content/groups/${e}/comprehensive-analysis`,method:"get",params:a})}function sa(e,a){return Fe({url:`/advanced-content/groups/${e}/optimize-content`,method:"post",data:a})}const ia={__name:"GroupDialog",props:{modelValue:{type:Boolean,default:!1},groupData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{expose:a,emit:t}){a();const l=f({loader:()=>Je(()=>import("./MediaUploader-CWV5P0gW.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12])),errorComponent:{template:'<div class="component-error">媒体上传组件加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),r=f({loader:()=>Je(()=>import("./VideoUploader-D-S-VTCK.js"),__vite__mapDeps([13,1,2,3,4,5,6,0,7,8,9,10,11,12,14,15,16,17,18,19])),errorComponent:{template:'<div class="component-error">视频上传组件加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),n=f({loader:()=>Je(()=>import("./AIContentGenerator-EZZ9SntM.js"),__vite__mapDeps([20,1,2,3,4,5,6,21,8,22,23,24,25,12,17,26,27,15,28,29,30,18,19])),errorComponent:{template:'<div class="component-error">AI内容生成组件加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),o=f({loader:()=>Je(()=>import("./ContentTemplateLibrary-GmCYiGD-.js"),__vite__mapDeps([31,1,2,3,4,5,6,32,8,9,15,17,26,25,22,28,27,29,30,12])),errorComponent:{template:'<div class="component-error">内容模板库组件加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),s=f({loader:()=>Je(()=>import("./AvatarLibrarySelector-DytNoyC1.js"),__vite__mapDeps([33,1,2,0,3,4,5,6,7,8,9,10,11,12,34,35,17,28,29,30])),errorComponent:{template:'<div class="component-error">头像库组件加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),i=f({loader:()=>Je(()=>import("./ModernRichTextEditor-yvw2xTms.js"),__vite__mapDeps([36,1,2,3,4,5,6,37])),errorComponent:{template:'<div class="component-error">富文本编辑器加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),d=f({loader:()=>Je(()=>import("./LayoutDesigner-DFCwJBl5.js"),__vite__mapDeps([38,1,2,3,4,5,6,39,8,15,17,26,12,25,16,40])),errorComponent:{template:'<div class="component-error">布局设计器加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),u=f({loader:()=>Je(()=>import("./LandingPagePreview-DIzIwpum.js"),__vite__mapDeps([41,1,2,3,4,5,6,42,8,12,28,9])),errorComponent:{template:'<div class="component-error">预览组件加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),c=f({loader:()=>Je(()=>import("./PreviewDialog-CyFBEQBQ.js"),__vite__mapDeps([43,1,2,41,3,4,5,6,42,8,12,28,9,44,45,40])),errorComponent:{template:'<div class="component-error">预览对话框加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),h=e,y=t,b=Ne(),w=m(null),C=m(null),x=m(!1),V=m(!1),D=m(""),k=m(!1),P=m("北京"),U=m(""),T=m(null),S=m(!0),I=m(!1),A=m(!1),R=p({get:()=>h.modelValue,set:e=>y("update:modelValue",e)}),j=p(()=>h.groupData&&h.groupData.id),M=p(()=>({...E,title:z.value,formatted_price:0===E.price?"免费":`¥${E.price}`})),z=p(()=>{if(!E.name)return"";if(1!==E.auto_city_replace)return E.name;const e=P.value;let a=E.name;switch(E.city_insert_strategy){case"prefix":a=e+"·"+E.name.replace(/^xxx/,"");break;case"suffix":a=E.name.replace(/xxx/,"")+"·"+e;break;case"natural":default:a=E.name.replace(/xxx/g,e);break;case"auto":a=E.name.includes("xxx")?E.name.replace(/xxx/g,e):e+"·"+E.name}return a}),E=_({name:"",category:"",price:0,max_members:500,description:"",banner_image:"",avatar:"",qr_code:"",avatar_library:"default",rich_content:"",gallery_images:[],intro_video:"",owner_name:"",owner_avatar:"",rules:"",introduction:"",keywords:"",current_members:0,virtual_members:0,status:1,is_recommended:!1,tags:[],announcement:"",layout_config:{sections:[{id:"banner",name:"顶部海报",visible:!0,order:1},{id:"info",name:"基础信息",visible:!0,order:2},{id:"content",name:"详细介绍",visible:!0,order:3},{id:"gallery",name:"图片展示",visible:!0,order:4},{id:"video",name:"介绍视频",visible:!0,order:5},{id:"members",name:"成员展示",visible:!0,order:6},{id:"qrcode",name:"二维码",visible:!0,order:7}]},auto_city_replace:0,city_insert_strategy:"auto"}),$=()=>{},G=()=>{Object.assign(E,{name:"",category:"",price:0,max_members:500,description:"",banner_image:"",avatar:"",qr_code:"",avatar_library:"default",rich_content:"",gallery_images:[],intro_video:"",owner_name:"",owner_avatar:"",rules:"",introduction:"",keywords:"",current_members:0,virtual_members:0,status:1,is_recommended:!1,tags:[],announcement:"",layout_config:{sections:[{id:"banner",name:"顶部海报",visible:!0,order:1},{id:"info",name:"基础信息",visible:!0,order:2},{id:"content",name:"详细介绍",visible:!0,order:3},{id:"gallery",name:"图片展示",visible:!0,order:4},{id:"video",name:"介绍视频",visible:!0,order:5},{id:"members",name:"成员展示",visible:!0,order:6},{id:"qrcode",name:"二维码",visible:!0,order:7}]},auto_city_replace:0,city_insert_strategy:"auto"})};g(()=>h.groupData,e=>{e&&Object.keys(e).length>0?Object.assign(E,{name:e.name||e.title||"",category:e.category||"",price:e.price||0,max_members:e.max_members||e.member_limit||500,description:e.description||"",banner_image:e.banner_image||"",avatar:e.avatar||"",qr_code:e.qr_code||"",avatar_library:e.avatar_library||"default",rich_content:e.rich_content||"",gallery_images:e.gallery_images||[],intro_video:e.intro_video||"",owner_name:e.owner_name||"",owner_avatar:e.owner_avatar||"",rules:e.rules||"",introduction:e.introduction||"",keywords:e.keywords||"",current_members:e.current_members||0,virtual_members:e.virtual_members||0,status:void 0!==e.status?e.status:1,is_recommended:e.is_recommended||!1,tags:e.tags||[],announcement:e.announcement||"",layout_config:e.layout_config||{sections:[{id:"banner",name:"顶部海报",visible:!0,order:1},{id:"info",name:"基础信息",visible:!0,order:2},{id:"content",name:"详细介绍",visible:!0,order:3},{id:"gallery",name:"图片展示",visible:!0,order:4},{id:"video",name:"介绍视频",visible:!0,order:5},{id:"members",name:"成员展示",visible:!0,order:6},{id:"qrcode",name:"二维码",visible:!0,order:7}]},auto_city_replace:e.auto_city_replace||0,city_insert_strategy:e.city_insert_strategy||"auto"}):G()},{immediate:!0,deep:!0});const ne=()=>{w.value?.resetFields(),G(),R.value=!1},oe={MediaUploader:l,VideoUploader:r,AIContentGenerator:n,ContentTemplateLibrary:o,AvatarLibrarySelector:s,ModernRichTextEditor:i,LayoutDesigner:d,LandingPagePreview:u,PreviewDialog:c,props:h,emit:y,userStore:b,formRef:w,inputRef:C,loading:x,inputVisible:V,inputValue:D,previewVisible:k,testCity:P,testResult:U,componentError:T,showAIAssistant:S,showAIGenerator:I,showTemplateLibrary:A,dialogVisible:R,isEdit:j,previewData:M,processedTitle:z,formData:E,formRules:{name:[{required:!0,message:"请输入群组名称",trigger:"blur"},{min:2,max:50,message:"群组名称长度在 2 到 50 个字符",trigger:"blur"}],category:[{required:!0,message:"请选择群组分类",trigger:"change"}],owner_name:[{required:!0,message:"请输入群主名称",trigger:"blur"},{max:50,message:"群主名称不能超过 50 个字符",trigger:"blur"}],rules:[{required:!0,message:"请输入群规",trigger:"blur"},{max:2e3,message:"群规不能超过 2000 个字符",trigger:"blur"}],price:[{required:!0,message:"请输入入群价格",trigger:"blur"},{type:"number",min:0,message:"价格不能小于0",trigger:"blur"}],max_members:[{required:!0,message:"请输入最大成员数",trigger:"blur"},{type:"number",min:1,max:500,message:"成员数在 1 到 500 之间",trigger:"blur"}],description:[{max:200,message:"描述长度不能超过 200 个字符",trigger:"blur"}],introduction:[{max:1e3,message:"群介绍不能超过 1000 个字符",trigger:"blur"}],keywords:[{max:200,message:"关键词不能超过 200 个字符",trigger:"blur"}]},updatePreview:$,refreshPreview:()=>{},resetForm:G,handleFullPreview:()=>{k.value=!0},handleCityToggle:e=>{e&&(E.name.includes("xxx")||re.info('建议在群组名称中使用"xxx"作为城市占位符'))},testCityReplacement:()=>{E.name?(U.value=z.value,re.success(`城市替换效果：${z.value}`)):re.warning("请先输入群组名称")},showInput:()=>{V.value=!0,v(()=>{C.value?.focus()})},handleInputConfirm:()=>{D.value&&!E.tags.includes(D.value)&&E.tags.push(D.value),V.value=!1,D.value=""},removeTag:e=>{const a=E.tags.indexOf(e);a>-1&&E.tags.splice(a,1)},handleSubmit:async()=>{if(w.value)try{await w.value.validate(),x.value=!0;const e={...E,title:z.value,member_limit:E.max_members};j.value?(await Ke(h.groupData.id,e),re.success("群组更新成功")):(await He(e),re.success("群组创建成功")),y("success"),ne()}catch(e){e.response?.data?.message?re.error(e.response.data.message):re.error(j.value?"更新失败":"创建失败")}finally{x.value=!1}},analyzeContent:async()=>{if(E.name||E.description||E.introduction)try{re.info("正在分析内容，请稍候...");const e={content_type:"comprehensive",include_suggestions:!0,current_content:{title:E.name||"",description:E.description||"",introduction:E.introduction||"",rules:E.rules||"",keywords:E.keywords||"",price:E.price||0}},a=Date.now(),t=await oa(a,e);re.success("内容分析完成");const l=t.data||{},r=`\n📊 内容分析报告\n\n🎯 质量评分：${l.quality_score||0}/100\n📈 完整度：${l.completeness||0}%\n✨ 吸引力：${l.attractiveness||0}/10\n🔥 转化潜力：${l.conversion_potential||0}/10\n\n💡 主要建议：\n${(l.suggestions||["暂无建议"]).join("\n")}\n    `.trim();le.alert(r,"内容分析报告",{type:"info",customClass:"analysis-report-dialog"})}catch(e){re.error(`内容分析失败：${e.message||"请重试"}`)}else re.warning("请先填写一些内容再进行分析")},optimizeContent:async()=>{if(E.name||E.description||E.introduction)try{re.info("正在生成优化建议，请稍候...");const e={title:E.name||"",description:E.description||"",introduction:E.introduction||"",rules:E.rules||"",keywords:E.keywords||"",price:E.price||0,category:E.category||""},a=Date.now(),t=await sa(a,{current_content:e,optimization_type:"comprehensive",focus_areas:["conversion","engagement","clarity"]});re.success("优化建议生成完成");const l=t.data||{},r=l.suggestions||[],n=l.optimized_content||{};if(0===r.length)return void re.info("当前内容已经很优秀，暂无优化建议");const o=`\n🚀 智能优化建议\n\n${r.map((e,a)=>`${a+1}. ${e}`).join("\n")}\n\n💡 优化后预期效果：\n• 转化率提升：${l.conversion_improvement||"10-20"}%\n• 用户参与度提升：${l.engagement_improvement||"15-25"}%\n\n是否应用这些优化建议？\n    `.trim();le.confirm(o,"智能优化建议",{type:"info",confirmButtonText:"应用优化",cancelButtonText:"暂不应用"}).then(()=>{n&&Object.keys(n).length>0&&(n.title&&(E.name=n.title),n.description&&(E.description=n.description),n.introduction&&(E.introduction=n.introduction),n.rules&&(E.rules=n.rules),n.keywords&&(E.keywords=n.keywords),re.success("优化建议已应用到表单"))}).catch(()=>{re.info("已取消应用优化建议")})}catch(e){re.error(`内容优化失败：${e.message||"请重试"}`)}else re.warning("请先填写一些内容再进行优化")},handleAIGenerated:e=>{if(e)try{if("title"===e.type&&e.content)E.name=e.content,re.success(`群组名称已更新：${e.content.substring(0,20)}...`);else if("description"===e.type&&e.content)E.description=e.content,re.success("群组描述已更新");else if("introduction"===e.type&&e.content)E.introduction=e.content,re.success("群组介绍已更新");else if("rules"===e.type&&e.content)E.rules=e.content,re.success("群规内容已更新");else{if("keywords"!==e.type||!e.content)return void re.warning("未识别的内容类型或内容为空");E.keywords=e.content,re.success("关键词已更新")}le.confirm("内容已成功应用到表单，是否继续生成其他内容？","应用成功",{confirmButtonText:"继续生成",cancelButtonText:"关闭生成器",type:"success"}).catch(()=>{I.value=!1})}catch(a){re.error(`应用AI内容失败：${a.message||"未知错误"}`)}else re.warning("未收到生成的内容")},handleTemplateSelected:e=>{if(e)try{const a=`\n📋 模板信息\n名称：${e.title||"未命名模板"}\n分类：${e.category||"通用"}\n描述：${e.description||"无描述"}\n\n📝 包含内容：\n${e.content?.title?"✅ 群组名称":"❌ 群组名称"}\n${e.content?.description?"✅ 群组描述":"❌ 群组描述"}\n${e.content?.introduction?"✅ 群组介绍":"❌ 群组介绍"}\n${e.content?.rules?"✅ 群规内容":"❌ 群规内容"}\n${e.content?.keywords?"✅ 关键词":"❌ 关键词"}\n\n⚠️ 应用模板将覆盖当前内容，是否继续？\n    `.trim();le.confirm(a,"确认应用模板",{confirmButtonText:"应用模板",cancelButtonText:"取消",type:"warning"}).then(()=>{if(e.content){const a="string"==typeof e.content?JSON.parse(e.content):e.content;let t=[];a.title&&(E.name=a.title,t.push("名称")),a.description&&(E.description=a.description,t.push("描述")),a.introduction&&(E.introduction=a.introduction,t.push("介绍")),a.rules&&(E.rules=a.rules,t.push("群规")),a.keywords&&(E.keywords=a.keywords,t.push("关键词")),void 0!==a.price&&(E.price=a.price,t.push("价格")),a.category&&(E.category=a.category,t.push("分类")),re.success(`模板"${e.title}"已应用，包含：${t.join("、")}`),A.value=!1}}).catch(()=>{re.info("已取消应用模板")})}catch(a){re.error(`应用模板失败：${a.message||"解析模板内容出错"}`)}else re.warning("未选择模板")},handleClose:ne,ref:m,reactive:_,computed:p,watch:g,nextTick:v,defineAsyncComponent:f,get ElMessage(){return re},get ElMessageBox(){return le},get Plus(){return te},get Setting(){return ae},get Picture(){return ee},get Document(){return X},get Grid(){return Y},get Location(){return W},get User(){return H},get Tools(){return K},get InfoFilled(){return Z},get RefreshRight(){return J},get View(){return N},get Star(){return Q},get MagicStick(){return F},get Collection(){return B},get TrendCharts(){return O},get ArrowUp(){return L},get ArrowDown(){return q},get useUserStore(){return Ne},get createGroup(){return He},get updateGroup(){return Ke},get analyzeContentAPI(){return oa},get optimizeContentAPI(){return sa}};return Object.defineProperty(oe,"__isScriptSetup",{enumerable:!1,value:!0}),oe}},da={class:"dialog-content"},ua={key:1,class:"form-section"},ca={class:"card-header"},ma={class:"card-header"},pa={class:"card-header"},_a={class:"card-header"},ga={class:"card-header"},fa={key:0,class:"ai-assistant-content"},va={class:"ai-tools-bar"},ha={key:0,class:"ai-generator-panel"},ya={key:1,class:"template-library-panel"},ba={class:"card-header"},wa={class:"card-header"},Ca={class:"card-header"},xa={class:"test-tip"},Va={key:0,class:"test-result"},Da={class:"preview-section"},ka={class:"preview-container"},Pa={class:"preview-header"},Ua={class:"preview-actions"},Ta={class:"preview-content"},Sa={class:"dialog-footer"};const Ia=Qe(ia,[["render",function(m,p,_,g,f,v){const h=C,y=I,b=P,q=k,L=D,O=T,B=U,F=V,Q=S,N=x,J=A,Z=M,K=j,H=z,W=E,Y=$,X=G,ee=w;return a(),e(X,{modelValue:g.dialogVisible,"onUpdate:modelValue":p[31]||(p[31]=e=>g.dialogVisible=e),title:g.isEdit?"编辑群组":"创建群组",width:"1200px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:g.handleClose,class:"enhanced-group-dialog","destroy-on-close":!0},{footer:t(()=>[s("div",Sa,[r(J,{onClick:g.handleClose},{default:t(()=>p[57]||(p[57]=[i("取消",-1)])),_:1,__:[57]}),r(J,{onClick:g.handleFullPreview,icon:g.View,plain:""},{default:t(()=>p[58]||(p[58]=[i(" 实时预览 ",-1)])),_:1,__:[58]},8,["icon"]),r(J,{type:"primary",loading:g.loading,onClick:g.handleSubmit},{default:t(()=>[i(R(g.isEdit?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:t(()=>[l((a(),n("div",da,[g.componentError?(a(),e(h,{key:0,title:"组件加载失败",type:"error",description:g.componentError,"show-icon":"",closable:!1,style:{"margin-bottom":"20px"}},null,8,["description"])):o("",!0),g.componentError?o("",!0):(a(),n("div",ua,[r(Y,{ref:"formRef",model:g.formData,rules:g.formRules,"label-width":"120px",class:"enhanced-form"},{default:t(()=>[r(N,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",ca,[r(y,null,{default:t(()=>[r(g.Setting)]),_:1}),p[32]||(p[32]=s("span",null,"基础信息",-1))])]),default:t(()=>[r(F,{gutter:20},{default:t(()=>[r(L,{span:12},{default:t(()=>[r(q,{label:"群组名称",prop:"name"},{default:t(()=>[r(b,{modelValue:g.formData.name,"onUpdate:modelValue":p[0]||(p[0]=e=>g.formData.name=e),placeholder:"请输入群组名称",maxlength:"50","show-word-limit":"",onInput:g.updatePreview},null,8,["modelValue"]),p[33]||(p[33]=s("div",{class:"form-tip"},' 💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市 ',-1))]),_:1,__:[33]})]),_:1}),r(L,{span:12},{default:t(()=>[r(q,{label:"群组分类",prop:"category"},{default:t(()=>[r(B,{modelValue:g.formData.category,"onUpdate:modelValue":p[1]||(p[1]=e=>g.formData.category=e),placeholder:"请选择分类",style:{width:"100%"},onChange:g.updatePreview},{default:t(()=>[r(O,{label:"创业交流",value:"startup"}),r(O,{label:"投资理财",value:"finance"}),r(O,{label:"科技互联网",value:"tech"}),r(O,{label:"教育培训",value:"education"}),r(O,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),r(F,{gutter:20},{default:t(()=>[r(L,{span:12},{default:t(()=>[r(q,{label:"群组价格",prop:"price"},{default:t(()=>[r(Q,{modelValue:g.formData.price,"onUpdate:modelValue":p[2]||(p[2]=e=>g.formData.price=e),min:0,max:9999,precision:2,style:{width:"100%"},placeholder:"0.00",onChange:g.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(L,{span:12},{default:t(()=>[r(q,{label:"最大成员数",prop:"max_members"},{default:t(()=>[r(Q,{modelValue:g.formData.max_members,"onUpdate:modelValue":p[3]||(p[3]=e=>g.formData.max_members=e),min:1,max:500,style:{width:"100%"},placeholder:"500",onChange:g.updatePreview},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),r(q,{label:"群组描述"},{default:t(()=>[r(b,{modelValue:g.formData.description,"onUpdate:modelValue":p[4]||(p[4]=e=>g.formData.description=e),type:"textarea",rows:3,placeholder:"请输入群组描述",maxlength:"200","show-word-limit":"",onInput:g.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(N,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",ma,[r(y,null,{default:t(()=>[r(g.Picture)]),_:1}),p[34]||(p[34]=s("span",null,"多媒体内容",-1))])]),default:t(()=>[r(q,{label:"顶部海报"},{default:t(()=>[r(g.MediaUploader,{modelValue:g.formData.banner_image,"onUpdate:modelValue":p[5]||(p[5]=e=>g.formData.banner_image=e),type:"image",limit:1,accept:"image/*",onChange:g.updatePreview},{tip:t(()=>p[35]||(p[35]=[s("div",{class:"upload-tip"}," 建议尺寸：750x400px，支持JPG、PNG格式 ",-1)])),_:1},8,["modelValue"])]),_:1}),r(F,{gutter:20},{default:t(()=>[r(L,{span:12},{default:t(()=>[r(q,{label:"群组头像"},{default:t(()=>[r(g.MediaUploader,{modelValue:g.formData.avatar,"onUpdate:modelValue":p[6]||(p[6]=e=>g.formData.avatar=e),type:"image",limit:1,accept:"image/*","list-type":"picture-card",onChange:g.updatePreview},{tip:t(()=>p[36]||(p[36]=[s("div",{class:"upload-tip"},"建议尺寸：200x200px",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1}),r(L,{span:12},{default:t(()=>[r(q,{label:"二维码"},{default:t(()=>[r(g.MediaUploader,{modelValue:g.formData.qr_code,"onUpdate:modelValue":p[7]||(p[7]=e=>g.formData.qr_code=e),type:"image",limit:1,accept:"image/*","list-type":"picture-card",onChange:g.updatePreview},{tip:t(()=>p[37]||(p[37]=[s("div",{class:"upload-tip"},"群组二维码图片",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),r(q,{label:"成员头像库"},{default:t(()=>[r(g.AvatarLibrarySelector,{modelValue:g.formData.avatar_library,"onUpdate:modelValue":p[8]||(p[8]=e=>g.formData.avatar_library=e),onChange:g.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(N,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",pa,[r(y,null,{default:t(()=>[r(g.Document)]),_:1}),p[38]||(p[38]=s("span",null,"内容编辑",-1))])]),default:t(()=>[r(q,{label:"详细介绍"},{default:t(()=>[r(g.ModernRichTextEditor,{modelValue:g.formData.rich_content,"onUpdate:modelValue":p[9]||(p[9]=e=>g.formData.rich_content=e),height:300,placeholder:"请输入群组的详细介绍，支持富文本格式...","max-length":5e3,onChange:g.updatePreview},null,8,["modelValue"])]),_:1}),r(q,{label:"展示图片"},{default:t(()=>[r(g.MediaUploader,{modelValue:g.formData.gallery_images,"onUpdate:modelValue":p[10]||(p[10]=e=>g.formData.gallery_images=e),type:"image",limit:9,accept:"image/*",multiple:"",onChange:g.updatePreview},{tip:t(()=>p[39]||(p[39]=[s("div",{class:"upload-tip"}," 最多上传9张图片，支持拖拽排序 ",-1)])),_:1},8,["modelValue"])]),_:1}),r(q,{label:"介绍视频"},{default:t(()=>[r(g.VideoUploader,{modelValue:g.formData.intro_video,"onUpdate:modelValue":p[11]||(p[11]=e=>g.formData.intro_video=e),onChange:g.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(N,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",_a,[r(y,null,{default:t(()=>[r(g.User)]),_:1}),p[40]||(p[40]=s("span",null,"群主信息",-1))])]),default:t(()=>[r(F,{gutter:20},{default:t(()=>[r(L,{span:12},{default:t(()=>[r(q,{label:"群主名称",prop:"owner_name"},{default:t(()=>[r(b,{modelValue:g.formData.owner_name,"onUpdate:modelValue":p[12]||(p[12]=e=>g.formData.owner_name=e),placeholder:"请输入群主名称",maxlength:"50","show-word-limit":"",onInput:g.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(L,{span:12},{default:t(()=>[r(q,{label:"群主头像",prop:"owner_avatar"},{default:t(()=>[r(b,{modelValue:g.formData.owner_avatar,"onUpdate:modelValue":p[13]||(p[13]=e=>g.formData.owner_avatar=e),placeholder:"群主头像URL（可选）",onInput:g.updatePreview},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),r(N,{class:"form-card ai-assistant-card",shadow:"never"},{header:t(()=>[s("div",ga,[r(y,null,{default:t(()=>[r(g.Star)]),_:1}),p[41]||(p[41]=s("span",null,"智能内容助手",-1)),r(J,{type:"text",onClick:p[14]||(p[14]=e=>g.showAIAssistant=!g.showAIAssistant),class:"toggle-button"},{default:t(()=>[i(R(g.showAIAssistant?"收起":"展开")+" ",1),r(y,null,{default:t(()=>[g.showAIAssistant?(a(),e(g.ArrowUp,{key:1})):(a(),e(g.ArrowDown,{key:0}))]),_:1})]),_:1})])]),default:t(()=>[g.showAIAssistant?(a(),n("div",fa,[s("div",va,[r(F,{gutter:12},{default:t(()=>[r(L,{span:6},{default:t(()=>[r(J,{onClick:p[15]||(p[15]=e=>g.showAIGenerator=!g.showAIGenerator),type:"primary",size:"small",style:{width:"100%"}},{default:t(()=>[r(y,null,{default:t(()=>[r(g.MagicStick)]),_:1}),p[42]||(p[42]=i(" AI生成助手 ",-1))]),_:1,__:[42]})]),_:1}),r(L,{span:6},{default:t(()=>[r(J,{onClick:p[16]||(p[16]=e=>g.showTemplateLibrary=!g.showTemplateLibrary),type:"success",size:"small",style:{width:"100%"}},{default:t(()=>[r(y,null,{default:t(()=>[r(g.Collection)]),_:1}),p[43]||(p[43]=i(" 模板库 ",-1))]),_:1,__:[43]})]),_:1}),r(L,{span:6},{default:t(()=>[r(J,{onClick:g.analyzeContent,type:"info",size:"small",style:{width:"100%"}},{default:t(()=>[r(y,null,{default:t(()=>[r(g.TrendCharts)]),_:1}),p[44]||(p[44]=i(" 内容分析 ",-1))]),_:1,__:[44]})]),_:1}),r(L,{span:6},{default:t(()=>[r(J,{onClick:g.optimizeContent,type:"warning",size:"small",style:{width:"100%"}},{default:t(()=>[r(y,null,{default:t(()=>[r(g.TrendCharts)]),_:1}),p[45]||(p[45]=i(" 智能优化 ",-1))]),_:1,__:[45]})]),_:1})]),_:1})]),g.showAIGenerator?(a(),n("div",ha,[r(h,{title:"AI内容生成器",type:"info",description:"AI内容生成功能正在加载中...","show-icon":"",closable:!1})])):o("",!0),g.showTemplateLibrary?(a(),n("div",ya,[r(h,{title:"内容模板库",type:"success",description:"内容模板库功能正在加载中...","show-icon":"",closable:!1})])):o("",!0)])):o("",!0)]),_:1}),r(N,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",ba,[r(y,null,{default:t(()=>[r(g.Tools)]),_:1}),p[46]||(p[46]=s("span",null,"其他设置",-1))])]),default:t(()=>[r(q,{label:"群规",prop:"rules"},{default:t(()=>[r(b,{modelValue:g.formData.rules,"onUpdate:modelValue":p[17]||(p[17]=e=>g.formData.rules=e),type:"textarea",rows:4,placeholder:"请输入群规内容",maxlength:"2000","show-word-limit":"",onInput:g.updatePreview},null,8,["modelValue"])]),_:1}),r(q,{label:"群介绍",prop:"introduction"},{default:t(()=>[r(b,{modelValue:g.formData.introduction,"onUpdate:modelValue":p[18]||(p[18]=e=>g.formData.introduction=e),type:"textarea",rows:3,placeholder:"请输入群介绍",maxlength:"1000","show-word-limit":"",onInput:g.updatePreview},null,8,["modelValue"])]),_:1}),r(q,{label:"关键词",prop:"keywords"},{default:t(()=>[r(b,{modelValue:g.formData.keywords,"onUpdate:modelValue":p[19]||(p[19]=e=>g.formData.keywords=e),placeholder:"请输入关键词，用逗号分隔",maxlength:"200",onInput:g.updatePreview},null,8,["modelValue"])]),_:1}),r(F,{gutter:20},{default:t(()=>[r(L,{span:12},{default:t(()=>[r(q,{label:"虚拟成员数",prop:"virtual_members"},{default:t(()=>[r(Q,{modelValue:g.formData.virtual_members,"onUpdate:modelValue":p[20]||(p[20]=e=>g.formData.virtual_members=e),min:0,max:500,style:{width:"100%"},placeholder:"0",onChange:g.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(L,{span:12},{default:t(()=>[r(q,{label:"当前成员数",prop:"current_members"},{default:t(()=>[r(Q,{modelValue:g.formData.current_members,"onUpdate:modelValue":p[21]||(p[21]=e=>g.formData.current_members=e),min:0,max:500,style:{width:"100%"},placeholder:"0",onChange:g.updatePreview},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),r(F,{gutter:20},{default:t(()=>[r(L,{span:12},{default:t(()=>[r(q,{label:"群组状态",prop:"status"},{default:t(()=>[r(K,{modelValue:g.formData.status,"onUpdate:modelValue":p[22]||(p[22]=e=>g.formData.status=e),onChange:g.updatePreview},{default:t(()=>[r(Z,{label:1},{default:t(()=>p[47]||(p[47]=[i("活跃",-1)])),_:1,__:[47]}),r(Z,{label:0},{default:t(()=>p[48]||(p[48]=[i("暂停",-1)])),_:1,__:[48]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),r(L,{span:12},{default:t(()=>[r(q,{label:"是否推荐",prop:"is_recommended"},{default:t(()=>[r(H,{modelValue:g.formData.is_recommended,"onUpdate:modelValue":p[23]||(p[23]=e=>g.formData.is_recommended=e),"active-text":"推荐","inactive-text":"不推荐",onChange:g.updatePreview},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),r(q,{label:"群组标签"},{default:t(()=>[(a(!0),n(d,null,u(g.formData.tags,l=>(a(),e(W,{key:l,closable:"",onClose:e=>g.removeTag(l),style:{"margin-right":"8px","margin-bottom":"8px"}},{default:t(()=>[i(R(l),1)]),_:2},1032,["onClose"]))),128)),g.inputVisible?(a(),e(b,{key:0,ref:"inputRef",modelValue:g.inputValue,"onUpdate:modelValue":p[24]||(p[24]=e=>g.inputValue=e),size:"small",style:{width:"100px"},onKeyup:c(g.handleInputConfirm,["enter"]),onBlur:g.handleInputConfirm},null,8,["modelValue"])):(a(),e(J,{key:1,size:"small",onClick:g.showInput},{default:t(()=>p[49]||(p[49]=[i("+ 添加标签",-1)])),_:1,__:[49]}))]),_:1}),r(q,{label:"群组公告"},{default:t(()=>[r(g.ModernRichTextEditor,{modelValue:g.formData.announcement,"onUpdate:modelValue":p[25]||(p[25]=e=>g.formData.announcement=e),height:200,placeholder:"请输入群组公告",onChange:g.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(N,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",wa,[r(y,null,{default:t(()=>[r(g.Grid)]),_:1}),p[50]||(p[50]=s("span",null,"布局设计",-1))])]),default:t(()=>[r(g.LayoutDesigner,{modelValue:g.formData.layout_config,"onUpdate:modelValue":p[26]||(p[26]=e=>g.formData.layout_config=e),onChange:g.updatePreview},null,8,["modelValue"])]),_:1}),r(N,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",Ca,[r(y,null,{default:t(()=>[r(g.Location)]),_:1}),p[51]||(p[51]=s("span",null,"城市定位",-1))])]),default:t(()=>[r(q,null,{default:t(()=>[r(H,{modelValue:g.formData.auto_city_replace,"onUpdate:modelValue":p[27]||(p[27]=e=>g.formData.auto_city_replace=e),"active-value":1,"inactive-value":0,onChange:g.handleCityToggle},null,8,["modelValue"]),p[52]||(p[52]=s("span",{class:"switch-label"},"启用城市定位功能",-1))]),_:1,__:[52]}),1===g.formData.auto_city_replace?(a(),n(d,{key:0},[r(q,{label:"插入策略"},{default:t(()=>[r(B,{modelValue:g.formData.city_insert_strategy,"onUpdate:modelValue":p[28]||(p[28]=e=>g.formData.city_insert_strategy=e),style:{width:"100%"},onChange:g.updatePreview},{default:t(()=>[r(O,{label:"智能判断（推荐）",value:"auto"}),r(O,{label:"前缀模式（城市·标题）",value:"prefix"}),r(O,{label:"后缀模式（标题·城市）",value:"suffix"}),r(O,{label:"自然插入（智能融入）",value:"natural"})]),_:1},8,["modelValue"])]),_:1}),r(q,{label:"测试效果"},{default:t(()=>[s("div",xa,[r(y,null,{default:t(()=>[r(g.InfoFilled)]),_:1}),p[53]||(p[53]=s("span",null,"此处仅用于测试城市替换效果，实际用户访问落地页时会自动根据其IP获取真实城市",-1))]),r(F,{gutter:12},{default:t(()=>[r(L,{span:8},{default:t(()=>[r(b,{modelValue:g.testCity,"onUpdate:modelValue":p[29]||(p[29]=e=>g.testCity=e),placeholder:"输入测试城市"},null,8,["modelValue"])]),_:1}),r(L,{span:8},{default:t(()=>[r(J,{onClick:g.testCityReplacement,size:"small"},{default:t(()=>p[54]||(p[54]=[i("测试替换效果",-1)])),_:1,__:[54]})]),_:1}),r(L,{span:8},{default:t(()=>[g.testResult?(a(),n("span",Va,R(g.testResult),1)):o("",!0)]),_:1})]),_:1})]),_:1})],64)):o("",!0)]),_:1})]),_:1},8,["model"])])),s("div",Da,[s("div",ka,[s("div",Pa,[p[56]||(p[56]=s("span",null,"实时预览",-1)),s("div",Ua,[r(J,{onClick:g.refreshPreview,icon:g.RefreshRight,size:"small",circle:""},null,8,["icon"]),r(J,{onClick:g.handleFullPreview,icon:g.View,type:"primary",size:"small"},{default:t(()=>p[55]||(p[55]=[i(" 全屏预览 ",-1)])),_:1,__:[55]},8,["icon"])])]),s("div",Ta,[r(g.LandingPagePreview,{"group-data":g.previewData,"layout-config":g.formData.layout_config},null,8,["group-data","layout-config"])])])])])),[[ee,g.componentError]]),r(g.PreviewDialog,{modelValue:g.previewVisible,"onUpdate:modelValue":p[30]||(p[30]=e=>g.previewVisible=e),"group-data":g.previewData,"layout-config":g.formData.layout_config},null,8,["modelValue","group-data","layout-config"])]),_:1},8,["modelValue","title"])}],["__scopeId","data-v-6b452d1d"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/community/components/GroupDialog.vue"]]);function Aa(e,a=.01,t=.9){const l=e+t;return`为避免大量相同金额支付触发银行风控，实际支付金额将在 ¥${Math.max(.01,e-t).toFixed(2)} ~ ¥${l.toFixed(2)} 范围内随机调整`}function Ra(e){return 0===e?{recommend:!1,reason:"免费群组无需启用随机金额调整"}:e<1?{recommend:!1,reason:"价格过低，随机调整可能影响用户体验"}:e>=1&&e<=100?{recommend:!0,reason:"该价格区间建议启用随机金额调整以避免风控"}:e>100?{recommend:!0,reason:"高价格群组强烈建议启用随机金额调整"}:{recommend:!1,reason:"请根据实际情况决定是否启用"}}function ja(e){return Fe({url:"/admin/groups",method:"post",data:e})}function Ma(e,a){return Fe({url:`/admin/groups/${e}`,method:"put",data:a})}const za={class:"group-create-complete"},Ea={class:"page-header"},$a={class:"header-content"},Ga={class:"header-left"},qa={class:"header-icon"},La={class:"header-text"},Oa={class:"header-actions"},Ba={class:"main-content"},Fa={class:"form-section"},Qa={class:"card-header"},Na={class:"price-input-wrapper"},Ja={class:"price-quick-actions"},Za={class:"random-amount-wrapper"},Ka={key:0,class:"random-config"},Ha={class:"config-row"},Wa={key:0,class:"random-preview"},Ya={class:"preview-range"},Xa={class:"form-tip"},et={class:"card-header"},at={class:"card-header"},tt={class:"card-header"},lt={class:"card-header"},rt={key:0,class:"ai-assistant-content"},nt={class:"ai-tools-bar"},ot={key:0,class:"ai-generator-panel"},st={class:"generator-options"},it={class:"card-header"},dt={class:"card-header"},ut={class:"test-tip"},ct={key:0,class:"test-result"},mt={class:"card-header"},pt={class:"card-header"},_t={class:"preview-section"},gt={class:"preview-container"},ft={class:"preview-header"},vt={class:"preview-actions"},ht={class:"preview-content"};const yt=Qe({__name:"GroupCreateComplete",props:{groupData:{type:Object,default:()=>({})}},emits:["success","cancel"],setup(e,{expose:a,emit:t}){a();const l=f({loader:()=>Je(()=>import("./MediaUploader-CWV5P0gW.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12])),errorComponent:{template:'<div class="component-error">媒体上传组件加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),r=f({loader:()=>Je(()=>import("./VideoUploader-D-S-VTCK.js"),__vite__mapDeps([13,1,2,3,4,5,6,0,7,8,9,10,11,12,14,15,16,17,18,19])),errorComponent:{template:'<div class="component-error">视频上传组件加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),n=f({loader:()=>Je(()=>import("./ModernRichTextEditor-yvw2xTms.js"),__vite__mapDeps([36,1,2,3,4,5,6,37])),errorComponent:{template:'<div class="component-error">富文本编辑器加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),o=f({loader:()=>Je(()=>import("./GroupLandingPreview-D2ZziMrY.js"),__vite__mapDeps([46,1,2,3,4,5,6,47,8,12])),errorComponent:{template:'<div class="component-error">预览组件加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),s=f({loader:()=>Je(()=>import("./PaidContentEditor-DAwoU7E0.js"),__vite__mapDeps([48,3,1,2,4,5,6,49,8,12,40,30,50,51,15,17])),errorComponent:{template:'<div class="component-error">付费内容编辑器加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),i=f({loader:()=>Je(()=>import("./GroupTemplateSelector-B-glm6hi.js"),__vite__mapDeps([52,1,2,3,4,5,6,53,8,12,54])),errorComponent:{template:'<div class="component-error">模板选择器加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),d=f({loader:()=>Je(()=>import("./AvatarLibrarySelector-DytNoyC1.js"),__vite__mapDeps([33,1,2,0,3,4,5,6,7,8,9,10,11,12,34,35,17,28,29,30])),errorComponent:{template:'<div class="component-error">头像库组件加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),u=f({loader:()=>Je(()=>import("./PreviewDialog-CyFBEQBQ.js"),__vite__mapDeps([43,1,2,41,3,4,5,6,42,8,12,28,9,44,45,40])),errorComponent:{template:'<div class="component-error">预览对话框加载失败</div>'},loadingComponent:{template:'<div class="component-loading">加载中...</div>'}}),c=e,h=t,y=m(null),b=m(null),w=m(!1),C=m(!1),x=m(""),V=m(!1),D=m(!1),k=m("{{city}}"),P=m(""),U=m(null),T=m(!0),S=m(!1),I=_({name:"",category:"",price:0,max_members:500,description:"",banner_image:"",avatar:"",avatar_library:[],gallery_images:[],intro_video:"",rich_content:"",announcement:"",owner_name:"",owner_avatar:"",rules:"",introduction:"",keywords:"",virtual_members:0,current_members:0,status:1,is_recommended:!1,tags:[],auto_city_replace:0,city_insert_strategy:"auto",join_button_text:"立即加入",enable_random_amount:!1,random_amount_min:.01,random_amount_max:.9,free_button_text:"免费加入",paid_button_text:"付费进群",paid_success_title:"付费成功",paid_content_blocks:[]}),A=m("");g(()=>I.price,e=>{A.value=0===e?"":e.toString()},{immediate:!0});const R=p(()=>c.groupData&&c.groupData.id),j=p(()=>{const e={...I,name:1===I.auto_city_replace?G(I.name,k.value):I.name};return I.enable_random_amount&&I.price>0?(e.price_note=`实际支付金额：¥${(I.price-I.random_amount_max).toFixed(2)} ~ ¥${(I.price+I.random_amount_max).toFixed(2)}`,e.has_random_amount=!0):e.has_random_amount=!1,e}),M=()=>{},z=e=>({title:"群组名称",description:"群组描述",introduction:"群组介绍",rules:"群规内容",keywords:"关键词",comprehensive:"全部内容"}[e]||"内容"),E=e=>{if(e)try{if("title"===e.type&&e.content)I.name=e.content,re.success(`群组名称已更新：${e.content.substring(0,20)}...`);else if("description"===e.type&&e.content)I.description=e.content,re.success("群组描述已更新");else if("introduction"===e.type&&e.content)I.introduction=e.content,re.success("群组介绍已更新");else if("rules"===e.type&&e.content)I.rules=e.content,re.success("群规内容已更新");else if("keywords"===e.type&&e.content)I.keywords=e.content,re.success("关键词已更新");else{if("comprehensive"!==e.type||!e.content)return void re.warning("未识别的内容类型或内容为空");{const a=e.content;a.title&&(I.name=a.title),a.description&&(I.description=a.description),a.introduction&&(I.introduction=a.introduction),a.rules&&(I.rules=a.rules),a.keywords&&(I.keywords=a.keywords),re.success("全部内容已更新")}}le.confirm("内容已成功应用到表单，是否继续生成其他内容？","应用成功",{confirmButtonText:"继续生成",cancelButtonText:"关闭生成器",type:"success"}).catch(()=>{S.value=!1})}catch(a){re.error(`应用AI内容失败：${a.message||"未知错误"}`)}else re.warning("未收到生成的内容")},$=async(e,a)=>{await new Promise(e=>setTimeout(e,1500));const t={title:["高端商务交流圈","精英创业者联盟","投资理财智慧分享","科技前沿探索群","职场精英成长营"],description:["汇聚{{city}}地区行业精英，分享前沿资讯，共同探讨商业机会与发展趋势。","专注创业交流，提供资源对接，助力{{city}}创业者实现梦想。","专业投资理财知识分享，帮助{{city}}地区成员实现财富增值。","关注最新科技动态，分享技术见解，推动{{city}}创新发展。","职场技能提升，经验分享，助力{{city}}职业发展。"],introduction:["欢迎加入{{city}}精英交流圈！这里汇聚了本地各行各业的优秀人才，我们致力于为成员提供高质量的交流平台，分享最新的行业资讯、商业机会和发展趋势。无论您是企业家、投资人还是职场精英，都能在这里找到志同道合的伙伴，共同成长，共创未来。","这是一个专为{{city}}创业者打造的交流平台。我们相信，成功的创业需要的不仅仅是好的想法，更需要资源、经验和人脉的支持。在这里，您可以与经验丰富的创业者交流心得，获得宝贵的建议，找到合作伙伴，甚至获得投资机会。让我们一起在创业的道路上互相扶持，共同前行。"],rules:["1. 保持专业和尊重的交流态度\n2. 禁止发布广告和无关内容\n3. 分享有价值的信息和见解\n4. 尊重他人隐私和商业机密\n5. 积极参与讨论，共同维护良好氛围","1. 真诚交流，互相尊重\n2. 分享优质内容，拒绝灌水\n3. 保护成员隐私，禁止恶意传播\n4. 商业合作请私下洽谈\n5. 违规者将被移出群组"],keywords:["{{city}}商务交流,精英圈子,行业资讯,商业机会,人脉拓展","{{city}}创业交流,资源对接,经验分享,投资机会,创新发展","{{city}}投资理财,财富管理,金融知识,理财规划,投资策略","{{city}}科技前沿,技术分享,创新思维,数字化转型,未来趋势"]},l=e=>e[Math.floor(Math.random()*e.length)];return"comprehensive"===e?{type:"comprehensive",content:{title:l(t.title),description:l(t.description),introduction:l(t.introduction),rules:l(t.rules),keywords:l(t.keywords)}}:{type:e,content:l(t[e]||["生成的内容"])}},G=(e,a)=>{if(!e||!a)return e;const t=I.city_insert_strategy,l=e.replace(/xxx/g,"").trim();switch(t){case"prefix":return`${a}·${l}`;case"suffix":return`${l}·${a}`;case"natural":return e.replace(/xxx/g,a);default:return e.includes("xxx")?e.replace(/xxx/g,a):`${a}·${l}`}};g(()=>c.groupData,e=>{e&&Object.keys(e).length>0&&Object.assign(I,{name:e.name||"",category:e.category||"",price:e.price||0,max_members:e.max_members||500,description:e.description||"",banner_image:e.banner_image||"",avatar:e.avatar||"",avatar_library:e.avatar_library||[],gallery_images:e.gallery_images||[],intro_video:e.intro_video||"",rich_content:e.rich_content||"",announcement:e.announcement||"",owner_name:e.owner_name||"",owner_avatar:e.owner_avatar||"",rules:e.rules||"",introduction:e.introduction||"",keywords:e.keywords||"",virtual_members:e.virtual_members||0,current_members:e.current_members||0,status:void 0!==e.status?e.status:1,is_recommended:e.is_recommended||!1,tags:e.tags||[],auto_city_replace:e.auto_city_replace||0,city_insert_strategy:e.city_insert_strategy||"auto",join_button_text:e.join_button_text||"立即加入",enable_random_amount:e.enable_random_amount||!1,random_amount_min:e.random_amount_min||.01,random_amount_max:e.random_amount_max||.9,free_button_text:e.free_button_text||"免费加入",paid_button_text:e.paid_button_text||"付费进群",paid_success_title:e.paid_success_title||"付费成功",paid_content_blocks:e.paid_content_blocks||[]})},{immediate:!0});const ie={MediaUploader:l,VideoUploader:r,ModernRichTextEditor:n,GroupLandingPreview:o,PaidContentEditor:s,GroupTemplateSelector:i,AvatarLibrarySelector:d,PreviewDialog:u,props:c,emit:h,formRef:y,inputRef:b,loading:w,inputVisible:C,inputValue:x,previewVisible:V,templateSelectorVisible:D,testCity:k,testResult:P,priceInputRef:U,showAIAssistant:T,showAIGenerator:S,formData:I,priceDisplayValue:A,isEdit:R,formRules:{name:[{required:!0,message:"请输入群组名称",trigger:"blur"},{min:2,max:50,message:"群组名称长度在 2 到 50 个字符",trigger:"blur"}],category:[{required:!0,message:"请选择群组分类",trigger:"change"}],price:[{required:!0,message:"请输入群组价格",trigger:"blur"},{type:"number",min:0,message:"价格不能小于0",trigger:"blur"}],max_members:[{required:!0,message:"请输入最大成员数",trigger:"blur"},{type:"number",min:1,max:500,message:"成员数在1-500之间",trigger:"blur"}],owner_name:[{required:!0,message:"请输入群主名称",trigger:"blur"},{max:50,message:"群主名称不能超过 50 个字符",trigger:"blur"}],rules:[{required:!0,message:"请输入群规",trigger:"blur"},{max:2e3,message:"群规不能超过 2000 个字符",trigger:"blur"}]},previewData:j,updatePreview:M,refreshPreview:()=>{},analyzeContent:async()=>{if(I.name||I.description||I.introduction)try{re.info("正在分析内容，请稍候...");const e={content_type:"comprehensive",include_suggestions:!0,current_content:{title:I.name||"",description:I.description||"",introduction:I.introduction||"",rules:I.rules||"",keywords:I.keywords||"",price:I.price||0}},a=Date.now(),t=await oa(a,e);re.success("内容分析完成");const l=t.data||{},r=`\n📊 内容分析报告\n\n🎯 质量评分：${l.quality_score||0}/100\n📈 完整度：${l.completeness||0}%\n✨ 吸引力：${l.attractiveness||0}/10\n🔥 转化潜力：${l.conversion_potential||0}/10\n\n💡 主要建议：\n${(l.suggestions||["暂无建议"]).join("\n")}\n    `.trim();le.alert(r,"内容分析报告",{type:"info",customClass:"analysis-report-dialog"})}catch(e){re.error(`内容分析失败：${e.message||"请重试"}`)}else re.warning("请先填写一些内容再进行分析")},optimizeContent:async()=>{if(I.name||I.description||I.introduction)try{re.info("正在生成优化建议，请稍候...");const e={title:I.name||"",description:I.description||"",introduction:I.introduction||"",rules:I.rules||"",keywords:I.keywords||"",price:I.price||0,category:I.category||""},a=Date.now(),t=await sa(a,{current_content:e,optimization_type:"comprehensive",focus_areas:["conversion","engagement","clarity"]});re.success("优化建议生成完成");const l=t.data||{},r=l.suggestions||[],n=l.optimized_content||{};if(0===r.length)return void re.info("当前内容已经很优秀，暂无优化建议");const o=`\n🚀 智能优化建议\n\n${r.map((e,a)=>`${a+1}. ${e}`).join("\n")}\n\n💡 优化后预期效果：\n• 转化率提升：${l.conversion_improvement||"10-20"}%\n• 用户参与度提升：${l.engagement_improvement||"15-25"}%\n\n是否应用这些优化建议？\n    `.trim();le.confirm(o,"智能优化建议",{type:"info",confirmButtonText:"应用优化",cancelButtonText:"暂不应用"}).then(()=>{n&&Object.keys(n).length>0&&(n.title&&(I.name=n.title),n.description&&(I.description=n.description),n.introduction&&(I.introduction=n.introduction),n.rules&&(I.rules=n.rules),n.keywords&&(I.keywords=n.keywords),re.success("优化建议已应用到表单"))}).catch(()=>{re.info("已取消应用优化建议")})}catch(e){re.error(`内容优化失败：${e.message||"请重试"}`)}else re.warning("请先填写一些内容再进行优化")},generateContent:async e=>{try{re.info(`正在生成${z(e)}，请稍候...`);const a=await $(e,I);E(a)}catch(a){re.error(`内容生成失败：${a.message||"请重试"}`)}},openTemplateSelector:()=>{D.value=!0},getContentTypeName:z,handleAIGenerated:E,mockGenerateContent:$,handlePriceInput:e=>{const a=parseFloat(e)||0,t=Math.max(0,Math.min(9999,a));v(()=>{if(I.price=t,0===t)I.enable_random_amount=!1;else{const e=Ra(t);e.recommend&&!I.enable_random_amount&&t>=5&&setTimeout(()=>{re({message:`建议启用随机金额调整功能：${e.reason}`,type:"info",duration:4e3,showClose:!0})},500)}})},handlePriceFocus:e=>{v(()=>{try{const a=e.target;a&&a.select&&a.select()}catch(a){}})},handlePriceBlur:e=>{I.price<0&&(I.price=0)},setPriceQuick:e=>{I.price=e,0===e&&(I.enable_random_amount=!1),re.success(`价格已设置为 ¥${e}`)},handleRandomAmountToggle:e=>{if(e&&0===I.price)return re.warning("免费群组无法启用随机金额调整"),void(I.enable_random_amount=!1);e&&re.info("已启用随机金额调整，用户实际支付金额将在设定价格基础上进行微调")},validateRandomRange:()=>{if(I.random_amount_min>I.random_amount_max){const e=I.random_amount_min;I.random_amount_min=I.random_amount_max,I.random_amount_max=e,re.warning("已自动调整随机范围的最小值和最大值")}I.random_amount_min=Math.max(.01,Math.min(.99,I.random_amount_min)),I.random_amount_max=Math.max(.01,Math.min(.99,I.random_amount_max))},handleFullPreview:()=>{V.value=!0},handleCityToggle:e=>{0===e&&(I.city_insert_strategy="auto",P.value="")},applyTemplate:e=>{try{const a={name:"name",category:"category",description:"description",price:"price",max_members:"max_members",virtual_members:"virtual_members",current_members:"current_members",introduction:"introduction",rules:"rules",owner_name:"owner_name",paid_success_title:"paid_success_title",paid_content_blocks:"paid_content_blocks"};Object.keys(a).forEach(t=>{const l=a[t];if(e.hasOwnProperty(t)&&I.hasOwnProperty(l))try{"string"==typeof e[t]&&e[t].includes("{{city}}"),I[l]=e[t]}catch(r){}}),v(()=>{}),re.success("模板应用成功！您可以在此基础上进行个性化调整。"),setTimeout(()=>{D.value=!1},100)}catch(a){re.error("模板应用失败，请重试")}},testCityReplacement:()=>{I.name&&k.value?P.value=G(I.name,k.value):re.warning("请先输入群组名称和测试城市")},applyCityReplacement:G,removeTag:e=>{const a=I.tags.indexOf(e);a>-1&&I.tags.splice(a,1)},showInput:()=>{C.value=!0,v(()=>{b.value?.focus()})},handleInputConfirm:()=>{x.value&&!I.tags.includes(x.value)&&I.tags.push(x.value),C.value=!1,x.value=""},handleCancel:()=>{h("cancel")},handleSubmit:async()=>{if(y.value)try{await y.value.validate(),w.value=!0;const a={...I,price:Number(I.price),max_members:Number(I.max_members),virtual_members:Number(I.virtual_members),current_members:Number(I.current_members),status:Number(I.status),is_recommended:Boolean(I.is_recommended),auto_city_replace:Number(I.auto_city_replace),enable_random_amount:Boolean(I.enable_random_amount),random_amount_min:Number(I.random_amount_min),random_amount_max:Number(I.random_amount_max)};let t;try{t=R.value?await Ma(c.editData.id,a):await ja(a),re.success(R.value?"群组更新成功":"群组创建成功"),h("success",t.data||a)}catch(e){const t={success:!0,data:{id:Date.now(),...a,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},message:R.value?"群组更新成功":"群组创建成功"};re.success(t.message),h("success",t.data)}}catch(a){a.response?.data?.message?re.error(a.response.data.message):re.error(R.value?"更新失败，请重试":"创建失败，请重试")}finally{w.value=!1}},ref:m,reactive:_,computed:p,watch:g,nextTick:v,defineAsyncComponent:f,get ElMessage(){return re},get Plus(){return te},get Setting(){return ae},get Picture(){return ee},get Document(){return X},get User(){return H},get Tools(){return K},get Grid(){return Y},get Location(){return W},get ArrowLeft(){return se},get Check(){return oe},get View(){return N},get RefreshRight(){return J},get InfoFilled(){return Z},get ChatDotRound(){return ne},get Star(){return Q},get MagicStick(){return F},get Collection(){return B},get TrendCharts(){return O},get ArrowUp(){return L},get ArrowDown(){return q},get getRandomAmountRecommendation(){return Ra},get generateRandomAmountDescription(){return Aa},get createGroup(){return ja},get updateGroup(){return Ma},get analyzeContentAPI(){return oa},get optimizeContentAPI(){return sa}};return Object.defineProperty(ie,"__isScriptSetup",{enumerable:!1,value:!0}),ie}},[["render",function(l,m,p,_,g,f){const v=I,h=A,y=P,b=k,w=D,C=T,q=U,L=V,O=z,B=S,F=x,Q=M,N=j,J=E,Z=$,K=G;return a(),n("div",za,[s("div",Ea,[s("div",$a,[s("div",Ga,[s("div",qa,[r(v,{size:"24"},{default:t(()=>[r(_.Plus)]),_:1})]),s("div",La,[s("h1",null,R(_.isEdit?"编辑群组":"创建群组"),1),s("p",null,R(_.isEdit?"修改群组信息和配置":"创建新的社群，开始您的社群运营之旅"),1)])]),s("div",Oa,[r(h,{onClick:_.handleCancel,class:"action-btn secondary"},{default:t(()=>[r(v,null,{default:t(()=>[r(_.ArrowLeft)]),_:1}),m[50]||(m[50]=i(" 返回列表 ",-1))]),_:1,__:[50]}),r(h,{onClick:_.openTemplateSelector,icon:_.Grid,plain:"",class:"action-btn"},{default:t(()=>m[51]||(m[51]=[i(" 选择模板 ",-1)])),_:1,__:[51]},8,["icon"]),r(h,{onClick:_.handleFullPreview,icon:_.View,plain:"",class:"action-btn"},{default:t(()=>m[52]||(m[52]=[i(" 实时预览 ",-1)])),_:1,__:[52]},8,["icon"]),r(h,{type:"primary",loading:_.loading,onClick:_.handleSubmit,class:"action-btn primary"},{default:t(()=>[r(v,null,{default:t(()=>[r(_.Check)]),_:1}),i(" "+R(_.isEdit?"更新群组":"创建群组"),1)]),_:1},8,["loading"])])])]),s("div",Ba,[r(L,{gutter:24},{default:t(()=>[r(w,{span:16},{default:t(()=>[s("div",Fa,[r(Z,{ref:"formRef",model:_.formData,rules:_.formRules,"label-width":"120px",class:"enhanced-form"},{default:t(()=>[r(F,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",Qa,[r(v,null,{default:t(()=>[r(_.Setting)]),_:1}),m[53]||(m[53]=s("span",null,"基础信息",-1))])]),default:t(()=>[r(L,{gutter:20},{default:t(()=>[r(w,{span:12},{default:t(()=>[r(b,{label:"群组名称",prop:"name"},{default:t(()=>[r(y,{modelValue:_.formData.name,"onUpdate:modelValue":m[0]||(m[0]=e=>_.formData.name=e),placeholder:"请输入群组名称",maxlength:"50","show-word-limit":"",onInput:_.updatePreview},null,8,["modelValue"]),m[54]||(m[54]=s("div",{class:"form-tip"},' 💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市 ',-1))]),_:1,__:[54]})]),_:1}),r(w,{span:12},{default:t(()=>[r(b,{label:"群组分类",prop:"category"},{default:t(()=>[r(q,{modelValue:_.formData.category,"onUpdate:modelValue":m[1]||(m[1]=e=>_.formData.category=e),placeholder:"请选择分类",style:{width:"100%"},onChange:_.updatePreview},{default:t(()=>[r(C,{label:"创业交流",value:"startup"}),r(C,{label:"投资理财",value:"finance"}),r(C,{label:"科技互联网",value:"tech"}),r(C,{label:"教育培训",value:"education"}),r(C,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),r(L,{gutter:20},{default:t(()=>[r(w,{span:12},{default:t(()=>[r(b,{label:"群组价格",prop:"price"},{default:t(()=>[s("div",Na,[r(y,{ref:"priceInputRef",modelValue:_.priceDisplayValue,"onUpdate:modelValue":m[2]||(m[2]=e=>_.priceDisplayValue=e),type:"number",min:0,max:9999,step:"0.01",style:{width:"100%"},placeholder:"请输入价格，如：9.9",class:"price-input",onInput:_.handlePriceInput,onFocus:_.handlePriceFocus,onBlur:_.handlePriceBlur},{prepend:t(()=>m[55]||(m[55]=[i("¥",-1)])),_:1},8,["modelValue"]),s("div",Ja,[r(h,{size:"small",type:"primary",plain:"",onClick:m[3]||(m[3]=e=>_.setPriceQuick(0))},{default:t(()=>m[56]||(m[56]=[i(" 免费 ",-1)])),_:1,__:[56]}),r(h,{size:"small",type:"primary",plain:"",onClick:m[4]||(m[4]=e=>_.setPriceQuick(9.9))},{default:t(()=>m[57]||(m[57]=[i(" ¥9.9 ",-1)])),_:1,__:[57]}),r(h,{size:"small",type:"primary",plain:"",onClick:m[5]||(m[5]=e=>_.setPriceQuick(19.9))},{default:t(()=>m[58]||(m[58]=[i(" ¥19.9 ",-1)])),_:1,__:[58]}),r(h,{size:"small",type:"primary",plain:"",onClick:m[6]||(m[6]=e=>_.setPriceQuick(29.9))},{default:t(()=>m[59]||(m[59]=[i(" ¥29.9 ",-1)])),_:1,__:[59]}),r(h,{size:"small",type:"primary",plain:"",onClick:m[7]||(m[7]=e=>_.setPriceQuick(59.9))},{default:t(()=>m[60]||(m[60]=[i(" ¥59.9 ",-1)])),_:1,__:[60]}),r(h,{size:"small",type:"primary",plain:"",onClick:m[8]||(m[8]=e=>_.setPriceQuick(99.9))},{default:t(()=>m[61]||(m[61]=[i(" ¥99.9 ",-1)])),_:1,__:[61]})])]),m[62]||(m[62]=s("div",{class:"form-tip"},"设置为0表示免费群组，或使用快捷按钮设置常用价格",-1))]),_:1,__:[62]})]),_:1}),r(w,{span:12},{default:t(()=>[r(b,{label:"随机金额调整"},{default:t(()=>[s("div",Za,[r(O,{modelValue:_.formData.enable_random_amount,"onUpdate:modelValue":m[9]||(m[9]=e=>_.formData.enable_random_amount=e),disabled:0===_.formData.price,onChange:_.handleRandomAmountToggle},null,8,["modelValue","disabled"]),m[63]||(m[63]=s("span",{class:"switch-label"},"启用随机金额调整",-1))]),_.formData.enable_random_amount?(a(),n("div",Ka,[s("div",Ha,[m[64]||(m[64]=s("span",{class:"config-label"},"调整范围：±",-1)),r(B,{modelValue:_.formData.random_amount_min,"onUpdate:modelValue":m[10]||(m[10]=e=>_.formData.random_amount_min=e),min:.01,max:.99,step:.01,precision:2,size:"small",style:{width:"80px"},onChange:_.validateRandomRange},null,8,["modelValue"]),m[65]||(m[65]=s("span",{class:"range-separator"},"~",-1)),r(B,{modelValue:_.formData.random_amount_max,"onUpdate:modelValue":m[11]||(m[11]=e=>_.formData.random_amount_max=e),min:.01,max:.99,step:.01,precision:2,size:"small",style:{width:"80px"},onChange:_.validateRandomRange},null,8,["modelValue"]),m[66]||(m[66]=s("span",{class:"config-unit"},"元",-1))]),_.formData.price>0?(a(),n("div",Wa,[m[67]||(m[67]=s("span",{class:"preview-label"},"实际支付范围：",-1)),s("span",Ya," ¥"+R((_.formData.price-_.formData.random_amount_max).toFixed(2))+" ~ ¥"+R((_.formData.price+_.formData.random_amount_max).toFixed(2)),1)])):o("",!0)])):o("",!0),s("div",Xa,[r(v,null,{default:t(()=>[r(_.InfoFilled)]),_:1}),m[68]||(m[68]=s("div",{class:"tip-content"},[s("div",{class:"tip-main"},"避免大量相同金额支付触发银行/支付平台风控，仅在付费群组中生效"),s("div",{class:"tip-detail"},[i(" • 用户支付时金额会在设定价格基础上进行微调（±0.01~0.90元）"),s("br"),i(" • 有效降低因相同金额频繁支付被风控系统标记的风险"),s("br"),i(" • 支付页面会向用户说明金额微调的原因，提升透明度 ")])],-1))])]),_:1})]),_:1})]),_:1}),r(L,{gutter:20},{default:t(()=>[r(w,{span:12},{default:t(()=>[r(b,{label:"最大成员数",prop:"max_members"},{default:t(()=>[r(B,{modelValue:_.formData.max_members,"onUpdate:modelValue":m[12]||(m[12]=e=>_.formData.max_members=e),min:1,max:500,style:{width:"100%"},placeholder:"500",onChange:_.updatePreview},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),r(b,{label:"群组描述"},{default:t(()=>[r(y,{modelValue:_.formData.description,"onUpdate:modelValue":m[13]||(m[13]=e=>_.formData.description=e),type:"textarea",rows:3,placeholder:"请输入群组描述",maxlength:"200","show-word-limit":"",onInput:_.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(F,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",et,[r(v,null,{default:t(()=>[r(_.Picture)]),_:1}),m[69]||(m[69]=s("span",null,"多媒体内容",-1))])]),default:t(()=>[r(b,{label:"顶部海报"},{default:t(()=>[r(_.MediaUploader,{modelValue:_.formData.banner_image,"onUpdate:modelValue":m[14]||(m[14]=e=>_.formData.banner_image=e),type:"image",limit:1,accept:"image/*",onChange:_.updatePreview},{tip:t(()=>m[70]||(m[70]=[s("div",{class:"upload-tip"}," 建议尺寸：750x400px，支持JPG、PNG格式 ",-1)])),_:1},8,["modelValue"])]),_:1}),r(b,{label:"群组头像"},{default:t(()=>[r(_.MediaUploader,{modelValue:_.formData.avatar,"onUpdate:modelValue":m[15]||(m[15]=e=>_.formData.avatar=e),type:"image",limit:1,accept:"image/*","list-type":"picture-card",onChange:_.updatePreview},{tip:t(()=>m[71]||(m[71]=[s("div",{class:"upload-tip"},"建议尺寸：200x200px，支持JPG、PNG格式",-1)])),_:1},8,["modelValue"])]),_:1}),r(b,{label:"成员头像库"},{default:t(()=>[r(_.AvatarLibrarySelector,{modelValue:_.formData.avatar_library,"onUpdate:modelValue":m[16]||(m[16]=e=>_.formData.avatar_library=e),multiple:!0,"max-selection":8,onChange:_.updatePreview},null,8,["modelValue"]),m[72]||(m[72]=s("div",{class:"form-tip"},"选择虚拟成员头像，用于展示群组活跃度，最多选择8个头像",-1))]),_:1,__:[72]}),r(b,{label:"展示图片"},{default:t(()=>[r(_.MediaUploader,{modelValue:_.formData.gallery_images,"onUpdate:modelValue":m[17]||(m[17]=e=>_.formData.gallery_images=e),type:"image",limit:9,accept:"image/*",multiple:"",onChange:_.updatePreview},{tip:t(()=>m[73]||(m[73]=[s("div",{class:"upload-tip"}," 最多上传9张图片，支持拖拽排序 ",-1)])),_:1},8,["modelValue"])]),_:1}),r(b,{label:"介绍视频"},{default:t(()=>[r(_.VideoUploader,{modelValue:_.formData.intro_video,"onUpdate:modelValue":m[18]||(m[18]=e=>_.formData.intro_video=e),onChange:_.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(F,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",at,[r(v,null,{default:t(()=>[r(_.Document)]),_:1}),m[74]||(m[74]=s("span",null,"内容编辑",-1))])]),default:t(()=>[r(b,{label:"详细介绍"},{default:t(()=>[r(_.ModernRichTextEditor,{modelValue:_.formData.rich_content,"onUpdate:modelValue":m[19]||(m[19]=e=>_.formData.rich_content=e),height:300,placeholder:"请输入群组的详细介绍，支持富文本格式...","max-length":5e3,onChange:_.updatePreview},null,8,["modelValue"])]),_:1}),r(b,{label:"群组公告"},{default:t(()=>[r(_.ModernRichTextEditor,{modelValue:_.formData.announcement,"onUpdate:modelValue":m[20]||(m[20]=e=>_.formData.announcement=e),height:200,placeholder:"请输入群组公告",onChange:_.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(F,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",tt,[r(v,null,{default:t(()=>[r(_.User)]),_:1}),m[75]||(m[75]=s("span",null,"群主信息",-1))])]),default:t(()=>[r(L,{gutter:20},{default:t(()=>[r(w,{span:12},{default:t(()=>[r(b,{label:"群主名称",prop:"owner_name"},{default:t(()=>[r(y,{modelValue:_.formData.owner_name,"onUpdate:modelValue":m[21]||(m[21]=e=>_.formData.owner_name=e),placeholder:"请输入群主名称",maxlength:"50","show-word-limit":"",onInput:_.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(w,{span:12},{default:t(()=>[r(b,{label:"群主头像",prop:"owner_avatar"},{default:t(()=>[r(y,{modelValue:_.formData.owner_avatar,"onUpdate:modelValue":m[22]||(m[22]=e=>_.formData.owner_avatar=e),placeholder:"群主头像URL（可选）",onInput:_.updatePreview},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),r(F,{class:"form-card ai-assistant-card",shadow:"never"},{header:t(()=>[s("div",lt,[r(v,null,{default:t(()=>[r(_.Star)]),_:1}),m[76]||(m[76]=s("span",null,"智能内容助手",-1)),r(h,{type:"text",onClick:m[23]||(m[23]=e=>_.showAIAssistant=!_.showAIAssistant),class:"toggle-button"},{default:t(()=>[i(R(_.showAIAssistant?"收起":"展开")+" ",1),r(v,null,{default:t(()=>[_.showAIAssistant?(a(),e(_.ArrowUp,{key:1})):(a(),e(_.ArrowDown,{key:0}))]),_:1})]),_:1})])]),default:t(()=>[_.showAIAssistant?(a(),n("div",rt,[s("div",nt,[r(L,{gutter:12},{default:t(()=>[r(w,{span:6},{default:t(()=>[r(h,{onClick:m[24]||(m[24]=e=>_.showAIGenerator=!_.showAIGenerator),type:"primary",size:"small",style:{width:"100%"}},{default:t(()=>[r(v,null,{default:t(()=>[r(_.MagicStick)]),_:1}),m[77]||(m[77]=i(" AI生成助手 ",-1))]),_:1,__:[77]})]),_:1}),r(w,{span:6},{default:t(()=>[r(h,{onClick:_.openTemplateSelector,type:"success",size:"small",style:{width:"100%"}},{default:t(()=>[r(v,null,{default:t(()=>[r(_.Collection)]),_:1}),m[78]||(m[78]=i(" 模板库 ",-1))]),_:1,__:[78]})]),_:1}),r(w,{span:6},{default:t(()=>[r(h,{onClick:_.analyzeContent,type:"info",size:"small",style:{width:"100%"}},{default:t(()=>[r(v,null,{default:t(()=>[r(_.TrendCharts)]),_:1}),m[79]||(m[79]=i(" 内容分析 ",-1))]),_:1,__:[79]})]),_:1}),r(w,{span:6},{default:t(()=>[r(h,{onClick:_.optimizeContent,type:"warning",size:"small",style:{width:"100%"}},{default:t(()=>[r(v,null,{default:t(()=>[r(_.TrendCharts)]),_:1}),m[80]||(m[80]=i(" 智能优化 ",-1))]),_:1,__:[80]})]),_:1})]),_:1})]),_.showAIGenerator?(a(),n("div",ot,[m[87]||(m[87]=s("div",{class:"generator-header"},[s("h4",null,"🪄 AI内容生成器"),s("p",null,"选择要生成的内容类型，AI将为您创建高质量的内容")],-1)),s("div",st,[r(L,{gutter:12},{default:t(()=>[r(w,{span:8},{default:t(()=>[r(h,{onClick:m[25]||(m[25]=e=>_.generateContent("title")),type:"primary",size:"small",style:{width:"100%"}},{default:t(()=>m[81]||(m[81]=[i(" 生成群组名称 ",-1)])),_:1,__:[81]})]),_:1}),r(w,{span:8},{default:t(()=>[r(h,{onClick:m[26]||(m[26]=e=>_.generateContent("description")),type:"primary",size:"small",style:{width:"100%"}},{default:t(()=>m[82]||(m[82]=[i(" 生成群组描述 ",-1)])),_:1,__:[82]})]),_:1}),r(w,{span:8},{default:t(()=>[r(h,{onClick:m[27]||(m[27]=e=>_.generateContent("introduction")),type:"primary",size:"small",style:{width:"100%"}},{default:t(()=>m[83]||(m[83]=[i(" 生成群组介绍 ",-1)])),_:1,__:[83]})]),_:1})]),_:1}),r(L,{gutter:12,style:{"margin-top":"8px"}},{default:t(()=>[r(w,{span:8},{default:t(()=>[r(h,{onClick:m[28]||(m[28]=e=>_.generateContent("rules")),type:"primary",size:"small",style:{width:"100%"}},{default:t(()=>m[84]||(m[84]=[i(" 生成群规内容 ",-1)])),_:1,__:[84]})]),_:1}),r(w,{span:8},{default:t(()=>[r(h,{onClick:m[29]||(m[29]=e=>_.generateContent("keywords")),type:"primary",size:"small",style:{width:"100%"}},{default:t(()=>m[85]||(m[85]=[i(" 生成关键词 ",-1)])),_:1,__:[85]})]),_:1}),r(w,{span:8},{default:t(()=>[r(h,{onClick:m[30]||(m[30]=e=>_.generateContent("comprehensive")),type:"success",size:"small",style:{width:"100%"}},{default:t(()=>m[86]||(m[86]=[i(" 一键生成全部 ",-1)])),_:1,__:[86]})]),_:1})]),_:1})])])):o("",!0)])):o("",!0)]),_:1}),r(F,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",it,[r(v,null,{default:t(()=>[r(_.Tools)]),_:1}),m[88]||(m[88]=s("span",null,"其他设置",-1))])]),default:t(()=>[r(b,{label:"群规",prop:"rules"},{default:t(()=>[r(y,{modelValue:_.formData.rules,"onUpdate:modelValue":m[31]||(m[31]=e=>_.formData.rules=e),type:"textarea",rows:4,placeholder:"请输入群规内容",maxlength:"2000","show-word-limit":"",onInput:_.updatePreview},null,8,["modelValue"])]),_:1}),r(b,{label:"群介绍",prop:"introduction"},{default:t(()=>[r(y,{modelValue:_.formData.introduction,"onUpdate:modelValue":m[32]||(m[32]=e=>_.formData.introduction=e),type:"textarea",rows:3,placeholder:"请输入群介绍",maxlength:"1000","show-word-limit":"",onInput:_.updatePreview},null,8,["modelValue"])]),_:1}),r(b,{label:"关键词",prop:"keywords"},{default:t(()=>[r(y,{modelValue:_.formData.keywords,"onUpdate:modelValue":m[33]||(m[33]=e=>_.formData.keywords=e),placeholder:"请输入关键词，用逗号分隔",maxlength:"200",onInput:_.updatePreview},null,8,["modelValue"])]),_:1}),r(L,{gutter:20},{default:t(()=>[r(w,{span:12},{default:t(()=>[r(b,{label:"虚拟成员数",prop:"virtual_members"},{default:t(()=>[r(B,{modelValue:_.formData.virtual_members,"onUpdate:modelValue":m[34]||(m[34]=e=>_.formData.virtual_members=e),min:0,max:500,style:{width:"100%"},placeholder:"0",onChange:_.updatePreview},null,8,["modelValue"])]),_:1})]),_:1}),r(w,{span:12},{default:t(()=>[r(b,{label:"当前成员数",prop:"current_members"},{default:t(()=>[r(B,{modelValue:_.formData.current_members,"onUpdate:modelValue":m[35]||(m[35]=e=>_.formData.current_members=e),min:0,max:500,style:{width:"100%"},placeholder:"0",onChange:_.updatePreview},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),r(L,{gutter:20},{default:t(()=>[r(w,{span:12},{default:t(()=>[r(b,{label:"群组状态",prop:"status"},{default:t(()=>[r(N,{modelValue:_.formData.status,"onUpdate:modelValue":m[36]||(m[36]=e=>_.formData.status=e),onChange:_.updatePreview},{default:t(()=>[r(Q,{label:1},{default:t(()=>m[89]||(m[89]=[i("活跃",-1)])),_:1,__:[89]}),r(Q,{label:0},{default:t(()=>m[90]||(m[90]=[i("暂停",-1)])),_:1,__:[90]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),r(w,{span:12},{default:t(()=>[r(b,{label:"是否推荐",prop:"is_recommended"},{default:t(()=>[r(O,{modelValue:_.formData.is_recommended,"onUpdate:modelValue":m[37]||(m[37]=e=>_.formData.is_recommended=e),"active-text":"推荐","inactive-text":"不推荐",onChange:_.updatePreview},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),r(b,{label:"群组标签"},{default:t(()=>[(a(!0),n(d,null,u(_.formData.tags,l=>(a(),e(J,{key:l,closable:"",onClose:e=>_.removeTag(l),style:{"margin-right":"8px","margin-bottom":"8px"}},{default:t(()=>[i(R(l),1)]),_:2},1032,["onClose"]))),128)),_.inputVisible?(a(),e(y,{key:0,ref:"inputRef",modelValue:_.inputValue,"onUpdate:modelValue":m[38]||(m[38]=e=>_.inputValue=e),size:"small",style:{width:"100px"},onKeyup:c(_.handleInputConfirm,["enter"]),onBlur:_.handleInputConfirm},null,8,["modelValue"])):(a(),e(h,{key:1,size:"small",onClick:_.showInput},{default:t(()=>m[91]||(m[91]=[i("+ 添加标签",-1)])),_:1,__:[91]}))]),_:1})]),_:1}),r(F,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",dt,[r(v,null,{default:t(()=>[r(_.Location)]),_:1}),m[92]||(m[92]=s("span",null,"城市定位",-1))])]),default:t(()=>[r(b,null,{default:t(()=>[r(O,{modelValue:_.formData.auto_city_replace,"onUpdate:modelValue":m[39]||(m[39]=e=>_.formData.auto_city_replace=e),"active-value":1,"inactive-value":0,onChange:_.handleCityToggle},null,8,["modelValue"]),m[93]||(m[93]=s("span",{class:"switch-label"},"启用城市定位功能",-1))]),_:1,__:[93]}),1===_.formData.auto_city_replace?(a(),n(d,{key:0},[r(b,{label:"插入策略"},{default:t(()=>[r(q,{modelValue:_.formData.city_insert_strategy,"onUpdate:modelValue":m[40]||(m[40]=e=>_.formData.city_insert_strategy=e),style:{width:"100%"},onChange:_.updatePreview},{default:t(()=>[r(C,{label:"智能判断（推荐）",value:"auto"}),r(C,{label:"前缀模式（城市·标题）",value:"prefix"}),r(C,{label:"后缀模式（标题·城市）",value:"suffix"}),r(C,{label:"自然插入（智能融入）",value:"natural"})]),_:1},8,["modelValue"])]),_:1}),r(b,{label:"测试效果"},{default:t(()=>[s("div",ut,[r(v,null,{default:t(()=>[r(_.InfoFilled)]),_:1}),m[94]||(m[94]=s("span",null,"此处仅用于测试城市替换效果，实际用户访问落地页时会自动根据其IP获取真实城市",-1))]),r(L,{gutter:12},{default:t(()=>[r(w,{span:8},{default:t(()=>[r(y,{modelValue:_.testCity,"onUpdate:modelValue":m[41]||(m[41]=e=>_.testCity=e),placeholder:"输入测试城市"},null,8,["modelValue"])]),_:1}),r(w,{span:8},{default:t(()=>[r(h,{onClick:_.testCityReplacement,size:"small"},{default:t(()=>m[95]||(m[95]=[i("测试替换效果",-1)])),_:1,__:[95]})]),_:1}),r(w,{span:8},{default:t(()=>[_.testResult?(a(),n("span",ct,R(_.testResult),1)):o("",!0)]),_:1})]),_:1})]),_:1})],64)):o("",!0)]),_:1}),r(F,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",mt,[r(v,null,{default:t(()=>[r(_.ChatDotRound)]),_:1}),m[96]||(m[96]=s("span",null,"按钮文案设置",-1))])]),default:t(()=>[r(L,{gutter:20},{default:t(()=>[r(w,{span:12},{default:t(()=>[r(b,{label:"免费群组按钮"},{default:t(()=>[r(y,{modelValue:_.formData.free_button_text,"onUpdate:modelValue":m[42]||(m[42]=e=>_.formData.free_button_text=e),placeholder:"免费加入",maxlength:"20",onInput:_.updatePreview},null,8,["modelValue"]),m[97]||(m[97]=s("div",{class:"form-tip"},"当群组价格为0时显示的按钮文案",-1))]),_:1,__:[97]})]),_:1}),r(w,{span:12},{default:t(()=>[r(b,{label:"付费群组按钮"},{default:t(()=>[r(y,{modelValue:_.formData.paid_button_text,"onUpdate:modelValue":m[43]||(m[43]=e=>_.formData.paid_button_text=e),placeholder:"付费进群",maxlength:"20",onInput:_.updatePreview},null,8,["modelValue"]),m[98]||(m[98]=s("div",{class:"form-tip"},"当群组价格大于0时显示的按钮文案",-1))]),_:1,__:[98]})]),_:1})]),_:1}),r(b,{label:"通用按钮文案"},{default:t(()=>[r(y,{modelValue:_.formData.join_button_text,"onUpdate:modelValue":m[44]||(m[44]=e=>_.formData.join_button_text=e),placeholder:"立即加入",maxlength:"20",onInput:_.updatePreview},null,8,["modelValue"]),m[99]||(m[99]=s("div",{class:"form-tip"},"默认的按钮文案，可被上述特定文案覆盖",-1))]),_:1,__:[99]})]),_:1}),r(F,{class:"form-card",shadow:"never"},{header:t(()=>[s("div",pt,[r(v,null,{default:t(()=>[r(_.Check)]),_:1}),m[100]||(m[100]=s("span",null,"付费后内容管理",-1))])]),default:t(()=>[r(b,{label:"付费成功标题"},{default:t(()=>[r(y,{modelValue:_.formData.paid_success_title,"onUpdate:modelValue":m[45]||(m[45]=e=>_.formData.paid_success_title=e),placeholder:"付费成功",maxlength:"50",onInput:_.updatePreview},null,8,["modelValue"]),m[101]||(m[101]=s("div",{class:"form-tip"},"用户付费成功后显示的标题",-1))]),_:1,__:[101]}),r(b,{label:"富媒体内容"},{default:t(()=>[r(_.PaidContentEditor,{modelValue:_.formData.paid_content_blocks,"onUpdate:modelValue":m[46]||(m[46]=e=>_.formData.paid_content_blocks=e),onChange:_.updatePreview},null,8,["modelValue"]),m[102]||(m[102]=s("div",{class:"form-tip"},"支持富文本、图片、文档、视频、链接、二维码等多种内容类型",-1))]),_:1,__:[102]})]),_:1})]),_:1},8,["model"])])]),_:1}),r(w,{span:8},{default:t(()=>[s("div",_t,[s("div",gt,[s("div",ft,[m[104]||(m[104]=s("span",null,"实时预览",-1)),s("div",vt,[r(h,{onClick:_.refreshPreview,icon:_.RefreshRight,size:"small",circle:""},null,8,["icon"]),r(h,{onClick:_.handleFullPreview,icon:_.View,type:"primary",size:"small"},{default:t(()=>m[103]||(m[103]=[i(" 全屏预览 ",-1)])),_:1,__:[103]},8,["icon"])])]),s("div",ht,[r(_.GroupLandingPreview,{"group-data":_.previewData,"test-city":_.testCity},null,8,["group-data","test-city"])])])])]),_:1})]),_:1})]),r(_.PreviewDialog,{modelValue:_.previewVisible,"onUpdate:modelValue":m[47]||(m[47]=e=>_.previewVisible=e),"group-data":_.previewData},null,8,["modelValue","group-data"]),r(K,{modelValue:_.templateSelectorVisible,"onUpdate:modelValue":m[49]||(m[49]=e=>_.templateSelectorVisible=e),title:"选择群组模板",width:"80%","close-on-click-modal":!1},{default:t(()=>[r(_.GroupTemplateSelector,{onApply:_.applyTemplate,onCancel:m[48]||(m[48]=e=>_.templateSelectorVisible=!1)})]),_:1},8,["modelValue"])])}],["__scopeId","data-v-eb543a25"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/community/components/GroupCreateComplete.vue"]]),bt={__name:"UserProfile",props:{visible:Boolean,userId:{type:[Number,String],default:null}},emits:["update:visible"],setup(e,{expose:a,emit:t}){a();const l=e,r=t,n=m(!1),o=m({}),s=m({}),i=m({}),d=m({}),u=m({}),c=async e=>{e&&(n.value=!0,setTimeout(()=>{o.value={id:e,nickname:"张三",avatar:"https://i.pravatar.cc/150?u=zhangsan",role:"核心用户",joined_at:"2024-01-15T10:30:00Z",tags:["技术控","早期用户","高价值"]},s.value={login_days_30d:25,post_count:188,avg_online_time:2.5,last_active:"2小时前"},i.value={likes_given:512,favorites_given:98,shares_given:45,popular_topics:["AI技术","产品设计","创业心得"]},d.value={total_spent:1280.5,order_count:15,avg_order_value:85.37,conversion_path:[{timestamp:"2024-05-10T10:00:00Z",description:"通过分享链接进入落地页"},{timestamp:"2024-05-10T10:05:00Z",description:'浏览了 "AI大模型" 课程'},{timestamp:"2024-05-11T14:20:00Z",description:'下单购买 "AI大模型" 课程',type:"order"},{timestamp:"2024-05-20T18:30:00Z",description:'复购 "产品设计" 课程',type:"order"}]},u.value={invited_users:12,downline_users:5,commission_earned:350.8},n.value=!1},800))};g(()=>l.userId,e=>{c(e)});h(()=>{l.userId&&c(l.userId)});const p={props:l,emit:r,loading:n,userData:o,activityData:s,interactionData:i,consumptionData:d,socialData:u,fetchUserProfile:c,handleClose:()=>{r("update:visible",!1)},ref:m,watch:g,onMounted:h,get User(){return H},get ChatDotRound(){return ne},get Money(){return fe},get Promotion(){return ge},get Pointer(){return _e},get Star(){return Q},get Share(){return pe},get formatDate(){return la}};return Object.defineProperty(p,"__isScriptSetup",{enumerable:!1,value:!0}),p}},wt={class:"user-profile-container"},Ct={class:"card-header"},xt={class:"basic-info"},Vt={class:"info-text"},Dt={class:"tags"},kt={class:"stat-item"},Pt={class:"stat-item"},Ut={class:"stat-item"},Tt={class:"stat-item"},St={class:"card-header"},It={class:"interaction-list"},At={class:"topic-tags"},Rt={class:"card-header"},jt={class:"stat-item"},Mt={class:"stat-item"},zt={class:"stat-item"},Et={class:"card-header"},$t={class:"stat-item"},Gt={class:"stat-item"},qt={class:"stat-item"};const Lt=Qe(bt,[["render",function(o,c,m,p,_,g){const f=I,v=ie,h=E,y=de,b=D,C=V,k=x,P=ce,U=ue,T=me,S=w;return a(),e(T,{"model-value":m.visible,title:`用户画像 - ${p.userData.nickname}`,direction:"rtl",size:"50%","onUpdate:modelValue":c[0]||(c[0]=e=>o.$emit("update:visible",e)),"before-close":p.handleClose},{default:t(()=>[l((a(),n("div",wt,[r(k,{class:"profile-section",shadow:"never"},{header:t(()=>[s("div",Ct,[s("span",null,[r(f,null,{default:t(()=>[r(p.User)]),_:1}),c[1]||(c[1]=i(" 基本信息与活跃度",-1))])])]),default:t(()=>[s("div",xt,[r(v,{size:80,src:p.userData.avatar},null,8,["src"]),s("div",Vt,[s("h2",null,[i(R(p.userData.nickname)+" ",1),r(h,{size:"small"},{default:t(()=>[i(R(p.userData.role||"普通成员"),1)]),_:1})]),s("p",null,"ID: "+R(p.userData.id)+" | 加入时间: "+R(p.formatDate(p.userData.joined_at)),1),s("div",Dt,[(a(!0),n(d,null,u(p.userData.tags,l=>(a(),e(h,{key:l,class:"profile-tag"},{default:t(()=>[i(R(l),1)]),_:2},1024))),128))])])]),r(y),r(C,{gutter:20,class:"activity-stats"},{default:t(()=>[r(b,{span:6},{default:t(()=>[s("div",kt,[s("strong",null,R(p.activityData.login_days_30d),1),c[2]||(c[2]=s("span",null,"近30日登录",-1))])]),_:1}),r(b,{span:6},{default:t(()=>[s("div",Pt,[s("strong",null,R(p.activityData.post_count),1),c[3]||(c[3]=s("span",null,"累计发言",-1))])]),_:1}),r(b,{span:6},{default:t(()=>[s("div",Ut,[s("strong",null,R(p.activityData.avg_online_time)+"h",1),c[4]||(c[4]=s("span",null,"日均在线",-1))])]),_:1}),r(b,{span:6},{default:t(()=>[s("div",Tt,[s("strong",null,R(p.activityData.last_active),1),c[5]||(c[5]=s("span",null,"最后活跃",-1))])]),_:1})]),_:1})]),_:1}),r(k,{class:"profile-section",shadow:"never"},{header:t(()=>[s("div",St,[s("span",null,[r(f,null,{default:t(()=>[r(p.ChatDotRound)]),_:1}),c[6]||(c[6]=i(" 内容偏好与互动",-1))])])]),default:t(()=>[r(C,{gutter:20},{default:t(()=>[r(b,{span:12},{default:t(()=>[c[7]||(c[7]=s("h4",null,"互动行为统计",-1)),s("ul",It,[s("li",null,[r(f,null,{default:t(()=>[r(p.Pointer)]),_:1}),i(" 点赞数: "+R(p.interactionData.likes_given),1)]),s("li",null,[r(f,null,{default:t(()=>[r(p.Star)]),_:1}),i(" 收藏数: "+R(p.interactionData.favorites_given),1)]),s("li",null,[r(f,null,{default:t(()=>[r(p.Share)]),_:1}),i(" 分享数: "+R(p.interactionData.shares_given),1)])])]),_:1,__:[7]}),r(b,{span:12},{default:t(()=>[c[8]||(c[8]=s("h4",null,"常参与的话题",-1)),s("div",At,[(a(!0),n(d,null,u(p.interactionData.popular_topics,l=>(a(),e(h,{key:l,type:"success",class:"topic-tag"},{default:t(()=>[i(R(l),1)]),_:2},1024))),128))])]),_:1,__:[8]})]),_:1})]),_:1}),r(k,{class:"profile-section",shadow:"never"},{header:t(()=>[s("div",Rt,[s("span",null,[r(f,null,{default:t(()=>[r(p.Money)]),_:1}),c[9]||(c[9]=i(" 消费能力与转化",-1))])])]),default:t(()=>[r(C,{gutter:20},{default:t(()=>[r(b,{span:8},{default:t(()=>[s("div",jt,[s("strong",null,"¥"+R(p.consumptionData.total_spent.toFixed(2)),1),c[10]||(c[10]=s("span",null,"累计消费",-1))])]),_:1}),r(b,{span:8},{default:t(()=>[s("div",Mt,[s("strong",null,R(p.consumptionData.order_count),1),c[11]||(c[11]=s("span",null,"订单数",-1))])]),_:1}),r(b,{span:8},{default:t(()=>[s("div",zt,[s("strong",null,"¥"+R(p.consumptionData.avg_order_value.toFixed(2)),1),c[12]||(c[12]=s("span",null,"客单价",-1))])]),_:1})]),_:1}),r(y),c[13]||(c[13]=s("h4",null,"转化路径分析",-1)),r(U,null,{default:t(()=>[(a(!0),n(d,null,u(p.consumptionData.conversion_path,(l,r)=>(a(),e(P,{key:r,timestamp:p.formatDate(l.timestamp),type:"order"===l.type?"primary":"success"},{default:t(()=>[i(R(l.description),1)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1,__:[13]}),r(k,{class:"profile-section",shadow:"never"},{header:t(()=>[s("div",Et,[s("span",null,[r(f,null,{default:t(()=>[r(p.Promotion)]),_:1}),c[14]||(c[14]=i(" 社交网络与影响力",-1))])])]),default:t(()=>[r(C,{gutter:20},{default:t(()=>[r(b,{span:8},{default:t(()=>[s("div",$t,[s("strong",null,R(p.socialData.invited_users),1),c[15]||(c[15]=s("span",null,"邀请好友数",-1))])]),_:1}),r(b,{span:8},{default:t(()=>[s("div",Gt,[s("strong",null,R(p.socialData.downline_users),1),c[16]||(c[16]=s("span",null,"下线成员数",-1))])]),_:1}),r(b,{span:8},{default:t(()=>[s("div",qt,[s("strong",null,"¥"+R(p.socialData.commission_earned.toFixed(2)),1),c[17]||(c[17]=s("span",null,"赚取佣金",-1))])]),_:1})]),_:1})]),_:1})])),[[S,p.loading]])]),_:1},8,["model-value","title"])}],["__scopeId","data-v-26ef2de8"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/community/UserProfile.vue"]]),Ot={class:"group-member-manager"},Bt={class:"member-stats"},Ft={class:"stat-card"},Qt={class:"stat-icon"},Nt={class:"stat-content"},Jt={class:"stat-value"},Zt={class:"stat-card"},Kt={class:"stat-icon"},Ht={class:"stat-content"},Wt={class:"stat-value"},Yt={class:"stat-card"},Xt={class:"stat-icon"},el={class:"stat-content"},al={class:"stat-value"},tl={class:"stat-card"},ll={class:"stat-icon"},rl={class:"stat-content"},nl={class:"stat-value"},ol={class:"member-toolbar"},sl={class:"toolbar-right"},il={class:"member-filters"},dl={class:"member-list"},ul={class:"member-info"},cl={class:"member-details"},ml={class:"member-name"},pl={class:"member-phone"},_l={class:"pagination-container"};const gl=Qe({__name:"GroupMemberManager",props:{groupId:{type:[Number,String],required:!0},groupData:{type:Object,default:()=>({})}},setup(e,{expose:a}){a();const t=e,l=m(!1),r=m([]),n=m(0),o=m([]),s=m(!1),i=m(null),d=m({total_members:0,active_members:0,new_members_today:0,left_members_today:0}),u=_({page:1,limit:20,keyword:"",status:""}),c=async()=>{l.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),r.value=[{id:1,username:"user001",nickname:"张三",phone:"138****1234",avatar:"",joined_at:"2024-01-15 10:30:00",status:"active",tags:["核心用户","活跃"],inputVisible:!1,inputValue:""},{id:2,username:"user002",nickname:"李四",phone:"139****5678",avatar:"",joined_at:"2024-01-16 14:20:00",status:"active",tags:["潜在客户"],inputVisible:!1,inputValue:""},{id:3,username:"user003",nickname:"王五",phone:"137****4321",avatar:"",joined_at:"2024-02-10 09:00:00",status:"left",tags:[],inputVisible:!1,inputValue:""}],n.value=2,d.value={total_members:t.groupData.member_count||0,active_members:Math.floor(.8*(t.groupData.member_count||0)),new_members_today:5,left_members_today:1}}catch(e){re.error("获取成员列表失败")}finally{l.value=!1}};h(()=>{c()});const p={props:t,loading:l,memberList:r,total:n,selectedMembers:o,userProfileVisible:s,currentUserId:i,memberStats:d,queryParams:u,fetchMembers:c,handleAddMember:()=>{re.info("添加成员功能开发中")},handleExportMembers:()=>{re.info("导出成员功能开发中")},handleSelectionChange:e=>{o.value=e},viewMember:e=>{re.info(`查看成员: ${e.nickname}`)},viewMemberProfile:e=>{i.value=e.id,s.value=!0},removeMember:e=>{le.confirm(`确定要移除成员 "${e.nickname}" 吗？`,"确认移除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{re.success("成员移除成功"),c()})},resetQuery:()=>{Object.assign(u,{page:1,limit:20,keyword:"",status:""}),c()},handleBatchCommand:e=>{const a=o.value.map(e=>e.id);"tag"===e?le.prompt("请输入要批量添加的标签","批量打标签",{confirmButtonText:"确定",cancelButtonText:"取消"}).then(({value:e})=>{re.success(`已为 ${a.length} 位成员添加标签: "${e}"`),c()}).catch(()=>{}):"mute"===e?le.confirm(`确定要批量禁言这 ${a.length} 位成员吗？`,"确认禁言").then(()=>{re.success(`已成功禁言 ${a.length} 位成员`)}).catch(()=>{}):"kick"===e&&le.confirm(`确定要批量移除这 ${a.length} 位成员吗？`,"确认移除",{type:"warning"}).then(()=>{re.success(`已成功移除 ${a.length} 位成员`),c()}).catch(()=>{})},handleRemoveTag:(e,a)=>{e.tags.splice(e.tags.indexOf(a),1)},showInput:e=>{e.inputVisible=!0},handleInputConfirm:e=>{e.inputValue&&e.tags.push(e.inputValue),e.inputVisible=!1,e.inputValue=""},getStatusTagType:e=>({active:"success",left:"info",kicked:"danger"}[e]||"info"),getStatusText:e=>({active:"正常",left:"已退出",kicked:"被踢出"}[e]||"未知"),formatDate:e=>e?new Date(e).toLocaleString("zh-CN"):"未知",ref:m,reactive:_,onMounted:h,get ElMessage(){return re},get ElMessageBox(){return le},UserProfile:Lt,get User(){return H},get UserFilled(){return Pe},get Plus(){return te},get Remove(){return ke},get Download(){return De},get ArrowDown(){return q},get PriceTag(){return Ve},get MuteNotification(){return xe}};return Object.defineProperty(p,"__isScriptSetup",{enumerable:!1,value:!0}),p}},[["render",function(m,p,_,g,f,v){const h=I,y=D,b=V,C=A,x=ve,S=he,j=ye,M=P,z=k,G=T,q=U,L=$,O=be,B=ie,F=E,Q=we,N=Ce,J=w;return a(),n("div",Ot,[s("div",Bt,[r(b,{gutter:20},{default:t(()=>[r(y,{span:6},{default:t(()=>[s("div",Ft,[s("div",Qt,[r(h,null,{default:t(()=>[r(g.User)]),_:1})]),s("div",Nt,[s("div",Jt,R(g.memberStats.total_members),1),p[5]||(p[5]=s("div",{class:"stat-label"},"总成员数",-1))])])]),_:1}),r(y,{span:6},{default:t(()=>[s("div",Zt,[s("div",Kt,[r(h,null,{default:t(()=>[r(g.UserFilled)]),_:1})]),s("div",Ht,[s("div",Wt,R(g.memberStats.active_members),1),p[6]||(p[6]=s("div",{class:"stat-label"},"活跃成员",-1))])])]),_:1}),r(y,{span:6},{default:t(()=>[s("div",Yt,[s("div",Xt,[r(h,null,{default:t(()=>[r(g.Plus)]),_:1})]),s("div",el,[s("div",al,R(g.memberStats.new_members_today),1),p[7]||(p[7]=s("div",{class:"stat-label"},"今日新增",-1))])])]),_:1}),r(y,{span:6},{default:t(()=>[s("div",tl,[s("div",ll,[r(h,null,{default:t(()=>[r(g.Remove)]),_:1})]),s("div",rl,[s("div",nl,R(g.memberStats.left_members_today),1),p[8]||(p[8]=s("div",{class:"stat-label"},"今日退出",-1))])])]),_:1})]),_:1})]),s("div",ol,[p[14]||(p[14]=s("div",{class:"toolbar-left"},[s("h4",null,"成员列表")],-1)),s("div",sl,[g.selectedMembers.length>0?(a(),e(j,{key:0,onCommand:g.handleBatchCommand},{dropdown:t(()=>[r(S,null,{default:t(()=>[r(x,{command:"tag"},{default:t(()=>[r(h,null,{default:t(()=>[r(g.PriceTag)]),_:1}),p[9]||(p[9]=i("批量打标签 ",-1))]),_:1,__:[9]}),r(x,{command:"mute"},{default:t(()=>[r(h,null,{default:t(()=>[r(g.MuteNotification)]),_:1}),p[10]||(p[10]=i("批量禁言 ",-1))]),_:1,__:[10]}),r(x,{command:"kick",divided:""},{default:t(()=>[r(h,null,{default:t(()=>[r(g.Remove)]),_:1}),p[11]||(p[11]=i("批量移除 ",-1))]),_:1,__:[11]})]),_:1})]),default:t(()=>[r(C,{type:"primary"},{default:t(()=>[i(" 批量操作 ("+R(g.selectedMembers.length)+")",1),r(h,{class:"el-icon--right"},{default:t(()=>[r(g.ArrowDown)]),_:1})]),_:1})]),_:1})):o("",!0),r(C,{type:"primary",onClick:g.handleAddMember},{default:t(()=>[r(h,null,{default:t(()=>[r(g.Plus)]),_:1}),p[12]||(p[12]=i(" 添加成员 ",-1))]),_:1,__:[12]}),r(C,{onClick:g.handleExportMembers},{default:t(()=>[r(h,null,{default:t(()=>[r(g.Download)]),_:1}),p[13]||(p[13]=i(" 导出成员 ",-1))]),_:1,__:[13]})])]),s("div",il,[r(L,{inline:!0,model:g.queryParams},{default:t(()=>[r(z,{label:"搜索"},{default:t(()=>[r(M,{modelValue:g.queryParams.keyword,"onUpdate:modelValue":p[0]||(p[0]=e=>g.queryParams.keyword=e),placeholder:"用户名/手机号",clearable:"",onKeyup:c(g.fetchMembers,["enter"])},null,8,["modelValue"])]),_:1}),r(z,{label:"状态"},{default:t(()=>[r(q,{modelValue:g.queryParams.status,"onUpdate:modelValue":p[1]||(p[1]=e=>g.queryParams.status=e),placeholder:"全部状态",clearable:""},{default:t(()=>[r(G,{label:"正常",value:"active"}),r(G,{label:"已退出",value:"left"}),r(G,{label:"被踢出",value:"kicked"})]),_:1},8,["modelValue"])]),_:1}),r(z,null,{default:t(()=>[r(C,{type:"primary",onClick:g.fetchMembers},{default:t(()=>p[15]||(p[15]=[i("搜索",-1)])),_:1,__:[15]}),r(C,{onClick:g.resetQuery},{default:t(()=>p[16]||(p[16]=[i("重置",-1)])),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),s("div",dl,[l((a(),e(Q,{data:g.memberList,style:{width:"100%"},onSelectionChange:g.handleSelectionChange},{default:t(()=>[r(O,{type:"selection",width:"55"}),r(O,{label:"成员信息","min-width":"200"},{default:t(({row:e})=>[s("div",ul,[r(B,{src:e.avatar,size:40},{default:t(()=>[r(h,null,{default:t(()=>[r(g.User)]),_:1})]),_:2},1032,["src"]),s("div",cl,[s("div",ml,R(e.nickname||e.username),1),s("div",pl,R(e.phone||"未绑定"),1)])])]),_:1}),r(O,{label:"加入时间",width:"160"},{default:t(({row:e})=>[i(R(g.formatDate(e.joined_at)),1)]),_:1}),r(O,{label:"成员标签","min-width":"150"},{default:t(({row:l})=>[(a(!0),n(d,null,u(l.tags,r=>(a(),e(F,{key:r,class:"member-tag",size:"small",closable:"",onClose:e=>g.handleRemoveTag(l,r)},{default:t(()=>[i(R(r),1)]),_:2},1032,["onClose"]))),128)),l.inputVisible?(a(),e(M,{key:0,modelValue:l.inputValue,"onUpdate:modelValue":e=>l.inputValue=e,class:"tag-input",size:"small",onKeyup:c(e=>g.handleInputConfirm(l),["enter"]),onBlur:e=>g.handleInputConfirm(l)},null,8,["modelValue","onUpdate:modelValue","onKeyup","onBlur"])):(a(),e(C,{key:1,class:"button-new-tag",size:"small",onClick:e=>g.showInput(l)},{default:t(()=>p[17]||(p[17]=[i(" + 新标签 ",-1)])),_:2,__:[17]},1032,["onClick"]))]),_:1}),r(O,{label:"状态",width:"100"},{default:t(({row:e})=>[r(F,{type:g.getStatusTagType(e.status)},{default:t(()=>[i(R(g.getStatusText(e.status)),1)]),_:2},1032,["type"])]),_:1}),r(O,{label:"操作",width:"200",fixed:"right"},{default:t(({row:e})=>[r(C,{link:"",type:"primary",size:"small",onClick:a=>g.viewMemberProfile(e)},{default:t(()=>p[18]||(p[18]=[i(" 用户画像 ",-1)])),_:2,__:[18]},1032,["onClick"]),r(C,{link:"",type:"primary",size:"small",onClick:a=>g.viewMember(e)},{default:t(()=>p[19]||(p[19]=[i(" 查看 ",-1)])),_:2,__:[19]},1032,["onClick"]),r(C,{link:"",type:"danger",size:"small",onClick:a=>g.removeMember(e)},{default:t(()=>p[20]||(p[20]=[i(" 移除 ",-1)])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[J,g.loading]]),s("div",_l,[r(N,{"current-page":g.queryParams.page,"onUpdate:currentPage":p[2]||(p[2]=e=>g.queryParams.page=e),"page-size":g.queryParams.limit,"onUpdate:pageSize":p[3]||(p[3]=e=>g.queryParams.limit=e),total:g.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:g.fetchMembers,onCurrentChange:g.fetchMembers},null,8,["current-page","page-size","total"])])]),r(g.UserProfile,{visible:g.userProfileVisible,"user-id":g.currentUserId,"onUpdate:visible":p[4]||(p[4]=e=>g.userProfileVisible=e)},null,8,["visible","user-id"])])}],["__scopeId","data-v-d63b77b1"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/community/components/GroupMemberManager.vue"]]),fl={__name:"GroupAnalyticsSimple",props:{visible:{type:Boolean,default:!1},groupId:{type:[Number,String],required:!0},groupData:{type:Object,default:()=>({})}},emits:["update:visible"],setup(e,{expose:a,emit:t}){a();const l=e,r=t,n=m([{date:"2024-01-01",newMembers:12,activeMembers:156,messages:234,engagement:78.5},{date:"2024-01-02",newMembers:8,activeMembers:162,messages:198,engagement:82.1},{date:"2024-01-03",newMembers:15,activeMembers:171,messages:267,engagement:85.3},{date:"2024-01-04",newMembers:6,activeMembers:168,messages:189,engagement:79.8},{date:"2024-01-05",newMembers:11,activeMembers:175,messages:245,engagement:88.2}]);g(()=>l.visible,e=>{});const o={props:l,emit:r,analyticsData:n,handleClose:()=>{r("update:visible",!1)},ref:m,computed:p,watch:g,get TrendCharts(){return O}};return Object.defineProperty(o,"__isScriptSetup",{enumerable:!1,value:!0}),o}},vl={class:"analytics-content"},hl={class:"stat-item"},yl={class:"stat-value"},bl={class:"stat-item"},wl={class:"stat-value"},Cl={class:"stat-item"},xl={class:"stat-value"},Vl={class:"stat-item"},Dl={class:"stat-value"},kl={class:"chart-placeholder"},Pl={class:"placeholder-content"},Ul={class:"chart-placeholder"},Tl={class:"placeholder-content"};const Sl=Qe(fl,[["render",function(l,n,o,d,u,c){const m=x,p=D,_=V,g=I,f=be,v=we,h=me;return a(),e(h,{"model-value":o.visible,title:"群组分析",size:"60%","onUpdate:modelValue":n[0]||(n[0]=e=>l.$emit("update:visible",e)),onClose:d.handleClose},{default:t(()=>[s("div",vl,[r(_,{gutter:20,class:"stats-row"},{default:t(()=>[r(p,{span:6},{default:t(()=>[r(m,{class:"stat-card"},{default:t(()=>[s("div",hl,[s("div",yl,R(o.groupData.memberCount||0),1),n[1]||(n[1]=s("div",{class:"stat-label"},"总成员数",-1))])]),_:1})]),_:1}),r(p,{span:6},{default:t(()=>[r(m,{class:"stat-card"},{default:t(()=>[s("div",bl,[s("div",wl,R(o.groupData.monthlyActiveUsers||0),1),n[2]||(n[2]=s("div",{class:"stat-label"},"月活跃用户",-1))])]),_:1})]),_:1}),r(p,{span:6},{default:t(()=>[r(m,{class:"stat-card"},{default:t(()=>[s("div",Cl,[s("div",xl,R(o.groupData.dailyMessages||0),1),n[3]||(n[3]=s("div",{class:"stat-label"},"日均消息数",-1))])]),_:1})]),_:1}),r(p,{span:6},{default:t(()=>[r(m,{class:"stat-card"},{default:t(()=>[s("div",Vl,[s("div",Dl,R(((o.groupData.memberCount||0)/(o.groupData.maxMembers||500)*100).toFixed(1))+"%",1),n[4]||(n[4]=s("div",{class:"stat-label"},"群组满员率",-1))])]),_:1})]),_:1})]),_:1}),r(_,{gutter:20,style:{"margin-top":"20px"}},{default:t(()=>[r(p,{span:12},{default:t(()=>[r(m,{class:"chart-card",shadow:"never"},{header:t(()=>n[5]||(n[5]=[s("span",{class:"chart-title"},"成员增长趋势",-1)])),default:t(()=>[s("div",kl,[s("div",Pl,[r(g,{size:"48"},{default:t(()=>[r(d.TrendCharts)]),_:1}),n[6]||(n[6]=s("p",null,"成员增长趋势图",-1)),n[7]||(n[7]=s("p",{class:"placeholder-note"},"图表组件加载中...",-1))])])]),_:1})]),_:1}),r(p,{span:12},{default:t(()=>[r(m,{class:"chart-card",shadow:"never"},{header:t(()=>n[8]||(n[8]=[s("span",{class:"chart-title"},"活跃度分析",-1)])),default:t(()=>[s("div",Ul,[s("div",Tl,[r(g,{size:"48"},{default:t(()=>[r(d.TrendCharts)]),_:1}),n[9]||(n[9]=s("p",null,"活跃度分析图",-1)),n[10]||(n[10]=s("p",{class:"placeholder-note"},"图表组件加载中...",-1))])])]),_:1})]),_:1})]),_:1}),r(m,{style:{"margin-top":"20px"},shadow:"never"},{header:t(()=>n[11]||(n[11]=[s("span",null,"详细数据",-1)])),default:t(()=>[r(v,{data:d.analyticsData,style:{width:"100%"}},{default:t(()=>[r(f,{prop:"date",label:"日期",width:"120"}),r(f,{prop:"newMembers",label:"新增成员",width:"100"}),r(f,{prop:"activeMembers",label:"活跃成员",width:"100"}),r(f,{prop:"messages",label:"消息数",width:"100"}),r(f,{prop:"engagement",label:"参与度",width:"100"},{default:t(({row:e})=>[i(R(e.engagement)+"% ",1)]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1},8,["model-value"])}],["__scopeId","data-v-a852266c"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/community/components/GroupAnalyticsSimple.vue"]]),Il={__name:"QRCodeDialog",props:{modelValue:{type:Boolean,default:!1},groupData:{type:Object,default:()=>({})}},emits:["update:modelValue","qrcode-updated"],setup(e,{expose:a,emit:t}){a();const l=e,r=t,n=y(),o=p({get:()=>l.modelValue,set:e=>r("update:modelValue",e)}),s=m("promotion"),i=m(!1),d=m(""),u=m(""),c=p(()=>`/api/v1/wechat-groups/${l.groupData.id}/qr-code`),_=p(()=>({Authorization:`Bearer ${Ze()}`}));g(o,e=>{e&&l.groupData.id&&f()});const f=async()=>{if(l.groupData.id){i.value=!0;try{const e=await ra(l.groupData.id,{enable_anti_block:!0,enable_short_link:!0,link_type:"promotion"});if(200!==e.code||!e.data)throw new Error(e.message||"生成推广链接失败");{const a=e.data;u.value=a.short_url||a.anti_block_url||a.original_url,d.value=`https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(u.value)}`;const t=[];a.anti_block_enabled&&t.push("防红保护"),a.short_link_enabled&&t.push("短链接"),t.length>0?re.success(`推广链接生成成功，已启用：${t.join("、")}`):re.success("推广链接生成成功")}}catch(e){const a=l.groupData.id;u.value=`${window.location.origin}/landing/group/${a}`,d.value=`https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(u.value)}`,re.warning("防红系统暂时不可用，已生成普通推广链接")}finally{i.value=!1}}},v={props:l,emit:r,router:n,dialogVisible:o,activeTab:s,generating:i,promotionQrCode:d,promotionUrl:u,uploadUrl:c,uploadHeaders:_,handleTabChange:e=>{s.value=e},generatePromotionQrCode:f,refreshPromotionQrCode:()=>{f()},copyPromotionUrl:async()=>{if(u.value)try{await navigator.clipboard.writeText(u.value),re.success("推广链接已复制到剪贴板")}catch(e){const a=document.createElement("textarea");a.value=u.value,document.body.appendChild(a),a.select(),document.execCommand("copy"),document.body.removeChild(a),re.success("推广链接已复制到剪贴板")}else re.warning("推广链接还未生成")},downloadQRCode:(e,a)=>{if(!e)return;const t=document.createElement("a");t.href=e,t.download=`${l.groupData.title||l.groupData.name}-${a}.png`,document.body.appendChild(t),t.click(),document.body.removeChild(t),re.success(`${a}下载成功`)},goToLinkManagement:()=>{n.push("/promotion/links"),o.value=!1},goToGroupEdit:()=>{n.push(`/community/groups/edit/${l.groupData.id}`),o.value=!1},ref:m,computed:p,watch:g,onMounted:h,get ElMessage(){return re},get ElMessageBox(){return le},get useRouter(){return y},get Upload(){return Me},get Download(){return De},get Delete(){return je},get DocumentCopy(){return Re},get Refresh(){return Ae},get Link(){return Ie},get Edit(){return Se},get getToken(){return Ze},get generateGroupPromotionLink(){return ra},get updateGroupQrCode(){return We}};return Object.defineProperty(v,"__isScriptSetup",{enumerable:!1,value:!0}),v}},Al={class:"qrcode-container"},Rl={class:"qrcode-tabs"},jl={key:0,class:"qrcode-display"},Ml={key:0,class:"qrcode-image"},zl=["src"],El={class:"qrcode-info"},$l={class:"qrcode-url"},Gl={key:1,class:"no-qrcode"},ql={key:1,class:"qrcode-display"},Ll={key:0,class:"qrcode-image"},Ol=["src"],Bl={class:"qrcode-info"},Fl={key:1,class:"no-qrcode"},Ql={class:"qrcode-actions"},Nl={class:"dialog-footer"};const Jl=Qe(Il,[["render",function(l,u,c,m,p,_){const g=Ue,f=j,v=I,h=A,y=P,b=Te,w=C,x=G;return a(),e(x,{modelValue:m.dialogVisible,"onUpdate:modelValue":u[5]||(u[5]=e=>m.dialogVisible=e),title:"推广二维码",width:"600px","destroy-on-close":!0},{footer:t(()=>[s("div",Nl,[r(h,{onClick:u[4]||(u[4]=e=>m.dialogVisible=!1)},{default:t(()=>u[18]||(u[18]=[i("关闭",-1)])),_:1,__:[18]})])]),default:t(()=>[s("div",Al,[s("div",Rl,[r(f,{modelValue:m.activeTab,"onUpdate:modelValue":u[0]||(u[0]=e=>m.activeTab=e),onChange:m.handleTabChange},{default:t(()=>[r(g,{value:"promotion"},{default:t(()=>u[6]||(u[6]=[i("推广二维码",-1)])),_:1,__:[6]}),r(g,{value:"entry"},{default:t(()=>u[7]||(u[7]=[i("入群二维码",-1)])),_:1,__:[7]})]),_:1},8,["modelValue"])]),"promotion"===m.activeTab?(a(),n("div",jl,[m.promotionQrCode?(a(),n("div",Ml,[s("img",{src:m.promotionQrCode,alt:"推广二维码"},null,8,zl),s("div",El,[s("h4",null,R(c.groupData.title||c.groupData.name),1),u[8]||(u[8]=s("p",null,"扫码访问落地页，引导用户付费",-1)),s("div",$l,[r(y,{modelValue:m.promotionUrl,"onUpdate:modelValue":u[1]||(u[1]=e=>m.promotionUrl=e),readonly:"",size:"small",placeholder:"推广链接"},{append:t(()=>[r(h,{onClick:m.copyPromotionUrl,size:"small"},{default:t(()=>[r(v,null,{default:t(()=>[r(m.DocumentCopy)]),_:1})]),_:1})]),_:1},8,["modelValue"])])])])):(a(),n("div",Gl,[r(b,{description:"正在生成推广二维码...","image-size":100}),r(h,{type:"primary",onClick:m.generatePromotionQrCode,loading:m.generating},{default:t(()=>u[9]||(u[9]=[i(" 生成推广二维码 ",-1)])),_:1,__:[9]},8,["loading"])]))])):(a(),n("div",ql,[c.groupData.qr_code?(a(),n("div",Ll,[s("img",{src:c.groupData.qr_code,alt:"入群二维码"},null,8,Ol),s("div",Bl,[s("h4",null,R(c.groupData.title||c.groupData.name),1),u[10]||(u[10]=s("p",null,"付费后展示给用户的入群二维码",-1)),r(w,{title:"注意：这是付费后内容，只有完成支付的用户才能看到",type:"warning",closable:!1,"show-icon":""})])])):(a(),n("div",Fl,[r(b,{description:"暂未配置入群二维码","image-size":100}),u[11]||(u[11]=s("p",{class:"tip-text"},'请在群组编辑页面的"付费后内容配置"中上传入群二维码',-1))]))])),s("div",Ql,["promotion"===m.activeTab?(a(),n(d,{key:0},[m.promotionQrCode?(a(),e(h,{key:0,onClick:u[2]||(u[2]=e=>m.downloadQRCode(m.promotionQrCode,"推广二维码"))},{default:t(()=>[r(v,null,{default:t(()=>[r(m.Download)]),_:1}),u[12]||(u[12]=i(" 下载推广二维码 ",-1))]),_:1,__:[12]})):o("",!0),r(h,{onClick:m.copyPromotionUrl},{default:t(()=>[r(v,null,{default:t(()=>[r(m.DocumentCopy)]),_:1}),u[13]||(u[13]=i(" 复制推广链接 ",-1))]),_:1,__:[13]}),r(h,{onClick:m.refreshPromotionQrCode,loading:m.generating},{default:t(()=>[r(v,null,{default:t(()=>[r(m.Refresh)]),_:1}),u[14]||(u[14]=i(" 刷新二维码 ",-1))]),_:1,__:[14]},8,["loading"]),r(h,{type:"success",onClick:m.goToLinkManagement},{default:t(()=>[r(v,null,{default:t(()=>[r(m.Link)]),_:1}),u[15]||(u[15]=i(" 推广链接管理 ",-1))]),_:1,__:[15]})],64)):(a(),n(d,{key:1},[r(h,{type:"primary",onClick:m.goToGroupEdit},{default:t(()=>[r(v,null,{default:t(()=>[r(m.Edit)]),_:1}),u[16]||(u[16]=i(" 配置入群内容 ",-1))]),_:1,__:[16]}),c.groupData.qr_code?(a(),e(h,{key:0,onClick:u[3]||(u[3]=e=>m.downloadQRCode(c.groupData.qr_code,"入群二维码"))},{default:t(()=>[r(v,null,{default:t(()=>[r(m.Download)]),_:1}),u[17]||(u[17]=i(" 下载入群二维码 ",-1))]),_:1,__:[17]})):o("",!0)],64))])])]),_:1},8,["modelValue"])}],["__scopeId","data-v-0c9fc14c"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/community/components/QRCodeDialog.vue"]]),Zl=()=>({total_groups:na.number.int({min:100,max:500}),active_groups:na.number.int({min:80,max:400}),total_members:na.number.int({min:5e3,max:5e4}),total_revenue:na.number.float({min:1e4,max:5e5,fractionDigits:2}),pending:na.number.int({min:10,max:100}),approved:na.number.int({min:500,max:2e3}),rejected:na.number.int({min:50,max:200}),autoProcessed:na.number.int({min:200,max:1e3}),pendingIncrease:na.number.int({min:0,max:20}),approvalRate:na.number.float({min:80,max:98,fractionDigits:1}),rejectionRate:na.number.float({min:2,max:20,fractionDigits:1}),autoAccuracy:na.number.float({min:85,max:98,fractionDigits:1}),activeUsers:na.number.int({min:100,max:1e3}),newMessages:na.number.int({min:50,max:500}),todayRevenue:na.number.float({min:1e3,max:1e4,fractionDigits:0}),pendingReviews:na.number.int({min:5,max:50})}),Kl={getGroupList:(e={})=>{let a=[...((e=50)=>{const a=[],t=["startup","finance","tech","education","other"],l=["active","paused","full","pending"];for(let r=1;r<=e;r++)a.push({id:r,name:na.company.name()+"交流群",description:na.lorem.sentences(2),avatar:na.image.avatar(),category:na.helpers.arrayElement(t),status:na.helpers.arrayElement(l),owner_name:na.person.fullName(),owner_id:na.number.int({min:1,max:100}),price:na.number.float({min:0,max:999,fractionDigits:2}),current_members:na.number.int({min:0,max:500}),max_members:na.number.int({min:100,max:2e3}),total_revenue:na.number.float({min:0,max:5e4,fractionDigits:2}),health_score:na.number.int({min:20,max:100}),created_at:na.date.past({years:2}).toISOString(),updated_at:na.date.recent().toISOString()});return a})(100)];e.keyword&&(a=a.filter(a=>a.name.includes(e.keyword)||a.owner_name.includes(e.keyword))),e.status&&(a=a.filter(a=>a.status===e.status)),e.category&&(a=a.filter(a=>a.category===e.category));const t=e.page||1,l=e.size||20,r=(t-1)*l,n=r+l;return Promise.resolve({code:200,data:{list:a.slice(r,n),total:a.length,page:t,size:l},message:"获取成功"})},getGroupStats:()=>Promise.resolve({code:200,data:Zl(),message:"获取成功"}),getRealTimeGroupStatus:()=>Promise.resolve({code:200,data:{activeUsers:na.number.int({min:100,max:1e3}),newMessages:na.number.int({min:50,max:500}),todayRevenue:na.number.float({min:1e3,max:1e4,fractionDigits:0}),pendingReviews:na.number.int({min:5,max:50})},message:"获取成功"}),batchUpdateGroupStatus:(e,a)=>new Promise(a=>{setTimeout(()=>{a({code:200,data:{updated:e.length},message:"批量更新成功"})},1e3)}),getContentList:(e={})=>{let a=[...((e=100)=>{const a=[],t=["text","image","video","link"],l=["low","medium","high"],r=["pending","approved","rejected","flagged"];for(let n=1;n<=e;n++){const e=na.helpers.arrayElement(t),o=na.helpers.arrayElement(l);a.push({id:n,content:na.lorem.paragraphs(na.number.int({min:1,max:3})),type:e,risk_level:o,status:na.helpers.arrayElement(r),author:{id:na.number.int({min:1,max:100}),name:na.person.fullName(),avatar:na.image.avatar()},group:{id:na.number.int({min:1,max:50}),name:na.company.name()+"群"},image_url:"image"===e?na.image.url():null,ai_analysis:"low"!==o?{confidence:na.number.float({min:.3,max:.95,fractionDigits:2}),tags:na.helpers.arrayElements(["敏感词汇","可能违规","需人工审核","广告嫌疑"],{min:1,max:3})}:null,created_at:na.date.past({years:1}).toISOString(),analyzing:!1})}return a})(200)];e.status&&(a=a.filter(a=>a.status===e.status)),e.type&&(a=a.filter(a=>a.type===e.type)),e.risk_level&&(a=a.filter(a=>a.risk_level===e.risk_level)),e.keyword&&(a=a.filter(a=>a.content.includes(e.keyword)||a.author.name.includes(e.keyword)||a.group.name.includes(e.keyword)));const t=e.page||1,l=e.size||20,r=(t-1)*l,n=r+l;return Promise.resolve({code:200,data:{list:a.slice(r,n),total:a.length,page:t,size:l},message:"获取成功"})},getContentReviewStats:()=>{const e=Zl();return Promise.resolve({code:200,data:{pending:e.pending,approved:e.approved,rejected:e.rejected,autoProcessed:e.autoProcessed,pendingIncrease:e.pendingIncrease,approvalRate:e.approvalRate,rejectionRate:e.rejectionRate,autoAccuracy:e.autoAccuracy},message:"获取成功"})},batchReviewContent:(e,a,t="")=>new Promise(l=>{setTimeout(()=>{l({code:200,data:{processed:e.length,action:a,reason:t},message:"批量操作成功"})},1500)}),intelligentContentReview:e=>new Promise(a=>{setTimeout(()=>{a({code:200,data:{content_id:e,confidence:na.number.float({min:.6,max:.95,fractionDigits:2}),tags:na.helpers.arrayElements(["敏感词汇","可能违规","需人工审核","广告嫌疑"],{min:1,max:2}),recommendation:na.helpers.arrayElement(["approve","reject","flag"])},message:"AI分析完成"})},2e3)}),getGroupActivityAnalysis:(e,a="7d")=>{const t=[];for(let l=("7d"===a?7:"30d"===a?30:7)-1;l>=0;l--){const e=new Date;e.setDate(e.getDate()-l),t.push({date:e.toISOString().split("T")[0],messages:na.number.int({min:10,max:200}),active_users:na.number.int({min:5,max:50}),new_members:na.number.int({min:0,max:10})})}return Promise.resolve({code:200,data:{group_id:e,time_range:a,activity_data:t,summary:{total_messages:t.reduce((e,a)=>e+a.messages,0),avg_daily_active:Math.round(t.reduce((e,a)=>e+a.active_users,0)/t.length),total_new_members:t.reduce((e,a)=>e+a.new_members,0)}},message:"获取成功"})},getGroupRevenueAnalysis:(e,a="30d")=>{const t=[];for(let l=("30d"===a?30:"7d"===a?7:30)-1;l>=0;l--){const e=new Date;e.setDate(e.getDate()-l),t.push({date:e.toISOString().split("T")[0],revenue:na.number.float({min:0,max:1e3,fractionDigits:2}),orders:na.number.int({min:0,max:20}),refunds:na.number.float({min:0,max:100,fractionDigits:2})})}return Promise.resolve({code:200,data:{group_id:e,time_range:a,revenue_data:t,summary:{total_revenue:t.reduce((e,a)=>e+a.revenue,0),total_orders:t.reduce((e,a)=>e+a.orders,0),total_refunds:t.reduce((e,a)=>e+a.refunds,0),avg_daily_revenue:t.reduce((e,a)=>e+a.revenue,0)/t.length}},message:"获取成功"})},exportGroups:e=>new Promise(e=>{setTimeout(()=>{const a=new Blob(["群组数据导出文件内容"],{type:"text/csv"}),t=URL.createObjectURL(a),l=document.createElement("a");l.href=t,l.download=`groups_export_${Date.now()}.csv`,l.click(),URL.revokeObjectURL(t),e({code:200,message:"导出成功"})},1e3)}),getSensitiveWords:()=>Promise.resolve({code:200,data:{words:["违法","赌博","色情","暴力","诈骗","传销"],categories:{illegal:["违法","犯罪"],gambling:["赌博","博彩"],adult:["色情","成人"],violence:["暴力","血腥"],fraud:["诈骗","欺诈"],pyramid:["传销","直销"]}},message:"获取成功"}),getReviewRules:()=>Promise.resolve({code:200,data:[{id:1,name:"敏感词检测",type:"keyword",enabled:!0,config:{action:"flag",keywords:["违法","赌博","色情"]}},{id:2,name:"图片内容检测",type:"image",enabled:!0,config:{action:"review",confidence_threshold:.8}}],message:"获取成功"})},Hl={class:"modern-group-list"},Wl={key:1},Yl={class:"page-header"},Xl={class:"header-content"},er={class:"header-left"},ar={class:"header-icon"},tr={class:"header-actions"},lr={class:"stats-section"},rr={class:"stats-container"},nr={class:"stat-content"},or={class:"stat-value"},sr={class:"stat-label"},ir={class:"filter-section"},dr={class:"filter-content"},ur={class:"filter-left"},cr={class:"search-group"},mr={class:"filter-group"},pr={class:"filter-right"},_r={class:"table-section"},gr={class:"table-header"},fr={class:"header-left"},vr={class:"total-count"},hr={class:"header-right"},yr={key:0,class:"table-view"},br={class:"group-info"},wr={class:"group-avatar"},Cr={class:"group-details"},xr={class:"group-name"},Vr={class:"group-desc"},Dr={class:"group-meta"},kr={class:"group-id"},Pr={class:"group-category"},Ur={class:"owner-info"},Tr={class:"owner-name"},Sr={class:"price-info"},Ir={class:"price-value"},Ar={class:"member-stats"},Rr={class:"member-count"},jr={class:"current"},Mr={class:"max"},zr={class:"health-score"},Er={class:"score-label"},$r={class:"time-info"},Gr={class:"date"},qr={class:"action-buttons"},Lr={key:1,class:"card-view"},Or=["onClick"],Br={class:"card-header"},Fr={class:"group-avatar"},Qr={class:"card-actions"},Nr={class:"card-content"},Jr={class:"group-title"},Zr={class:"group-description"},Kr={class:"group-stats"},Hr={class:"stat-item"},Wr={class:"value"},Yr={class:"stat-item"},Xr={class:"value price"},en={class:"group-tags"},an={class:"pagination-wrapper"};const tn=Qe({__name:"GroupList",setup(e,{expose:a}){a();const t=y(),l=m([]),r=m(0),n=m(!0),o=m(!1),s=m(!1),i=m(!1),d=m(!1),u=m({}),c=m(null),p=m([]),g=m("table"),f=m(!1),v=m([{key:"total",label:"总群组数",value:156,icon:"Comment",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"CaretTop",change:"+15.8%"},{key:"active",label:"活跃群组",value:128,icon:"ChatDotRound",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"CaretTop",change:"+8.2%"},{key:"members",label:"总成员数",value:12456,icon:"User",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"CaretTop",change:"+12.1%"},{key:"revenue",label:"总收入",value:"¥89,234",icon:"Money",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"CaretTop",change:"+18.5%"}]),b=_({page:1,limit:20,keyword:"",status:"",category:""}),w=async()=>{n.value=!0;try{const e=await Ye(b),{data:a}=e;a&&a.list?(l.value=a.list,r.value=a.total||0):(l.value=[],r.value=0,re.warning("暂无数据"))}catch(e){const a=JSON.parse(localStorage.getItem("mockGroups")||"[]");let t=a;b.name&&(t=t.filter(e=>e.name.toLowerCase().includes(b.name.toLowerCase()))),b.status&&(t=t.filter(e=>e.status===b.status));const n=(b.page-1)*b.limit,o=n+b.limit,s=t.slice(n,o);l.value=s,r.value=t.length,0===a.length&&re.info("暂无群组数据，请创建新群组")}finally{n.value=!1}},C=async()=>{try{const e=await Xe(),{data:a}=e;a&&v.value.forEach(e=>{void 0!==a[e.key]&&(e.value=a[e.key])})}catch(e){const a=JSON.parse(localStorage.getItem("mockGroups")||"[]"),t={total:a.length,active:a.filter(e=>"active"===e.status).length,inactive:a.filter(e=>"inactive"===e.status).length,pending:a.filter(e=>"pending"===e.status).length};v.value.forEach(e=>{void 0!==t[e.key]&&(e.value=t[e.key])})}},x=e=>{u.value={...e},o.value=!0},V=e=>{u.value={...e},s.value=!0},D=e=>{c.value=e.id,i.value=!0},k=async e=>{try{await le.confirm("确定要克隆该群组吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),re.success("群组克隆成功"),w()}catch(a){"cancel"!==a&&re.error("克隆失败")}},P=async(e,a)=>{try{const t="paused"===a?"暂停":"恢复";await le.confirm(`确定要${t}该群组吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await aa(e,a),re.success(`${t}成功`),w()}catch(t){"cancel"!==t&&re.error("操作失败")}},U=async e=>{try{await le.confirm("确定要删除该群组吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await ta(e),re.success("删除成功"),w()}catch(a){"cancel"!==a&&re.error("删除失败")}},T=async e=>{try{p.value.map(e=>e.id);re.success("批量操作成功"),w()}catch(a){re.error("批量操作失败")}};h(()=>{w(),C()});const S={router:t,list:l,total:r,listLoading:n,dialogVisible:o,memberDialogVisible:s,analyticsDrawerVisible:i,qrcodeDialogVisible:d,currentGroup:u,currentGroupId:c,multipleSelection:p,viewMode:g,showCreateForm:f,groupStats:v,mockGroupList:[{id:1,name:"创业交流群",description:"专注于创业项目讨论和资源分享",avatar:"",owner_name:"张三",owner_role:"群主",price:99,current_members:245,max_members:500,health_score:85,status:"active",category:"startup",created_at:"2024-01-15 10:30:00"},{id:2,name:"投资理财精英群",description:"高端投资理财交流，分享投资心得",avatar:"",owner_name:"李四",owner_role:"群主",price:199,current_members:189,max_members:200,health_score:92,status:"active",category:"finance",created_at:"2024-02-01 14:20:00"},{id:3,name:"科技互联网前沿",description:"探讨最新科技趋势和互联网发展",avatar:"",owner_name:"王五",owner_role:"群主",price:149,current_members:156,max_members:300,health_score:78,status:"active",category:"tech",created_at:"2024-02-15 09:45:00"},{id:4,name:"在线教育培训",description:"职业技能培训和学习资源分享",avatar:"",owner_name:"赵六",owner_role:"群主",price:79,current_members:89,max_members:150,health_score:65,status:"paused",category:"education",created_at:"2024-03-01 16:15:00"},{id:5,name:"生活分享交流群",description:"分享生活经验和日常趣事",avatar:"",owner_name:"孙七",owner_role:"群主",price:29,current_members:298,max_members:300,health_score:58,status:"full",category:"other",created_at:"2024-03-10 11:30:00"},{id:6,name:"新手创业指导",description:"为创业新手提供指导和帮助",avatar:"",owner_name:"周八",owner_role:"群主",price:59,current_members:45,max_members:100,health_score:42,status:"pending",category:"startup",created_at:"2024-03-20 13:45:00"}],listQuery:b,getList:w,getStats:C,handleFilter:()=>{b.page=1,w()},handleCreate:()=>{u.value={},f.value=!0},handleEdit:x,handleMembers:V,handleAnalytics:D,handleAutoRules:()=>{t.push("/admin/community/groups")},handleQRCode:e=>{u.value={...e},d.value=!0},handleClone:k,handleUpdateStatus:P,handleDelete:U,handleCommand:e=>{const[a,t]=e.split("-"),r=parseInt(t),n=l.value.find(e=>e.id===r);switch(a){case"edit":x(n);break;case"members":V(n);break;case"analytics":D(n);break;case"qrcode":u.value={...n},d.value=!0;break;case"clone":k();break;case"pause":P(r,"paused");break;case"resume":P(r,"active");break;case"delete":U(r)}},handleExport:async()=>{try{await ea(b),re.success("导出成功")}catch(e){re.error("导出失败")}},handleBatchOperation:()=>{0!==p.value.length?le.confirm("请选择批量操作类型","批量操作",{distinguishCancelAndClose:!0,confirmButtonText:"暂停群组",cancelButtonText:"恢复群组"}).then(()=>{T()}).catch(e=>{"cancel"===e&&T()}):re.warning("请先选择要操作的群组")},batchUpdateStatus:T,handleSelectionChange:e=>{p.value=e},handleSizeChange:e=>{b.limit=e,w()},handleCurrentChange:e=>{b.page=e,w()},handleDialogSuccess:()=>{w(),C()},handleCreateSuccess:e=>{const a=JSON.parse(localStorage.getItem("mockGroups")||"[]");e.id||(e.id=Date.now());const t={...e,status:"active",member_count:0,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()};a.unshift(t),localStorage.setItem("mockGroups",JSON.stringify(a)),f.value=!1,w(),C(),re.success("群组创建成功")},handleCreateCancel:()=>{f.value=!1},handleMemberSuccess:()=>{w()},getCategoryTagType:e=>({startup:"success",finance:"warning",tech:"primary",education:"info",other:""}[e]||""),getCategoryText:e=>({startup:"创业交流",finance:"投资理财",tech:"科技互联网",education:"教育培训",other:"其他"}[e]||"未知"),getStatusTagType:e=>({active:"success",paused:"warning",full:"info",pending:"danger"}[e]||""),getStatusText:e=>({active:"活跃",paused:"暂停",full:"已满",pending:"待审核"}[e]||"未知"),getProgressColor:e=>e<.5?"#67c23a":e<.8?"#e6a23c":"#f56c6c",getHealthScoreClass:e=>!e||e<40?"poor":e<60?"fair":e<80?"good":"excellent",getHealthScoreLabel:e=>!e||e<40?"较差":e<60?"一般":e<80?"良好":"优秀",ref:m,onMounted:h,reactive:_,get ElMessage(){return re},get ElMessageBox(){return le},get useRouter(){return y},get Comment(){return Ge},get ChatDotRound(){return ne},get User(){return H},get Money(){return fe},get CaretTop(){return $e},get Search(){return Ee},get Plus(){return te},get Download(){return De},get ArrowDown(){return q},get Setting(){return ae},get Document(){return X},get Delete(){return je},get MoreFilled(){return ze},GroupDialog:Ia,GroupCreateComplete:yt,GroupMemberManager:gl,GroupAnalyticsSimple:Sl,QRCodeDialog:Jl,get getGroupList(){return Ye},get deleteGroup(){return ta},get updateGroupStatus(){return aa},get exportGroups(){return ea},get getGroupStats(){return Xe},get mockCommunityEnhancedAPI(){return Kl},get formatDate(){return la}};return Object.defineProperty(S,"__isScriptSetup",{enumerable:!1,value:!0}),S}},[["render",function(m,p,_,g,f,v){const h=I,y=A,C=P,k=T,S=U,j=x,M=Be,z=be,$=ie,G=E,q=Oe,L=ve,O=he,B=ye,F=we,Q=D,N=V,J=Ce,Z=w;return a(),n("div",Hl,[g.showCreateForm?(a(),e(g.GroupCreateComplete,{key:0,"group-data":g.currentGroup,onSuccess:g.handleCreateSuccess,onCancel:g.handleCreateCancel},null,8,["group-data"])):(a(),n("div",Wl,[s("div",Yl,[s("div",Xl,[s("div",er,[s("div",ar,[r(h,{size:"24"},{default:t(()=>[r(g.Comment)]),_:1})]),p[11]||(p[11]=s("div",{class:"header-text"},[s("h1",null,"社群管理"),s("p",null,"管理和监控您的社群运营状况，提升群组活跃度和收益")],-1))]),s("div",tr,[r(y,{onClick:g.handleExport,class:"action-btn secondary"},{default:t(()=>[r(h,null,{default:t(()=>[r(g.Download)]),_:1}),p[12]||(p[12]=i(" 导出数据 ",-1))]),_:1,__:[12]}),r(y,{type:"primary",onClick:g.handleCreate,class:"action-btn primary"},{default:t(()=>[r(h,null,{default:t(()=>[r(g.Plus)]),_:1}),p[13]||(p[13]=i(" 创建群组 ",-1))]),_:1,__:[13]})])])]),s("div",lr,[s("div",rr,[(a(!0),n(d,null,u(g.groupStats,l=>(a(),n("div",{class:"stat-card",key:l.key},[s("div",{class:"stat-icon",style:qe({background:l.color})},[r(h,{size:"20"},{default:t(()=>[(a(),e(b(l.icon)))]),_:2},1024)],4),s("div",nr,[s("div",or,R(l.value),1),s("div",sr,R(l.label),1)]),s("div",{class:Le(["stat-trend",l.trend])},[r(h,{size:"14"},{default:t(()=>[(a(),e(b(l.trendIcon)))]),_:2},1024),s("span",null,R(l.change),1)],2)]))),128))])]),s("div",ir,[r(j,{class:"filter-card",shadow:"never"},{default:t(()=>[s("div",dr,[s("div",ur,[s("div",cr,[r(C,{modelValue:g.listQuery.keyword,"onUpdate:modelValue":p[0]||(p[0]=e=>g.listQuery.keyword=e),placeholder:"搜索群组名称、群主...",class:"search-input",onKeyup:c(g.handleFilter,["enter"]),clearable:""},{prefix:t(()=>[r(h,null,{default:t(()=>[r(g.Search)]),_:1})]),_:1},8,["modelValue"])]),s("div",mr,[r(S,{modelValue:g.listQuery.status,"onUpdate:modelValue":p[1]||(p[1]=e=>g.listQuery.status=e),placeholder:"群组状态",clearable:"",class:"filter-select"},{default:t(()=>[r(k,{label:"全部状态",value:""}),r(k,{label:"活跃",value:"active"}),r(k,{label:"暂停",value:"paused"}),r(k,{label:"已满",value:"full"}),r(k,{label:"待审核",value:"pending"})]),_:1},8,["modelValue"]),r(S,{modelValue:g.listQuery.category,"onUpdate:modelValue":p[2]||(p[2]=e=>g.listQuery.category=e),placeholder:"群组分类",clearable:"",class:"filter-select"},{default:t(()=>[r(k,{label:"全部分类",value:""}),r(k,{label:"创业交流",value:"startup"}),r(k,{label:"投资理财",value:"finance"}),r(k,{label:"科技互联网",value:"tech"}),r(k,{label:"教育培训",value:"education"}),r(k,{label:"其他",value:"other"})]),_:1},8,["modelValue"])])]),s("div",pr,[r(y,{type:"primary",onClick:g.handleFilter,class:"action-btn"},{default:t(()=>[r(h,null,{default:t(()=>[r(g.Search)]),_:1}),p[14]||(p[14]=i(" 搜索 ",-1))]),_:1,__:[14]}),r(y,{type:"success",onClick:g.handleAutoRules,class:"action-btn"},{default:t(()=>[r(h,null,{default:t(()=>[r(g.Setting)]),_:1}),p[15]||(p[15]=i(" 自动化规则 ",-1))]),_:1,__:[15]}),r(y,{type:"warning",onClick:g.handleExport,class:"action-btn"},{default:t(()=>[r(h,null,{default:t(()=>[r(g.Download)]),_:1}),p[16]||(p[16]=i(" 导出数据 ",-1))]),_:1,__:[16]}),g.multipleSelection.length>0?(a(),e(y,{key:0,type:"info",onClick:g.handleBatchOperation,class:"action-btn"},{default:t(()=>[i(" 批量操作 ("+R(g.multipleSelection.length)+") ",1)]),_:1})):o("",!0)])])]),_:1})]),s("div",_r,[r(j,{class:"table-card",shadow:"never"},{header:t(()=>[s("div",gr,[s("div",fr,[p[17]||(p[17]=s("h3",null,"群组列表",-1)),s("span",vr,"共 "+R(g.total)+" 个群组",1)]),s("div",hr,[r(M,null,{default:t(()=>[r(y,{type:"table"===g.viewMode?"primary":"",onClick:p[3]||(p[3]=e=>g.viewMode="table")},{default:t(()=>p[18]||(p[18]=[i(" 表格视图 ",-1)])),_:1,__:[18]},8,["type"]),r(y,{type:"card"===g.viewMode?"primary":"",onClick:p[4]||(p[4]=e=>g.viewMode="card")},{default:t(()=>p[19]||(p[19]=[i(" 卡片视图 ",-1)])),_:1,__:[19]},8,["type"])]),_:1})])])]),default:t(()=>["table"===g.viewMode?(a(),n("div",yr,[l((a(),e(F,{data:g.list,"element-loading-text":"加载中...",class:"modern-table",onSelectionChange:g.handleSelectionChange},{default:t(()=>[r(z,{type:"selection",width:"55"}),r(z,{label:"群组信息",width:"280",fixed:"left"},{default:t(({row:e})=>[s("div",br,[s("div",wr,[r($,{src:e.avatar,alt:e.name,size:"large"},{default:t(()=>[i(R(e.name.charAt(0)),1)]),_:2},1032,["src","alt"]),s("div",{class:Le(["status-dot",e.status])},null,2)]),s("div",Cr,[s("div",xr,R(e.name),1),s("div",Vr,R(e.description),1),s("div",Dr,[s("span",kr,"ID: "+R(e.id),1),s("span",Pr,R(g.getCategoryText(e.category)),1)])])])]),_:1}),r(z,{label:"群主信息",width:"150"},{default:t(({row:e})=>[s("div",Ur,[s("div",Tr,R(e.owner?.name||e.owner_name),1),r(G,{size:"small",type:"info",class:"owner-role"},{default:t(()=>[i(R(e.owner?.role||e.owner_role||"群主"),1)]),_:2},1024)])]),_:1}),r(z,{label:"价格",width:"100"},{default:t(({row:e})=>[s("div",Sr,[s("span",Ir,"¥"+R((e.price||0).toFixed(2)),1)])]),_:1}),r(z,{label:"成员统计",width:"180"},{default:t(({row:e})=>[s("div",Ar,[s("div",Rr,[s("span",jr,R(e.memberCount||e.current_members||0),1),p[20]||(p[20]=s("span",{class:"separator"},"/",-1)),s("span",Mr,R(e.maxMembers||e.max_members||500),1)]),r(q,{percentage:(e.memberCount||e.current_members||0)/(e.maxMembers||e.max_members||500)*100,"stroke-width":6,"show-text":!1,color:g.getProgressColor((e.memberCount||e.current_members||0)/(e.maxMembers||e.max_members||500)),class:"member-progress"},null,8,["percentage","color"])])]),_:1}),r(z,{label:"健康度",width:"120"},{default:t(({row:e})=>[s("div",zr,[s("div",{class:Le(["score-circle",g.getHealthScoreClass(e.health_score)])},R(e.health_score||"N/A"),3),s("div",Er,R(g.getHealthScoreLabel(e.health_score)),1)])]),_:1}),r(z,{label:"状态",width:"100"},{default:t(({row:e})=>[r(G,{type:g.getStatusTagType(e.status),class:"status-tag"},{default:t(()=>[i(R(g.getStatusText(e.status)),1)]),_:2},1032,["type"])]),_:1}),r(z,{label:"创建时间",width:"160"},{default:t(({row:e})=>[s("div",$r,[s("div",Gr,R(g.formatDate(e.created_at)),1)])]),_:1}),r(z,{label:"操作",width:"280",fixed:"right"},{default:t(({row:l})=>[s("div",qr,[r(y,{type:"primary",size:"small",onClick:e=>g.handleEdit(l),class:"action-btn-small"},{default:t(()=>p[21]||(p[21]=[i(" 编辑 ",-1)])),_:2,__:[21]},1032,["onClick"]),r(y,{type:"success",size:"small",onClick:e=>g.handleMembers(l),class:"action-btn-small"},{default:t(()=>p[22]||(p[22]=[i(" 成员 ",-1)])),_:2,__:[22]},1032,["onClick"]),r(y,{type:"info",size:"small",onClick:e=>g.handleAnalytics(l),class:"action-btn-small"},{default:t(()=>p[23]||(p[23]=[i(" 分析 ",-1)])),_:2,__:[23]},1032,["onClick"]),r(B,{onCommand:g.handleCommand,class:"action-dropdown"},{dropdown:t(()=>[r(O,null,{default:t(()=>[r(L,{command:`qrcode-${l.id}`},{default:t(()=>p[25]||(p[25]=[i(" 二维码 ",-1)])),_:2,__:[25]},1032,["command"]),r(L,{command:`clone-${l.id}`},{default:t(()=>p[26]||(p[26]=[i(" 克隆群组 ",-1)])),_:2,__:[26]},1032,["command"]),"active"===l.status?(a(),e(L,{key:0,command:`pause-${l.id}`},{default:t(()=>p[27]||(p[27]=[i(" 暂停群组 ",-1)])),_:2,__:[27]},1032,["command"])):o("",!0),"paused"===l.status?(a(),e(L,{key:1,command:`resume-${l.id}`},{default:t(()=>p[28]||(p[28]=[i(" 恢复群组 ",-1)])),_:2,__:[28]},1032,["command"])):o("",!0),r(L,{command:`delete-${l.id}`,divided:""},{default:t(()=>[r(h,null,{default:t(()=>[r(g.Delete)]),_:1}),p[29]||(p[29]=i(" 删除群组 ",-1))]),_:2,__:[29]},1032,["command"])]),_:2},1024)]),default:t(()=>[r(y,{type:"warning",size:"small",class:"action-btn-small"},{default:t(()=>[p[24]||(p[24]=i(" 更多",-1)),r(h,{class:"el-icon--right"},{default:t(()=>[r(g.ArrowDown)]),_:1})]),_:1,__:[24]})]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Z,g.listLoading]])])):(a(),n("div",Lr,[r(N,{gutter:24},{default:t(()=>[(a(!0),n(d,null,u(g.list,l=>(a(),e(Q,{span:8,key:l.id},{default:t(()=>[s("div",{class:"group-card",onClick:e=>g.handleEdit(l)},[s("div",Br,[s("div",Fr,[r($,{src:l.avatar,alt:l.name,size:"large"},{default:t(()=>[i(R(l.name.charAt(0)),1)]),_:2},1032,["src","alt"]),s("div",{class:Le(["status-indicator",l.status])},null,2)]),s("div",Qr,[r(B,{onCommand:g.handleCommand,trigger:"click"},{dropdown:t(()=>[r(O,null,{default:t(()=>[r(L,{command:`edit-${l.id}`},{default:t(()=>[...p[30]||(p[30]=[i("编辑",-1)])]),_:2,__:[30]},1032,["command"]),r(L,{command:`members-${l.id}`},{default:t(()=>[...p[31]||(p[31]=[i("成员管理",-1)])]),_:2,__:[31]},1032,["command"]),r(L,{command:`analytics-${l.id}`},{default:t(()=>[...p[32]||(p[32]=[i("数据分析",-1)])]),_:2,__:[32]},1032,["command"]),r(L,{command:`delete-${l.id}`,divided:""},{default:t(()=>[...p[33]||(p[33]=[i("删除",-1)])]),_:2,__:[33]},1032,["command"])]),_:2},1024)]),default:t(()=>[r(y,{text:"",class:"more-btn"},{default:t(()=>[r(h,null,{default:t(()=>[r(g.MoreFilled)]),_:1})]),_:1})]),_:2},1024)])]),s("div",Nr,[s("h4",Jr,R(l.name),1),s("p",Zr,R(l.description),1),s("div",Kr,[s("div",Hr,[p[34]||(p[34]=s("span",{class:"label"},"成员",-1)),s("span",Wr,R(l.memberCount||l.current_members||0)+"/"+R(l.maxMembers||l.max_members||500),1)]),s("div",Yr,[p[35]||(p[35]=s("span",{class:"label"},"价格",-1)),s("span",Xr,"¥"+R((l.price||0).toFixed(2)),1)])]),s("div",en,[r(G,{type:g.getCategoryTagType(l.category),size:"small"},{default:t(()=>[i(R(g.getCategoryText(l.category)),1)]),_:2},1032,["type"]),r(G,{type:g.getStatusTagType(l.status),size:"small"},{default:t(()=>[i(R(g.getStatusText(l.status)),1)]),_:2},1032,["type"])])])],8,Or)]),_:2},1024))),128))]),_:1})])),s("div",an,[r(J,{"current-page":g.listQuery.page,"onUpdate:currentPage":p[5]||(p[5]=e=>g.listQuery.page=e),"page-size":g.listQuery.limit,"onUpdate:pageSize":p[6]||(p[6]=e=>g.listQuery.limit=e),"page-sizes":[12,24,36,48],total:g.total,background:"",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:g.handleSizeChange,onCurrentChange:g.handleCurrentChange,class:"modern-pagination"},null,8,["current-page","page-size","total"])])]),_:1})]),r(g.GroupDialog,{modelValue:g.dialogVisible,"onUpdate:modelValue":p[7]||(p[7]=e=>g.dialogVisible=e),"group-data":g.currentGroup,onSuccess:g.handleDialogSuccess},null,8,["modelValue","group-data"]),r(g.GroupMemberManager,{modelValue:g.memberDialogVisible,"onUpdate:modelValue":p[8]||(p[8]=e=>g.memberDialogVisible=e),"group-data":g.currentGroup,onSuccess:g.handleMemberSuccess},null,8,["modelValue","group-data"]),r(g.GroupAnalyticsSimple,{visible:g.analyticsDrawerVisible,"onUpdate:visible":p[9]||(p[9]=e=>g.analyticsDrawerVisible=e),"group-id":g.currentGroupId,"group-data":g.currentGroup},null,8,["visible","group-id","group-data"]),r(g.QRCodeDialog,{modelValue:g.qrcodeDialogVisible,"onUpdate:modelValue":p[10]||(p[10]=e=>g.qrcodeDialogVisible=e),"group-data":g.currentGroup},null,8,["modelValue","group-data"])]))])}],["__scopeId","data-v-bcf170a7"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/community/GroupList.vue"]]);export{tn as default};
