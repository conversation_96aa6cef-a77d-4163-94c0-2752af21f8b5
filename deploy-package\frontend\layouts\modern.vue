<template>
  <div class="modern-layout">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="bg-circle circle-1"></div>
      <div class="bg-circle circle-2"></div>
      <div class="bg-circle circle-3"></div>
      <div class="bg-circle circle-4"></div>
    </div>
    
    <!-- 导航栏 -->
    <nav class="modern-nav">
      <div class="nav-container">
        <div class="nav-brand">
          <NuxtLink to="/" class="brand-link">
            <div class="brand-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
            </div>
            <span class="brand-text">晨鑫流量变现系统</span>
          </NuxtLink>
        </div>
        
        <div class="nav-menu" :class="{ 'menu-open': menuOpen }">
          <NuxtLink to="/" class="nav-item" @click="closeMenu">
            <Icon name="home" />
            <span>首页</span>
          </NuxtLink>
          <NuxtLink to="/browse" class="nav-item" @click="closeMenu">
            <Icon name="search" />
            <span>浏览群组</span>
          </NuxtLink>
          <NuxtLink to="/dashboard" class="nav-item" @click="closeMenu">
            <Icon name="dashboard" />
            <span>仪表盘</span>
          </NuxtLink>
          <NuxtLink to="/profile" class="nav-item" @click="closeMenu">
            <Icon name="user" />
            <span>个人中心</span>
          </NuxtLink>
        </div>
        
        <div class="nav-actions">
          <button class="modern-btn modern-btn-secondary" @click="toggleTheme">
            <Icon :name="isDark ? 'sun' : 'moon'" />
          </button>
          
          <div class="user-menu" v-if="user">
            <button class="user-avatar" @click="toggleUserMenu">
              <img :src="user.avatar || '/default-avatar.png'" :alt="user.name" />
              <div class="online-indicator"></div>
            </button>
            
            <div class="user-dropdown" v-show="userMenuOpen" @click="closeUserMenu">
              <div class="dropdown-header">
                <img :src="user.avatar || '/default-avatar.png'" :alt="user.name" />
                <div class="user-info">
                  <div class="user-name">{{ user.name }}</div>
                  <div class="user-email">{{ user.email }}</div>
                </div>
              </div>
              
              <div class="dropdown-divider"></div>
              
              <NuxtLink to="/profile" class="dropdown-item">
                <Icon name="user" />
                <span>个人资料</span>
              </NuxtLink>
              <NuxtLink to="/settings" class="dropdown-item">
                <Icon name="settings" />
                <span>设置</span>
              </NuxtLink>
              
              <div class="dropdown-divider"></div>
              
              <button class="dropdown-item logout-btn" @click="logout">
                <Icon name="logout" />
                <span>退出登录</span>
              </button>
            </div>
          </div>
          
          <div class="auth-buttons" v-else>
            <NuxtLink to="/login" class="modern-btn modern-btn-secondary">
              登录
            </NuxtLink>
            <NuxtLink to="/register" class="modern-btn modern-btn-primary">
              注册
            </NuxtLink>
          </div>
          
          <button class="mobile-menu-btn" @click="toggleMenu">
            <Icon :name="menuOpen ? 'close' : 'menu'" />
          </button>
        </div>
      </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="main-content">
      <div class="content-container">
        <slot />
      </div>
    </main>
    
    <!-- 页脚 -->
    <footer class="modern-footer">
      <div class="footer-container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>晨鑫流量变现系统</h3>
            <p>智能社群营销与多级分销平台</p>
            <div class="social-links">
              <a href="#" class="social-link">
                <Icon name="twitter" />
              </a>
              <a href="#" class="social-link">
                <Icon name="github" />
              </a>
              <a href="#" class="social-link">
                <Icon name="discord" />
              </a>
            </div>
          </div>
          
          <div class="footer-section">
            <h4>产品</h4>
            <ul>
              <li><a href="/features">功能特色</a></li>
              <li><a href="/pricing">价格方案</a></li>
              <li><a href="/api">API文档</a></li>
            </ul>
          </div>
          
          <div class="footer-section">
            <h4>支持</h4>
            <ul>
              <li><a href="/help">帮助中心</a></li>
              <li><a href="/contact">联系我们</a></li>
              <li><a href="/status">系统状态</a></li>
            </ul>
          </div>
          
          <div class="footer-section">
            <h4>法律</h4>
            <ul>
              <li><a href="/privacy">隐私政策</a></li>
              <li><a href="/terms">服务条款</a></li>
              <li><a href="/cookies">Cookie政策</a></li>
            </ul>
          </div>
        </div>
        
        <div class="footer-bottom">
          <p>&copy; 2025 晨鑫流量变现系统. 保留所有权利。</p>
          <p>由 <span class="text-gradient">晨鑫科技</span> 强力驱动</p>
        </div>
      </div>
    </footer>
    
    <!-- 返回顶部按钮 -->
    <Transition name="fade">
      <button 
        v-show="showBackToTop" 
        class="back-to-top" 
        @click="scrollToTop"
      >
        <Icon name="arrow-up" />
      </button>
    </Transition>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// 响应式状态
const menuOpen = ref(false);
const userMenuOpen = ref(false);
const showBackToTop = ref(false);
const isDark = ref(false);

// 用户信息（示例）
const user = ref(null);

// 方法
const toggleMenu = () => {
  menuOpen.value = !menuOpen.value;
};

const closeMenu = () => {
  menuOpen.value = false;
};

const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value;
};

const closeUserMenu = () => {
  userMenuOpen.value = false;
};

const toggleTheme = () => {
  isDark.value = !isDark.value;
  document.documentElement.classList.toggle('dark', isDark.value);
  if (process.client) {
    localStorage.setItem('theme', isDark.value ? 'dark' : 'light');
  }
};

const logout = () => {
  // 实现登出逻辑
  user.value = null;
  closeUserMenu();
};

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};

const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300;
};

// 生命周期
onMounted(() => {
  // 检查主题设置
  if (process.client) {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      isDark.value = true;
      document.documentElement.classList.add('dark');
    }
  }
  
  // 监听滚动事件
  window.addEventListener('scroll', handleScroll);
  
  // 点击外部关闭菜单
  document.addEventListener('click', (e) => {
    if (!e.target.closest('.user-menu')) {
      userMenuOpen.value = false;
    }
    if (!e.target.closest('.nav-menu') && !e.target.closest('.mobile-menu-btn')) {
      menuOpen.value = false;
    }
  });
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>

<style scoped>
.modern-layout {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: float 20s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  animation-delay: 5s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  left: -75px;
  animation-delay: 10s;
}

.circle-4 {
  width: 100px;
  height: 100px;
  top: 20%;
  right: 10%;
  animation-delay: 15s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

/* 导航栏 */
.modern-nav {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: white;
  font-weight: 700;
  font-size: 1.25rem;
  transition: all var(--transition-normal);
}

.brand-link:hover {
  transform: scale(1.05);
}

.brand-icon {
  width: 32px;
  height: 32px;
  background: var(--bg-gradient-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
}

.brand-icon svg {
  width: 18px;
  height: 18px;
  color: white;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  transition: all var(--transition-normal);
  position: relative;
}

.nav-item::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--bg-gradient-primary);
  transition: all var(--transition-normal);
  transform: translateX(-50%);
}

.nav-item:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.nav-item:hover::before {
  width: 80%;
}

.nav-item.router-link-active {
  color: white;
  background: rgba(255, 255, 255, 0.15);
}

.nav-item.router-link-active::before {
  width: 80%;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-menu {
  position: relative;
}

.user-avatar {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  overflow: hidden;
}

.user-avatar:hover {
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.1);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 10px;
  height: 10px;
  background: var(--success-color);
  border: 2px solid white;
  border-radius: 50%;
}

.user-dropdown {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: 1rem;
  z-index: 1001;
}

.dropdown-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.dropdown-header img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.875rem;
  color: var(--gray-600);
}

.dropdown-divider {
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
  margin: 0.75rem 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  border: none;
  background: none;
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--gray-700);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.dropdown-item:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--gray-900);
}

.logout-btn {
  color: var(--error-color);
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.auth-buttons {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.mobile-menu-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 主要内容 */
.main-content {
  flex: 1;
  padding: 2rem 0;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* 页脚 */
.modern-footer {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 1.5rem 1.5rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.footer-section h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.footer-section p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section ul li a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color var(--transition-normal);
}

.footer-section ul li a:hover {
  color: white;
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all var(--transition-normal);
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.875rem;
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  background: var(--bg-gradient-primary);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  z-index: 1000;
}

.back-to-top:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }
  
  .nav-menu {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    flex-direction: column;
    padding: 1rem;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }
  
  .nav-menu.menu-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .nav-item {
    width: 100%;
    justify-content: center;
    color: var(--gray-700);
  }
  
  .nav-item:hover {
    color: var(--gray-900);
    background: rgba(0, 0, 0, 0.05);
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .auth-buttons {
    display: none;
  }
  
  .content-container {
    padding: 0 1rem;
  }
  
  .footer-container {
    padding: 2rem 1rem 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .user-dropdown {
    right: -100px;
    width: 250px;
  }
}

@media (max-width: 480px) {
  .brand-text {
    display: none;
  }
  
  .back-to-top {
    bottom: 1rem;
    right: 1rem;
    width: 45px;
    height: 45px;
  }
}
</style>