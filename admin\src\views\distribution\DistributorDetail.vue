<template>
  <div class="modern-distributor-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="goBack" class="back-btn">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <div class="header-icon">
            <el-icon size="24"><UserFilled /></el-icon>
          </div>
          <div class="header-text">
            <h1>分销员详情</h1>
            <p>查看和管理分销员的详细信息、业绩统计和团队数据</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleEdit" class="action-btn secondary">
            <el-icon><Edit /></el-icon>
            编辑信息
          </el-button>
          <el-button @click="exportData" class="action-btn secondary">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button @click="refreshStats" class="action-btn primary">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 基本信息卡片 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
            <el-button type="primary" size="small" @click="handleEdit">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </div>
        </template>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-avatar">
              <el-avatar :size="80" :src="distributor.avatar">
                {{ distributor.name.charAt(0) }}
              </el-avatar>
            </div>
            <div class="info-details">
              <h3>{{ distributor.name }}</h3>
              <p class="info-email">{{ distributor.email }}</p>
              <p class="info-phone">{{ distributor.phone }}</p>
              <el-tag :type="getLevelTagType(distributor.level)" size="medium">
                {{ getLevelName(distributor.level) }}
              </el-tag>
            </div>
          </div>
          <div class="info-grid-right">
            <div class="info-row">
              <span class="info-label">用户ID:</span>
              <span class="info-value">{{ distributor.id }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">邀请码:</span>
              <span class="info-value">{{ distributor.invite_code }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">上级:</span>
              <span class="info-value">{{ distributor.parent ? distributor.parent.name : '无' }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">分销组:</span>
              <span class="info-value">{{ distributor.distribution_group ? distributor.distribution_group.name : '未分配' }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">状态:</span>
              <el-tag :type="distributor.status === 1 ? 'success' : 'danger'">
                {{ distributor.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </div>
            <div class="info-row">
              <span class="info-label">注册时间:</span>
              <span class="info-value">{{ distributor.created_at }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 统计数据卡片 -->
      <div class="stats-section">
        <div class="stats-container" v-loading="statsLoading">
          <div class="stat-card" v-for="stat in distributorStatCards" :key="stat.key">
            <div class="stat-icon" :style="{ background: stat.color }">
              <el-icon size="20">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
            <div class="stat-trend" :class="stat.trend">
              <el-icon size="14">
                <component :is="stat.trendIcon" />
              </el-icon>
              <span>{{ stat.change }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 下级分销员 -->
      <el-card class="table-card" v-if="distributor.children && distributor.children.length > 0">
        <template #header>
          <div class="card-header">
            <h3>下级分销员</h3>
            <el-button type="primary" size="small" @click="viewAllChildren">
              查看全部
            </el-button>
          </div>
        </template>
        <div class="children-list">
          <div class="child-item" v-for="child in distributor.children" :key="child.id">
            <el-avatar :size="40" :src="child.avatar">{{ child.name.charAt(0) }}</el-avatar>
            <div class="child-info">
              <div class="child-name">{{ child.name }}</div>
              <div class="child-email">{{ child.email }}</div>
            </div>
            <div class="child-actions">
              <el-button size="mini" type="text" @click="viewChild(child)">查看</el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 最近佣金记录 -->
      <el-card class="table-card">
        <template #header>
          <div class="card-header">
            <h3>最近佣金记录</h3>
            <el-button type="primary" size="small" @click="viewAllCommissions">
              查看全部
            </el-button>
          </div>
        </template>
        <el-table :data="recentCommissions" border fit highlight-current-row>
          <el-table-column label="时间" width="160">
            <template #default="{row}">
              {{ row.created_at }}
            </template>
          </el-table-column>
          <el-table-column label="订单号" width="140">
            <template #default="{row}">
              {{ row.order ? row.order.order_no : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="订单金额" width="120">
            <template #default="{row}">
              ¥{{ row.order ? row.order.amount : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="佣金金额" width="120">
            <template #default="{row}">
              <span class="commission-amount">¥{{ row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="佣金类型" width="120">
            <template #default="{row}">
              <el-tag size="small" :type="getCommissionTypeTag(row.type)">
                {{ getCommissionTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{row}">
              <el-tag size="small" :type="row.status === 'paid' ? 'success' : 'warning'">
                {{ row.status === 'paid' ? '已发放' : '待发放' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="150">
            <template #default="{row}">
              {{ row.remark || '-' }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog title="编辑分销员信息" v-model="editDialogVisible" width="600px">
      <el-form :model="editForm" :rules="editRules" ref="editForm" label-width="100px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="分销组">
          <el-select v-model="editForm.distribution_group_id" placeholder="请选择分销组">
            <el-option v-for="group in groupOptions" :key="group.id" :label="group.name" :value="group.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveEdit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import request from '@/utils/request'
import { ElNotification } from 'element-plus'
import { exportDistributors } from '@/api/export'
import { 
  ArrowLeft, Edit, Download, Refresh, UserFilled,
  ShoppingBag, CreditCard, Avatar, TrendCharts, ArrowUp
} from '@element-plus/icons-vue'

// 路由相关
const route = useRoute()
const router = useRouter()
const distributorId = route.params.id

// 数据定义
const loading = ref(true)
const statsLoading = ref(true)
const distributor = ref({})
const stats = ref({})
const recentCommissions = ref([])
const groupOptions = ref([])

// 分销商统计卡片数据 - 与其他页面保持一致的设计
const distributorStatCards = ref([
  {
    key: 'orders',
    label: '订单总数',
    value: '0',
    icon: 'ShoppingBag',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15个'
  },
  {
    key: 'commission',
    label: '累计佣金',
    value: '¥0',
    icon: 'CreditCard',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+12.5%'
  },
  {
    key: 'team',
    label: '团队人数',
    value: '0',
    icon: 'Avatar',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+8人'
  },
  {
    key: 'conversion',
    label: '转化率',
    value: '0%',
    icon: 'TrendCharts',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '****%'
  }
])

// 编辑弹窗
const editDialogVisible = ref(false)
const editForm = ref({
  name: '',
  email: '',
  phone: '',
  distribution_group_id: null,
  status: 1
})

const editRules = reactive({
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }]
})

// 数据加载函数
const loadDistributorStats = async () => {
  try {
    statsLoading.value = true
    console.log('加载分销商统计数据...')
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟统计数据
    const mockStats = {
      total_orders: 156,
      total_commission: 28650.50,
      team_members: 23,
      conversion_rate: 12.8
    }
    
    // 更新统计卡片数据
    distributorStatCards.value[0].value = mockStats.total_orders.toString()
    distributorStatCards.value[1].value = '¥' + mockStats.total_commission.toLocaleString()
    distributorStatCards.value[2].value = mockStats.team_members.toString()
    distributorStatCards.value[3].value = mockStats.conversion_rate + '%'
    
    console.log('分销商统计数据加载完成')
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElNotification({
      title: '错误',
      message: '加载统计数据失败',
      type: 'error'
    })
  } finally {
    statsLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchDistributorDetail()
  fetchGroupOptions()
  loadDistributorStats()
})

// 方法定义
async function fetchDistributorDetail() {
  try {
    loading.value = true
    const response = await request({
      url: `/distributors/${distributorId}`,
      method: 'get'
    })
    
    const data = response.data
    distributor.value = data.distributor
    stats.value = data.stats
    recentCommissions.value = data.recent_commissions
    
    loading.value = false
  } catch (error) {
    loading.value = false
    ElNotification({
      title: '错误',
      message: '获取分销员详情失败',
      type: 'error'
    })
  }
}

async function fetchGroupOptions() {
  try {
    const response = await request({
      url: '/distribution-groups',
      method: 'get',
      params: { all: true }
    })
    groupOptions.value = response.data.data
  } catch (error) {
    console.error('获取分销组选项失败:', error)
  }
}

function goBack() {
  router.go(-1)
}

function handleEdit() {
  editForm.value = {
    name: distributor.value.name,
    email: distributor.value.email,
    phone: distributor.value.phone,
    distribution_group_id: distributor.value.distribution_group_id,
    status: distributor.value.status
  }
  editDialogVisible.value = true
}

function saveEdit() {
  this.$refs.editForm.validate(async (valid) => {
    if (valid) {
      try {
        await request({
          url: `/distributors/${distributorId}`,
          method: 'put',
          data: editForm.value
        })
        
        editDialogVisible.value = false
        await fetchDistributorDetail()
        
        ElNotification({
          title: '成功',
          message: '更新成功',
          type: 'success'
        })
      } catch (error) {
        ElNotification({
          title: '错误',
          message: '更新失败',
          type: 'error'
        })
      }
    }
  })
}

function refreshStats() {
  fetchDistributorDetail()
  loadDistributorStats()
}

  async function exportData() {
    // 导出数据逻辑
    try {
    ElNotification({
      title: '提示',
      message: '正在导出分销商详情...',
      type: 'info'
    })
    
    // 构建导出参数
    const exportParams = {
      distributor_id: distributorId,
      format: 'excel',
      fields: [
        'id', 'username', 'level', 'total_commission', 'balance',
        'direct_members', 'team_members', 'created_at'
      ]
    }
    
    // 调用导出API
    const response = await exportDistributors(exportParams)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.download = `分销商详情_${new Date().toLocaleDateString()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElNotification({
      title: '成功',
      message: '导出成功',
      type: 'success'
    })
  } catch (error) {
    ElNotification({
      title: '错误',
      message: '导出失败：' + error.message,
      type: 'error'
    })
  }
}

function viewAllChildren() {
  // 查看所有下级
  router.push(`/distribution/children/${distributorId}`)
}

function viewChild(child) {
  router.push(`/distribution/detail/${child.id}`)
}

function viewAllCommissions() {
  // 查看所有佣金记录
  router.push(`/finance/commission?user_id=${distributorId}`)
}

// 工具函数
function getLevelName(level) {
  const levels = {
    1: '初级分销员',
    2: '中级分销员',
    3: '高级分销员',
    4: '金牌分销员'
  }
  return levels[level] || '初级分销员'
}

function getLevelTagType(level) {
  const types = {
    1: 'info',
    2: 'success',
    3: 'warning',
    4: 'danger'
  }
  return types[level] || 'info'
}

function getCommissionTypeName(type) {
  const types = {
    'direct': '直接佣金',
    'indirect': '间接佣金',
    'bonus': '奖金佣金'
  }
  return types[type] || '佣金'
}

function getCommissionTypeTag(type) {
  const tags = {
    'direct': 'success',
    'indirect': 'warning',
    'bonus': 'danger'
  }
  return tags[type] || 'info'
}
</script>

<style lang="scss" scoped>
.modern-distributor-detail {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  // 页面头部样式
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .back-btn {
          margin-right: 8px;
          background: #f5f7fa;
          border-color: #dcdfe6;
          color: #606266;
          border-radius: 8px;
          
          &:hover {
            background: #ecf5ff;
            border-color: #409eff;
            color: #409eff;
          }
        }
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          height: 40px;
          padding: 0 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }

  .detail-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
  }

  // 统计卡片区域
  .stats-section {
    margin-bottom: 24px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 信息卡片样式
  .info-card,
  .table-card {
    background: white;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
    overflow: hidden;
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }
    }
    
    :deep(.el-card__body) {
      padding: 24px;
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 30px;
    
    .info-item {
      display: flex;
      align-items: center;
      gap: 20px;
      
      .info-avatar {
        border: 3px solid #e4e7ed;
        transition: all 0.3s ease;
        
        &:hover {
          transform: scale(1.05);
          border-color: #667eea;
        }
      }
    }
  }

  .info-details {
    h3 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 20px;
      font-weight: 600;
    }
    
    .info-email, .info-phone {
      margin: 5px 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .info-grid-right {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .info-row {
    display: flex;
    align-items: center;
    padding: 8px 0;
    
    .info-label {
      font-weight: 500;
      color: #606266;
      margin-right: 10px;
      min-width: 80px;
    }
    
    .info-value {
      color: #303133;
      flex: 1;
      font-weight: 500;
    }
  }

  // 表格样式
  :deep(.el-table) {
    .el-table__header th {
      background: #fafbfc !important;
      border-bottom: 1px solid #e4e7ed;
      font-weight: 600;
      color: #606266;
      font-size: 13px;
      padding: 16px 12px;
    }
    
    .el-table__body tr {
      transition: all 0.3s ease;
      
      &:hover {
        background: #f8f9ff !important;
      }
      
      td {
        border-bottom: 1px solid #f0f2f5;
        padding: 16px 12px;
      }
    }
  }

  .children-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
  }

  .child-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-radius: 12px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
      border-color: #667eea;
    }
  }

  .child-info {
    flex: 1;
    margin-left: 12px;
    
    .child-name {
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
      font-size: 14px;
    }
    
    .child-email {
      font-size: 12px;
      color: #909399;
    }
  }

  .commission-amount {
    font-weight: 700;
    color: #f56c6c;
  }
}

// 对话框样式
:deep(.el-dialog) {
  .dialog-footer {
    text-align: right;
    margin-top: 20px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .modern-distributor-detail {
    .stats-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .modern-distributor-detail {
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .detail-content {
      padding: 0 16px;
    }
    
    .stats-container {
      grid-template-columns: 1fr;
    }
    
    .info-grid {
      grid-template-columns: 1fr;
    }
    
    .info-grid-right {
      grid-template-columns: 1fr;
    }
    
    .children-list {
      grid-template-columns: 1fr;
    }
  }
}
</style> 