<template>
  <div class="modern-agent-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><Avatar /></el-icon>
          </div>
          <div class="header-text">
            <h1>代理商管理</h1>
            <p>全面管理平台代理商，包括代理商信息、状态管理、绩效分析和等级管理</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleExport" class="action-btn secondary">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button @click="handleBatchOperation" class="action-btn secondary">
            <el-icon><Operation /></el-icon>
            批量操作
          </el-button>
          <el-button type="primary" @click="handleAdd" class="action-btn primary">
            <el-icon><Plus /></el-icon>
            新增代理商
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-container" v-loading="statsLoading">
        <div class="stat-card" v-for="stat in agentStatCards" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选与搜索 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="searchForm" @submit.prevent="handleQuery">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索代理商名称或编码"
            clearable
            @keyup.enter="handleQuery"
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="代理商等级">
          <el-select v-model="searchForm.agent_level" placeholder="全部等级" clearable class="filter-select">
            <el-option label="平台代理商" value="platform" />
            <el-option label="分站代理商" value="substation" />
          </el-select>
        </el-form-item>
        <el-form-item label="代理商类型">
          <el-select v-model="searchForm.agent_type" placeholder="全部类型" clearable class="filter-select">
            <el-option label="个人代理" value="individual" />
            <el-option label="企业代理" value="enterprise" />
            <el-option label="渠道代理" value="channel" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable class="filter-select">
            <el-option label="正常" value="active" />
            <el-option label="未激活" value="inactive" />
            <el-option label="暂停" value="suspended" />
            <el-option label="已过期" value="expired" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery" class="search-btn">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetQuery" class="reset-btn">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 代理商列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h3>代理商列表</h3>
            <el-tag size="small" type="info">共 {{ agents.total || 0 }} 条记录</el-tag>
          </div>
          <div class="header-right">
            <el-button-group>
              <el-button size="small" :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                <el-icon><List /></el-icon>
              </el-button>
              <el-button size="small" :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
                <el-icon><Grid /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>
      
      <el-table 
        v-if="viewMode === 'table'"
        :data="agents.data" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
        class="modern-table"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="代理商信息" width="220">
          <template #default="{ row }">
            <div class="agent-info">
              <el-avatar :size="40" class="agent-avatar">
                <el-icon><Avatar /></el-icon>
              </el-avatar>
              <div class="agent-details">
                <div class="agent-name">{{ row.agent_name }}</div>
                <div class="agent-code">编码: {{ row.agent_code }}</div>
                <div class="agent-user" v-if="row.user">用户: {{ row.user.username }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="agent_level_label" label="代理商等级">
          <template #default="{ row }">
            <el-tag :type="getAgentLevelColor(row.agent_level)">
              {{ row.agent_level === 'platform' ? '平台代理商' : '分站代理商' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="agent_type_label" label="代理商类型">
          <template #default="{ row }">
            <el-tag :type="getAgentTypeColor(row.agent_type)">
              {{ getAgentTypeText(row.agent_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="commission_display" label="佣金设置">
          <template #default="{ row }">
            <span v-if="row.no_commission" class="no-commission">不抽佣</span>
            <span v-else class="commission-rate">{{ row.commission_rate }}%</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="total_commission" label="总佣金">
          <template #default="{ row }">
            <span class="amount">¥{{ row.total_commission || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status_label" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="validity_display" label="有效期">
          <template #default="{ row }">
            <span v-if="row.is_permanent" class="permanent">永久有效</span>
            <span v-else-if="row.end_date" :class="getValidityClass(row.end_date)">
              {{ formatDate(row.end_date) }}
            </span>
            <span v-else class="no-expiry">未设置</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="viewAgent(row)">
              查看
            </el-button>
            <el-button link type="info" size="small" @click="editAgent(row)">
              编辑
            </el-button>
            <el-button link type="warning" size="small" @click="toggleStatus(row)">
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, row)" trigger="click">
              <el-button link type="primary" size="small">
                更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'edit', row}">编辑</el-dropdown-item>
                  <el-dropdown-item :command="{action: 'renew', row}" v-if="!row.is_permanent">续费</el-dropdown-item>
                  <el-dropdown-item :command="{action: 'status', row}">状态管理</el-dropdown-item>
                  <el-dropdown-item :command="{action: 'delete', row}" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="agents.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建代理商对话框 -->
    <el-dialog v-model="createDialogVisible" title="新增代理商" width="600px">
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="120px">
        <el-form-item label="关联用户" prop="user_id">
          <el-select v-model="createForm.user_id" placeholder="选择用户" filterable>
            <el-option
              v-for="user in availableUsers"
              :key="user.id"
              :label="`${user.name} (${user.username})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="代理商名称" prop="agent_name">
          <el-input v-model="createForm.agent_name" placeholder="请输入代理商名称" />
        </el-form-item>
        <el-form-item label="代理商等级" prop="agent_level">
          <el-radio-group v-model="createForm.agent_level">
            <el-radio label="platform">平台代理商</el-radio>
            <el-radio label="substation">分站代理商</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="代理商类型" prop="agent_type">
          <el-select v-model="createForm.agent_type" placeholder="选择类型">
            <el-option label="个人代理" value="individual" />
            <el-option label="企业代理" value="enterprise" />
            <el-option label="渠道代理" value="channel" />
          </el-select>
        </el-form-item>
        <el-form-item label="佣金比例" prop="commission_rate">
          <el-input-number
            v-model="createForm.commission_rate"
            :min="0"
            :max="100"
            :precision="2"
            controls-position="right"
          />
          <span style="margin-left: 10px;">%</span>
        </el-form-item>
        <el-form-item label="有效期" prop="validity_period">
          <el-select v-model="createForm.validity_period" placeholder="选择有效期">
            <el-option label="1周" value="week" />
            <el-option label="1个月" value="month" />
            <el-option label="3个月" value="quarter" />
            <el-option label="6个月" value="half_year" />
            <el-option label="1年" value="year" />
            <el-option label="永久" value="permanent" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="createForm.validity_period === 'custom'" label="自定义结束日期">
          <el-date-picker
            v-model="createForm.custom_end_date"
            type="date"
            placeholder="选择结束日期"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="createForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCreate" :loading="createLoading">
          确认创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, Refresh, Plus, ArrowDown, Avatar, Check, Star, OfficeBuilding,
  RefreshLeft, Operation, Download, List, Grid, UserFilled, ArrowUp
} from '@element-plus/icons-vue'
import StatCard from '@/components/dashboard/StatCard.vue'
import { agentApi } from '@/api/agent'

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const statsLoading = ref(true)
const currentPage = ref(1)
const pageSize = ref(20)
const createDialogVisible = ref(false)
const viewMode = ref('table') // 视图模式：table 或 card
const selectedAgents = ref([])

const agentStats = ref({})
const agents = ref({ data: [], total: 0 })
const availableUsers = ref([])

// 代理商统计卡片数据 - 与其他页面保持一致的设计
const agentStatCards = ref([
  {
    key: 'total',
    label: '总代理商',
    value: '156',
    icon: 'Avatar',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+12%'
  },
  {
    key: 'platform',
    label: '平台代理商',
    value: '89',
    icon: 'Star',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+8%'
  },
  {
    key: 'substation',
    label: '分站代理商',
    value: '67',
    icon: 'OfficeBuilding',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15%'
  },
  {
    key: 'commission',
    label: '总佣金',
    value: '¥128,567',
    icon: 'Check',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+23.5%'
  }
])

const searchForm = reactive({
  keyword: '',
  agent_level: '',
  agent_type: '',
  status: ''
})

const createForm = reactive({
  user_id: '',
  agent_name: '',
  agent_level: 'platform',
  agent_type: 'individual',
  commission_rate: 10,
  validity_period: 'year',
  custom_end_date: '',
  remark: ''
})

const createRules = {
  user_id: [{ required: true, message: '请选择关联用户', trigger: 'change' }],
  agent_name: [{ required: true, message: '请输入代理商名称', trigger: 'blur' }],
  agent_level: [{ required: true, message: '请选择代理商等级', trigger: 'change' }],
  agent_type: [{ required: true, message: '请选择代理商类型', trigger: 'change' }],
  commission_rate: [{ required: true, message: '请输入佣金比例', trigger: 'blur' }],
  validity_period: [{ required: true, message: '请选择有效期', trigger: 'change' }]
}

const createFormRef = ref()

// 方法
const loadAgentStats = async () => {
  try {
    const response = await agentApi.getStats()
    agentStats.value = response.data
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  }
}

const loadAgents = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm
    }
    const response = await agentApi.getList(params)
    agents.value = response.data
  } catch (error) {
    ElMessage.error('加载代理商列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleQuery = () => {
  currentPage.value = 1
  loadAgents()
}

// 重置查询
const resetQuery = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleQuery()
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedAgents.value = selection
}

// 导出数据
const handleExport = async () => {
  try {
    ElMessage.success('导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 批量操作
const handleBatchOperation = async () => {
  if (selectedAgents.value.length === 0) {
    ElMessage.warning('请选择要操作的代理商')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要操作选中的 ${selectedAgents.value.length} 个代理商吗？`, '批量操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.success('批量操作成功')
    loadAgents()
    loadAgentStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  }
}

// 新增代理商
const handleAdd = () => {
  createDialogVisible.value = true
  loadAvailableUsers()
}

// 编辑代理商
const editAgent = (agent) => {
  ElMessage.info(`编辑代理商 ${agent.agent_name}`)
}

// 切换状态
const toggleStatus = async (agent) => {
  try {
    await ElMessageBox.confirm(`确定要${agent.status === 'active' ? '禁用' : '启用'}代理商 ${agent.agent_name} 吗？`, '状态切换', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    agent.status = agent.status === 'active' ? 'inactive' : 'active'
    ElMessage.success(`${agent.status === 'active' ? '启用' : '禁用'}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadAgents()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadAgents()
}

const showCreateDialog = () => {
  createDialogVisible.value = true
  // 这里应该加载可用用户列表
  loadAvailableUsers()
}

const loadAvailableUsers = async () => {
  // 模拟加载用户数据
  availableUsers.value = [
    { id: 1, name: '张三', username: 'zhangsan' },
    { id: 2, name: '李四', username: 'lisi' }
  ]
}

const confirmCreate = async () => {
  try {
    await createFormRef.value.validate()
    createLoading.value = true
    
    await agentApi.create(createForm)
    ElMessage.success('代理商创建成功')
    createDialogVisible.value = false
    loadAgents()
    loadAgentStats()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('创建代理商失败')
    }
  } finally {
    createLoading.value = false
  }
}

const viewAgent = (agent) => {
  ElMessage.info(`查看代理商 ${agent.agent_name} 详情`)
}

const handleCommand = ({ action, row }) => {
  switch (action) {
    case 'edit':
      ElMessage.info(`编辑代理商 ${row.agent_name}`)
      break
    case 'renew':
      ElMessage.info(`续费代理商 ${row.agent_name}`)
      break
    case 'status':
      ElMessage.info(`管理代理商 ${row.agent_name} 状态`)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}

const handleDelete = async (agent) => {
  try {
    await ElMessageBox.confirm(`确定要删除代理商 ${agent.agent_name} 吗？`, '确认删除', {
      type: 'warning'
    })
    
    await agentApi.delete(agent.id)
    ElMessage.success('删除成功')
    loadAgents()
    loadAgentStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

const getStatusColor = (status) => {
  const colors = {
    'active': 'success',
    'inactive': 'info',
    'suspended': 'warning',
    'expired': 'danger'
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'active': '正常',
    'inactive': '未激活',
    'suspended': '暂停',
    'expired': '已过期'
  }
  return texts[status] || '未知'
}

const getAgentLevelColor = (level) => {
  return level === 'platform' ? 'primary' : 'success'
}

const getAgentTypeColor = (type) => {
  const colors = {
    'individual': 'primary',
    'enterprise': 'success',
    'channel': 'warning'
  }
  return colors[type] || 'info'
}

const getAgentTypeText = (type) => {
  const texts = {
    'individual': '个人代理',
    'enterprise': '企业代理',
    'channel': '渠道代理'
  }
  return texts[type] || '未知'
}

const getValidityClass = (endDate) => {
  const now = new Date()
  const end = new Date(endDate)
  const diffDays = Math.ceil((end - now) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'expired'
  if (diffDays <= 7) return 'expiring-soon'
  return 'valid'
}

// 模拟加载代理商数据
const loadMockAgents = async () => {
  try {
    loading.value = true
    console.log('加载代理商列表...')
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    agents.value = {
      data: [
        {
          id: 1,
          agent_name: '张三代理',
          agent_code: 'AG001',
          agent_level: 'platform',
          agent_type: 'individual',
          commission_rate: 10.5,
          total_commission: 15678.90,
          status: 'active',
          is_permanent: true,
          end_date: null,
          user: {
            id: 1,
            username: 'zhangsan',
            name: '张三'
          }
        },
        {
          id: 2,
          agent_name: '李四企业代理',
          agent_code: 'AG002',
          agent_level: 'substation',
          agent_type: 'enterprise',
          commission_rate: 8.0,
          total_commission: 8945.30,
          status: 'active',
          is_permanent: false,
          end_date: '2024-12-31',
          user: {
            id: 2,
            username: 'lisi',
            name: '李四'
          }
        },
        {
          id: 3,
          agent_name: '王五渠道代理',
          agent_code: 'AG003',
          agent_level: 'platform',
          agent_type: 'channel',
          commission_rate: 12.0,
          total_commission: 23456.78,
          status: 'inactive',
          is_permanent: false,
          end_date: '2024-06-30',
          user: {
            id: 3,
            username: 'wangwu',
            name: '王五'
          }
        }
      ],
      total: 3
    }
    
    console.log('代理商列表加载完成')
  } catch (error) {
    console.error('加载代理商列表失败:', error)
    ElMessage.error('加载代理商列表失败')
  } finally {
    loading.value = false
  }
}

// 模拟加载统计数据
const loadMockStats = async () => {
  try {
    statsLoading.value = true
    console.log('加载统计数据...')
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 更新统计卡片数据
    agentStatCards.value[0].value = '156'
    agentStatCards.value[1].value = '89'
    agentStatCards.value[2].value = '67'
    agentStatCards.value[3].value = '¥128,567'
    
    console.log('统计数据加载完成')
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  // 使用模拟数据替代真实API调用
  loadMockStats()
  loadMockAgents()
})
</script>

<style lang="scss" scoped>
.modern-agent-list {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  // 页面头部样式
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          height: 40px;
          padding: 0 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }

  // 筛选卡片样式
  .filter-card {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .search-input {
      width: 250px;
    }
    
    .filter-select {
      width: 150px;
    }
    
    .search-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      }
    }
    
    .reset-btn {
      background: #f5f7fa;
      border-color: #dcdfe6;
      color: #606266;
      border-radius: 8px;
      
      &:hover {
        background: #ecf5ff;
        border-color: #409eff;
        color: #409eff;
      }
    }
  }

  // 统计卡片区域
  .stats-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 表格卡片样式
  .table-card {
    max-width: 1400px;
    margin: 0 auto 40px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
          
          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
          
          .el-tag {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: none;
          }
        }
      }
    }
    
    :deep(.el-card__body) {
      padding: 0;
    }
    
    .modern-table {
      :deep(.el-table__header) {
        background: #fafbfc;
        
        th {
          background: #fafbfc !important;
          border-bottom: 1px solid #e4e7ed;
          font-weight: 600;
          color: #606266;
          font-size: 13px;
          padding: 16px 12px;
        }
      }
      
      :deep(.el-table__body) {
        tr {
          transition: all 0.3s ease;
          
          &:hover {
            background: #f8f9ff !important;
          }
          
          td {
            border-bottom: 1px solid #f0f2f5;
            padding: 16px 12px;
          }
        }
      }
      
      .agent-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .agent-avatar {
          border: 2px solid #f0f2ff;
          transition: all 0.3s ease;
          
          &:hover {
            transform: scale(1.1);
          }
        }
        
        .agent-details {
          .agent-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 2px;
          }
          
          .agent-code {
            font-size: 12px;
            color: #909399;
            margin-bottom: 1px;
          }
          
          .agent-user {
            font-size: 11px;
            color: #c0c4cc;
          }
        }
      }
    }
    
    .pagination {
      margin-top: 20px;
      text-align: center;
    }
  }

  // 代理商特定样式
  .amount {
    color: #67C23A;
    font-weight: bold;
  }

  .no-commission {
    color: #67C23A;
  }

  .commission-rate {
    color: #409EFF;
  }

  .permanent {
    color: #67C23A;
  }

  .valid {
    color: #409EFF;
  }

  .expiring-soon {
    color: #E6A23C;
  }

  .expired {
    color: #F56C6C;
  }

  .no-expiry {
    color: #909399;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .modern-agent-list {
    .stats-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .modern-agent-list {
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .stats-container {
      grid-template-columns: 1fr;
    }
    
    .filter-card {
      .el-form {
        .el-form-item {
          width: 100%;
          margin-bottom: 16px;
          
          .search-input,
          .filter-select {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>