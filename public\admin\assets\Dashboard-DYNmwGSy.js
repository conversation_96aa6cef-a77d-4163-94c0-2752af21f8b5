/* empty css             *//* empty css                 *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                    *//* empty css                *//* empty css               *//* empty css                  */import{l as a,q as t,G as l,z as e,C as s,A as i,r as o,c as n,o as r,m as d,E as c}from"./vue-vendor-BcnDv-68.js";import{q as u,a as m,c as p}from"./anti-block-CJ1NNk3N.js";import{_,u as h}from"./index-eUTsTR3J.js";import{X as v,a1 as g,U as y,V as f,W as b,bJ as k,Y as w,Z as D,s as x,ai as S,aj as j,ak as C,bg as A,a2 as V,a3 as F,a4 as H,a5 as T,a6 as z,bK as I,aZ as P}from"./element-plus-C2UshkXo.js";import"./index-D4AyIzGN.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const R={class:"anti-block-dashboard"},U={class:"page-header"},q={class:"quick-actions"},M={class:"stats-overview"},N={class:"stat-card"},Q={class:"stat-content"},Z={class:"stat-number"},O={class:"stat-detail"},$={class:"text-success"},L={class:"text-warning"},B={class:"text-danger"},E={class:"stat-card"},X={class:"stat-content"},Y={class:"stat-number"},G={class:"stat-detail"},J={class:"text-success"},K={class:"text-info"},W={class:"stat-card"},aa={class:"stat-content"},ta={class:"stat-number"},la={class:"stat-card"},ea={class:"stat-content"},sa={class:"stat-number"},ia={class:"stat-detail"},oa={class:"help-section"},na={slot:"header",class:"card-header"},ra={class:"help-item"},da={class:"help-content"},ca={class:"help-item"},ua={class:"help-content"},ma={class:"help-item"},pa={class:"help-content"},_a={class:"domain-status"},ha={slot:"header",class:"card-header"},va={class:"domain-text"},ga={slot:"footer"},ya={class:"help-content-detail"},fa={class:"monitor-content"};const ba=_({__name:"Dashboard",setup(a,{expose:t}){t();const l=h(),e=o({domain_stats:{total:0,active:0,abnormal:0,blocked:0},link_stats:{total:0,active:0,today_created:0,today_clicks:0}}),s=o([]),i=o(!1),d=o(!1),c=o(!1),_=o(!1),g=o(!1),y=o({domain:"",domain_type:"redirect",priority:80,remarks:""}),f=n(()=>"admin"===l.userInfo?.role),b=n(()=>e.value?.domain_stats?.total&&0!==e.value.domain_stats.total?Math.round(e.value.domain_stats.active/e.value.domain_stats.total*100):0),w=n(()=>{const a=b.value;return a>=90?"优秀":a>=80?"良好":a>=60?"一般":"需要关注"}),D=n(()=>{const a=b.value;return a>=90?"text-success":a>=80?"text-primary":a>=60?"text-warning":"text-danger"});r(()=>{x(),S()});const x=async()=>{try{const t={domain_stats:{total:15,active:12,abnormal:2,blocked:1},link_stats:{total:1248,active:1156,today_created:23,today_clicks:892,total_clicks:45678},system_health:{score:85,status:"good"},domain_health:{excellent:8,good:4,warning:2,poor:1},recent_activity:[{id:1,type:"domain_check",domain:"safe-domain-1.com",status:"healthy",health_score:95,timestamp:new Date(Date.now()-3e5).toISOString()},{id:2,type:"short_link_created",short_code:"A6X8Y9Z0",original_url:"https://example.com/landing/group/1",timestamp:new Date(Date.now()-9e5).toISOString()},{id:3,type:"domain_warning",domain:"backup-domain-1.com",status:"warning",health_score:75,timestamp:new Date(Date.now()-18e5).toISOString()}]};await new Promise(a=>setTimeout(a,500)),e.value=t;try{const{data:a}=await u();e.value=a}catch(a){}}catch(t){v.error("加载统计数据失败")}},S=async()=>{s.value=[{domain:"short1.linkhub.pro",domain_type:"redirect",health_score:95,status:1,use_count:1250,last_check_time:new Date},{domain:"short2.linkhub.pro",domain_type:"redirect",health_score:88,status:1,use_count:856,last_check_time:new Date},{domain:"landing.linkhub.pro",domain_type:"landing",health_score:100,status:1,use_count:432,last_check_time:new Date}]},j={userStore:l,stats:e,recentDomains:s,checking:i,addDomainVisible:d,helpVisible:c,monitorVisible:_,submitting:g,domainForm:y,domainRules:{domain:[{required:!0,message:"请输入域名",trigger:"blur"},{pattern:/^[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/,message:"请输入有效的域名格式",trigger:"blur"}],domain_type:[{required:!0,message:"请选择域名类型",trigger:"change"}]},domainRecommendations:[{type:"短链接域名",count:"3-5个",priority:"70-100",purpose:"生成防红短链接"},{type:"中转页域名",count:"2-3个",priority:"80-90",purpose:"微信/QQ跳转中转"},{type:"API服务域名",count:"1-2个",priority:"90-100",purpose:"API接口访问"}],healthRules:[{condition:"HTTP访问失败",penalty:"-20分",description:"域名无法正常访问"},{condition:"DNS解析失败",penalty:"-20分",description:"域名解析异常"},{condition:"微信检测封禁",penalty:"-50分",description:"被微信平台封禁"},{condition:"响应时间>5秒",penalty:"-10分",description:"访问速度过慢"}],isAdmin:f,systemHealthScore:b,healthStatus:w,healthClass:D,loadStats:x,loadRecentDomains:S,checkAllDomains:async()=>{i.value=!0;try{await p({limit:20}),v.success("域名检测完成"),x(),S()}catch(a){v.error("域名检测失败")}finally{i.value=!1}},checkSingleDomain:async a=>{a.checking=!0;try{v.success(`${a.domain} 检测完成`)}catch(t){v.error("检测失败")}finally{a.checking=!1}},showAddDomainDialog:()=>{d.value=!0,y.value={domain:"",domain_type:"redirect",priority:80,remarks:""}},addDomainAction:async()=>{try{await y.value.validate(),g.value=!0,await m(y.value),v.success("域名添加成功"),d.value=!1,x(),S()}catch(a){if(a.fields)return;v.error("添加域名失败")}finally{g.value=!1}},editDomain:a=>{},refreshDomains:()=>{S()},showHelpDialog:()=>{c.value=!0},showMonitorDialog:()=>{_.value=!0},getDomainTypeName:a=>({redirect:"短链接",landing:"中转页",api:"API服务"}[a]||a),getDomainTypeColor:a=>({redirect:"primary",landing:"success",api:"warning"}[a]||""),getStatusName:a=>({1:"正常",2:"异常",3:"封禁",4:"维护"}[a]||"未知"),getStatusColor:a=>({1:"success",2:"warning",3:"danger",4:"info"}[a]||""),getHealthColor:a=>a>=90?"#67c23a":a>=80?"#409eff":a>=60?"#e6a23c":"#f56c6c",formatTime:a=>a?k(a).format("MM-DD HH:mm"):"-",ref:o,computed:n,onMounted:r,get ElMessage(){return v},get getAntiBlockStats(){return u},get checkDomains(){return p},get addDomain(){return m},get useUserStore(){return h},get dayjs(){return k}};return Object.defineProperty(j,"__isScriptSetup",{enumerable:!1,value:!0}),j}},[["render",function(o,n,r,u,m,p){const _=g,h=w,v=y,k=f,ba=j,ka=C,wa=A,Da=S,xa=H,Sa=F,ja=z,Ca=T,Aa=I,Va=V,Fa=b,Ha=P;return d(),a("div",R,[t("div",U,[n[11]||(n[11]=t("div",{class:"page-title"},[t("h1",null,"🛡️ 防红系统管理"),t("p",{class:"page-desc"},"智能域名管理，自动检测切换，确保推广链接稳定可用")],-1)),t("div",q,[l(_,{type:"primary",onClick:u.checkAllDomains,loading:u.checking},{default:i(()=>n[8]||(n[8]=[t("i",{class:"el-icon-refresh"},null,-1),c(" 立即检测 ",-1)])),_:1,__:[8]},8,["loading"]),u.isAdmin?(d(),e(_,{key:0,type:"success",onClick:u.showAddDomainDialog},{default:i(()=>n[9]||(n[9]=[t("i",{class:"el-icon-plus"},null,-1),c(" 添加域名 ",-1)])),_:1,__:[9]})):s("",!0),l(_,{type:"info",onClick:u.showHelpDialog},{default:i(()=>n[10]||(n[10]=[t("i",{class:"el-icon-question"},null,-1),c(" 使用说明 ",-1)])),_:1,__:[10]})])]),t("div",M,[l(v,{gutter:20},{default:i(()=>[l(h,{span:6},{default:i(()=>[t("div",N,[n[13]||(n[13]=t("div",{class:"stat-icon domain-icon"},[t("i",{class:"el-icon-connection"})],-1)),t("div",Q,[t("div",Z,D(u.stats.domain_stats.total),1),n[12]||(n[12]=t("div",{class:"stat-label"},"总域名数",-1)),t("div",O,[t("span",$,D(u.stats.domain_stats.active)+" 正常",1),t("span",L,D(u.stats.domain_stats.abnormal)+" 异常",1),t("span",B,D(u.stats.domain_stats.blocked)+" 封禁",1)])])])]),_:1}),l(h,{span:6},{default:i(()=>[t("div",E,[n[15]||(n[15]=t("div",{class:"stat-icon link-icon"},[t("i",{class:"el-icon-link"})],-1)),t("div",X,[t("div",Y,D(u.stats.link_stats.total),1),n[14]||(n[14]=t("div",{class:"stat-label"},"短链接数",-1)),t("div",G,[t("span",J,D(u.stats.link_stats.active)+" 激活",1),t("span",K,"今日新增 "+D(u.stats.link_stats.today_created),1)])])])]),_:1}),l(h,{span:6},{default:i(()=>[t("div",W,[n[18]||(n[18]=t("div",{class:"stat-icon click-icon"},[t("i",{class:"el-icon-view"})],-1)),t("div",aa,[t("div",ta,D(u.stats.link_stats.today_clicks),1),n[16]||(n[16]=t("div",{class:"stat-label"},"今日访问",-1)),n[17]||(n[17]=t("div",{class:"stat-detail"},[t("span",{class:"text-primary"},"实时统计")],-1))])])]),_:1}),l(h,{span:6},{default:i(()=>[t("div",la,[n[20]||(n[20]=t("div",{class:"stat-icon health-icon"},[t("i",{class:"el-icon-success"})],-1)),t("div",ea,[t("div",sa,D(u.systemHealthScore)+"%",1),n[19]||(n[19]=t("div",{class:"stat-label"},"系统健康度",-1)),t("div",ia,[t("span",{class:x(u.healthClass)},D(u.healthStatus),3)])])])]),_:1})]),_:1})]),t("div",oa,[l(k,{class:"help-card"},{default:i(()=>[t("div",na,[n[22]||(n[22]=t("span",null,"📖 快速使用指南",-1)),l(_,{type:"text",onClick:u.showHelpDialog},{default:i(()=>n[21]||(n[21]=[c("查看详细说明",-1)])),_:1,__:[21]})]),l(v,{gutter:20},{default:i(()=>[l(h,{span:8},{default:i(()=>[t("div",ra,[n[26]||(n[26]=t("div",{class:"help-step"},"1",-1)),t("div",da,[n[24]||(n[24]=t("h4",null,"配置域名池",-1)),n[25]||(n[25]=t("p",null,"添加多个备用域名，系统会自动选择最佳域名生成短链接",-1)),l(_,{type:"text",onClick:n[0]||(n[0]=a=>o.$router.push("/admin/anti-block/domains"))},{default:i(()=>n[23]||(n[23]=[c(" 管理域名池 → ",-1)])),_:1,__:[23]})])])]),_:1}),l(h,{span:8},{default:i(()=>[t("div",ca,[n[30]||(n[30]=t("div",{class:"help-step"},"2",-1)),t("div",ua,[n[28]||(n[28]=t("h4",null,"自动生成短链接",-1)),n[29]||(n[29]=t("p",null,"分销员推广链接将自动使用防红短链接，无需手动操作",-1)),l(_,{type:"text",onClick:n[1]||(n[1]=a=>o.$router.push("/admin/anti-block/links"))},{default:i(()=>n[27]||(n[27]=[c(" 查看短链接 → ",-1)])),_:1,__:[27]})])])]),_:1}),l(h,{span:8},{default:i(()=>[t("div",ma,[n[34]||(n[34]=t("div",{class:"help-step"},"3",-1)),t("div",pa,[n[32]||(n[32]=t("h4",null,"监控和维护",-1)),n[33]||(n[33]=t("p",null,"系统每5分钟自动检测，异常域名自动切换，无需人工干预",-1)),l(_,{type:"text",onClick:u.showMonitorDialog},{default:i(()=>n[31]||(n[31]=[c(" 查看监控 → ",-1)])),_:1,__:[31]})])])]),_:1})]),_:1})]),_:1})]),t("div",_a,[l(k,null,{default:i(()=>[t("div",ha,[n[37]||(n[37]=t("span",null,"🌐 域名状态监控",-1)),t("div",null,[l(_,{type:"text",onClick:u.refreshDomains},{default:i(()=>n[35]||(n[35]=[c("刷新",-1)])),_:1,__:[35]}),l(_,{type:"text",onClick:n[2]||(n[2]=a=>o.$router.push("/admin/anti-block/domains"))},{default:i(()=>n[36]||(n[36]=[c(" 查看全部 → ",-1)])),_:1,__:[36]})])]),l(Da,{data:u.recentDomains,style:{width:"100%"}},{default:i(()=>[l(ba,{prop:"domain",label:"域名",width:"200"},{default:i(a=>[t("span",va,D(a.row.domain),1)]),_:1}),l(ba,{prop:"domain_type",label:"类型",width:"100"},{default:i(a=>[l(ka,{size:"small",type:u.getDomainTypeColor(a.row.domain_type)},{default:i(()=>[c(D(u.getDomainTypeName(a.row.domain_type)),1)]),_:2},1032,["type"])]),_:1}),l(ba,{prop:"health_score",label:"健康度",width:"120"},{default:i(a=>[l(wa,{percentage:a.row.health_score,color:u.getHealthColor(a.row.health_score),"stroke-width":8},null,8,["percentage","color"])]),_:1}),l(ba,{prop:"status",label:"状态",width:"100"},{default:i(a=>[l(ka,{type:u.getStatusColor(a.row.status),size:"small"},{default:i(()=>[c(D(u.getStatusName(a.row.status)),1)]),_:2},1032,["type"])]),_:1}),l(ba,{prop:"use_count",label:"使用次数",width:"100"}),l(ba,{prop:"last_check_time",label:"最后检测",width:"160"},{default:i(a=>[t("span",null,D(u.formatTime(a.row.last_check_time)),1)]),_:1}),u.isAdmin?(d(),e(ba,{key:0,label:"操作",width:"150"},{default:i(a=>[l(_,{type:"text",size:"small",onClick:t=>u.checkSingleDomain(a.row),loading:a.row.checking},{default:i(()=>n[38]||(n[38]=[c(" 检测 ",-1)])),_:2,__:[38]},1032,["onClick","loading"]),l(_,{type:"text",size:"small",onClick:t=>u.editDomain(a.row)},{default:i(()=>n[39]||(n[39]=[c(" 编辑 ",-1)])),_:2,__:[39]},1032,["onClick"])]),_:1})):s("",!0)]),_:1},8,["data"])]),_:1})]),l(Fa,{title:"添加域名",visible:u.addDomainVisible,width:"500px"},{default:i(()=>[l(Va,{model:u.domainForm,rules:u.domainRules,ref:"domainForm","label-width":"100px"},{default:i(()=>[l(Sa,{label:"域名",prop:"domain"},{default:i(()=>[l(xa,{modelValue:u.domainForm.domain,"onUpdate:modelValue":n[3]||(n[3]=a=>u.domainForm.domain=a),placeholder:"例如: short.example.com"},null,8,["modelValue"]),n[40]||(n[40]=t("div",{class:"form-tip"}," ⚠️ 请确保域名已正确解析到服务器，且已配置SSL证书 ",-1))]),_:1,__:[40]}),l(Sa,{label:"域名类型",prop:"domain_type"},{default:i(()=>[l(Ca,{modelValue:u.domainForm.domain_type,"onUpdate:modelValue":n[4]||(n[4]=a=>u.domainForm.domain_type=a),placeholder:"选择域名类型"},{default:i(()=>[l(ja,{label:"短链接域名",value:"redirect"}),l(ja,{label:"中转页域名",value:"landing"}),l(ja,{label:"API服务域名",value:"api"})]),_:1},8,["modelValue"]),n[41]||(n[41]=t("div",{class:"form-tip"}," 📝 建议：短链接域名用于生成短链接，中转页域名用于微信防红跳转 ",-1))]),_:1,__:[41]}),l(Sa,{label:"优先级",prop:"priority"},{default:i(()=>[l(Aa,{modelValue:u.domainForm.priority,"onUpdate:modelValue":n[5]||(n[5]=a=>u.domainForm.priority=a),min:0,max:100,"show-input":""},null,8,["modelValue"]),n[42]||(n[42]=t("div",{class:"form-tip"}," 💡 优先级越高，越优先使用。建议主域名设置90-100，备用域名设置60-80 ",-1))]),_:1,__:[42]}),l(Sa,{label:"备注"},{default:i(()=>[l(xa,{modelValue:u.domainForm.remarks,"onUpdate:modelValue":n[6]||(n[6]=a=>u.domainForm.remarks=a),type:"textarea",rows:"2",placeholder:"域名用途说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),t("div",ga,[l(_,{onClick:n[7]||(n[7]=a=>u.addDomainVisible=!1)},{default:i(()=>n[43]||(n[43]=[c("取消",-1)])),_:1,__:[43]}),l(_,{type:"primary",onClick:u.addDomain,loading:u.submitting},{default:i(()=>n[44]||(n[44]=[c("添加域名",-1)])),_:1,__:[44]},8,["onClick","loading"])])]),_:1},8,["visible"]),l(Fa,{title:"防红系统使用说明",visible:u.helpVisible,width:"800px"},{default:i(()=>[t("div",ya,[n[46]||(n[46]=t("h3",null,"🛡️ 什么是防红系统？",-1)),n[47]||(n[47]=t("p",null,"防红系统是一套智能域名管理和短链接生成系统，专门用于防止推广链接被微信、QQ等平台检测和封禁。",-1)),n[48]||(n[48]=t("h3",null,"🚀 主要功能特性",-1)),n[49]||(n[49]=t("ul",null,[t("li",null,[t("strong",null,"智能域名轮换"),c("：自动选择最佳域名生成短链接")]),t("li",null,[t("strong",null,"实时健康检测"),c("：每5分钟检测域名状态，发现异常立即处理")]),t("li",null,[t("strong",null,"自动切换机制"),c("：域名被封时自动切换到备用域名")]),t("li",null,[t("strong",null,"中转页面防护"),c("：微信/QQ访问自动跳转中转页面")]),t("li",null,[t("strong",null,"详细访问统计"),c("：完整的点击数据和来源分析")])],-1)),n[50]||(n[50]=t("h3",null,"⚙️ 配置步骤",-1)),n[51]||(n[51]=t("ol",null,[t("li",null,[t("strong",null,"准备域名"),c("：至少准备3-5个域名，确保已解析和配置SSL")]),t("li",null,[t("strong",null,"添加到域名池"),c("：在系统中添加域名，设置优先级")]),t("li",null,[t("strong",null,"设置定时任务"),c("：确保服务器crontab已配置域名检测任务")]),t("li",null,[t("strong",null,"测试功能"),c("：生成测试短链接，验证访问和跳转正常")])],-1)),n[52]||(n[52]=t("h3",null,"📋 域名配置建议",-1)),l(Da,{data:u.domainRecommendations,style:{margin:"10px 0"}},{default:i(()=>[l(ba,{prop:"type",label:"域名类型",width:"120"}),l(ba,{prop:"count",label:"建议数量",width:"100"}),l(ba,{prop:"priority",label:"优先级范围",width:"120"}),l(ba,{prop:"purpose",label:"主要用途"})]),_:1}),n[53]||(n[53]=t("h3",null,"⚠️ 注意事项",-1)),l(Ha,{type:"warning",closable:!1,style:{margin:"10px 0"}},{default:i(()=>n[45]||(n[45]=[t("ul",{style:{margin:"0","padding-left":"20px"}},[t("li",null,"域名必须已备案并正确解析到服务器"),t("li",null,"建议使用不同注册商、不同后缀的域名"),t("li",null,"避免使用包含敏感词的域名"),t("li",null,"定期检查域名到期时间，及时续费")],-1)])),_:1,__:[45]}),n[54]||(n[54]=t("h3",null,"🔧 定时任务配置",-1)),n[55]||(n[55]=t("div",{class:"code-block"},[t("p",null,"在服务器上添加以下crontab任务："),t("pre",null,"# 每5分钟检测域名状态\n*/5 * * * * cd /项目路径 && php artisan domains:check >/dev/null 2>&1\n\n# 每小时全面检测\n0 * * * * cd /项目路径 && php artisan domains:check --force >/dev/null 2>&1\n          ")],-1))])]),_:1},8,["visible"]),l(Fa,{title:"系统监控状态",visible:u.monitorVisible,width:"600px"},{default:i(()=>[t("div",fa,[n[56]||(n[56]=t("h4",null,"🔍 检测机制",-1)),n[57]||(n[57]=t("ul",null,[t("li",null,[t("strong",null,"HTTP状态检测"),c("：检查域名是否能正常访问")]),t("li",null,[t("strong",null,"DNS解析检测"),c("：验证域名解析是否正常")]),t("li",null,[t("strong",null,"微信平台检测"),c("：模拟微信访问，检测是否被封")]),t("li",null,[t("strong",null,"SSL证书检测"),c("：检查证书有效性和到期时间")])],-1)),n[58]||(n[58]=t("h4",null,"⚡ 自动处理流程",-1)),n[59]||(n[59]=t("div",{class:"process-flow"},[t("div",{class:"flow-item"},"检测异常"),t("div",{class:"flow-arrow"},"→"),t("div",{class:"flow-item"},"标记状态"),t("div",{class:"flow-arrow"},"→"),t("div",{class:"flow-item"},"切换域名"),t("div",{class:"flow-arrow"},"→"),t("div",{class:"flow-item"},"发送告警")],-1)),n[60]||(n[60]=t("h4",null,"📊 健康评分规则",-1)),l(Da,{data:u.healthRules,size:"small"},{default:i(()=>[l(ba,{prop:"condition",label:"检测条件",width:"150"}),l(ba,{prop:"penalty",label:"扣分",width:"80"}),l(ba,{prop:"description",label:"说明"})]),_:1})])]),_:1},8,["visible"])])}],["__scopeId","data-v-4eb90293"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/anti-block/Dashboard.vue"]]);export{ba as default};
