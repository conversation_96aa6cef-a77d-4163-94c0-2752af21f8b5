/* empty css             *//* empty css                *//* empty css                         *//* empty css                *//* empty css                   *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css               *//* empty css                     *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                       *//* empty css                        */import{l as e,m as t,G as a,A as l,q as n,C as s,E as r,F as o,Y as i,z as u,r as c,M as d,c as p}from"./vue-vendor-BcnDv-68.js";import{_ as m,$ as f,U as g,Y as y,a3 as _,a5 as v,a6 as b,aq as h,a4 as w,a1 as j,ah as V,Z as k,ap as C,bp as x,bq as R,br as U,ak as q,V as T,aB as A,ag as $,X as I}from"./element-plus-C2UshkXo.js";import{_ as L}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const S={class:"ai-content-generator"},z={class:"card-header"},D={class:"card-title"},M={class:"content-type-selector"},P={class:"generation-config"},O={class:"generation-actions"},B={key:0,class:"generation-results"},H={class:"results-list"},E={class:"result-header"},F={class:"result-index"},Q={class:"result-actions"},G={class:"result-content"},Y={class:"batch-actions"},N={key:1,class:"generation-history"},X={class:"history-item"},Z={class:"history-type"},J={class:"history-content"};const K=L({__name:"AIContentGenerator",emits:["content-generated"],setup(e,{expose:t,emit:a}){t();const l=a,n=c("title"),s=c(!1),r=c([]),o=c([]),i=d({industry:"",audience:"",style:"professional",count:3,keywords:"",customPrompt:""}),u={title:{tech:["{city}程序员技术交流群","{city}互联网从业者聚集地","{city}IT精英学习成长群","{city}技术大牛经验分享群"],finance:["{city}投资理财交流群","{city}财富增值学习群","{city}金融精英圈","{city}理财规划师群"],education:["{city}学习成长交流群","{city}知识分享互助群","{city}教育资源共享群","{city}终身学习者联盟"]},description:{professional:"汇聚行业精英，分享前沿资讯，共同成长进步。严格筛选成员，确保高质量交流环境。",casual:"轻松愉快的交流氛围，大家一起聊天学习，分享生活和工作中的点点滴滴。",warm:"温馨的大家庭，互帮互助，共同进步。每个人都能在这里找到归属感。"},faq:[{question:"这个群主要讨论什么内容？",answer:"主要围绕{industry}相关话题，包括行业动态、经验分享、资源交换等。"},{question:"群里有什么规则吗？",answer:"禁止发广告、恶意刷屏，鼓励有价值的分享和讨论，营造良好的交流环境。"},{question:"如何更好地融入群聊？",answer:"主动参与讨论，分享有价值的内容，尊重其他成员，积极互动交流。"}],reviews:[{username:"行业专家",content:"群里的分享质量很高，学到了很多实用的知识和经验。",rating:5},{username:"资深从业者",content:"氛围很好，大家都很乐于分享，是个不错的学习平台。",rating:5},{username:"新手小白",content:"刚入行的时候加入的，得到了很多前辈的指导和帮助。",rating:4}]},m=async()=>{if(i.industry&&i.audience){s.value=!0;try{await new Promise(e=>setTimeout(e,2e3));const e=[];for(let t=0;t<i.count;t++){const a=await f();e.push({id:Date.now()+t,content:a,rating:0,type:n.value})}r.value=e,o.value.unshift({timestamp:new Date,type:n.value,content:e[0].content,config:{...i}}),I.success(`成功生成 ${e.length} 个内容方案`)}catch(e){I.error("生成失败，请重试")}finally{s.value=!1}}else I.warning("请先选择行业类型和目标人群")},f=async()=>{const{industry:e,audience:t,style:a,keywords:l,customPrompt:s}=i;switch(n.value){case"title":return g(e,t,l);case"description":return y(a,e,s);case"faq":return _(e);case"reviews":return v();case"intro":return b(e,t);default:return"生成的内容"}},g=(e,t,a)=>{const l=u.title[e]||u.title.tech;let n=l[Math.floor(Math.random()*l.length)].replace("{city}","xxx");if(a){const e=a.split(",").map(e=>e.trim()),t=e[Math.floor(Math.random()*e.length)];n=n.replace("程序员",t).replace("投资理财",t)}return n},y=(e,t,a)=>{let l=u.description[e]||u.description.professional;return a&&(l+="\n\n"+a),l.replace("{industry}",h(t))},_=(e,t)=>u.faq.map(t=>({...t,answer:t.answer.replace("{industry}",h(e))})).map(e=>`${e.question}----${e.answer}`).join("\n"),v=(e,t)=>u.reviews.map(e=>`${e.username}----${e.content}----${e.rating}`).join("\n"),b=(e,t,a)=>`欢迎加入我们的${h(e)}交流群！这里汇聚了众多${w(t)}，大家可以在这里分享经验、交流心得、互相学习。我们致力于打造一个高质量的交流平台，让每个成员都能在这里收获成长。`,h=e=>({tech:"互联网科技",finance:"金融投资",education:"教育培训",ecommerce:"电商零售",health:"健康医疗",entertainment:"娱乐游戏",realestate:"房产装修",food:"美食餐饮",travel:"旅游出行",other:"其他行业"}[e]||e),w=e=>({students:"学生朋友",newcomers:"职场新人",experts:"资深专家",entrepreneurs:"创业者",mothers:"宝妈群体",seniors:"中老年朋友",youngpro:"年轻白领",freelancers:"自由职业者"}[e]||e),j={emit:l,selectedType:n,generating:s,generatedResults:r,generationHistory:o,config:i,contentTemplates:u,handleTypeChange:e=>{},generateContent:m,generateSingleContent:f,generateTitle:g,generateDescription:y,generateFAQ:_,generateReviews:v,generateIntro:b,useResult:e=>{l("content-generated",{type:n.value,content:e.content}),I.success("内容已应用")},copyResult:async e=>{if("undefined"!=typeof navigator&&navigator.clipboard)try{await navigator.clipboard.writeText(e.content),I.success("内容已复制到剪贴板")}catch(t){I.error("复制失败")}else I.warning("剪贴板功能在当前环境中不可用")},rateResult:(e,t)=>{e.rating=t},clearResults:()=>{r.value=[]},regenerateAll:()=>{m()},exportResults:()=>{const e=r.value.map((e,t)=>`方案${t+1}:\n${e.content}\n\n`).join(""),t=new Blob([e],{type:"text/plain"}),a=URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download=`AI生成内容_${(new Date).toISOString().slice(0,10)}.txt`,l.click(),URL.revokeObjectURL(a)},reuseHistory:e=>{Object.assign(i,e.config),n.value=e.type},getTypeLabel:e=>({title:"群组标题",description:"群组描述",faq:"FAQ问答",reviews:"用户评论",intro:"群组介绍"}[e]||e),getIndustryLabel:h,getAudienceLabel:w,formatTime:e=>new Date(e).toLocaleString("zh-CN"),ref:c,reactive:d,computed:p,get ElMessage(){return I},get ElMessageBox(){return $},get Star(){return A}};return Object.defineProperty(j,"__isScriptSetup",{enumerable:!1,value:!0}),j}},[["render",function(c,d,p,A,$,I){const L=V,K=q,W=f,ee=m,te=b,ae=v,le=_,ne=y,se=g,re=h,oe=w,ie=j,ue=C,ce=x,de=U,pe=R,me=T;return t(),e("div",S,[a(me,{class:"generator-card",shadow:"never"},{header:l(()=>[n("div",z,[n("span",D,[a(L,null,{default:l(()=>[a(A.Star)]),_:1}),d[7]||(d[7]=r(" AI智能内容生成 ",-1))]),a(K,{type:"success",size:"small"},{default:l(()=>d[8]||(d[8]=[r("Beta",-1)])),_:1,__:[8]})])]),default:l(()=>[n("div",M,[a(ee,{modelValue:A.selectedType,"onUpdate:modelValue":d[0]||(d[0]=e=>A.selectedType=e),onChange:A.handleTypeChange},{default:l(()=>[a(W,{label:"title"},{default:l(()=>d[9]||(d[9]=[r("群组标题",-1)])),_:1,__:[9]}),a(W,{label:"description"},{default:l(()=>d[10]||(d[10]=[r("群组描述",-1)])),_:1,__:[10]}),a(W,{label:"faq"},{default:l(()=>d[11]||(d[11]=[r("FAQ问答",-1)])),_:1,__:[11]}),a(W,{label:"reviews"},{default:l(()=>d[12]||(d[12]=[r("用户评论",-1)])),_:1,__:[12]}),a(W,{label:"intro"},{default:l(()=>d[13]||(d[13]=[r("群组介绍",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"])]),n("div",P,[a(se,{gutter:20},{default:l(()=>[a(ne,{span:12},{default:l(()=>[a(le,{label:"行业类型"},{default:l(()=>[a(ae,{modelValue:A.config.industry,"onUpdate:modelValue":d[1]||(d[1]=e=>A.config.industry=e),placeholder:"选择行业"},{default:l(()=>[a(te,{label:"互联网/科技",value:"tech"}),a(te,{label:"金融/投资",value:"finance"}),a(te,{label:"教育/培训",value:"education"}),a(te,{label:"电商/零售",value:"ecommerce"}),a(te,{label:"健康/医疗",value:"health"}),a(te,{label:"娱乐/游戏",value:"entertainment"}),a(te,{label:"房产/装修",value:"realestate"}),a(te,{label:"美食/餐饮",value:"food"}),a(te,{label:"旅游/出行",value:"travel"}),a(te,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(ne,{span:12},{default:l(()=>[a(le,{label:"目标人群"},{default:l(()=>[a(ae,{modelValue:A.config.audience,"onUpdate:modelValue":d[2]||(d[2]=e=>A.config.audience=e),placeholder:"选择目标人群"},{default:l(()=>[a(te,{label:"学生群体",value:"students"}),a(te,{label:"职场新人",value:"newcomers"}),a(te,{label:"资深专家",value:"experts"}),a(te,{label:"创业者",value:"entrepreneurs"}),a(te,{label:"宝妈群体",value:"mothers"}),a(te,{label:"中老年人",value:"seniors"}),a(te,{label:"年轻白领",value:"youngpro"}),a(te,{label:"自由职业者",value:"freelancers"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(se,{gutter:20},{default:l(()=>[a(ne,{span:12},{default:l(()=>[a(le,{label:"内容风格"},{default:l(()=>[a(ae,{modelValue:A.config.style,"onUpdate:modelValue":d[3]||(d[3]=e=>A.config.style=e),placeholder:"选择风格"},{default:l(()=>[a(te,{label:"专业严谨",value:"professional"}),a(te,{label:"轻松幽默",value:"casual"}),a(te,{label:"温馨亲切",value:"warm"}),a(te,{label:"激励鼓舞",value:"motivational"}),a(te,{label:"简洁明了",value:"concise"}),a(te,{label:"详细全面",value:"detailed"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(ne,{span:12},{default:l(()=>[a(le,{label:"生成数量"},{default:l(()=>[a(re,{modelValue:A.config.count,"onUpdate:modelValue":d[4]||(d[4]=e=>A.config.count=e),min:1,max:10,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(le,{label:"关键词"},{default:l(()=>[a(oe,{modelValue:A.config.keywords,"onUpdate:modelValue":d[5]||(d[5]=e=>A.config.keywords=e),placeholder:"输入相关关键词，用逗号分隔",type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),a(le,{label:"自定义要求"},{default:l(()=>[a(oe,{modelValue:A.config.customPrompt,"onUpdate:modelValue":d[6]||(d[6]=e=>A.config.customPrompt=e),placeholder:"描述您的具体要求，如：需要包含价格信息、突出优势等",type:"textarea",rows:3},null,8,["modelValue"])]),_:1})]),n("div",O,[a(ie,{type:"primary",onClick:A.generateContent,loading:A.generating,size:"large"},{default:l(()=>[a(L,null,{default:l(()=>[a(A.Star)]),_:1}),r(" "+k(A.generating?"生成中...":"智能生成"),1)]),_:1},8,["loading"]),a(ie,{onClick:A.clearResults},{default:l(()=>d[14]||(d[14]=[r("清空结果",-1)])),_:1,__:[14]})]),A.generatedResults.length>0?(t(),e("div",B,[a(ue,{"content-position":"left"},{default:l(()=>d[15]||(d[15]=[r("生成结果",-1)])),_:1,__:[15]}),n("div",H,[(t(!0),e(o,null,i(A.generatedResults,(s,o)=>(t(),e("div",{key:o,class:"result-item"},[n("div",E,[n("span",F,"方案 "+k(o+1),1),n("div",Q,[a(ie,{type:"primary",size:"small",onClick:e=>A.useResult(s)},{default:l(()=>[...d[16]||(d[16]=[r(" 使用此内容 ",-1)])]),_:2,__:[16]},1032,["onClick"]),a(ie,{size:"small",onClick:e=>A.copyResult(s)},{default:l(()=>[...d[17]||(d[17]=[r(" 复制 ",-1)])]),_:2,__:[17]},1032,["onClick"]),a(ce,{modelValue:s.rating,"onUpdate:modelValue":e=>s.rating=e,size:"small",onChange:e=>A.rateResult(s,e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])]),n("div",G,[n("pre",null,k(s.content),1)])]))),128))]),n("div",Y,[a(ie,{onClick:A.regenerateAll},{default:l(()=>d[18]||(d[18]=[r("重新生成全部",-1)])),_:1,__:[18]}),a(ie,{onClick:A.exportResults},{default:l(()=>d[19]||(d[19]=[r("导出结果",-1)])),_:1,__:[19]})])])):s("",!0),A.generationHistory.length>0?(t(),e("div",N,[a(ue,{"content-position":"left"},{default:l(()=>d[20]||(d[20]=[r("历史记录",-1)])),_:1,__:[20]}),a(pe,null,{default:l(()=>[(t(!0),e(o,null,i(A.generationHistory.slice(0,5),(e,s)=>(t(),u(de,{key:s,timestamp:A.formatTime(e.timestamp)},{default:l(()=>[n("div",X,[n("div",Z,k(A.getTypeLabel(e.type)),1),n("div",J,k(e.content.substring(0,50))+"...",1),a(ie,{type:"text",size:"small",onClick:t=>A.reuseHistory(e)},{default:l(()=>[...d[21]||(d[21]=[r(" 重新使用 ",-1)])]),_:2,__:[21]},1032,["onClick"])])]),_:2},1032,["timestamp"]))),128))]),_:1})])):s("",!0)]),_:1})])}],["__scopeId","data-v-dd917231"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/AIContentGenerator.vue"]]);export{K as default};
