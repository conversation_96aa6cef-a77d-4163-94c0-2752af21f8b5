<template>
  <div class="test-page">
    <div class="test-container">
      <h1>路由重定向测试页面</h1>
      <p>点击下面的链接测试路由重定向是否正常工作：</p>

      <div class="test-sections">
        <div class="test-section">
          <h3>权限管理</h3>
          <div class="test-links">
            <a href="/#/permission/roles" target="_blank">角色管理 (旧路径)</a>
            <a href="/#/admin/roles" target="_blank">角色管理 (新路径)</a>
          </div>
        </div>

        <div class="test-section">
          <h3>财务管理</h3>
          <div class="test-links">
            <a href="/#/finance" target="_blank">财务管理 (旧路径)</a>
            <a href="/#/admin/finance" target="_blank">财务管理 (新路径)</a>
            <a href="/#/finance/commission" target="_blank">佣金记录 (旧路径)</a>
            <a href="/#/admin/commission-logs" target="_blank">佣金记录 (新路径)</a>
          </div>
        </div>

        <div class="test-section">
          <h3>支付设置</h3>
          <div class="test-links">
            <a href="/#/payment/settings" target="_blank">支付设置 (旧路径)</a>
            <a href="/#/admin/payment-settings" target="_blank">支付设置 (新路径)</a>
            <a href="/#/payment/orders" target="_blank">支付订单 (旧路径)</a>
            <a href="/#/admin/payment-orders" target="_blank">支付订单 (新路径)</a>
          </div>
        </div>

        <div class="test-section">
          <h3>系统设置</h3>
          <div class="test-links">
            <a href="/#/system/settings" target="_blank">系统设置 (旧路径)</a>
            <a href="/#/admin/system-monitor" target="_blank">系统监控 (新路径)</a>
            <a href="/#/system/notifications" target="_blank">通知管理 (旧路径)</a>
            <a href="/#/admin/notifications" target="_blank">通知管理 (新路径)</a>
          </div>
        </div>
      </div>

      <div class="test-actions">
        <el-button type="primary" @click="goBack">返回</el-button>
        <el-button @click="goToDashboard">返回仪表板</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const goToDashboard = () => {
  router.push('/admin/dashboard')
}
</script>

<style scoped>
.test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.test-container {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 800px;
  width: 100%;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
}

.test-sections {
  margin: 30px 0;
}

.test-section {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.test-section h3 {
  color: #555;
  margin-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 8px;
}

.test-links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.test-links a {
  display: block;
  padding: 10px 15px;
  background: #007bff;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  text-align: center;
  transition: background-color 0.3s;
}

.test-links a:hover {
  background: #0056b3;
}

.test-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 30px;
}
</style>
