/**
 * 应用配置文件
 * 统一管理所有配置项
 */

import { API_CONFIG, API_ENDPOINTS } from './api'
import { roleHierarchy } from './navigation'

// 应用环境配置
export const APP_CONFIG = {
  // 应用信息
  APP_NAME: '晨鑫流量变现系统',
  APP_VERSION: '2.0.0',
  APP_DESCRIPTION: '晨鑫流量变现系统 - 新一代智能社群营销与多级分销平台',
  
  // 环境配置
  ENV: import.meta.env.MODE || 'development',
  DEBUG: import.meta.env.DEV || false,
  
  // 域名配置
  DOMAIN: import.meta.env.VITE_APP_DOMAIN || 'localhost',
  API_DOMAIN: import.meta.env.VITE_API_DOMAIN || 'localhost',
  
  // 预览模式配置
  PREVIEW_MODE: {
    ENABLED: false,
    TOKEN: 'preview_token_2024',
    EXPIRE_TIME: 30 * 60 * 1000 // 30分钟
  },
  
  // 主题配置
  THEME: {
    DEFAULT: 'light',
    AVAILABLE: ['light', 'dark', 'auto'],
    PRIMARY_COLOR: '#409EFF',
    SUCCESS_COLOR: '#67C23A',
    WARNING_COLOR: '#E6A23C',
    DANGER_COLOR: '#F56C6C',
    INFO_COLOR: '#909399'
  },
  
  // 布局配置
  LAYOUT: {
    SIDEBAR_WIDTH: 240,
    SIDEBAR_COLLAPSED_WIDTH: 64,
    HEADER_HEIGHT: 60,
    FOOTER_HEIGHT: 40,
    TAB_BAR_HEIGHT: 40
  },
  
  // 动画配置
  ANIMATION: {
    DURATION: 300,
    EASING: 'ease-in-out'
  },
  
  // 缓存配置
  CACHE: {
    ENABLED: true,
    TTL: 5 * 60 * 1000, // 5分钟
    STORAGE_KEY_PREFIX: 'linkhub_admin_'
  },
  
  // 上传配置
  UPLOAD: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_COUNT: 10,
    ACCEPT: {
      IMAGE: 'image/*',
      VIDEO: 'video/*',
      AUDIO: 'audio/*',
      DOCUMENT: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt'
    },
    COMPRESSION: {
      ENABLED: true,
      QUALITY: 0.8,
      MAX_WIDTH: 1920,
      MAX_HEIGHT: 1080
    }
  },
  
  // 分页配置
  PAGINATION: {
    PAGE_SIZE: 20,
    PAGE_SIZES: [10, 20, 50, 100],
    LAYOUT: 'total, sizes, prev, pager, next, jumper'
  },
  
  // 搜索配置
  SEARCH: {
    DEBOUNCE_TIME: 300,
    MIN_LENGTH: 2,
    MAX_SUGGESTIONS: 10
  },
  
  // 通知配置
  NOTIFICATION: {
    DURATION: 3000,
    MAX_COUNT: 3,
    POSITION: 'top-right'
  },
  
  // 日志配置
  LOG: {
    ENABLED: true,
    LEVEL: import.meta.env.DEV ? 'debug' : 'error',
    MAX_ENTRIES: 1000
  },
  
  // 性能监控
  PERFORMANCE: {
    ENABLED: !import.meta.env.DEV,
    SAMPLE_RATE: 0.1,
    MAX_MEASURES: 100
  },
  
  // 错误处理
  ERROR: {
    RETRY_COUNT: 3,
    RETRY_DELAY: 1000,
    SHOW_DIALOG: true,
    AUTO_REPORT: !import.meta.env.DEV
  },
  
  // 权限配置
  PERMISSION: {
    CHECK_INTERVAL: 60 * 1000, // 1分钟
    CACHE_TIME: 5 * 60 * 1000, // 5分钟
    ROLE_HIERARCHY: roleHierarchy
  }
}

// 角色权限配置
export const PERMISSION_CONFIG = {
  // 角色层级
  ROLES: roleHierarchy,
  
  // 权限检查
  CHECK_PERMISSIONS: true,
  
  // 权限缓存
  CACHE_PERMISSIONS: true,
  
  // 权限过期时间
  PERMISSION_EXPIRE: 30 * 60 * 1000, // 30分钟
  
  // 超级管理员角色
  SUPER_ADMIN_ROLE: 'admin'
}

// 路由配置
export const ROUTE_CONFIG = {
  // 默认路由
  DEFAULT_ROUTE: '/admin/dashboard',

  // 登录路由
  LOGIN_ROUTE: '/login',

  // 404路由
  NOT_FOUND_ROUTE: '/404',

  // 白名单路由（不需要登录）
  WHITE_LIST: ['/login', '/register', '/404', '/403'],

  // 需要权限的路由前缀
  PROTECTED_PREFIXES: ['/admin', '/api'],

  // 预览模式路由
  PREVIEW_ROUTES: ['/preview', '/demo']
}

// 国际化配置
export const I18N_CONFIG = {
  DEFAULT_LOCALE: 'zh-CN',
  AVAILABLE_LOCALES: ['zh-CN', 'en-US'],
  LOCALE_STORAGE_KEY: 'linkhub_locale'
}

// 日期时间配置
export const DATE_CONFIG = {
  FORMAT: {
    DATE: 'YYYY-MM-DD',
    TIME: 'HH:mm:ss',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    MONTH: 'YYYY-MM'
  },
  TIMEZONE: 'Asia/Shanghai',
  FIRST_DAY_OF_WEEK: 1 // 周一为第一天
}

// 表格配置
export const TABLE_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZES: [10, 20, 50, 100],
  LOADING_DELAY: 300,
  MAX_EXPORT_ROWS: 10000,
  
  // 列配置
  COLUMN: {
    MIN_WIDTH: 80,
    MAX_WIDTH: 300,
    RESIZABLE: true,
    SORTABLE: true,
    FILTERABLE: true
  }
}

// 图表配置
export const CHART_CONFIG = {
  COLORS: [
    '#5470C6', '#91CC75', '#FAC858', '#EE6666',
    '#73C0DE', '#3BA272', '#FC8452', '#9A60B4',
    '#EA7CCC', '#FFD93D', '#6BCF7F', '#4ECDC4'
  ],
  
  THEME: 'light',
  
  // 响应式断点
  RESPONSIVE: {
    SMALL: 480,
    MEDIUM: 768,
    LARGE: 1024,
    XLARGE: 1200
  }
}

// 导出所有配置
export default {
  API_CONFIG,
  API_ENDPOINTS,
  APP_CONFIG,
  PERMISSION_CONFIG,
  ROUTE_CONFIG,
  I18N_CONFIG,
  DATE_CONFIG,
  TABLE_CONFIG,
  CHART_CONFIG
}