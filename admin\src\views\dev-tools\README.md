# 开发工具和测试文件

本目录包含用于开发和测试的页面组件，仅在开发环境中使用。

## 文件说明

### 测试页面
- `TestPage.vue` - 通用测试页面
- `TestRoute.vue` - 路由测试页面
- `TestPreview.vue` - 预览测试页面

### 导航测试
- `NavigationTest.vue` - 导航功能测试
- `TestNavigationSystem.vue` - 导航系统测试
- `RouteTest.vue` - 路由系统测试
- `ScrollTest.vue` - 滚动测试

### 权限测试
- `PermissionTest.vue` - 权限功能测试

### 业务功能测试
- `GroupAddEnhancedTest.vue` - 增强群组添加测试
- `GroupAddTest.vue` - 群组添加测试
- `PaymentTest.vue` - 支付功能测试

### 界面测试
- `ScreenTestSuite.vue` - 屏幕测试套件
- `DataScreenTest.vue` - 数据屏幕测试

## 注意事项

- 这些文件仅在开发环境中可访问
- 生产环境构建时会自动排除这些文件
- 如需在生产环境添加新的测试功能，请创建专门的调试模式