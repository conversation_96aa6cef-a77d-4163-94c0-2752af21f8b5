#!/bin/bash

# 晨鑫流量变现系统 用户前台系统部署脚本
# 支持开发环境和生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Node.js 版本
check_node_version() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js 18.0+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js 版本过低，当前版本: $(node -v)，需要 18.0+"
        exit 1
    fi
    
    log_info "Node.js 版本检查通过: $(node -v)"
}

# 检查 npm 版本
check_npm_version() {
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_info "npm 版本: $(npm -v)"
}

# 安装依赖
install_dependencies() {
    log_info "开始安装依赖..."
    
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    log_info "依赖安装完成"
}

# 构建项目
build_project() {
    local env_mode=$1
    
    log_info "开始构建项目 (模式: $env_mode)..."
    
    case $env_mode in
        "development")
            npm run dev &
            log_info "开发服务器启动完成，访问 http://localhost:3000"
            ;;
        "production")
            npm run build
            log_info "生产构建完成"
            ;;
        "static")
            npm run generate
            log_info "静态文件生成完成"
            ;;
        *)
            log_error "未知的构建模式: $env_mode"
            exit 1
            ;;
    esac
}

# 部署到服务器
deploy_to_server() {
    local deploy_path=$1
    local backup_path=$2
    
    log_info "开始部署到服务器..."
    
    # 创建备份
    if [ -d "$deploy_path" ]; then
        log_info "创建备份到 $backup_path"
        cp -r "$deploy_path" "$backup_path"
    fi
    
    # 部署新文件
    if [ -d ".output" ]; then
        log_info "部署 Nuxt 构建文件..."
        rsync -av --delete .output/ "$deploy_path/"
    elif [ -d "dist" ]; then
        log_info "部署静态文件..."
        rsync -av --delete dist/ "$deploy_path/"
    else
        log_error "未找到构建文件，请先运行构建命令"
        exit 1
    fi
    
    log_info "部署完成"
}

# 启动服务
start_service() {
    log_info "启动 Nuxt 服务..."
    
    # 检查是否有进程在运行
    if pgrep -f "nuxt" > /dev/null; then
        log_warn "检测到已运行的 Nuxt 进程，正在重启..."
        pkill -f "nuxt"
        sleep 2
    fi
    
    # 启动服务
    if [ -f ".output/server/index.mjs" ]; then
        nohup node .output/server/index.mjs > logs/app.log 2>&1 &
        log_info "服务启动完成"
    else
        log_error "未找到服务文件，请先构建项目"
        exit 1
    fi
}

# 检查服务状态
check_service() {
    local port=${1:-3000}
    
    log_info "检查服务状态..."
    
    # 等待服务启动
    sleep 5
    
    if curl -s "http://localhost:$port" > /dev/null; then
        log_info "✅ 服务运行正常，访问地址: http://localhost:$port"
    else
        log_error "❌ 服务启动失败，请检查日志"
        exit 1
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    
    # 清理 node_modules
    if [ "$1" == "full" ]; then
        rm -rf node_modules
        log_info "已清理 node_modules"
    fi
    
    # 清理构建文件
    rm -rf .output dist .nuxt
    log_info "已清理构建文件"
}

# 主函数
main() {
    local mode=${1:-"development"}
    local action=${2:-"build"}
    
    log_info "晨鑫流量变现系统 用户前台系统部署脚本"
    log_info "模式: $mode, 操作: $action"
    log_info "=========================="
    
    # 检查环境
    check_node_version
    check_npm_version
    
    # 创建必要目录
    mkdir -p logs
    
    case $action in
        "install")
            install_dependencies
            ;;
        "build")
            install_dependencies
            build_project "$mode"
            ;;
        "deploy")
            local deploy_path=${3:-"/var/www/linkhub-frontend"}
            local backup_path=${4:-"/var/www/backup/linkhub-frontend-$(date +%Y%m%d_%H%M%S)"}
            
            install_dependencies
            build_project "production"
            deploy_to_server "$deploy_path" "$backup_path"
            ;;
        "start")
            start_service
            check_service
            ;;
        "restart")
            build_project "production"
            start_service
            check_service
            ;;
        "clean")
            cleanup ${3:-"partial"}
            ;;
        "full")
            install_dependencies
            build_project "production"
            start_service
            check_service
            ;;
        *)
            log_error "未知操作: $action"
            echo "可用操作: install, build, deploy, start, restart, clean, full"
            exit 1
            ;;
    esac
    
    log_info "操作完成！"
}

# 显示帮助信息
show_help() {
    cat << EOF
晨鑫流量变现系统 用户前台系统部署脚本

用法: $0 [模式] [操作] [参数...]

模式:
  development   开发模式 (默认)
  production    生产模式
  static        静态文件生成

操作:
  install       只安装依赖
  build         构建项目 (默认)
  deploy        部署到服务器
  start         启动服务
  restart       重启服务
  clean         清理文件
  full          完整部署流程

示例:
  $0 development build                    # 开发模式构建
  $0 production deploy /var/www/app       # 生产模式部署
  $0 production start                     # 启动生产服务
  $0 production restart                   # 重启服务
  $0 development clean full               # 完整清理

环境变量:
  NODE_ENV      环境变量
  PORT          服务端口 (默认: 3000)
  
EOF
}

# 处理参数
if [ "$1" == "--help" ] || [ "$1" == "-h" ]; then
    show_help
    exit 0
fi

# 执行主函数
main "$@" 