// 导航性能优化工具类
export class NavigationOptimizer {
  constructor() {
    this.preloadCache = new Map()
    this.loadingStates = new Map()
    this.observers = new Set()
  }

  // 智能预加载 - 基于用户行为
  async preloadRoute(routePath) {
    if (this.preloadCache.has(routePath) || this.loadingStates.has(routePath)) {
      return this.preloadCache.get(routePath)
    }

    this.loadingStates.set(routePath, true)
    
    try {
      console.log(`🔄 预加载路由: ${routePath}`)
      
      // 动态导入路由组件
      const component = await import(`@/views${routePath}.vue`)
      this.preloadCache.set(routePath, component)
      
      console.log(`✅ 路由预加载成功: ${routePath}`)
      return component
    } catch (error) {
      console.warn(`⚠️ 路由预加载失败: ${routePath}`, error)
      return null
    } finally {
      this.loadingStates.delete(routePath)
    }
  }

  // 批量预加载关键路由
  async preloadCriticalRoutes() {
    const criticalRoutes = [
      '/dashboard/ModernDashboard',
      '/community/GroupList', 
      '/user/UserList',
      '/finance/FinanceDashboard',
      '/anti-block/Dashboard'
    ]

    const promises = criticalRoutes.map(route => this.preloadRoute(route))
    await Promise.all(promises)
    console.log('🎉 关键路由预加载完成')
  }

  // 基于用户交互的智能预加载
  setupHoverPreload() {
    // 监听菜单项hover事件，预加载对应路由
    document.addEventListener('mouseenter', (event) => {
      const menuItem = event.target.closest('[data-route-path]')
      if (menuItem) {
        const routePath = menuItem.dataset.routePath
        // 延迟预加载，避免频繁触发
        setTimeout(() => this.preloadRoute(routePath), 200)
      }
    }, { passive: true, capture: true })
  }

  // 页面可见性优化
  setupVisibilityOptimization() {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // 页面隐藏时暂停预加载
        this.pausePreloading = true
      } else {
        // 页面可见时恢复预加载
        this.pausePreloading = false
      }
    })
  }

  // 网络状况感知预加载
  setupNetworkAwarePreload() {
    if ('connection' in navigator) {
      const connection = navigator.connection
      
      // 根据网络状况调整预加载策略
      const adjustPreloadStrategy = () => {
        const effectiveType = connection.effectiveType
        
        if (effectiveType === '4g') {
          this.preloadAggressively = true
        } else if (effectiveType === '3g') {
          this.preloadAggressively = false
        } else {
          // 2g或更慢，禁用预加载
          this.preloadEnabled = false
        }
      }

      connection.addEventListener('change', adjustPreloadStrategy)
      adjustPreloadStrategy()
    }
  }

  // 初始化所有优化策略
  init() {
    console.log('🚀 初始化导航性能优化...')
    
    // 延迟2秒后开始预加载，避免影响首屏
    setTimeout(() => {
      this.preloadCriticalRoutes()
      this.setupHoverPreload()
      this.setupVisibilityOptimization()
      this.setupNetworkAwarePreload()
    }, 2000)
  }

  // 获取预加载统计信息
  getStats() {
    return {
      preloaded: this.preloadCache.size,
      loading: this.loadingStates.size,
      routes: Array.from(this.preloadCache.keys())
    }
  }
}

// 创建单例实例
export const navigationOptimizer = new NavigationOptimizer()

// 路由组件缓存策略
export const routeCacheStrategy = {
  // 需要缓存的组件
  cacheable: [
    'Dashboard',
    'ModernDashboard', 
    'UserList',
    'GroupList',
    'OrderManagement',
    'FinanceDashboard',
    'AntiBlockDashboard'
  ],
  
  // 缓存大小限制
  maxCacheSize: 10,
  
  // 检查是否应该缓存
  shouldCache(routeName) {
    return this.cacheable.includes(routeName)
  }
}

// 页面加载性能监控
export const performanceMonitor = {
  navigationStart: 0,
  
  startNavigation(from, to) {
    this.navigationStart = performance.now()
    console.log(`📊 导航开始: ${from} → ${to}`)
  },
  
  endNavigation(routeName) {
    const duration = performance.now() - this.navigationStart
    console.log(`📊 导航完成: ${routeName}, 耗时: ${duration.toFixed(2)}ms`)
    
    // 记录到性能指标
    if (window.gtag) {
      window.gtag('event', 'page_load_time', {
        event_category: 'Navigation',
        event_label: routeName,
        value: Math.round(duration)
      })
    }
  }
}