<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use ZipArchive;

/**
 * 备份服务
 * 提供数据库和文件的自动备份功能
 */
class BackupService
{
    private string $backupPath;
    private array $config;

    public function __construct()
    {
        $this->backupPath = storage_path('app/backups');
        $this->config = config('backup', [
            'retention_days' => 30,
            'max_backups' => 10,
            'compress' => true,
            'include_files' => true,
        ]);

        $this->ensureBackupDirectory();
    }

    /**
     * 执行完整备份
     */
    public function fullBackup(): array
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $backupName = "full_backup_{$timestamp}";
        
        Log::info('开始执行完整备份', ['backup_name' => $backupName]);

        try {
            $results = [
                'backup_name' => $backupName,
                'timestamp' => $timestamp,
                'database' => $this->backupDatabase($backupName),
                'files' => $this->config['include_files'] ? $this->backupFiles($backupName) : null,
                'size' => 0,
                'duration' => 0,
            ];

            $startTime = microtime(true);
            
            // 如果启用压缩，创建压缩包
            if ($this->config['compress']) {
                $results['archive'] = $this->createArchive($backupName, $results);
            }

            $results['duration'] = round(microtime(true) - $startTime, 2);
            $results['size'] = $this->getBackupSize($backupName);

            // 清理旧备份
            $this->cleanupOldBackups();

            Log::info('完整备份执行成功', $results);

            return $results;

        } catch (\Exception $e) {
            Log::error('完整备份执行失败', [
                'backup_name' => $backupName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * 备份数据库
     */
    public function backupDatabase(string $backupName): array
    {
        $dbConfig = config('database.connections.' . config('database.default'));
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "database_{$timestamp}.sql";
        $filepath = $this->backupPath . "/{$backupName}/{$filename}";

        // 确保备份目录存在
        $backupDir = dirname($filepath);
        if (!File::exists($backupDir)) {
            File::makeDirectory($backupDir, 0755, true);
        }

        $startTime = microtime(true);

        try {
            // 使用mysqldump命令备份数据库
            $command = sprintf(
                'mysqldump --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
                escapeshellarg($dbConfig['host']),
                escapeshellarg($dbConfig['port']),
                escapeshellarg($dbConfig['username']),
                escapeshellarg($dbConfig['password']),
                escapeshellarg($dbConfig['database']),
                escapeshellarg($filepath)
            );

            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                throw new \Exception('数据库备份失败: ' . implode("\n", $output));
            }

            $fileSize = File::size($filepath);
            $duration = round(microtime(true) - $startTime, 2);

            return [
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => $fileSize,
                'size_human' => $this->formatBytes($fileSize),
                'duration' => $duration,
                'tables' => $this->getTableCount(),
                'records' => $this->getRecordCount(),
            ];

        } catch (\Exception $e) {
            // 如果mysqldump失败，尝试使用Laravel的数据库功能
            return $this->backupDatabaseFallback($filepath);
        }
    }

    /**
     * 备份数据库（备用方法）
     */
    private function backupDatabaseFallback(string $filepath): array
    {
        $startTime = microtime(true);
        
        $tables = DB::select('SHOW TABLES');
        $sql = "-- 晨鑫流量变现系统 Database Backup\n";
        $sql .= "-- Generated on: " . now()->toDateTimeString() . "\n\n";
        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

        foreach ($tables as $table) {
            $tableName = array_values((array) $table)[0];
            
            // 获取表结构
            $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`")[0];
            $sql .= "-- Table: {$tableName}\n";
            $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
            $sql .= $createTable->{'Create Table'} . ";\n\n";

            // 获取表数据
            $rows = DB::table($tableName)->get();
            if ($rows->count() > 0) {
                $sql .= "-- Data for table: {$tableName}\n";
                foreach ($rows as $row) {
                    $values = array_map(function ($value) {
                        return is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
                    }, (array) $row);
                    
                    $sql .= "INSERT INTO `{$tableName}` VALUES (" . implode(', ', $values) . ");\n";
                }
                $sql .= "\n";
            }
        }

        $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";

        File::put($filepath, $sql);

        $fileSize = File::size($filepath);
        $duration = round(microtime(true) - $startTime, 2);

        return [
            'filename' => basename($filepath),
            'filepath' => $filepath,
            'size' => $fileSize,
            'size_human' => $this->formatBytes($fileSize),
            'duration' => $duration,
            'tables' => count($tables),
            'method' => 'fallback',
        ];
    }

    /**
     * 备份文件
     */
    public function backupFiles(string $backupName): array
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "files_{$timestamp}.tar.gz";
        $filepath = $this->backupPath . "/{$backupName}/{$filename}";

        $startTime = microtime(true);

        // 要备份的目录
        $directories = [
            'storage/app/public',
            'public/uploads',
            '.env',
            'composer.json',
            'composer.lock',
        ];

        $existingDirs = array_filter($directories, function ($dir) {
            return File::exists(base_path($dir));
        });

        if (empty($existingDirs)) {
            return [
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => 0,
                'size_human' => '0 B',
                'duration' => 0,
                'files_count' => 0,
                'status' => 'no_files',
            ];
        }

        // 创建tar.gz压缩包
        $command = sprintf(
            'cd %s && tar -czf %s %s',
            escapeshellarg(base_path()),
            escapeshellarg($filepath),
            implode(' ', array_map('escapeshellarg', $existingDirs))
        );

        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            throw new \Exception('文件备份失败: ' . implode("\n", $output));
        }

        $fileSize = File::exists($filepath) ? File::size($filepath) : 0;
        $duration = round(microtime(true) - $startTime, 2);

        return [
            'filename' => $filename,
            'filepath' => $filepath,
            'size' => $fileSize,
            'size_human' => $this->formatBytes($fileSize),
            'duration' => $duration,
            'directories' => $existingDirs,
            'files_count' => $this->countFilesInDirectories($existingDirs),
        ];
    }

    /**
     * 创建压缩归档
     */
    private function createArchive(string $backupName, array $results): array
    {
        $archiveName = "{$backupName}.zip";
        $archivePath = $this->backupPath . "/{$archiveName}";
        $backupDir = $this->backupPath . "/{$backupName}";

        $zip = new ZipArchive();
        if ($zip->open($archivePath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('无法创建压缩包: ' . $archivePath);
        }

        // 添加数据库备份文件
        if (isset($results['database']['filepath']) && File::exists($results['database']['filepath'])) {
            $zip->addFile($results['database']['filepath'], basename($results['database']['filepath']));
        }

        // 添加文件备份
        if (isset($results['files']['filepath']) && File::exists($results['files']['filepath'])) {
            $zip->addFile($results['files']['filepath'], basename($results['files']['filepath']));
        }

        // 添加备份信息文件
        $info = [
            'backup_name' => $backupName,
            'created_at' => now()->toISOString(),
            'database' => $results['database'] ?? null,
            'files' => $results['files'] ?? null,
            'system_info' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'server' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            ],
        ];

        $zip->addFromString('backup_info.json', json_encode($info, JSON_PRETTY_PRINT));
        $zip->close();

        // 删除临时文件夹
        File::deleteDirectory($backupDir);

        return [
            'filename' => $archiveName,
            'filepath' => $archivePath,
            'size' => File::size($archivePath),
            'size_human' => $this->formatBytes(File::size($archivePath)),
        ];
    }

    /**
     * 恢复备份
     */
    public function restore(string $backupName): array
    {
        $backupPath = $this->backupPath . "/{$backupName}";
        
        if (!File::exists($backupPath)) {
            throw new \Exception("备份文件不存在: {$backupName}");
        }

        Log::info('开始恢复备份', ['backup_name' => $backupName]);

        $results = [
            'backup_name' => $backupName,
            'restored_at' => now()->toISOString(),
        ];

        try {
            // 如果是压缩包，先解压
            if (str_ends_with($backupName, '.zip')) {
                $results['extract'] = $this->extractArchive($backupPath);
                $backupPath = $this->backupPath . '/' . pathinfo($backupName, PATHINFO_FILENAME);
            }

            // 恢复数据库
            $dbBackupFile = $this->findDatabaseBackup($backupPath);
            if ($dbBackupFile) {
                $results['database'] = $this->restoreDatabase($dbBackupFile);
            }

            // 恢复文件
            $fileBackupFile = $this->findFileBackup($backupPath);
            if ($fileBackupFile) {
                $results['files'] = $this->restoreFiles($fileBackupFile);
            }

            Log::info('备份恢复成功', $results);

            return $results;

        } catch (\Exception $e) {
            Log::error('备份恢复失败', [
                'backup_name' => $backupName,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * 获取备份列表
     */
    public function getBackupList(): array
    {
        $backups = [];
        $files = File::glob($this->backupPath . '/*');

        foreach ($files as $file) {
            $name = basename($file);
            $isDirectory = File::isDirectory($file);
            $isArchive = str_ends_with($name, '.zip');

            if ($isDirectory || $isArchive) {
                $backups[] = [
                    'name' => $name,
                    'path' => $file,
                    'type' => $isArchive ? 'archive' : 'directory',
                    'size' => $isArchive ? File::size($file) : $this->getDirectorySize($file),
                    'size_human' => $this->formatBytes($isArchive ? File::size($file) : $this->getDirectorySize($file)),
                    'created_at' => Carbon::createFromTimestamp(File::lastModified($file)),
                    'age_days' => Carbon::createFromTimestamp(File::lastModified($file))->diffInDays(now()),
                ];
            }
        }

        // 按创建时间倒序排列
        usort($backups, function ($a, $b) {
            return $b['created_at']->timestamp - $a['created_at']->timestamp;
        });

        return $backups;
    }

    /**
     * 删除备份
     */
    public function deleteBackup(string $backupName): bool
    {
        $backupPath = $this->backupPath . "/{$backupName}";

        if (!File::exists($backupPath)) {
            throw new \Exception("备份不存在: {$backupName}");
        }

        try {
            if (File::isDirectory($backupPath)) {
                File::deleteDirectory($backupPath);
            } else {
                File::delete($backupPath);
            }

            Log::info('备份删除成功', ['backup_name' => $backupName]);

            return true;

        } catch (\Exception $e) {
            Log::error('备份删除失败', [
                'backup_name' => $backupName,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * 清理旧备份
     */
    public function cleanupOldBackups(): array
    {
        $backups = $this->getBackupList();
        $deleted = [];

        // 按保留天数删除
        foreach ($backups as $backup) {
            if ($backup['age_days'] > $this->config['retention_days']) {
                $this->deleteBackup($backup['name']);
                $deleted[] = $backup['name'];
            }
        }

        // 按最大备份数量删除
        $remainingBackups = array_filter($backups, function ($backup) use ($deleted) {
            return !in_array($backup['name'], $deleted);
        });

        if (count($remainingBackups) > $this->config['max_backups']) {
            $excessBackups = array_slice($remainingBackups, $this->config['max_backups']);
            foreach ($excessBackups as $backup) {
                $this->deleteBackup($backup['name']);
                $deleted[] = $backup['name'];
            }
        }

        return $deleted;
    }

    /**
     * 辅助方法
     */
    private function ensureBackupDirectory(): void
    {
        if (!File::exists($this->backupPath)) {
            File::makeDirectory($this->backupPath, 0755, true);
        }
    }

    private function getBackupSize(string $backupName): int
    {
        $path = $this->backupPath . "/{$backupName}";
        
        if (str_ends_with($backupName, '.zip')) {
            return File::size($path);
        }

        return $this->getDirectorySize($path);
    }

    private function getDirectorySize(string $directory): int
    {
        $size = 0;
        $files = File::allFiles($directory);
        
        foreach ($files as $file) {
            $size += $file->getSize();
        }

        return $size;
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    private function getTableCount(): int
    {
        return count(DB::select('SHOW TABLES'));
    }

    private function getRecordCount(): int
    {
        $tables = DB::select('SHOW TABLES');
        $totalRecords = 0;

        foreach ($tables as $table) {
            $tableName = array_values((array) $table)[0];
            $count = DB::table($tableName)->count();
            $totalRecords += $count;
        }

        return $totalRecords;
    }

    private function countFilesInDirectories(array $directories): int
    {
        $count = 0;
        
        foreach ($directories as $dir) {
            $fullPath = base_path($dir);
            if (File::isDirectory($fullPath)) {
                $count += count(File::allFiles($fullPath));
            } elseif (File::isFile($fullPath)) {
                $count++;
            }
        }

        return $count;
    }

    private function extractArchive(string $archivePath): array
    {
        $extractPath = pathinfo($archivePath, PATHINFO_DIRNAME) . '/' . pathinfo($archivePath, PATHINFO_FILENAME);
        
        $zip = new ZipArchive();
        if ($zip->open($archivePath) !== TRUE) {
            throw new \Exception('无法打开压缩包: ' . $archivePath);
        }

        if (!$zip->extractTo($extractPath)) {
            throw new \Exception('解压失败: ' . $archivePath);
        }

        $zip->close();

        return [
            'extracted_to' => $extractPath,
            'files_count' => count(File::allFiles($extractPath)),
        ];
    }

    private function findDatabaseBackup(string $backupPath): ?string
    {
        $files = File::glob($backupPath . '/database_*.sql');
        return $files[0] ?? null;
    }

    private function findFileBackup(string $backupPath): ?string
    {
        $files = File::glob($backupPath . '/files_*.tar.gz');
        return $files[0] ?? null;
    }

    private function restoreDatabase(string $sqlFile): array
    {
        $dbConfig = config('database.connections.' . config('database.default'));
        
        $command = sprintf(
            'mysql --host=%s --port=%s --user=%s --password=%s %s < %s',
            escapeshellarg($dbConfig['host']),
            escapeshellarg($dbConfig['port']),
            escapeshellarg($dbConfig['username']),
            escapeshellarg($dbConfig['password']),
            escapeshellarg($dbConfig['database']),
            escapeshellarg($sqlFile)
        );

        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            throw new \Exception('数据库恢复失败: ' . implode("\n", $output));
        }

        return [
            'sql_file' => $sqlFile,
            'status' => 'success',
        ];
    }

    private function restoreFiles(string $tarFile): array
    {
        $command = sprintf(
            'cd %s && tar -xzf %s',
            escapeshellarg(base_path()),
            escapeshellarg($tarFile)
        );

        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            throw new \Exception('文件恢复失败: ' . implode("\n", $output));
        }

        return [
            'tar_file' => $tarFile,
            'status' => 'success',
        ];
    }
}