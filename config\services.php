<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    /*
    |--------------------------------------------------------------------------
    | IP地理定位服务配置
    |--------------------------------------------------------------------------
    |
    | 支持多种IP地理定位服务，包括百度地图API、淘宝IP库、IP-API等
    | 可以根据需要启用或禁用特定的服务
    |
    */

    'ip_location' => [
        
        // 百度地图API配置（主要服务）
        'baidu' => [
            'enabled' => env('BAIDU_MAP_ENABLED', true),
            'api_key' => env('BAIDU_MAP_API_KEY'),
            'timeout' => env('BAIDU_MAP_TIMEOUT', 5),
        ],

        // 百度云IP地理位置API配置（备用服务）
        'baidu_cloud' => [
            'enabled' => env('BAIDU_CLOUD_ENABLED', true),
            'timeout' => env('BAIDU_CLOUD_TIMEOUT', 5),
        ],

        // 淘宝IP地址库配置
        'taobao' => [
            'enabled' => env('TAOBAO_IP_ENABLED', true),
            'timeout' => env('TAOBAO_IP_TIMEOUT', 3),
        ],

        // IP-API配置
        'ip_api' => [
            'enabled' => env('IP_API_ENABLED', true),
            'timeout' => env('IP_API_TIMEOUT', 3),
        ],

        // 缓存配置
        'cache' => [
            'enabled' => env('IP_LOCATION_CACHE_ENABLED', true),
            'ttl' => env('IP_LOCATION_CACHE_TTL', 3600), // 1小时
        ],

        // 回退配置
        'fallback' => [
            'default_city' => env('IP_LOCATION_DEFAULT_CITY', '本地'),
            'common_cities' => [
                '北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都',
                '重庆', '天津', '西安', '苏州', '长沙', '沈阳', '青岛', '郑州'
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 错误通知服务配置
    |--------------------------------------------------------------------------
    |
    | 配置系统错误通知的各种渠道，包括邮件、钉钉、企业微信等
    |
    */
    'error_notification' => [
        'email' => env('ERROR_NOTIFICATION_EMAIL'),
        'dingtalk' => env('ERROR_NOTIFICATION_DINGTALK_ENABLED', false),
        'dingtalk_webhook' => env('ERROR_NOTIFICATION_DINGTALK_WEBHOOK'),
        'wechat' => env('ERROR_NOTIFICATION_WECHAT_ENABLED', false),
        'wechat_webhook' => env('ERROR_NOTIFICATION_WECHAT_WEBHOOK'),
    ],

    /*
    |--------------------------------------------------------------------------
    | 支付网关服务配置
    |--------------------------------------------------------------------------
    |
    | 配置外部支付网关服务的连接信息
    |
    */
    'payment' => [
        'endpoint' => env('PAYMENT_GATEWAY_ENDPOINT'),
        'timeout' => env('PAYMENT_GATEWAY_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | 通知服务配置
    |--------------------------------------------------------------------------
    |
    | 配置外部通知服务的连接信息
    |
    */
    'notification' => [
        'endpoint' => env('NOTIFICATION_SERVICE_ENDPOINT'),
        'timeout' => env('NOTIFICATION_SERVICE_TIMEOUT', 10),
    ],

]; 