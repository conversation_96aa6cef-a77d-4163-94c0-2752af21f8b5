/* empty css             *//* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                        *//* empty css                         *//* empty css                *//* empty css               */import{z as e,m as a,A as t,G as l,q as s,E as o,K as r,r as i,c as n,M as d,w as u,n as c,B as p,l as m,F as g,Y as _,C as f,W as h,o as v}from"./vue-vendor-BcnDv-68.js";import{a3 as y,a4 as b,a5 as k,a6 as w,Z as C,a0 as V,U as D,Y as x,aq as S,_ as U,aL as j,a2 as R,a1 as q,W as T,X as L,a7 as Q,$ as z,V as A,u as $,ai as M,aj as O,cj as E,ah as I,b6 as P,b7 as Y,aZ as B,ck as F,aO as G,ax as H,av as N,ad as W,bB as Z,ae as K,ac as J,b9 as X,a_ as ee,aE as ae,af as te,aF as le,b1 as se,a9 as oe,bt as re,ag as ie,aW as ne,ak as de,aH as ue,aI as ce,aJ as pe,an as me}from"./element-plus-C2UshkXo.js";import{S as ge}from"./StatCard-CLi1H67J.js";/* empty css                   *//* empty css                     *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                       */import{s as _e,_ as fe}from"./index-eUTsTR3J.js";/* empty css                  *//* empty css                        *//* empty css                 *//* empty css                          */import{f as he}from"./format-3eU4VJ9V.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const ve={getList:(e={})=>_e({url:"/admin/promotions",method:"get",params:e}),getDetail:e=>_e({url:`/admin/promotions/${e}`,method:"get"}),create:e=>_e({url:"/admin/promotions",method:"post",data:e}),update:(e,a)=>_e({url:`/admin/promotions/${e}`,method:"put",data:a}),delete:e=>_e({url:`/admin/promotions/${e}`,method:"delete"}),batchAction:e=>_e({url:"/admin/promotions/batch-action",method:"post",data:e}),duplicate:(e,a={})=>_e({url:`/admin/promotions/${e}/duplicate`,method:"post",data:a}),regenerateShortCode:e=>_e({url:`/admin/promotions/${e}/regenerate-code`,method:"post"}),generateMaterials:e=>_e({url:"/admin/promotions/generate-materials",method:"post",data:e}),getStats:(e={})=>_e({url:"/admin/promotions/stats",method:"get",params:e}),getExpiringLinks:(e=7)=>_e({url:"/admin/promotions/expiring-links",method:"get",params:{days:e}}),cleanupExpired:()=>_e({url:"/admin/promotions/cleanup-expired",method:"post"})},ye={getInfo:e=>_e({url:`/short-links/${e}/info`,method:"get"}),preview:e=>_e({url:`/short-links/${e}/preview`,method:"get"}),getStats:(e,a={})=>_e({url:`/short-links/${e}/stats`,method:"get",params:a}),generateQRCode:(e,a={})=>_e({url:`/short-links/${e}/qrcode`,method:"get",params:a,responseType:"blob"}),batchCheck:e=>_e({url:"/short-links/batch-check",method:"post",data:{short_codes:e}}),getPopular:(e={})=>_e({url:"/short-links/popular",method:"get",params:e}),healthCheck:()=>_e({url:"/short-links/health-check",method:"get"})},be={getClickTrend:(e={})=>_e({url:"/admin/promotions/analytics/click-trend",method:"get",params:e}),getSourceAnalysis:(e={})=>_e({url:"/admin/promotions/analytics/source-analysis",method:"get",params:e}),getRegionAnalysis:(e={})=>_e({url:"/admin/promotions/analytics/region-analysis",method:"get",params:e}),getDeviceAnalysis:(e={})=>_e({url:"/admin/promotions/analytics/device-analysis",method:"get",params:e}),getConversionFunnel:(e={})=>_e({url:"/admin/promotions/analytics/conversion-funnel",method:"get",params:e}),exportReport:(e={})=>_e({url:"/admin/promotions/analytics/export-report",method:"post",data:e,responseType:"blob"})},ke={__name:"LinkDialog",props:{modelValue:{type:Boolean,default:!1},linkData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{expose:a,emit:t}){a();const l=e,s=t,o=i(),r=i(!1),p=n({get:()=>l.modelValue,set:e=>s("update:modelValue",e)}),m=n(()=>l.linkData&&l.linkData.id),g=d({name:"",target_url:"",type:"",custom_code:"",expires_at:"",max_clicks:0,daily_limit:0,status:"active",description:""});u(()=>l.modelValue,e=>{e&&c(()=>{_()})});const _=()=>{m.value?Object.keys(g).forEach(e=>{void 0!==l.linkData[e]&&(g[e]=l.linkData[e])}):Object.keys(g).forEach(e=>{g[e]="status"===e?"active":"max_clicks"===e||"daily_limit"===e?0:""}),o.value&&o.value.clearValidate()},f=()=>{p.value=!1},h={props:l,emit:s,formRef:o,loading:r,baseUrl:"https://short.example.com",visible:p,isEdit:m,formData:g,formRules:{name:[{required:!0,message:"请输入链接名称",trigger:"blur"},{min:2,max:50,message:"链接名称长度在 2 到 50 个字符",trigger:"blur"}],target_url:[{required:!0,message:"请输入目标URL",trigger:"blur"},{pattern:/^https?:\/\/.+/,message:"请输入有效的URL地址",trigger:"blur"}],type:[{required:!0,message:"请选择链接类型",trigger:"change"}],custom_code:[{pattern:/^[a-zA-Z0-9-]*$/,message:"自定义短码只能包含字母、数字和连字符",trigger:"blur"}]},initFormData:_,handleSubmit:async()=>{if(o.value)try{await o.value.validate(),r.value=!0;const e={...g};e.expires_at||delete e.expires_at,e.custom_code||delete e.custom_code,e.description||delete e.description,m.value?(await ve.update(l.linkData.id,e),L.success("推广链接更新成功")):(await ve.create(e),L.success("推广链接创建成功")),s("success"),f()}catch(e){e.response?.data?.message?L.error(e.response.data.message):L.error(m.value?"更新失败":"创建失败")}finally{r.value=!1}},handleClose:f,ref:i,reactive:d,computed:n,watch:u,nextTick:c,get ElMessage(){return L},get promotionApi(){return ve}};return Object.defineProperty(h,"__isScriptSetup",{enumerable:!1,value:!0}),h}},we={class:"dialog-footer"};const Ce=fe(ke,[["render",function(i,n,d,u,c,p){const m=b,g=y,_=w,f=k,h=V,v=S,L=x,Q=D,z=j,A=U,$=R,M=q,O=T;return a(),e(O,{modelValue:u.visible,"onUpdate:modelValue":n[10]||(n[10]=e=>u.visible=e),title:u.isEdit?"编辑推广链接":"创建推广链接",width:"600px","before-close":u.handleClose},{footer:t(()=>[s("div",we,[l(M,{onClick:u.handleClose},{default:t(()=>n[16]||(n[16]=[o("取消",-1)])),_:1,__:[16]}),l(M,{type:"primary",loading:u.loading,onClick:u.handleSubmit},{default:t(()=>[o(C(u.isEdit?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:t(()=>[l($,{ref:"formRef",model:u.formData,rules:u.formRules,"label-width":"100px",onSubmit:n[9]||(n[9]=r(()=>{},["prevent"]))},{default:t(()=>[l(g,{label:"链接名称",prop:"name"},{default:t(()=>[l(m,{modelValue:u.formData.name,"onUpdate:modelValue":n[0]||(n[0]=e=>u.formData.name=e),placeholder:"请输入链接名称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(g,{label:"目标URL",prop:"target_url"},{default:t(()=>[l(m,{modelValue:u.formData.target_url,"onUpdate:modelValue":n[1]||(n[1]=e=>u.formData.target_url=e),placeholder:"请输入目标URL，如：https://example.com",type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),l(g,{label:"链接类型",prop:"type"},{default:t(()=>[l(f,{modelValue:u.formData.type,"onUpdate:modelValue":n[2]||(n[2]=e=>u.formData.type=e),placeholder:"请选择链接类型",style:{width:"100%"}},{default:t(()=>[l(_,{label:"群组推广",value:"group"}),l(_,{label:"分销推广",value:"distribution"}),l(_,{label:"活动推广",value:"activity"}),l(_,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),l(g,{label:"自定义短码",prop:"custom_code"},{default:t(()=>[l(m,{modelValue:u.formData.custom_code,"onUpdate:modelValue":n[3]||(n[3]=e=>u.formData.custom_code=e),placeholder:"留空则自动生成，支持字母数字组合",maxlength:"20"},{prepend:t(()=>[s("span",null,C(u.baseUrl)+"/")]),_:1},8,["modelValue"]),n[11]||(n[11]=s("div",{class:"form-help"},[s("i",{class:"el-icon-info"}),o(" 自定义短码必须唯一，支持字母、数字和连字符 ")],-1))]),_:1,__:[11]}),l(g,{label:"过期时间",prop:"expires_at"},{default:t(()=>[l(h,{modelValue:u.formData.expires_at,"onUpdate:modelValue":n[4]||(n[4]=e=>u.formData.expires_at=e),type:"datetime",placeholder:"选择过期时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"]),n[12]||(n[12]=s("div",{class:"form-help"},[s("i",{class:"el-icon-info"}),o(" 留空表示永不过期 ")],-1))]),_:1,__:[12]}),l(g,{label:"访问限制"},{default:t(()=>[l(Q,{gutter:16},{default:t(()=>[l(L,{span:12},{default:t(()=>[l(g,{prop:"max_clicks"},{default:t(()=>[l(v,{modelValue:u.formData.max_clicks,"onUpdate:modelValue":n[5]||(n[5]=e=>u.formData.max_clicks=e),min:0,placeholder:"最大点击次数",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),l(L,{span:12},{default:t(()=>[l(g,{prop:"daily_limit"},{default:t(()=>[l(v,{modelValue:u.formData.daily_limit,"onUpdate:modelValue":n[6]||(n[6]=e=>u.formData.daily_limit=e),min:0,placeholder:"每日访问限制",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),n[13]||(n[13]=s("div",{class:"form-help"},[s("i",{class:"el-icon-info"}),o(" 设置为0表示无限制 ")],-1))]),_:1,__:[13]}),l(g,{label:"状态",prop:"status"},{default:t(()=>[l(A,{modelValue:u.formData.status,"onUpdate:modelValue":n[7]||(n[7]=e=>u.formData.status=e)},{default:t(()=>[l(z,{label:"active"},{default:t(()=>n[14]||(n[14]=[o("启用",-1)])),_:1,__:[14]}),l(z,{label:"paused"},{default:t(()=>n[15]||(n[15]=[o("暂停",-1)])),_:1,__:[15]})]),_:1},8,["modelValue"])]),_:1}),l(g,{label:"备注",prop:"description"},{default:t(()=>[l(m,{modelValue:u.formData.description,"onUpdate:modelValue":n[8]||(n[8]=e=>u.formData.description=e),type:"textarea",rows:3,placeholder:"请输入备注信息",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}],["__scopeId","data-v-83316fde"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/promotion/components/LinkDialog.vue"]]),Ve={__name:"LinkAnalyticsDrawer",props:{modelValue:{type:Boolean,default:!1},linkId:{type:[Number,String],default:null}},emits:["update:modelValue"],setup(e,{expose:a,emit:t}){a();const l=e,s=t,o=i(!1),r=i("clicks"),c=i([]),p=n({get:()=>l.modelValue,set:e=>s("update:modelValue",e)}),m=d({total_clicks:0,unique_visitors:0,conversions:0}),g=i([]),_=i([]),f=i([]),h=i([]),v=n(()=>0===m.total_clicks?"0.0":(m.conversions/m.total_clicks*100).toFixed(1));u(()=>l.modelValue,e=>{e&&l.linkId&&(y(),b())});const y=()=>{const e=new Date,a=new Date;a.setDate(e.getDate()-7),c.value=[a.toISOString().split("T")[0],e.toISOString().split("T")[0]]},b=async()=>{if(l.linkId){o.value=!0;try{const e={start_date:c.value[0],end_date:c.value[1]},a=await be.getClickTrend({link_id:l.linkId,...e});m.total_clicks=a.data.total_clicks||0,m.unique_visitors=a.data.unique_visitors||0;const t=await be.getConversionFunnel({link_id:l.linkId,...e});m.conversions=t.data.total_conversions||0,k(),w(),C(),V()}catch(e){L.error("加载统计数据失败")}finally{o.value=!1}}},k=()=>{g.value=[];for(let e=6;e>=0;e--){const a=new Date;a.setDate(a.getDate()-e),g.value.push({date:a.toISOString().split("T")[0],value:Math.floor(100*Math.random())+10})}},w=()=>{_.value=[{name:"微信",count:156,percentage:45},{name:"QQ",count:89,percentage:26},{name:"直接访问",count:67,percentage:19},{name:"其他",count:34,percentage:10}]},C=()=>{f.value=[{name:"广东",count:89,percentage:32},{name:"浙江",count:67,percentage:24},{name:"江苏",count:45,percentage:16},{name:"上海",count:23,percentage:8},{name:"其他",count:56,percentage:20}]},V=()=>{h.value=[{ip:"***********",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",referer:"https://example.com",location:"广东深圳",created_at:(new Date).toISOString()},{ip:"***********",user_agent:"Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)",referer:"https://weixin.qq.com",location:"浙江杭州",created_at:new Date(Date.now()-36e5).toISOString()}]},D={props:l,emit:s,loading:o,chartType:r,dateRange:c,visible:p,analytics:m,chartData:g,sourceStats:_,regionStats:f,recentRecords:h,conversionRate:v,initDateRange:y,loadAnalytics:b,generateChartData:k,generateSourceStats:w,generateRegionStats:C,generateRecentRecords:V,updateChart:()=>{k()},getChartTitle:()=>({clicks:"点击量趋势",visitors:"访客数趋势",conversions:"转化数趋势"}[r.value]||"数据趋势"),exportData:()=>{L.success("数据导出功能开发中...")},truncateText:(e,a)=>e?e.length>a?e.substring(0,a)+"...":e:"",formatDateTime:e=>e?new Date(e).toLocaleString("zh-CN"):"",handleClose:()=>{p.value=!1},ref:i,reactive:d,computed:n,watch:u,get ElMessage(){return L},get promotionAnalyticsApi(){return be}};return Object.defineProperty(D,"__isScriptSetup",{enumerable:!1,value:!0}),D}},De={class:"analytics-container"},xe={class:"stats-overview"},Se={class:"stat-item"},Ue={class:"stat-value"},je={class:"stat-item"},Re={class:"stat-value"},qe={class:"stat-item"},Te={class:"stat-value"},Le={class:"stat-item"},Qe={class:"stat-value"},ze={class:"filter-section"},Ae={class:"card-header"},$e={class:"chart-container"},Me={key:0,class:"chart-placeholder"},Oe={key:1,class:"trend-chart"},Ee={class:"chart-demo"},Ie={class:"chart-data"},Pe={class:"date"},Ye={class:"value"},Be={class:"source-stats"},Fe={class:"source-info"},Ge={class:"source-name"},He={class:"source-count"},Ne={class:"source-bar"},We={class:"source-percentage"},Ze={class:"region-stats"},Ke={class:"region-info"},Je={class:"region-name"},Xe={class:"region-count"},ea={class:"region-bar"},aa={class:"region-percentage"},ta=["title"],la=["title"],sa={class:"drawer-footer"};const oa=fe(Ve,[["render",function(r,i,n,d,u,c){const f=x,h=D,v=V,y=q,b=z,k=U,w=A,S=O,j=M,R=E,T=Q;return a(),e(R,{modelValue:d.visible,"onUpdate:modelValue":i[2]||(i[2]=e=>d.visible=e),title:"链接统计分析",size:"60%",direction:"rtl","before-close":d.handleClose},{footer:t(()=>[s("div",sa,[l(y,{onClick:d.exportData},{default:t(()=>i[16]||(i[16]=[o("导出数据",-1)])),_:1,__:[16]}),l(y,{type:"primary",onClick:d.handleClose},{default:t(()=>i[17]||(i[17]=[o("关闭",-1)])),_:1,__:[17]})])]),default:t(()=>[p((a(),m("div",De,[s("div",xe,[l(h,{gutter:16},{default:t(()=>[l(f,{span:6},{default:t(()=>[s("div",Se,[s("div",Ue,C(d.analytics.total_clicks||0),1),i[3]||(i[3]=s("div",{class:"stat-label"},"总点击量",-1))])]),_:1}),l(f,{span:6},{default:t(()=>[s("div",je,[s("div",Re,C(d.analytics.unique_visitors||0),1),i[4]||(i[4]=s("div",{class:"stat-label"},"独立访客",-1))])]),_:1}),l(f,{span:6},{default:t(()=>[s("div",qe,[s("div",Te,C(d.analytics.conversions||0),1),i[5]||(i[5]=s("div",{class:"stat-label"},"转化次数",-1))])]),_:1}),l(f,{span:6},{default:t(()=>[s("div",Le,[s("div",Qe,C(d.conversionRate)+"%",1),i[6]||(i[6]=s("div",{class:"stat-label"},"转化率",-1))])]),_:1})]),_:1})]),s("div",ze,[l(v,{modelValue:d.dateRange,"onUpdate:modelValue":i[0]||(i[0]=e=>d.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:d.loadAnalytics},null,8,["modelValue"]),l(y,{type:"primary",onClick:d.loadAnalytics},{default:t(()=>i[7]||(i[7]=[o("刷新数据",-1)])),_:1,__:[7]})]),l(w,{class:"chart-card"},{header:t(()=>[s("div",Ae,[i[11]||(i[11]=s("span",null,"📈 访问趋势",-1)),l(k,{modelValue:d.chartType,"onUpdate:modelValue":i[1]||(i[1]=e=>d.chartType=e),size:"small",onChange:d.updateChart},{default:t(()=>[l(b,{label:"clicks"},{default:t(()=>i[8]||(i[8]=[o("点击量",-1)])),_:1,__:[8]}),l(b,{label:"visitors"},{default:t(()=>i[9]||(i[9]=[o("访客数",-1)])),_:1,__:[9]}),l(b,{label:"conversions"},{default:t(()=>i[10]||(i[10]=[o("转化数",-1)])),_:1,__:[10]})]),_:1},8,["modelValue"])])]),default:t(()=>[s("div",$e,[d.chartData.length?(a(),m("div",Oe,[s("div",Ee,[s("h4",null,C(d.getChartTitle()),1),s("div",Ie,[(a(!0),m(g,null,_(d.chartData,(e,t)=>(a(),m("div",{key:t,class:"data-point"},[s("span",Pe,C(e.date),1),s("span",Ye,C(e.value),1)]))),128))])])])):(a(),m("div",Me,i[12]||(i[12]=[s("i",{class:"el-icon-loading"},null,-1),s("p",null,"加载中...",-1)])))])]),_:1}),l(h,{gutter:16},{default:t(()=>[l(f,{span:12},{default:t(()=>[l(w,{class:"data-card"},{header:t(()=>i[13]||(i[13]=[s("span",null,"📱 访问来源",-1)])),default:t(()=>[s("div",Be,[(a(!0),m(g,null,_(d.sourceStats,e=>(a(),m("div",{key:e.name,class:"source-item"},[s("div",Fe,[s("span",Ge,C(e.name),1),s("span",He,C(e.count),1)]),s("div",Ne,[s("div",{class:"source-fill",style:$({width:e.percentage+"%"})},null,4)]),s("span",We,C(e.percentage)+"%",1)]))),128))])]),_:1})]),_:1}),l(f,{span:12},{default:t(()=>[l(w,{class:"data-card"},{header:t(()=>i[14]||(i[14]=[s("span",null,"🌍 地区分布",-1)])),default:t(()=>[s("div",Ze,[(a(!0),m(g,null,_(d.regionStats,e=>(a(),m("div",{key:e.name,class:"region-item"},[s("div",Ke,[s("span",Je,C(e.name),1),s("span",Xe,C(e.count),1)]),s("div",ea,[s("div",{class:"region-fill",style:$({width:e.percentage+"%"})},null,4)]),s("span",aa,C(e.percentage)+"%",1)]))),128))])]),_:1})]),_:1})]),_:1}),l(w,{class:"records-card"},{header:t(()=>i[15]||(i[15]=[s("span",null,"📋 最近访问记录",-1)])),default:t(()=>[l(j,{data:d.recentRecords,style:{width:"100%"}},{default:t(()=>[l(S,{prop:"ip",label:"IP地址",width:"120"}),l(S,{prop:"user_agent",label:"用户代理","min-width":"200"},{default:t(({row:e})=>[s("div",{class:"user-agent",title:e.user_agent},C(d.truncateText(e.user_agent,50)),9,ta)]),_:1}),l(S,{prop:"referer",label:"来源页面",width:"150"},{default:t(({row:e})=>[s("div",{class:"referer",title:e.referer},C(d.truncateText(e.referer,30)),9,la)]),_:1}),l(S,{prop:"location",label:"地区",width:"100"}),l(S,{prop:"created_at",label:"访问时间",width:"160"},{default:t(({row:e})=>[o(C(d.formatDateTime(e.created_at)),1)]),_:1})]),_:1},8,["data"])]),_:1})])),[[T,d.loading]])]),_:1},8,["modelValue"])}],["__scopeId","data-v-049ea066"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/promotion/components/LinkAnalyticsDrawer.vue"]]),ra={__name:"QRCodeDialog",props:{modelValue:{type:Boolean,default:!1},link:{type:Object,default:null}},emits:["update:modelValue"],setup(e,{expose:a,emit:t}){a();const l=e,s=t,o=i(!1),r=i(!1),c=i(""),p=i([200,300,500]),m=d({size:300,margin:2,format:"png",errorCorrection:"M"}),g=n({get:()=>l.modelValue,set:e=>s("update:modelValue",e)}),_=async()=>{if(l.link){o.value=!0;try{const e=await ye.generateQRCode(l.link.short_code,m),a=new Blob([e],{type:`image/${m.format}`});c.value=URL.createObjectURL(a),L.success("二维码生成成功")}catch(e){L.error("生成二维码失败："+e.message)}finally{o.value=!1}}};u(()=>l.modelValue,e=>{e&&l.link?(c.value="",_()):e||c.value&&(URL.revokeObjectURL(c.value),c.value="")});const f={props:l,emit:s,generating:o,batchGenerating:r,qrCodeUrl:c,batchSizes:p,qrConfig:m,dialogVisible:g,generateQRCode:_,batchGenerate:async()=>{if(l.link&&0!==p.value.length){r.value=!0;try{const e=p.value.map(async e=>{const a={...m,size:e},t=await ye.generateQRCode(l.link.short_code,a);return{size:e,blob:new Blob([t],{type:`image/${m.format}`}),filename:`qrcode_${l.link.short_code}_${e}x${e}.${m.format}`}}),a=await Promise.all(e);a.forEach(({blob:e,filename:a})=>{const t=URL.createObjectURL(e),l=document.createElement("a");l.href=t,l.download=a,l.click(),URL.revokeObjectURL(t)}),L.success(`成功生成并下载 ${a.length} 个二维码`)}catch(e){L.error("批量生成失败："+e.message)}finally{r.value=!1}}},downloadQRCode:()=>{if(!c.value)return;const e=document.createElement("a");e.href=c.value,e.download=`qrcode_${l.link.short_code}_${m.size}x${m.size}.${m.format}`,e.click(),L.success("二维码下载成功")},copyQRCodeUrl:async()=>{if(l.link)try{await navigator.clipboard.writeText(l.link.short_url),L.success("链接已复制到剪贴板")}catch(e){L.error("复制失败")}},ref:i,reactive:d,computed:n,watch:u,get ElMessage(){return L},get Setting(){return X},get View(){return J},get Refresh(){return K},get Loading(){return Z},get Picture(){return W},get Grid(){return N},get Download(){return H},get QuestionFilled(){return G},get CopyDocument(){return F},get shortLinkApi(){return ye}};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}},ia={key:0,class:"qrcode-content"},na={class:"card-header"},da={class:"card-header"},ua={class:"header-actions"},ca={class:"preview-container"},pa={key:0,class:"loading-container"},ma={key:1,class:"qrcode-preview"},ga=["src","alt"],_a={class:"qrcode-info"},fa={class:"qrcode-title"},ha={class:"qrcode-url"},va={class:"qrcode-size"},ya={key:2,class:"empty-container"},ba={class:"card-header"},ka={class:"batch-options"},wa={class:"card-header"},Ca={class:"tips-content"},Va={class:"dialog-footer"};const Da=fe(ra,[["render",function(r,i,n,d,u,c){const p=I,g=w,_=k,h=y,v=x,b=D,V=R,S=A,U=q,j=Y,L=P,Q=B,z=T;return a(),e(z,{modelValue:d.dialogVisible,"onUpdate:modelValue":i[6]||(i[6]=e=>d.dialogVisible=e),title:"二维码生成",width:"600px","close-on-click-modal":!1},{footer:t(()=>[s("div",Va,[l(U,{onClick:i[5]||(i[5]=e=>d.dialogVisible=!1)},{default:t(()=>i[20]||(i[20]=[o("关闭",-1)])),_:1,__:[20]}),l(U,{type:"primary",disabled:!d.qrCodeUrl,onClick:d.downloadQRCode},{default:t(()=>[l(p,null,{default:t(()=>[l(d.Download)]),_:1}),i[21]||(i[21]=o(" 下载二维码 ",-1))]),_:1,__:[21]},8,["disabled"]),l(U,{type:"success",disabled:!d.qrCodeUrl,onClick:d.copyQRCodeUrl},{default:t(()=>[l(p,null,{default:t(()=>[l(d.CopyDocument)]),_:1}),i[22]||(i[22]=o(" 复制链接 ",-1))]),_:1,__:[22]},8,["disabled"])])]),default:t(()=>[n.link?(a(),m("div",ia,[l(S,{class:"config-card",shadow:"never"},{header:t(()=>[s("div",na,[l(p,null,{default:t(()=>[l(d.Setting)]),_:1}),i[7]||(i[7]=s("span",null,"二维码配置",-1))])]),default:t(()=>[l(V,{model:d.qrConfig,"label-width":"100px"},{default:t(()=>[l(b,{gutter:20},{default:t(()=>[l(v,{span:12},{default:t(()=>[l(h,{label:"尺寸大小"},{default:t(()=>[l(_,{modelValue:d.qrConfig.size,"onUpdate:modelValue":i[0]||(i[0]=e=>d.qrConfig.size=e),onChange:d.generateQRCode},{default:t(()=>[l(g,{label:"小 (200x200)",value:200}),l(g,{label:"中 (300x300)",value:300}),l(g,{label:"大 (500x500)",value:500}),l(g,{label:"超大 (800x800)",value:800})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:t(()=>[l(h,{label:"边距"},{default:t(()=>[l(_,{modelValue:d.qrConfig.margin,"onUpdate:modelValue":i[1]||(i[1]=e=>d.qrConfig.margin=e),onChange:d.generateQRCode},{default:t(()=>[l(g,{label:"无边距",value:0}),l(g,{label:"小边距",value:1}),l(g,{label:"标准边距",value:2}),l(g,{label:"大边距",value:4})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(b,{gutter:20},{default:t(()=>[l(v,{span:12},{default:t(()=>[l(h,{label:"格式"},{default:t(()=>[l(_,{modelValue:d.qrConfig.format,"onUpdate:modelValue":i[2]||(i[2]=e=>d.qrConfig.format=e),onChange:d.generateQRCode},{default:t(()=>[l(g,{label:"PNG",value:"png"}),l(g,{label:"JPG",value:"jpg"}),l(g,{label:"SVG",value:"svg"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:t(()=>[l(h,{label:"容错级别"},{default:t(()=>[l(_,{modelValue:d.qrConfig.errorCorrection,"onUpdate:modelValue":i[3]||(i[3]=e=>d.qrConfig.errorCorrection=e),onChange:d.generateQRCode},{default:t(()=>[l(g,{label:"低 (L)",value:"L"}),l(g,{label:"中 (M)",value:"M"}),l(g,{label:"高 (Q)",value:"Q"}),l(g,{label:"最高 (H)",value:"H"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l(S,{class:"preview-card",shadow:"never"},{header:t(()=>[s("div",da,[l(p,null,{default:t(()=>[l(d.View)]),_:1}),i[9]||(i[9]=s("span",null,"二维码预览",-1)),s("div",ua,[l(U,{type:"primary",size:"small",loading:d.generating,onClick:d.generateQRCode},{default:t(()=>[l(p,null,{default:t(()=>[l(d.Refresh)]),_:1}),i[8]||(i[8]=o(" 重新生成 ",-1))]),_:1,__:[8]},8,["loading"])])])]),default:t(()=>[s("div",ca,[d.generating?(a(),m("div",pa,[l(p,{class:"is-loading"},{default:t(()=>[l(d.Loading)]),_:1}),i[10]||(i[10]=s("p",null,"正在生成二维码...",-1))])):d.qrCodeUrl?(a(),m("div",ma,[s("img",{src:d.qrCodeUrl,alt:`${n.link.title}的二维码`,class:"qrcode-image"},null,8,ga),s("div",_a,[s("p",fa,C(n.link.title||"推广链接"),1),s("p",ha,C(n.link.short_url),1),s("p",va,C(d.qrConfig.size)+"x"+C(d.qrConfig.size)+"px",1)])])):(a(),m("div",ya,[l(p,null,{default:t(()=>[l(d.Picture)]),_:1}),i[11]||(i[11]=s("p",null,"点击生成二维码",-1))]))])]),_:1}),l(S,{class:"batch-card",shadow:"never"},{header:t(()=>[s("div",ba,[l(p,null,{default:t(()=>[l(d.Grid)]),_:1}),i[12]||(i[12]=s("span",null,"批量生成",-1))])]),default:t(()=>[s("div",ka,[l(L,{modelValue:d.batchSizes,"onUpdate:modelValue":i[4]||(i[4]=e=>d.batchSizes=e)},{default:t(()=>[l(j,{label:200},{default:t(()=>i[13]||(i[13]=[o("小尺寸 (200x200)",-1)])),_:1,__:[13]}),l(j,{label:300},{default:t(()=>i[14]||(i[14]=[o("中尺寸 (300x300)",-1)])),_:1,__:[14]}),l(j,{label:500},{default:t(()=>i[15]||(i[15]=[o("大尺寸 (500x500)",-1)])),_:1,__:[15]}),l(j,{label:800},{default:t(()=>i[16]||(i[16]=[o("超大尺寸 (800x800)",-1)])),_:1,__:[16]})]),_:1},8,["modelValue"]),l(U,{type:"success",loading:d.batchGenerating,disabled:0===d.batchSizes.length,onClick:d.batchGenerate,style:{"margin-top":"15px"}},{default:t(()=>[l(p,null,{default:t(()=>[l(d.Download)]),_:1}),i[17]||(i[17]=o(" 批量生成并下载 ",-1))]),_:1,__:[17]},8,["loading","disabled"])])]),_:1}),l(S,{class:"tips-card",shadow:"never"},{header:t(()=>[s("div",wa,[l(p,null,{default:t(()=>[l(d.QuestionFilled)]),_:1}),i[18]||(i[18]=s("span",null,"使用说明",-1))])]),default:t(()=>[s("div",Ca,[l(Q,{title:"二维码使用提示",type:"info",closable:!1,"show-icon":""},{default:t(()=>i[19]||(i[19]=[s("ul",{class:"tips-list"},[s("li",null,"二维码扫描后将跳转到推广链接"),s("li",null,"建议使用PNG格式以获得最佳质量"),s("li",null,"容错级别越高，二维码越复杂但容错能力越强"),s("li",null,"适当的边距可以提高扫描成功率"),s("li",null,"可以将二维码保存到本地或直接分享使用")],-1)])),_:1,__:[19]})])]),_:1})])):f("",!0)]),_:1},8,["modelValue"])}],["__scopeId","data-v-48eafd29"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/promotion/components/QRCodeDialog.vue"]]),xa={class:"app-container"},Sa={class:"filter-container"},Ua={class:"card-header"},ja={class:"link-info"},Ra={class:"link-name"},qa={class:"link-url"},Ta=["title"],La={class:"click-stats"},Qa={class:"total-clicks"},za={class:"today-clicks"},Aa={class:"conversion-stats"},$a={class:"conversions"},Ma={class:"conversion-rate"},Oa={class:"pagination-container"};const Ea=fe({__name:"LinkManagement",setup(e,{expose:a}){a();const t=i([]),l=i(0),s=i(!0),o=i(!1),r=i(!1),n=i(!1),u=i({}),c=i(null),p=i([]),m=i({total_links:0,total_clicks:0,unique_visitors:0,conversion_rate:0}),g=d({page:1,limit:20,keyword:"",type:"",status:""}),_=async()=>{s.value=!0;try{const{data:e}=await ve.getList(g);t.value=e.list,l.value=e.total}catch(e){L.error("获取推广链接列表失败")}finally{s.value=!1}},f=async()=>{try{const{data:e}=await ve.getStats();m.value=e}catch(e){}},h=async e=>{try{await navigator.clipboard.writeText(e),L.success("链接已复制到剪贴板")}catch(a){L.error("复制失败")}},y=async(e,a)=>{try{const t="paused"===a?"暂停":"恢复";await ie.confirm(`确定要${t}这个链接吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await ve.update(e,{status:a}),L.success(`${t}成功`),_()}catch(t){"cancel"!==t&&L.error("操作失败")}},b=async e=>{try{await ie.confirm("确定要删除这个推广链接吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await ve.delete(e),L.success("删除成功"),_()}catch(a){"cancel"!==a&&L.error("删除失败")}},k=async e=>{try{p.value.map(e=>e.id);L.success("批量操作成功"),_()}catch(a){L.error("批量操作失败")}};v(()=>{_(),f()});const w={list:t,total:l,listLoading:s,dialogVisible:o,analyticsDrawerVisible:r,qrcodeDialogVisible:n,currentLink:u,currentLinkId:c,multipleSelection:p,stats:m,listQuery:g,getList:_,getStats:f,handleFilter:()=>{g.page=1,_()},handleCreate:()=>{u.value={},o.value=!0},handleEdit:e=>{u.value={...e},o.value=!0},handleAnalytics:e=>{c.value=e.id,r.value=!0},handleQRCode:e=>{u.value={...e},n.value=!0},copyToClipboard:h,handleUpdateStatus:y,handleDelete:b,handleCommand:e=>{const[a,l]=e.split("-"),s=parseInt(l),o=t.value.find(e=>e.id===s);switch(a){case"qrcode":u.value={...o},n.value=!0;break;case"copy":h(o.short_url);break;case"pause":y(s,"paused");break;case"resume":y(s,"active");break;case"delete":b(s)}},handleExport:async()=>{try{L.success("导出成功")}catch(e){L.error("导出失败")}},handleBatchOperation:()=>{0!==p.value.length?ie.confirm("请选择批量操作类型","批量操作",{distinguishCancelAndClose:!0,confirmButtonText:"批量暂停",cancelButtonText:"批量恢复"}).then(()=>{k()}).catch(e=>{"cancel"===e&&k()}):L.warning("请先选择要操作的链接")},batchUpdateStatus:k,handleSelectionChange:e=>{p.value=e},handleSizeChange:e=>{g.limit=e,_()},handleCurrentChange:e=>{g.page=e,_()},handleDialogSuccess:()=>{_(),f()},navigateToList:()=>{_()},getTypeTagType:e=>({group:"primary",distribution:"success",activity:"warning",other:"info"}[e]||"info"),getTypeText:e=>({group:"群组推广",distribution:"分销推广",activity:"活动推广",other:"其他"}[e]||"未知"),getStatusTagType:e=>({active:"success",paused:"warning",expired:"danger"}[e]||"info"),getStatusText:e=>({active:"活跃",paused:"暂停",expired:"过期"}[e]||"未知"),truncateUrl:e=>e?e.length>30?e.substring(0,30)+"...":e:"",ref:i,onMounted:v,reactive:d,get ElMessage(){return L},get ElMessageBox(){return ie},get Link(){return re},get View(){return J},get User(){return oe},get TrendCharts(){return se},get Search(){return le},get Plus(){return te},get Download(){return H},get ArrowDown(){return ae},get DocumentCopy(){return ee},StatCard:ge,LinkDialog:Ce,LinkAnalyticsDrawer:oa,QRCodeDialog:Da,get promotionApi(){return ve},get promotionAnalyticsApi(){return be},get formatDate(){return he}};return Object.defineProperty(w,"__isScriptSetup",{enumerable:!1,value:!0}),w}},[["render",function(r,i,n,d,u,c){const g=b,_=w,v=k,y=q,V=x,S=D,U=O,j=ne,R=I,T=de,L=pe,z=ce,$=ue,E=M,P=me,Y=A,B=Q;return a(),m("div",xa,[s("div",Sa,[l(g,{modelValue:d.listQuery.keyword,"onUpdate:modelValue":i[0]||(i[0]=e=>d.listQuery.keyword=e),placeholder:"搜索链接名称、目标URL",style:{width:"200px"},class:"filter-item",onKeyup:h(d.handleFilter,["enter"]),clearable:""},null,8,["modelValue"]),l(v,{modelValue:d.listQuery.type,"onUpdate:modelValue":i[1]||(i[1]=e=>d.listQuery.type=e),placeholder:"链接类型",clearable:"",style:{width:"120px"},class:"filter-item"},{default:t(()=>[l(_,{label:"全部",value:""}),l(_,{label:"群组推广",value:"group"}),l(_,{label:"分销推广",value:"distribution"}),l(_,{label:"活动推广",value:"activity"}),l(_,{label:"其他",value:"other"})]),_:1},8,["modelValue"]),l(v,{modelValue:d.listQuery.status,"onUpdate:modelValue":i[2]||(i[2]=e=>d.listQuery.status=e),placeholder:"状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:t(()=>[l(_,{label:"全部",value:""}),l(_,{label:"活跃",value:"active"}),l(_,{label:"暂停",value:"paused"}),l(_,{label:"过期",value:"expired"})]),_:1},8,["modelValue"]),l(y,{class:"filter-item",type:"primary",icon:"Search",onClick:d.handleFilter},{default:t(()=>i[8]||(i[8]=[o(" 搜索 ",-1)])),_:1,__:[8]}),l(y,{class:"filter-item",type:"success",icon:"Plus",onClick:d.handleCreate},{default:t(()=>i[9]||(i[9]=[o(" 创建链接 ",-1)])),_:1,__:[9]}),l(y,{class:"filter-item",type:"warning",icon:"Download",onClick:d.handleExport},{default:t(()=>i[10]||(i[10]=[o(" 导出数据 ",-1)])),_:1,__:[10]})]),l(S,{gutter:20,class:"stats-row"},{default:t(()=>[l(V,{span:6},{default:t(()=>[l(d.StatCard,{type:"primary",icon:d.Link,value:d.stats.total_links,label:"总链接数",trend:{type:"up",value:"+15",desc:"较上月"},clickable:"",onClick:d.navigateToList},null,8,["icon","value"])]),_:1}),l(V,{span:6},{default:t(()=>[l(d.StatCard,{type:"success",icon:d.View,value:d.stats.total_clicks,label:"总点击量",trend:{type:"up",value:"+28.5%",desc:"较上月"}},null,8,["icon","value"])]),_:1}),l(V,{span:6},{default:t(()=>[l(d.StatCard,{type:"warning",icon:d.User,value:d.stats.unique_visitors,label:"独立访客",trend:{type:"up",value:"+18.2%",desc:"较上月"}},null,8,["icon","value"])]),_:1}),l(V,{span:6},{default:t(()=>[l(d.StatCard,{type:"danger",icon:d.TrendCharts,value:d.stats.conversion_rate,label:"转化率",suffix:"%",decimals:1,trend:{type:"up",value:"+2.1%",desc:"较上月"}},null,8,["icon","value"])]),_:1})]),_:1}),l(Y,null,{header:t(()=>[s("div",Ua,[i[12]||(i[12]=s("h3",null,"推广链接列表",-1)),s("div",null,[l(y,{type:"primary",size:"small",onClick:d.handleBatchOperation},{default:t(()=>i[11]||(i[11]=[o("批量操作",-1)])),_:1,__:[11]})])])]),default:t(()=>[p((a(),e(E,{data:d.list,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",onSelectionChange:d.handleSelectionChange},{default:t(()=>[l(U,{type:"selection",width:"55"}),l(U,{label:"链接ID",prop:"id",width:"80"}),l(U,{label:"链接信息",width:"250"},{default:t(({row:e})=>[s("div",ja,[s("div",Ra,C(e.name),1),s("div",qa,[l(j,{href:e.short_url,target:"_blank",type:"primary"},{default:t(()=>[o(C(e.short_url),1)]),_:2},1032,["href"]),l(y,{type:"text",size:"small",onClick:a=>d.copyToClipboard(e.short_url),class:"copy-btn"},{default:t(()=>[l(R,null,{default:t(()=>[l(d.DocumentCopy)]),_:1})]),_:2},1032,["onClick"])])])]),_:1}),l(U,{label:"类型",width:"100"},{default:t(({row:e})=>[l(T,{type:d.getTypeTagType(e.type)},{default:t(()=>[o(C(d.getTypeText(e.type)),1)]),_:2},1032,["type"])]),_:1}),l(U,{label:"目标URL",width:"200"},{default:t(({row:e})=>[s("div",{class:"target-url",title:e.target_url},C(d.truncateUrl(e.target_url)),9,Ta)]),_:1}),l(U,{label:"点击统计",width:"120"},{default:t(({row:e})=>[s("div",La,[s("div",Qa,C(e.click_count||0)+" 次",1),s("div",za,"今日: "+C(e.today_clicks||0),1)])]),_:1}),l(U,{label:"转化数据",width:"120"},{default:t(({row:e})=>[s("div",Aa,[s("div",$a,C(e.conversions||0)+" 转化",1),s("div",Ma,C(((e.conversions||0)/Math.max(e.click_count||1,1)*100).toFixed(1))+"%",1)])]),_:1}),l(U,{label:"状态",width:"100"},{default:t(({row:e})=>[l(T,{type:d.getStatusTagType(e.status)},{default:t(()=>[o(C(d.getStatusText(e.status)),1)]),_:2},1032,["type"])]),_:1}),l(U,{label:"创建时间",width:"160"},{default:t(({row:e})=>[o(C(d.formatDate(e.created_at)),1)]),_:1}),l(U,{label:"操作",width:"200",fixed:"right"},{default:t(({row:s})=>[l(y,{type:"primary",size:"small",onClick:e=>d.handleEdit(s)},{default:t(()=>i[13]||(i[13]=[o(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick"]),l(y,{type:"success",size:"small",onClick:e=>d.handleAnalytics(s)},{default:t(()=>i[14]||(i[14]=[o(" 统计 ",-1)])),_:2,__:[14]},1032,["onClick"]),l($,{onCommand:d.handleCommand},{dropdown:t(()=>[l(z,null,{default:t(()=>[l(L,{command:`qrcode-${s.id}`},{default:t(()=>i[16]||(i[16]=[o("生成二维码",-1)])),_:2,__:[16]},1032,["command"]),l(L,{command:`copy-${s.id}`},{default:t(()=>i[17]||(i[17]=[o("复制链接",-1)])),_:2,__:[17]},1032,["command"]),"active"===s.status?(a(),e(L,{key:0,command:`pause-${s.id}`},{default:t(()=>i[18]||(i[18]=[o("暂停链接",-1)])),_:2,__:[18]},1032,["command"])):f("",!0),"paused"===s.status?(a(),e(L,{key:1,command:`resume-${s.id}`},{default:t(()=>i[19]||(i[19]=[o("恢复链接",-1)])),_:2,__:[19]},1032,["command"])):f("",!0),l(L,{command:`delete-${s.id}`,divided:""},{default:t(()=>i[20]||(i[20]=[o("删除链接",-1)])),_:2,__:[20]},1032,["command"])]),_:2},1024)]),default:t(()=>[l(y,{type:"info",size:"small"},{default:t(()=>[i[15]||(i[15]=o(" 更多",-1)),l(R,{class:"el-icon--right"},{default:t(()=>[l(d.ArrowDown)]),_:1})]),_:1,__:[15]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[B,d.listLoading]]),s("div",Oa,[l(P,{"current-page":d.listQuery.page,"onUpdate:currentPage":i[3]||(i[3]=e=>d.listQuery.page=e),"page-size":d.listQuery.limit,"onUpdate:pageSize":i[4]||(i[4]=e=>d.listQuery.limit=e),"page-sizes":[10,20,30,50],total:d.total,background:"",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d.handleSizeChange,onCurrentChange:d.handleCurrentChange},null,8,["current-page","page-size","total"])])]),_:1}),l(d.LinkDialog,{modelValue:d.dialogVisible,"onUpdate:modelValue":i[5]||(i[5]=e=>d.dialogVisible=e),"link-data":d.currentLink,onSuccess:d.handleDialogSuccess},null,8,["modelValue","link-data"]),l(d.LinkAnalyticsDrawer,{modelValue:d.analyticsDrawerVisible,"onUpdate:modelValue":i[6]||(i[6]=e=>d.analyticsDrawerVisible=e),"link-id":d.currentLinkId},null,8,["modelValue","link-id"]),l(d.QRCodeDialog,{modelValue:d.qrcodeDialogVisible,"onUpdate:modelValue":i[7]||(i[7]=e=>d.qrcodeDialogVisible=e),"link-data":d.currentLink},null,8,["modelValue","link-data"])])}],["__scopeId","data-v-b43d7cff"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/promotion/LinkManagement.vue"]]);export{Ea as default};
