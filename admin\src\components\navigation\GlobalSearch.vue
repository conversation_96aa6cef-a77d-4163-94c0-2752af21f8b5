<!-- 全局搜索组件 -->
<!-- admin/src/components/navigation/GlobalSearch.vue -->

<template>
  <div 
    class="global-search-container"
    :class="containerClasses"
    ref="searchContainer"
  >
    <!-- 搜索输入框 -->
    <div class="search-input-wrapper">
      <el-input
        ref="searchInput"
        v-model="searchQuery"
        :placeholder="placeholder"
        :prefix-icon="Search"
        :size="size"
        clearable
        @focus="handleFocus"
        @blur="handleBlur"
        @input="handleInput"
        @keydown.enter="handleEnter"
        @keydown.up.prevent="navigateUp"
        @keydown.down.prevent="navigateDown"
        @keydown.esc="handleEscape"
        class="search-input"
      >
        <template #suffix>
          <div class="search-suffix">
            <!-- 搜索过滤器 -->
            <el-dropdown
              v-if="showFilters && filters.length > 0"
              @command="handleFilterCommand"
              trigger="click"
              class="filter-dropdown"
            >
              <el-button type="text" size="small" class="filter-btn">
                <el-icon><Filter /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="filter in filters"
                    :key="filter.key"
                    :command="filter.key"
                    :class="{ active: activeFilters.includes(filter.key) }"
                  >
                    <el-icon v-if="filter.icon">
                      <component :is="filter.icon" />
                    </el-icon>
                    {{ filter.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            
            <!-- 高级搜索 -->
            <el-button
              v-if="showAdvanced"
              type="text"
              size="small"
              @click="showAdvancedSearch = !showAdvancedSearch"
              class="advanced-btn"
              :class="{ active: showAdvancedSearch }"
            >
              <el-icon><Setting /></el-icon>
            </el-button>
            
            <!-- 快捷键提示 -->
            <div class="shortcut-hint" v-if="showShortcut">
              <kbd>{{ shortcutText }}</kbd>
            </div>
          </div>
        </template>
      </el-input>
      
      <!-- 活跃过滤器标签 -->
      <div class="active-filters" v-if="activeFilters.length > 0">
        <el-tag
          v-for="filterKey in activeFilters"
          :key="filterKey"
          closable
          size="small"
          @close="removeFilter(filterKey)"
          :type="getFilterType(filterKey)"
        >
          {{ getFilterLabel(filterKey) }}
        </el-tag>
      </div>
    </div>
    
    <!-- 高级搜索面板 -->
    <transition name="advanced-panel-fade">
      <div v-show="showAdvancedSearch" class="advanced-search-panel">
        <div class="panel-header">
          <h4>高级搜索</h4>
          <el-button type="text" size="small" @click="clearAdvanced">
            清除所有
          </el-button>
        </div>
        
        <div class="search-options">
          <!-- 搜索范围 -->
          <div class="option-group">
            <label class="option-label">搜索范围</label>
            <el-checkbox-group v-model="searchScope">
              <el-checkbox
                v-for="scope in availableScopes"
                :key="scope.value"
                :label="scope.value"
                :disabled="scope.disabled"
              >
                {{ scope.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
          
          <!-- 时间范围 -->
          <div class="option-group">
            <label class="option-label">时间范围</label>
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="small"
              style="width: 100%"
            />
          </div>
          
          <!-- 排序方式 */
          <div class="option-group">
            <label class="option-label">排序方式</label>
            <el-select v-model="sortBy" size="small" style="width: 100%">
              <el-option
                v-for="sort in sortOptions"
                :key="sort.value"
                :label="sort.label"
                :value="sort.value"
              />
            </el-select>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- 搜索结果面板 -->
    <transition name="results-fade">
      <div 
        v-show="showResults" 
        class="search-results-panel"
        @mousedown.prevent
      >
        <!-- 加载状态 -->
        <div v-if="isLoading" class="search-loading">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>搜索中...</span>
        </div>
        
        <!-- 搜索结果 -->
        <div v-else-if="hasResults" class="search-results">
          <!-- 搜索统计 -->
          <div class="search-stats">
            <span class="results-count">找到 {{ totalResults }} 个结果</span>
            <span class="search-time">用时 {{ searchTime }}ms</span>
          </div>
          
          <!-- 结果分组 -->
          <div
            v-for="group in groupedResults"
            :key="group.type"
            class="result-group"
          >
            <div class="group-header">
              <div class="group-title">
                <el-icon><component :is="group.icon" /></el-icon>
                <span>{{ group.title }}</span>
                <el-badge :value="group.count" class="group-count" />
              </div>
              <el-button
                type="text"
                size="small"
                @click="viewAllResults(group.type)"
                v-if="group.hasMore"
              >
                查看全部
              </el-button>
            </div>
            
            <div class="group-items">
              <div
                v-for="(item, index) in group.items"
                :key="item.id || index"
                class="result-item"
                :class="{ 
                  selected: selectedIndex === getGlobalIndex(group.type, index),
                  highlighted: item.highlighted 
                }"
                @click="selectResult(item)"
                @mouseenter="selectedIndex = getGlobalIndex(group.type, index)"
              >
                <div class="item-icon">
                  <el-icon><component :is="item.icon || 'Document'" /></el-icon>
                </div>
                
                <div class="item-content">
                  <div class="item-title" v-html="highlightText(item.title)"></div>
                  <div class="item-description" v-if="item.description">
                    {{ item.description }}
                  </div>
                  <div class="item-meta" v-if="item.meta">
                    <span class="meta-type">{{ item.meta.type }}</span>
                    <span class="meta-date" v-if="item.meta.date">
                      {{ formatDate(item.meta.date) }}
                    </span>
                    <span class="meta-author" v-if="item.meta.author">
                      {{ item.meta.author }}
                    </span>
                  </div>
                </div>
                
                <div class="item-actions">
                  <el-button
                    type="text"
                    size="small"
                    @click.stop="addToFavorites(item)"
                    :class="{ active: isFavorite(item) }"
                    class="favorite-btn"
                  >
                    <el-icon>
                      <component :is="isFavorite(item) ? 'StarFilled' : 'Star'" />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 无结果 -->
        <div v-else-if="searchQuery && !isLoading" class="no-results">
          <div class="no-results-icon">
            <el-icon><Search /></el-icon>
          </div>
          <div class="no-results-text">
            <h4>未找到相关结果</h4>
            <p>尝试使用不同的关键词或检查搜索条件</p>
          </div>
          <div class="search-suggestions" v-if="suggestions.length > 0">
            <p>您是否要找：</p>
            <div class="suggestion-list">
              <el-tag
                v-for="suggestion in suggestions"
                :key="suggestion"
                @click="applySuggestion(suggestion)"
                class="suggestion-tag"
              >
                {{ suggestion }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <!-- 默认内容 -->
        <div v-else class="default-content">
          <!-- 最近搜索 -->
          <div class="recent-searches" v-if="recentSearches.length > 0">
            <div class="section-title">
              <el-icon><Clock /></el-icon>
              <span>最近搜索</span>
              <el-button type="text" size="small" @click="clearRecentSearches">
                清除
              </el-button>
            </div>
            <div class="recent-list">
              <div
                v-for="(search, index) in recentSearches.slice(0, 5)"
                :key="index"
                class="recent-item"
                @click="applyRecentSearch(search)"
              >
                <el-icon><Search /></el-icon>
                <span>{{ search.query }}</span>
                <small>{{ formatTimeAgo(search.timestamp) }}</small>
              </div>
            </div>
          </div>
          
          <!-- 热门搜索 -->
          <div class="popular-searches" v-if="popularSearches.length > 0">
            <div class="section-title">
              <el-icon><TrendCharts /></el-icon>
              <span>热门搜索</span>
            </div>
            <div class="popular-list">
              <el-tag
                v-for="(search, index) in popularSearches"
                :key="index"
                @click="applyPopularSearch(search)"
                class="popular-tag"
                :type="getPopularType(index)"
              >
                {{ search }}
              </el-tag>
            </div>
          </div>
          
          <!-- 快速操作 -->
          <div class="quick-actions" v-if="quickActions.length > 0">
            <div class="section-title">
              <el-icon><Lightning /></el-icon>
              <span>快速操作</span>
            </div>
            <div class="actions-grid">
              <div
                v-for="action in quickActions"
                :key="action.key"
                class="action-item"
                @click="executeQuickAction(action)"
              >
                <el-icon><component :is="action.icon" /></el-icon>
                <span>{{ action.title }}</span>
                <kbd v-if="action.shortcut" class="action-shortcut">
                  {{ action.shortcut }}
                </kbd>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Search,
  Filter,
  Setting,
  Clock,
  Star,
  StarFilled,
  Document,
  User,
  Menu,
  DataLine,
  TrendCharts,
  Lightning,
  Loading,
  Plus
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  placeholder: {
    type: String,
    default: '搜索功能、页面、用户...'
  },
  size: {
    type: String,
    default: 'default'
  },
  showFilters: {
    type: Boolean,
    default: true
  },
  showAdvanced: {
    type: Boolean,
    default: true
  },
  showShortcut: {
    type: Boolean,
    default: true
  },
  shortcutText: {
    type: String,
    default: '⌘K'
  },
  maxResults: {
    type: Number,
    default: 50
  },
  debounceDelay: {
    type: Number,
    default: 300
  }
})

// Emits
const emit = defineEmits(['search', 'select', 'clear'])

const router = useRouter()

// 响应式数据
const searchContainer = ref(null)
const searchInput = ref(null)
const searchQuery = ref('')
const showResults = ref(false)
const showAdvancedSearch = ref(false)
const isLoading = ref(false)
const selectedIndex = ref(-1)
const activeFilters = ref([])
const searchScope = ref(['all'])
const dateRange = ref(null)
const sortBy = ref('relevance')

// 搜索结果
const searchResults = ref([])
const totalResults = ref(0)
const searchTime = ref(0)

// 历史记录
const recentSearches = ref(JSON.parse(localStorage.getItem('global-search-recent') || '[]'))
const favorites = ref(JSON.parse(localStorage.getItem('global-search-favorites') || '[]'))

// 静态数据
const filters = ref([
  { key: 'all', label: '全部', icon: 'Search', type: 'primary' },
  { key: 'pages', label: '页面', icon: 'Document', type: 'success' },
  { key: 'users', label: '用户', icon: 'User', type: 'warning' },
  { key: 'groups', label: '群组', icon: 'Menu', type: 'info' }
])

const availableScopes = ref([
  { value: 'all', label: '全部内容' },
  { value: 'current', label: '当前模块' },
  { value: 'favorites', label: '收藏夹' }
])

const sortOptions = ref([
  { value: 'relevance', label: '相关性' },
  { value: 'date', label: '时间' },
  { value: 'name', label: '名称' },
  { value: 'type', label: '类型' }
])

const popularSearches = ref([
  '群组管理', '用户分析', '数据导出', '系统设置', '财务报表'
])

const quickActions = ref([
  { key: 'new-group', title: '创建群组', icon: 'Plus', shortcut: 'Ctrl+N' },
  { key: 'export-data', title: '导出数据', icon: 'Download', shortcut: 'Ctrl+E' },
  { key: 'user-list', title: '用户列表', icon: 'User' },
  { key: 'system-monitor', title: '系统监控', icon: 'DataLine' }
])

const suggestions = ref(['群组', '用户', '数据', '设置'])

// 计算属性
const containerClasses = computed(() => ({
  'focused': showResults.value,
  'has-filters': activeFilters.value.length > 0,
  'advanced-open': showAdvancedSearch.value
}))

const hasResults = computed(() => searchResults.value.length > 0)

const groupedResults = computed(() => {
  const groups = {}
  
  searchResults.value.forEach(item => {
    const type = item.type || 'other'
    if (!groups[type]) {
      groups[type] = {
        type,
        title: getGroupTitle(type),
        icon: getGroupIcon(type),
        items: [],
        count: 0,
        hasMore: false
      }
    }
    groups[type].items.push(item)
    groups[type].count++
  })
  
  // 限制每组显示数量
  Object.values(groups).forEach(group => {
    if (group.items.length > 5) {
      group.hasMore = true
      group.items = group.items.slice(0, 5)
    }
  })
  
  return Object.values(groups)
})

// 方法
const handleFocus = () => {
  showResults.value = true
  if (searchQuery.value) {
    performSearch()
  }
}

const handleBlur = () => {
  // 延迟隐藏，允许点击结果
  setTimeout(() => {
    showResults.value = false
    selectedIndex.value = -1
  }, 150)
}

const handleInput = (value) => {
  if (value) {
    debounceSearch()
  } else {
    searchResults.value = []
    isLoading.value = false
  }
}

const handleEnter = () => {
  if (selectedIndex.value >= 0 && hasResults.value) {
    const selectedItem = getSelectedItem()
    if (selectedItem) {
      selectResult(selectedItem)
    }
  } else if (searchQuery.value) {
    performFullSearch()
  }
}

const handleEscape = () => {
  if (showAdvancedSearch.value) {
    showAdvancedSearch.value = false
  } else {
    showResults.value = false
    searchInput.value?.blur()
  }
}

const navigateUp = () => {
  if (selectedIndex.value > 0) {
    selectedIndex.value--
  }
}

const navigateDown = () => {
  const totalItems = getTotalResultsCount()
  if (selectedIndex.value < totalItems - 1) {
    selectedIndex.value++
  }
}

// 搜索相关方法
let searchTimer = null

const debounceSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    performSearch()
  }, props.debounceDelay)
}

const performSearch = async () => {
  if (!searchQuery.value.trim()) return
  
  isLoading.value = true
  const startTime = Date.now()
  
  try {
    // 模拟搜索API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟搜索结果
    const mockResults = generateMockResults(searchQuery.value)
    
    searchResults.value = mockResults
    totalResults.value = mockResults.length
    searchTime.value = Date.now() - startTime
    
    // 添加到搜索历史
    addToSearchHistory(searchQuery.value)
    
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    isLoading.value = false
  }
}

const generateMockResults = (query) => {
  const results = []
  const types = ['page', 'user', 'group', 'data']
  
  for (let i = 0; i < Math.min(20, Math.random() * 15 + 5); i++) {
    const type = types[Math.floor(Math.random() * types.length)]
    results.push({
      id: i,
      type,
      title: `${query} 相关结果 ${i + 1}`,
      description: `这是一个与 ${query} 相关的${getTypeName(type)}结果`,
      icon: getTypeIcon(type),
      path: `/mock/${type}/${i}`,
      meta: {
        type: getTypeName(type),
        date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        author: `用户${i + 1}`
      }
    })
  }
  
  return results
}

const selectResult = (item) => {
  if (item.path) {
    router.push(item.path)
  }
  
  emit('select', item)
  showResults.value = false
  searchQuery.value = ''
}

const performFullSearch = () => {
  // 跳转到搜索结果页面
  router.push(`/search?q=${encodeURIComponent(searchQuery.value)}`)
  emit('search', searchQuery.value)
}

// 过滤器相关方法
const handleFilterCommand = (filterKey) => {
  if (activeFilters.value.includes(filterKey)) {
    removeFilter(filterKey)
  } else {
    activeFilters.value.push(filterKey)
  }
  
  if (searchQuery.value) {
    performSearch()
  }
}

const removeFilter = (filterKey) => {
  activeFilters.value = activeFilters.value.filter(f => f !== filterKey)
  if (searchQuery.value) {
    performSearch()
  }
}

const getFilterLabel = (key) => {
  const filter = filters.value.find(f => f.key === key)
  return filter?.label || key
}

const getFilterType = (key) => {
  const filter = filters.value.find(f => f.key === key)
  return filter?.type || 'primary'
}

// 高级搜索方法
const clearAdvanced = () => {
  searchScope.value = ['all']
  dateRange.value = null
  sortBy.value = 'relevance'
  activeFilters.value = []
}

// 历史记录方法
const addToSearchHistory = (query) => {
  const timestamp = Date.now()
  const existingIndex = recentSearches.value.findIndex(item => item.query === query)
  
  if (existingIndex >= 0) {
    recentSearches.value.splice(existingIndex, 1)
  }
  
  recentSearches.value.unshift({ query, timestamp })
  recentSearches.value = recentSearches.value.slice(0, 10)
  
  localStorage.setItem('global-search-recent', JSON.stringify(recentSearches.value))
}

const clearRecentSearches = () => {
  recentSearches.value = []
  localStorage.removeItem('global-search-recent')
}

const applyRecentSearch = (search) => {
  searchQuery.value = search.query
  performSearch()
}

const applyPopularSearch = (search) => {
  searchQuery.value = search
  performSearch()
}

const applySuggestion = (suggestion) => {
  searchQuery.value = suggestion
  performSearch()
}

// 收藏相关方法
const addToFavorites = (item) => {
  const index = favorites.value.findIndex(fav => fav.id === item.id)
  
  if (index >= 0) {
    favorites.value.splice(index, 1)
    ElMessage.success('已取消收藏')
  } else {
    favorites.value.push(item)
    ElMessage.success('已加入收藏')
  }
  
  localStorage.setItem('global-search-favorites', JSON.stringify(favorites.value))
}

const isFavorite = (item) => {
  return favorites.value.some(fav => fav.id === item.id)
}

// 快速操作
const executeQuickAction = (action) => {
  switch (action.key) {
    case 'new-group':
      router.push('/admin/community/groups')
      break
    case 'export-data':
      router.push('/admin/system/data-export')
      break
    case 'user-list':
      router.push('/admin/users/list')
      break
    case 'system-monitor':
      router.push('/admin/system/monitor')
      break
  }
  
  showResults.value = false
}

// 工具方法
const getGroupTitle = (type) => {
  const titles = {
    page: '页面',
    user: '用户',
    group: '群组',
    data: '数据'
  }
  return titles[type] || '其他'
}

const getGroupIcon = (type) => {
  const icons = {
    page: 'Document',
    user: 'User',
    group: 'Menu',
    data: 'DataLine'
  }
  return icons[type] || 'Document'
}

const getTypeName = (type) => {
  return getGroupTitle(type)
}

const getTypeIcon = (type) => {
  return getGroupIcon(type)
}

const getTotalResultsCount = () => {
  return searchResults.value.length
}

const getGlobalIndex = (groupType, itemIndex) => {
  let globalIndex = 0
  const groups = groupedResults.value
  
  for (let i = 0; i < groups.length; i++) {
    if (groups[i].type === groupType) {
      return globalIndex + itemIndex
    }
    globalIndex += groups[i].items.length
  }
  
  return globalIndex
}

const getSelectedItem = () => {
  let currentIndex = 0
  
  for (const group of groupedResults.value) {
    for (const item of group.items) {
      if (currentIndex === selectedIndex.value) {
        return item
      }
      currentIndex++
    }
  }
  
  return null
}

const highlightText = (text) => {
  if (!searchQuery.value) return text
  
  const regex = new RegExp(`(${searchQuery.value})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(date))
}

const formatTimeAgo = (timestamp) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

const getPopularType = (index) => {
  const types = ['danger', 'warning', 'success', 'info', 'primary']
  return types[index % types.length]
}

const viewAllResults = (type) => {
  router.push(`/search?q=${encodeURIComponent(searchQuery.value)}&type=${type}`)
}

// 键盘快捷键支持
const handleGlobalKeydown = (e) => {
  if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
    e.preventDefault()
    searchInput.value?.focus()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleGlobalKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeydown)
  clearTimeout(searchTimer)
})
</script>

<style lang="scss" scoped>
.global-search-container {
  position: relative;
  width: 100%;
  max-width: 600px;
  
  &.focused {
    .search-input-wrapper .search-input {
      :deep(.el-input__wrapper) {
        border-color: var(--color-primary);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }
  
  &.has-filters {
    .search-input-wrapper {
      .active-filters {
        opacity: 1;
        max-height: 50px;
        margin-top: var(--spacing-sm);
      }
    }
  }
  
  &.advanced-open {
    .advanced-search-panel {
      max-height: 400px;
      padding: var(--spacing-md);
    }
  }
}

.search-input-wrapper {
  .search-input {
    :deep(.el-input__wrapper) {
      background: var(--bg-secondary);
      border: 2px solid var(--border-light);
      border-radius: var(--radius-full);
      transition: all var(--duration-normal) var(--ease-out);
      
      &:hover {
        border-color: var(--border-medium);
        background: var(--bg-primary);
      }
    }
    
    :deep(.el-input__inner) {
      font-size: var(--text-sm);
      color: var(--text-primary);
      
      &::placeholder {
        color: var(--text-light);
        font-style: italic;
      }
    }
  }
  
  .search-suffix {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding-right: var(--spacing-sm);
    
    .filter-btn,
    .advanced-btn {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      padding: 0;
      color: var(--text-muted);
      
      &:hover,
      &.active {
        background: var(--color-primary);
        color: white;
      }
    }
    
    .shortcut-hint {
      kbd {
        font-size: 10px;
        padding: 2px 6px;
        background: var(--bg-muted);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-sm);
        font-family: var(--font-family-mono, monospace);
        color: var(--text-muted);
      }
    }
  }
  
  .active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transition: all var(--duration-normal) var(--ease-out);
  }
}

// 高级搜索面板
.advanced-search-panel {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-height: 0;
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-out);
  z-index: var(--z-dropdown);
  
  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    
    h4 {
      margin: 0;
      font-size: var(--text-sm);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
    }
  }
  
  .search-options {
    display: grid;
    gap: var(--spacing-md);
    
    .option-group {
      .option-label {
        display: block;
        font-size: var(--text-xs);
        font-weight: var(--font-semibold);
        color: var(--text-muted);
        margin-bottom: var(--spacing-sm);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

// 搜索结果面板
.search-results-panel {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  right: 0;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-height: 600px;
  overflow-y: auto;
  z-index: var(--z-dropdown);
}

.search-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xl);
  color: var(--text-muted);
  
  .loading-icon {
    animation: spin 1s linear infinite;
  }
}

.search-results {
  .search-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    font-size: var(--text-xs);
    color: var(--text-muted);
    
    .results-count {
      font-weight: var(--font-semibold);
    }
  }
  
  .result-group {
    &:not(:last-child) {
      border-bottom: 1px solid var(--border-light);
    }
    
    .group-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-md);
      background: var(--bg-muted);
      
      .group-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--text-sm);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        
        .el-icon {
          color: var(--color-primary);
        }
        
        .group-count {
          :deep(.el-badge__content) {
            font-size: 10px;
            min-width: 16px;
            height: 16px;
            line-height: 16px;
          }
        }
      }
    }
    
    .group-items {
      .result-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
        cursor: pointer;
        transition: all var(--duration-normal) var(--ease-out);
        
        &:hover,
        &.selected {
          background: var(--bg-secondary);
          transform: translateX(4px);
        }
        
        &.highlighted {
          background: rgba(59, 130, 246, 0.05);
          border-left: 3px solid var(--color-primary);
        }
        
        .item-icon {
          width: 32px;
          height: 32px;
          background: rgba(59, 130, 246, 0.1);
          color: var(--color-primary);
          border-radius: var(--radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
        }
        
        .item-content {
          flex: 1;
          min-width: 0;
          
          .item-title {
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            color: var(--text-primary);
            margin-bottom: 2px;
            
            :deep(mark) {
              background: rgba(59, 130, 246, 0.2);
              color: var(--color-primary);
              padding: 1px 2px;
              border-radius: 2px;
            }
          }
          
          .item-description {
            font-size: var(--text-xs);
            color: var(--text-secondary);
            line-height: 1.4;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .item-meta {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--text-xs);
            color: var(--text-light);
            
            .meta-type {
              background: var(--bg-muted);
              padding: 1px 4px;
              border-radius: var(--radius-sm);
            }
          }
        }
        
        .item-actions {
          opacity: 0;
          transition: opacity var(--duration-normal) var(--ease-out);
          
          .favorite-btn {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            padding: 0;
            
            &.active .el-icon {
              color: var(--warning-500);
            }
          }
        }
        
        &:hover .item-actions {
          opacity: 1;
        }
      }
    }
  }
}

.no-results {
  text-align: center;
  padding: var(--spacing-xl);
  
  .no-results-icon {
    font-size: 48px;
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
  }
  
  .no-results-text {
    h4 {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-lg);
      color: var(--text-primary);
    }
    
    p {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-secondary);
    }
  }
  
  .search-suggestions {
    p {
      font-size: var(--text-sm);
      color: var(--text-muted);
      margin-bottom: var(--spacing-sm);
    }
    
    .suggestion-list {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: var(--spacing-xs);
      
      .suggestion-tag {
        cursor: pointer;
        
        &:hover {
          background: var(--color-primary);
          border-color: var(--color-primary);
          color: white;
        }
      }
    }
  }
}

.default-content {
  padding: var(--spacing-md);
  
  .section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    
    .el-icon {
      color: var(--color-primary);
    }
    
    .el-button {
      margin-left: auto;
      font-size: var(--text-xs);
    }
  }
  
  .recent-searches,
  .popular-searches,
  .quick-actions {
    margin-bottom: var(--spacing-lg);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .recent-list {
    .recent-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm);
      border-radius: var(--radius-sm);
      cursor: pointer;
      transition: background-color var(--duration-normal) var(--ease-out);
      
      &:hover {
        background: var(--bg-secondary);
      }
      
      .el-icon {
        color: var(--text-muted);
      }
      
      span {
        flex: 1;
        font-size: var(--text-sm);
        color: var(--text-primary);
      }
      
      small {
        color: var(--text-light);
        font-size: var(--text-xs);
      }
    }
  }
  
  .popular-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    
    .popular-tag {
      cursor: pointer;
      
      &:hover {
        transform: scale(1.05);
      }
    }
  }
  
  .actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
    
    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-xs);
      padding: var(--spacing-md);
      background: white;
      border: 1px solid var(--border-light);
      border-radius: var(--radius-lg);
      cursor: pointer;
      transition: all var(--duration-normal) var(--ease-out);
      position: relative;
      
      &:hover {
        background: var(--color-primary);
        border-color: var(--color-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }
      
      .el-icon {
        font-size: 20px;
        color: var(--color-primary);
        transition: color var(--duration-normal) var(--ease-out);
      }
      
      span {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        text-align: center;
      }
      
      .action-shortcut {
        position: absolute;
        top: 4px;
        right: 4px;
        font-size: 9px;
        padding: 1px 3px;
        background: var(--bg-muted);
        border-radius: var(--radius-sm);
        font-family: var(--font-family-mono, monospace);
      }
      
      &:hover {
        .el-icon {
          color: white;
        }
        
        .action-shortcut {
          background: rgba(255, 255, 255, 0.2);
          color: white;
        }
      }
    }
  }
}

// 动画
.advanced-panel-fade-enter-active,
.advanced-panel-fade-leave-active,
.results-fade-enter-active,
.results-fade-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  transform-origin: top center;
}

.advanced-panel-fade-enter-from,
.advanced-panel-fade-leave-to,
.results-fade-enter-from,
.results-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式
@media (max-width: 768px) {
  .global-search-container {
    .search-results-panel {
      max-height: 400px;
    }
    
    .default-content {
      .actions-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>