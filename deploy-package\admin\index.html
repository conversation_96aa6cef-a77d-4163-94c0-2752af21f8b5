<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>晨鑫 晨鑫流量变现系统 - 智能社群营销管理后台</title>
    <meta name="description" content="晨鑫 晨鑫流量变现系统 智能社群营销与多级分销平台管理后台" />
    <meta name="keywords" content="社群营销,多级分销,微信群管理,防红系统" />
    <meta name="author" content="晨鑫 晨鑫流量变现系统 Team" />
    
    <!-- 预加载样式 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 防止页面闪烁 -->
    <style>
      #app {
        position: relative;
        width: 100%;
        height: 100vh;
        overflow: hidden;
      }
      
      /* 加载动画 */
      .loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        color: white;
        font-size: 16px;
        font-weight: 500;
        margin-top: 20px;
        text-align: center;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 全局样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f5f5f5;
      }
      
      /* 隐藏滚动条 */
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
      }
    </style>
    <script type="module" crossorigin src="/admin/assets/index-0706f191.js"></script>
    <link rel="stylesheet" href="/admin/assets/index-64d35e61.css">
  </head>
  <body>
    <div id="app">
      <!-- 初始加载动画 -->
      <div class="loading" id="loading">
        <div style="text-align: center;">
          <div class="loading-spinner"></div>
          <div class="loading-text">
            <div>晨鑫 晨鑫流量变现系统</div>
            <div style="font-size: 14px; margin-top: 5px; opacity: 0.8;">正在加载管理后台...</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 应用脚本 -->
    
    
    <!-- 隐藏加载动画 -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            setTimeout(() => {
              loading.style.display = 'none';
            }, 300);
          }
        }, 100);
      });
    </script>
  </body>
</html> 