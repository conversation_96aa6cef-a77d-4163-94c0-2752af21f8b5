import{s as t}from"./index-eUTsTR3J.js";const e={getMy:()=>t({url:"/admin/agent/my",method:"get"}),getMyStats:()=>t({url:"/api/v1/admin/agent/my-stats",method:"get"}),getList:e=>t({url:"/admin/agent/list",method:"get",params:e}),getDetail:e=>t({url:`/admin/agent/${e}`,method:"get"}),create:e=>t({url:"/admin/agent",method:"post",data:e}),update:(e,a)=>t({url:`/admin/agent/${e}`,method:"put",data:a}),delete:e=>t({url:`/admin/agent/${e}`,method:"delete"}),getTeamData:()=>t({url:"/admin/agent/team",method:"get"}),getCommissionData:e=>t({url:"/admin/agent/commission",method:"get",params:e})};export{e as a};
