<template>
  <div class="modern-commission-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><CreditCard /></el-icon>
          </div>
          <div class="header-text">
            <h1>代理商佣金管理</h1>
            <p>管理代理商佣金结算，跟踪佣金发放状态，统计佣金数据</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleExportCommissions" :loading="exportLoading" class="action-btn secondary">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button type="primary" @click="handleAdd" class="action-btn primary">
            <el-icon><Plus /></el-icon>
            手动添加佣金
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-container" v-loading="statsLoading">
        <div class="stat-card" v-for="stat in commissionStatCards" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选与搜索 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="listQuery" @submit.prevent="handleQuery">
        <el-form-item label="订单号">
          <el-input 
            v-model="listQuery.order_no" 
            placeholder="请输入订单号" 
            clearable 
            @keyup.enter="handleQuery"
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="受益人">
          <el-input 
            v-model="listQuery.user_name" 
            placeholder="请输入用户名或昵称" 
            clearable 
            @keyup.enter="handleQuery"
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="listQuery.status" placeholder="全部状态" clearable class="filter-select">
            <el-option label="全部" value="" />
            <el-option label="已结算" :value="1" />
            <el-option label="待结算" :value="2" />
            <el-option label="已取消" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="listQuery.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="date-picker"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery" class="search-btn">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetQuery" class="reset-btn">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量操作按钮 -->
    <div class="batch-actions" v-if="multipleSelection.length > 0">
      <el-alert type="info" :closable="false" class="selection-alert">
        <template #title>
          已选择 {{ multipleSelection.length }} 个佣金记录，其中 {{ pendingCount }} 个待结算
        </template>
      </el-alert>
      <div class="batch-buttons">
        <el-button type="success" @click="handleBatchSettle" :disabled="pendingCount === 0">
          <el-icon><Check /></el-icon>
          批量结算 ({{ pendingCount }})
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h3>佣金记录</h3>
            <el-tag size="small" type="info">共 {{ total }} 条记录</el-tag>
          </div>
          <div class="header-right">
            <el-button-group>
              <el-button size="small" :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                <el-icon><List /></el-icon>
              </el-button>
              <el-button size="small" :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
                <el-icon><Grid /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <el-table
        v-if="viewMode === 'table'"
        :data="list"
        v-loading="listLoading"
        @selection-change="handleSelectionChange"
        class="modern-table"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" prop="id" align="center" width="80" />
        
        <el-table-column label="订单信息" width="220">
          <template #default="{ row }">
            <div class="commission-info">
              <el-avatar :size="40" class="commission-avatar">
                <el-icon><Wallet /></el-icon>
              </el-avatar>
              <div class="commission-details">
                <div class="order-no">{{ row.order?.order_no || '-' }}</div>
                <div class="order-amount">订单金额: ¥{{ formatNumber(row.order_amount) }}</div>
                <div class="commission-id">ID: {{ row.id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="受益人信息" width="220">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="40" class="user-avatar">
                <el-icon><UserFilled /></el-icon>
              </el-avatar>
              <div class="user-details">
                <div class="user-name">{{ row.user?.name || '-' }}</div>
                <div class="user-email">{{ row.user?.email || '-' }}</div>
                <div class="user-level">{{ getUserLevel(row.user?.level) }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="佣金详情" align="center">
          <template #default="{ row }">
            <div class="commission-detail">
              <div class="commission-rate">比例: {{ row.commission_rate }}%</div>
              <div class="commission-amount">¥{{ formatNumber(row.commission_amount) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="large">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="备注" prop="remark" min-width="120" show-overflow-tooltip />
        
        <el-table-column label="创建时间" width="160" align="center">
          <template #default="{row}">
            <div class="time-info">
              <div>{{ formatDate(row.created_at) }}</div>
              <div class="time-detail">{{ formatTime(row.created_at) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="viewDetail(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button 
              v-if="row.status === 2"
              link 
              type="success" 
              @click="handleSettle(row)"
            >
              <el-icon><Check /></el-icon>
              结算
            </el-button>
            <el-button link type="warning" @click="editCommission(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination 
        v-show="total > 0" 
        :total="total" 
        v-model:page="listQuery.page" 
        v-model:limit="listQuery.limit" 
        @pagination="fetchList" 
      />
    </el-card>

    <!-- 添加佣金对话框 -->
    <el-dialog
      title="添加佣金记录"
      v-model="addDialogVisible"
      width="600px"
    >
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef" label-width="120px">
        <el-form-item label="订单号" prop="order_id">
          <el-select 
            v-model="addForm.order_id" 
            placeholder="请选择订单"
            filterable
            remote
            :remote-method="remoteSearchOrders"
            style="width: 100%"
          >
            <el-option 
              v-for="order in orderOptions" 
              :key="order.id" 
              :label="order.order_no" 
              :value="order.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="受益用户" prop="user_id">
          <el-select 
            v-model="addForm.user_id" 
            placeholder="请选择用户"
            filterable
            remote
            :remote-method="remoteSearchUsers"
            style="width: 100%"
          >
            <el-option 
              v-for="user in userOptions" 
              :key="user.id" 
              :label="`${user.name} (${user.username})`" 
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="佣金比例" prop="commission_rate">
          <el-input-number 
            v-model="addForm.commission_rate" 
            :min="0" 
            :max="100" 
            :precision="2"
            style="width: 100%"
          />
          <span style="margin-left: 8px; color: #909399;">%</span>
        </el-form-item>
        
        <el-form-item label="佣金金额" prop="commission_amount">
          <el-input-number 
            v-model="addForm.commission_amount" 
            :min="0" 
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            v-model="addForm.remark" 
            type="textarea" 
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAdd" :loading="addLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { 
  getCommissionList, 
  getCommissionStats,
  settleCommission,
  batchSettleCommissions,
  addCommission,
  searchOrders,
  searchUsers
} from '@/api/finance';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Search, Refresh, Check, Download, Plus, RefreshLeft, Edit, View,
  UserFilled, CreditCard, Wallet, ArrowUp, List, Grid, Medal, Timer, DocumentCopy
} from '@element-plus/icons-vue';
import { exportCommissions } from '@/api/export';
import { formatDate, formatTime, formatNumber } from '@/utils/format';
import Pagination from '@/components/Pagination/index.vue';

const list = ref([]);
const total = ref(0);
const listLoading = ref(true);
const exportLoading = ref(false);
const addLoading = ref(false);
const multipleSelection = ref([]);
const statsLoading = ref(true);
const viewMode = ref('table'); // 视图模式：table 或 card

const listQuery = reactive({
  page: 1,
  limit: 15,
  order_no: '',
  user_name: '',
  status: '',
  date_range: []
});

const commissionStats = ref({});
const addDialogVisible = ref(false);
const addFormRef = ref(null);
const addForm = reactive({
  order_id: '',
  user_id: '',
  commission_rate: 0,
  commission_amount: 0,
  remark: ''
});

const addFormRules = {
  order_id: [{ required: true, message: '请选择订单', trigger: 'change' }],
  user_id: [{ required: true, message: '请选择用户', trigger: 'change' }],
  commission_rate: [{ required: true, message: '请输入佣金比例', trigger: 'blur' }],
  commission_amount: [{ required: true, message: '请输入佣金金额', trigger: 'blur' }]
};

const orderOptions = ref([]);
const userOptions = ref([]);

// 佣金统计卡片数据
const commissionStatCards = ref([
  {
    key: 'total',
    label: '总佣金',
    value: '¥0',
    icon: 'Medal',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+12%'
  },
  {
    key: 'paid',
    label: '已发放',
    value: '¥0',
    icon: 'Check',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+8%'
  },
  {
    key: 'pending',
    label: '待发放',
    value: '¥0',
    icon: 'Timer',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15%'
  },
  {
    key: 'count',
    label: '总笔数',
    value: '0',
    icon: 'DocumentCopy',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+89'
  }
]);

// 计算属性
const pendingCount = computed(() => {
  return multipleSelection.value.filter(item => item.status === 2).length;
});

onMounted(() => {
  fetchList();
  fetchStats();
});

const fetchList = async () => {
  listLoading.value = true;
  try {
    const params = { ...listQuery };
    if (params.date_range && params.date_range.length === 2) {
      params.start_date = params.date_range[0];
      params.end_date = params.date_range[1];
    }
    delete params.date_range;
    
    const response = await getCommissionList(params);
    list.value = response.data.data;
    total.value = response.data.total;
  } catch (error) {
    ElMessage.error('获取佣金列表失败');
    console.error(error);
  } finally {
    listLoading.value = false;
  }
};

const fetchStats = async () => {
  statsLoading.value = true;
  try {
    const response = await getCommissionStats();
    commissionStats.value = response.data.basic;
    
    // 更新统计卡片数据
    commissionStatCards.value[0].value = '¥' + formatNumber(commissionStats.value.total_amount || 0);
    commissionStatCards.value[1].value = '¥' + formatNumber(commissionStats.value.paid_amount || 0);
    commissionStatCards.value[2].value = '¥' + formatNumber(commissionStats.value.pending_amount || 0);
    commissionStatCards.value[3].value = (commissionStats.value.total_count || 0).toString();
    
    console.log('佣金统计数据加载完成');
  } catch (error) {
    console.error('获取统计数据失败', error);
    ElMessage.error('获取统计数据失败');
  } finally {
    statsLoading.value = false;
  }
};

const handleQuery = () => {
  listQuery.page = 1;
  fetchList();
};

const resetQuery = () => {
  Object.assign(listQuery, {
    page: 1,
    limit: 15,
    order_no: '',
    user_name: '',
    status: '',
    date_range: []
  });
  fetchList();
};

const handleSelectionChange = (selection) => {
  // 确保selection是数组
  const validSelection = Array.isArray(selection) ? selection : []
  multipleSelection.value = validSelection;
};

const handleSettle = (row) => {
  ElMessageBox.confirm(`确定要结算这笔佣金吗 (ID: ${row.id})?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      await settleCommission(row.id);
      ElMessage.success("结算成功");
      fetchList();
      fetchStats();
    } catch (error) {
      ElMessage.error("结算失败");
    }
  });
};

const handleBatchSettle = () => {
  const pendingItems = multipleSelection.value.filter(item => item.status === 2);
  if (pendingItems.length === 0) {
    ElMessage.warning('请选择待结算的佣金记录');
    return;
  }
  const ids = pendingItems.map(item => item.id);
  ElMessageBox.confirm(`确定要结算选中的 ${ids.length} 笔佣金吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      await batchSettleCommissions({ ids });
      ElMessage.success("批量结算成功");
      fetchList();
      fetchStats();
    } catch (error) {
      ElMessage.error("批量结算失败");
    }
  });
};

const handleAdd = () => {
  resetAddForm();
  addDialogVisible.value = true;
};

const submitAdd = () => {
  addFormRef.value.validate(async (valid) => {
    if (valid) {
      addLoading.value = true;
      try {
        await addCommission(addForm);
        ElMessage.success("添加成功");
        addDialogVisible.value = false;
        fetchList();
      } catch (error) {
        ElMessage.error("添加失败");
      } finally {
        addLoading.value = false;
      }
    }
  });
};

const resetAddForm = () => {
  if(addFormRef.value) {
    addFormRef.value.resetFields();
  }
  Object.assign(addForm, {
    order_id: '',
    user_id: '',
    commission_rate: 0,
    commission_amount: 0,
    remark: ''
  });
};

const remoteSearchOrders = async (query) => {
  if (query) {
    try {
      const response = await searchOrders({ keyword: query });
      orderOptions.value = response.data;
    } catch (error) {
      console.error('搜索订单失败', error);
    }
  }
};

const remoteSearchUsers = async (query) => {
  if (query) {
    try {
      const response = await searchUsers({ keyword: query });
      userOptions.value = response.data;
    } catch (error) {
      console.error('搜索用户失败', error);
    }
  }
};

const handleExportCommissions = async () => {
  exportLoading.value = true
  try {
    const params = {
      ...listQuery,
      format: 'excel',
      fields: [
        'id', 'order_no', 'order_amount', 'commission_rate', 
        'commission_amount', 'user_name', 'status', 'created_at'
      ]
    }
    
    const response = await exportCommissions(params)
    
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.download = `佣金明细_${new Date().toLocaleDateString()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const viewDetail = (row) => {
  ElMessage.info(`查看佣金详情：${row.id}`)
}

const editCommission = (row) => {
  ElMessage.info(`编辑佣金：${row.id}`)
}

// 工具函数
const getStatusName = (status) => {
  const statusMap = { 1: '已结算', 2: '待结算', 3: '已取消' };
  return statusMap[status] || '未知';
};

const getStatusTagType = (status) => {
  const typeMap = { 1: 'success', 2: 'warning', 3: 'info' };
  return typeMap[status] || 'info';
};

const getUserLevel = (level) => {
  const levelMap = {
    1: '初级分销商',
    2: '中级分销商',
    3: '高级分销商',
    4: '金牌分销商'
  }
  return levelMap[level] || '普通用户'
}
</script>

<style lang="scss" scoped>
.modern-commission-management {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  // 页面头部样式
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          height: 40px;
          padding: 0 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }

  // 统计卡片区域
  .stats-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 筛选卡片样式
  .filter-card {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .search-input {
      width: 250px;
    }
    
    .filter-select {
      width: 150px;
    }
    
    .date-picker {
      width: 250px;
    }
    
    .search-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      }
    }
    
    .reset-btn {
      background: #f5f7fa;
      border-color: #dcdfe6;
      color: #606266;
      border-radius: 8px;
      
      &:hover {
        background: #ecf5ff;
        border-color: #409eff;
        color: #409eff;
      }
    }
  }

  // 批量操作区域
  .batch-actions {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .selection-alert {
      margin-bottom: 16px;
    }
    
    .batch-buttons {
      display: flex;
      gap: 12px;
    }
  }

  // 表格卡片样式
  .table-card {
    max-width: 1400px;
    margin: 0 auto 40px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
          
          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
          
          .el-tag {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: none;
          }
        }
      }
    }
    
    :deep(.el-card__body) {
      padding: 0;
    }
    
    .modern-table {
      :deep(.el-table__header) {
        background: #fafbfc;
        
        th {
          background: #fafbfc !important;
          border-bottom: 1px solid #e4e7ed;
          font-weight: 600;
          color: #606266;
          font-size: 13px;
          padding: 16px 12px;
        }
      }
      
      :deep(.el-table__body) {
        tr {
          transition: all 0.3s ease;
          
          &:hover {
            background: #f8f9ff !important;
          }
          
          td {
            border-bottom: 1px solid #f0f2f5;
            padding: 16px 12px;
          }
        }
      }
      
      .commission-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .commission-avatar {
          border: 2px solid #f0f2ff;
          transition: all 0.3s ease;
          
          &:hover {
            transform: scale(1.1);
          }
        }
        
        .commission-details {
          text-align: left;
          
          .order-no {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 2px;
          }
          
          .order-amount {
            font-size: 12px;
            color: #909399;
            margin-bottom: 1px;
          }
          
          .commission-id {
            font-size: 11px;
            color: #c0c4cc;
          }
        }
      }
      
      .user-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .user-avatar {
          border: 2px solid #f0f2ff;
          transition: all 0.3s ease;
          
          &:hover {
            transform: scale(1.1);
          }
        }
        
        .user-details {
          text-align: left;
          
          .user-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 2px;
          }
          
          .user-email {
            font-size: 12px;
            color: #909399;
            margin-bottom: 1px;
          }
          
          .user-level {
            font-size: 11px;
            color: #c0c4cc;
          }
        }
      }
      
      .commission-detail {
        text-align: center;
        
        .commission-rate {
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
        }
        
        .commission-amount {
          font-weight: 700;
          color: #67c23a;
          font-size: 16px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .modern-commission-management {
    .stats-container {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }
  }
}

@media (max-width: 900px) {
  .modern-commission-management {
    .stats-container {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      
      .stat-card {
        padding: 16px;
        
        .stat-icon {
          width: 48px;
          height: 48px;
        }
        
        .stat-content .stat-value {
          font-size: 24px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .modern-commission-management {
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .stats-container {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      
      .stat-card {
        padding: 16px;
        
        .stat-icon {
          width: 44px;
          height: 44px;
        }
        
        .stat-content .stat-value {
          font-size: 20px;
        }
        
        .stat-content .stat-label {
          font-size: 12px;
        }
        
        .stat-trend {
          font-size: 10px;
        }
      }
    }
    
    .filter-card {
      .el-form {
        .el-form-item {
          width: 100%;
          margin-bottom: 16px;
          
          .search-input,
          .filter-select,
          .date-picker {
            width: 100%;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .modern-commission-management {
    .stats-container {
      grid-template-columns: 1fr;
      gap: 12px;
      
      .stat-card {
        padding: 18px;
        
        .stat-icon {
          width: 48px;
          height: 48px;
        }
        
        .stat-content .stat-value {
          font-size: 24px;
        }
        
        .stat-content .stat-label {
          font-size: 13px;
        }
      }
    }
  }
}
</style> 