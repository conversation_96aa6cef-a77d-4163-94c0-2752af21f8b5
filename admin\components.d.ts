/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ActivityHeatmap: typeof import('./src/components/dashboard/ActivityHeatmap.vue')['default']
    AIContentGenerator: typeof import('./src/components/AIContentGenerator.vue')['default']
    AIEnhancedSearch: typeof import('./src/components/AIEnhancedSearch.vue')['default']
    AvatarLibrarySelector: typeof import('./src/components/AvatarLibrarySelector.vue')['default']
    AvatarUpload: typeof import('./src/components/AvatarUpload.vue')['default']
    BannerPreview: typeof import('./src/components/preview/BannerPreview.vue')['default']
    BarChart: typeof import('./src/components/Charts/BarChart.vue')['default']
    Bookmarks: typeof import('./src/components/navigation/Bookmarks.vue')['default']
    Breadcrumb: typeof import('./src/components/navigation/Breadcrumb.vue')['default']
    ChinaMapChart: typeof import('./src/components/Charts/ChinaMapChart.vue')['default']
    CircularProgress: typeof import('./src/components/UI/CircularProgress.vue')['default']
    CollapsedTooltips: typeof import('./src/components/navigation/components/CollapsedTooltips.vue')['default']
    CollapseTransition: typeof import('./src/components/transitions/CollapseTransition.vue')['default']
    ContentPreview: typeof import('./src/components/preview/ContentPreview.vue')['default']
    ContentTemplateLibrary: typeof import('./src/components/ContentTemplateLibrary.vue')['default']
    ConversionOptimizer: typeof import('./src/components/ConversionOptimizer.vue')['default']
    CountTo: typeof import('./src/components/CountTo.vue')['default']
    DashboardActivities: typeof import('./src/components/dashboard/DashboardActivities.vue')['default']
    DashboardCard: typeof import('./src/components/dashboard/DashboardCard.vue')['default']
    DashboardCharts: typeof import('./src/components/dashboard/DashboardCharts.vue')['default']
    DashboardMetrics: typeof import('./src/components/dashboard/DashboardMetrics.vue')['default']
    DataTable: typeof import('./src/components/common/DataTable.vue')['default']
    DomainNavigation: typeof import('./src/components/navigation/DomainNavigation.vue')['default']
    DomainNavigationV2: typeof import('./src/components/navigation/DomainNavigationV2.vue')['default']
    DomainSection: typeof import('./src/components/navigation/components/DomainSection.vue')['default']
    DoughnutChart: typeof import('./src/components/Charts/DoughnutChart.vue')['default']
    DragDropEditor: typeof import('./src/components/DragDropEditor.vue')['default']
    DynamicForm: typeof import('./src/components/common/DynamicForm.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeletonItem: typeof import('element-plus/es')['ElSkeletonItem']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Enhanced404: typeof import('./src/components/Enhanced404.vue')['default']
    EnhancedGlobalSearch: typeof import('./src/components/EnhancedGlobalSearch.vue')['default']
    EnhancedGlobalSearchV2: typeof import('./src/components/EnhancedGlobalSearchV2.vue')['default']
    EnhancedNavigationSystem: typeof import('./src/components/navigation/EnhancedNavigationSystem.vue')['default']
    EnhancedSidebarItem: typeof import('./src/components/layout/EnhancedSidebarItem.vue')['default']
    EnhancedStatCard: typeof import('./src/components/dashboard/EnhancedStatCard.vue')['default']
    ErrorBoundary: typeof import('./src/components/ErrorBoundary.vue')['default']
    FileUploader: typeof import('./src/components/FileUploader.vue')['default']
    FunnelChart: typeof import('./src/components/Charts/FunnelChart.vue')['default']
    GalleryPreview: typeof import('./src/components/preview/GalleryPreview.vue')['default']
    GlobalSearch: typeof import('./src/components/navigation/GlobalSearch.vue')['default']
    GroupActivityWidget: typeof import('./src/components/widgets/GroupActivityWidget.vue')['default']
    GroupCreateForm: typeof import('./src/components/GroupCreateForm.vue')['default']
    GroupCreateSteps: typeof import('./src/components/GroupCreateSteps.vue')['default']
    GroupLandingPreview: typeof import('./src/components/GroupLandingPreview.vue')['default']
    GroupTemplateSelector: typeof import('./src/components/GroupTemplateSelector.vue')['default']
    GuideCard: typeof import('./src/components/UI/GuideCard.vue')['default']
    HelpTip: typeof import('./src/components/UI/HelpTip.vue')['default']
    ImageUpload: typeof import('./src/components/Upload/ImageUpload.vue')['default']
    InfoPreview: typeof import('./src/components/preview/InfoPreview.vue')['default']
    IntelligentWorkbench: typeof import('./src/components/IntelligentWorkbench.vue')['default']
    LandingPagePreview: typeof import('./src/components/LandingPagePreview.vue')['default']
    LandingPageTemplates: typeof import('./src/components/LandingPageTemplates.vue')['default']
    LayoutDesigner: typeof import('./src/components/LayoutDesigner.vue')['default']
    LineChart: typeof import('./src/components/Charts/LineChart.vue')['default']
    LoadingPage: typeof import('./src/components/LoadingPage.vue')['default']
    MediaCarousel: typeof import('./src/components/MediaCarousel.vue')['default']
    MediaUploader: typeof import('./src/components/MediaUploader.vue')['default']
    MembersPreview: typeof import('./src/components/preview/MembersPreview.vue')['default']
    MetricsGrid: typeof import('./src/components/dashboard/MetricsGrid.vue')['default']
    MiniLineChart: typeof import('./src/components/Charts/MiniLineChart.vue')['default']
    MobileNavigation: typeof import('./src/components/navigation/MobileNavigation.vue')['default']
    ModernDashboard: typeof import('./src/components/dashboard/ModernDashboard.vue')['default']
    ModernLayout: typeof import('./src/components/layout/ModernLayout.vue')['default']
    ModernMenuItem: typeof import('./src/components/layout/ModernMenuItem.vue')['default']
    ModernNavigationSidebar: typeof import('./src/components/navigation/ModernNavigationSidebar.vue')['default']
    ModernRichTextEditor: typeof import('./src/components/ModernRichTextEditor.vue')['default']
    ModuleItem: typeof import('./src/components/navigation/components/ModuleItem.vue')['default']
    NavigationEnhancer: typeof import('./src/components/NavigationEnhancer.vue')['default']
    NavigationExample: typeof import('./src/components/navigation/NavigationExample.vue')['default']
    NavigationGroupItem: typeof import('./src/components/navigation/NavigationGroupItem.vue')['default']
    NavigationHeader: typeof import('./src/components/navigation/NavigationHeader.vue')['default']
    NavigationMenuItem: typeof import('./src/components/navigation/NavigationMenuItem.vue')['default']
    NavigationSidebar: typeof import('./src/components/navigation/NavigationSidebar.vue')['default']
    NavigationTestPanel: typeof import('./src/components/NavigationTestPanel.vue')['default']
    NavigationUserPanel: typeof import('./src/components/navigation/NavigationUserPanel.vue')['default']
    NotificationCenter: typeof import('./src/components/dashboard/NotificationCenter.vue')['default']
    NotificationDrawer: typeof import('./src/components/NotificationDrawer.vue')['default']
    OptimizedNavigation: typeof import('./src/components/OptimizedNavigation.vue')['default']
    OptimizedSidebar: typeof import('./src/components/layout/OptimizedSidebar.vue')['default']
    OrderDetailDialog: typeof import('./src/components/OrderDetailDialog.vue')['default']
    PageLayout: typeof import('./src/components/layout/PageLayout.vue')['default']
    Pagination: typeof import('./src/components/Pagination/index.vue')['default']
    PaidContentEditor: typeof import('./src/components/PaidContentEditor.vue')['default']
    PaymentInfoDialog: typeof import('./src/components/PaymentInfoDialog.vue')['default']
    PerformanceMonitor: typeof import('./src/components/PerformanceMonitor.vue')['default']
    PieChart: typeof import('./src/components/Charts/PieChart.vue')['default']
    PopularGroups: typeof import('./src/components/dashboard/PopularGroups.vue')['default']
    PreviewDialog: typeof import('./src/components/PreviewDialog.vue')['default']
    PromotionAnalytics: typeof import('./src/components/PromotionAnalytics.vue')['default']
    QRCodeGenerator: typeof import('./src/components/QRCodeGenerator.vue')['default']
    QuickActionBar: typeof import('./src/components/navigation/components/QuickActionBar.vue')['default']
    QuickActions: typeof import('./src/components/dashboard/QuickActions.vue')['default']
    QuickCreateWizard: typeof import('./src/components/QuickCreateWizard.vue')['default']
    QuickStats: typeof import('./src/components/dashboard/QuickStats.vue')['default']
    RealtimeChart: typeof import('./src/components/dashboard/RealtimeChart.vue')['default']
    RecentActivities: typeof import('./src/components/dashboard/RecentActivities.vue')['default']
    RecentOrders: typeof import('./src/components/dashboard/RecentOrders.vue')['default']
    RecommendationPanel: typeof import('./src/components/navigation/components/RecommendationPanel.vue')['default']
    RefundDialog: typeof import('./src/components/RefundDialog.vue')['default']
    ResponsiveLayout: typeof import('./src/components/layout/ResponsiveLayout.vue')['default']
    ResponsiveTable: typeof import('./src/components/ResponsiveTable.vue')['default']
    RoleSwitcher: typeof import('./src/components/RoleSwitcher.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchHeader: typeof import('./src/components/navigation/components/SearchHeader.vue')['default']
    ShortcutHelp: typeof import('./src/components/ShortcutHelp.vue')['default']
    SimpleLayout: typeof import('./src/components/layout/SimpleLayout.vue')['default']
    SmartBreadcrumb: typeof import('./src/components/navigation/SmartBreadcrumb.vue')['default']
    SmartCityReplacement: typeof import('./src/components/SmartCityReplacement.vue')['default']
    SocialShare: typeof import('./src/components/SocialShare.vue')['default']
    StatCard: typeof import('./src/components/dashboard/StatCard.vue')['default']
    StatsOverviewWidget: typeof import('./src/components/widgets/StatsOverviewWidget.vue')['default']
    SystemStatus: typeof import('./src/components/dashboard/SystemStatus.vue')['default']
    VideoPlayer: typeof import('./src/components/VideoPlayer.vue')['default']
    VideoPreview: typeof import('./src/components/preview/VideoPreview.vue')['default']
    VideoUploader: typeof import('./src/components/VideoUploader.vue')['default']
    VirtualScrollList: typeof import('./src/components/common/VirtualScrollList.vue')['default']
    WelcomeBanner: typeof import('./src/components/dashboard/WelcomeBanner.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
