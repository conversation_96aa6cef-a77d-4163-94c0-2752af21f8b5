#!/usr/bin/env node

/**
 * 系统状态检查脚本
 * 检查所有组件是否正常工作
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  title: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}`)
}

// 检查结果统计
let checkResults = {
  passed: 0,
  failed: 0,
  warnings: 0
}

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    log.success(`${description}: ${filePath}`)
    checkResults.passed++
    return true
  } else {
    log.error(`${description}不存在: ${filePath}`)
    checkResults.failed++
    return false
  }
}

/**
 * 检查目录是否存在
 */
function checkDirectoryExists(dirPath, description) {
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    log.success(`${description}: ${dirPath}`)
    checkResults.passed++
    return true
  } else {
    log.error(`${description}不存在: ${dirPath}`)
    checkResults.failed++
    return false
  }
}

/**
 * 执行命令并检查结果
 */
function runCommand(command, description, options = {}) {
  try {
    const result = execSync(command, { 
      encoding: 'utf8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    })
    log.success(`${description}: 成功`)
    checkResults.passed++
    return result
  } catch (error) {
    log.error(`${description}: 失败`)
    if (!options.silent) {
      console.error(error.message)
    }
    checkResults.failed++
    return null
  }
}

/**
 * 检查Node.js版本
 */
function checkNodeVersion() {
  log.title('🔍 检查Node.js环境')
  
  try {
    const version = process.version
    const majorVersion = parseInt(version.slice(1).split('.')[0])
    
    if (majorVersion >= 18) {
      log.success(`Node.js版本: ${version} (推荐18+)`)
      checkResults.passed++
    } else {
      log.warning(`Node.js版本: ${version} (建议升级到18+)`)
      checkResults.warnings++
    }
  } catch (error) {
    log.error('无法检查Node.js版本')
    checkResults.failed++
  }
}

/**
 * 检查PHP环境
 */
function checkPhpEnvironment() {
  log.title('🐘 检查PHP环境')
  
  // 检查PHP版本
  const phpVersion = runCommand('php --version', 'PHP版本检查', { silent: true })
  if (phpVersion) {
    const version = phpVersion.split('\n')[0]
    log.info(`PHP版本: ${version}`)
  }
  
  // 检查Composer
  runCommand('composer --version', 'Composer检查', { silent: true })
  
  // 检查PHP扩展
  const extensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'tokenizer', 'xml', 'ctype', 'json', 'bcmath']
  extensions.forEach(ext => {
    runCommand(`php -m | grep -i ${ext}`, `PHP扩展 ${ext}`, { silent: true })
  })
}

/**
 * 检查项目文件结构
 */
function checkProjectStructure() {
  log.title('📁 检查项目文件结构')
  
  // 核心文件
  const coreFiles = [
    'composer.json',
    'package.json',
    '.env.example',
    'artisan',
    'admin/package.json',
    'frontend/package.json'
  ]
  
  coreFiles.forEach(file => {
    checkFileExists(file, '核心文件')
  })
  
  // 核心目录
  const coreDirectories = [
    'app',
    'config',
    'database',
    'routes',
    'admin/src',
    'frontend/pages',
    'docs'
  ]
  
  coreDirectories.forEach(dir => {
    checkDirectoryExists(dir, '核心目录')
  })
}

/**
 * 检查数据大屏组件
 */
function checkDataScreenComponents() {
  log.title('📊 检查数据大屏组件')
  
  const dataScreenFiles = [
    'admin/src/views/dashboard/UltraDataScreen.vue',
    'admin/src/views/dashboard/EnhancedDataScreen.vue',
    'admin/src/views/dashboard/DataScreenDemo.vue',
    'admin/src/router/dataScreen.js',
    'docs/DATA_SCREEN_GUIDE.md'
  ]
  
  dataScreenFiles.forEach(file => {
    checkFileExists(file, '数据大屏组件')
  })
}

/**
 * 检查导航系统组件
 */
function checkNavigationComponents() {
  log.title('🧭 检查导航系统组件')
  
  const navigationFiles = [
    'admin/src/components/navigation/EnhancedNavigationSystem.vue',
    'admin/src/components/navigation/NavigationExample.vue',
    'admin/src/styles/navigation.scss',
    'frontend/layouts/default.vue',
    'docs/NAVIGATION_OPTIMIZATION_GUIDE.md'
  ]
  
  navigationFiles.forEach(file => {
    checkFileExists(file, '导航系统组件')
  })
}

/**
 * 检查样式文件
 */
function checkStyleFiles() {
  log.title('🎨 检查样式文件')
  
  const styleFiles = [
    'admin/src/styles/navigation.scss',
    'admin/src/styles/main.scss'
  ]
  
  styleFiles.forEach(file => {
    checkFileExists(file, '样式文件')
  })
}

/**
 * 检查依赖包
 */
function checkDependencies() {
  log.title('📦 检查依赖包')
  
  // 检查后端依赖
  if (fs.existsSync('vendor')) {
    log.success('后端依赖已安装: vendor/')
    checkResults.passed++
  } else {
    log.warning('后端依赖未安装，请运行: composer install')
    checkResults.warnings++
  }
  
  // 检查管理端依赖
  if (fs.existsSync('admin/node_modules')) {
    log.success('管理端依赖已安装: admin/node_modules/')
    checkResults.passed++
  } else {
    log.warning('管理端依赖未安装，请运行: cd admin && npm install')
    checkResults.warnings++
  }
  
  // 检查前端依赖
  if (fs.existsSync('frontend/node_modules')) {
    log.success('前端依赖已安装: frontend/node_modules/')
    checkResults.passed++
  } else {
    log.warning('前端依赖未安装，请运行: cd frontend && npm install')
    checkResults.warnings++
  }
}

/**
 * 检查配置文件
 */
function checkConfigFiles() {
  log.title('⚙️ 检查配置文件')
  
  // 检查环境配置
  if (fs.existsSync('.env')) {
    log.success('后端环境配置: .env')
    checkResults.passed++
  } else {
    log.warning('后端环境配置不存在，请复制: cp .env.example .env')
    checkResults.warnings++
  }
  
  // 检查前端配置
  const frontendConfigs = [
    'admin/.env.local',
    'frontend/.env.local'
  ]
  
  frontendConfigs.forEach(config => {
    if (fs.existsSync(config)) {
      log.success(`前端配置: ${config}`)
      checkResults.passed++
    } else {
      log.warning(`前端配置不存在: ${config}`)
      checkResults.warnings++
    }
  })
}

/**
 * 检查数据库连接
 */
function checkDatabase() {
  log.title('🗄️ 检查数据库')
  
  if (fs.existsSync('.env')) {
    try {
      const envContent = fs.readFileSync('.env', 'utf8')
      const dbHost = envContent.match(/DB_HOST=(.+)/)?.[1] || 'localhost'
      const dbName = envContent.match(/DB_DATABASE=(.+)/)?.[1] || 'linkhub_pro'
      
      log.info(`数据库主机: ${dbHost}`)
      log.info(`数据库名称: ${dbName}`)
      
      // 尝试连接数据库
      runCommand('php artisan migrate:status', '数据库连接测试', { silent: true })
    } catch (error) {
      log.warning('无法读取数据库配置')
      checkResults.warnings++
    }
  }
}

/**
 * 检查构建文件
 */
function checkBuildFiles() {
  log.title('🏗️ 检查构建文件')
  
  const buildDirs = [
    'admin/dist',
    'frontend/.nuxt',
    'public/build'
  ]
  
  buildDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      log.success(`构建目录存在: ${dir}`)
      checkResults.passed++
    } else {
      log.info(`构建目录不存在: ${dir} (首次运行正常)`)
    }
  })
}

/**
 * 检查权限
 */
function checkPermissions() {
  log.title('🔐 检查文件权限')
  
  const writableDirs = [
    'storage',
    'bootstrap/cache'
  ]
  
  writableDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      try {
        fs.accessSync(dir, fs.constants.W_OK)
        log.success(`目录可写: ${dir}`)
        checkResults.passed++
      } catch (error) {
        log.error(`目录不可写: ${dir}`)
        checkResults.failed++
      }
    } else {
      log.warning(`目录不存在: ${dir}`)
      checkResults.warnings++
    }
  })
}

/**
 * 生成系统报告
 */
function generateReport() {
  log.title('📋 系统检查报告')
  
  const total = checkResults.passed + checkResults.failed + checkResults.warnings
  const successRate = ((checkResults.passed / total) * 100).toFixed(1)
  
  console.log(`\n${colors.bright}检查结果统计:${colors.reset}`)
  console.log(`${colors.green}✓ 通过: ${checkResults.passed}${colors.reset}`)
  console.log(`${colors.red}✗ 失败: ${checkResults.failed}${colors.reset}`)
  console.log(`${colors.yellow}⚠ 警告: ${checkResults.warnings}${colors.reset}`)
  console.log(`${colors.cyan}📊 成功率: ${successRate}%${colors.reset}`)
  
  if (checkResults.failed === 0) {
    console.log(`\n${colors.green}${colors.bright}🎉 系统检查完成，所有核心组件正常！${colors.reset}`)
  } else {
    console.log(`\n${colors.yellow}${colors.bright}⚠️ 发现 ${checkResults.failed} 个问题，请检查上述错误信息${colors.reset}`)
  }
  
  // 生成建议
  console.log(`\n${colors.bright}建议操作:${colors.reset}`)
  
  if (checkResults.warnings > 0) {
    console.log(`${colors.yellow}1. 安装缺失的依赖包${colors.reset}`)
    console.log(`${colors.yellow}2. 配置环境变量文件${colors.reset}`)
    console.log(`${colors.yellow}3. 运行数据库迁移${colors.reset}`)
  }
  
  if (checkResults.failed > 0) {
    console.log(`${colors.red}4. 修复文件权限问题${colors.reset}`)
    console.log(`${colors.red}5. 检查缺失的核心文件${colors.reset}`)
  }
  
  console.log(`${colors.cyan}6. 运行构建命令生成前端资源${colors.reset}`)
  console.log(`${colors.cyan}7. 启动开发服务器进行测试${colors.reset}`)
}

/**
 * 主函数
 */
function main() {
  console.log(`${colors.bright}${colors.magenta}`)
  console.log('╔══════════════════════════════════════════════════════════════╗')
  console.log('║                    晨鑫流量变现系统 系统检查                      ║')
  console.log('║                      System Health Check                     ║')
  console.log('╚══════════════════════════════════════════════════════════════╝')
  console.log(`${colors.reset}`)
  
  // 执行所有检查
  checkNodeVersion()
  checkPhpEnvironment()
  checkProjectStructure()
  checkDataScreenComponents()
  checkNavigationComponents()
  checkStyleFiles()
  checkDependencies()
  checkConfigFiles()
  checkDatabase()
  checkBuildFiles()
  checkPermissions()
  
  // 生成报告
  generateReport()
  
  // 退出码
  process.exit(checkResults.failed > 0 ? 1 : 0)
}

// 运行检查
if (require.main === module) {
  main()
}

module.exports = {
  checkFileExists,
  checkDirectoryExists,
  runCommand,
  checkResults
}