/node_modules
/public/hot
/public/storage
/storage/*.key
/vendor
.env
.env.backup
.env.production
.env.local
.env.testing.local
*.env.local

# API密钥和敏感配置
config/api-keys.php
config/secrets.php
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode

# Frontend build directories
/admin/dist
/admin/node_modules
/frontend/dist
/frontend/node_modules
/frontend/.output
/frontend/.nuxt

# ===========================================
# 开发规范 - 禁止提交的文件类型
# ===========================================

# 调试文件
*-test.html
*-debug.html
*-fix-*.html
*-diagnosis.html
*-preview-test.html
*-verification.html
*-complete.html
*-report.html
Mock*调试*.html
admin-direct.html
admin-preview*.html
centered-button-example.html
start-admin-preview.html
unified-login.html
*美化效果展示.html

# 临时脚本文件
debug-*.js
debug-*.cjs
fix-*.js
temp-*.js
*-temp.*
quick-fix.*
*-调试.js
*-配置测试.js
导航菜单调试.js
导航配置测试.js

# 批处理和脚本文件
quick-fix*.bat
debug-*.bat
fix-*.bat
start-*.bat
temp*.bat
ultimate-fix.bat
server-monitor.bat
stable-server.bat
*-deploy.sh
*-fix.sh
optimize-*.sh
baota-*.sh
新手*.sh
宝塔*.sh
前端构建*.sh

# PowerShell脚本
deploy-*.ps1
start-*.ps1
fix-*.ps1

# 文档报告文件
*-report.md
*-analysis.md
*-fix-*.md
*-检测报告.md
*-修复报告.md
*-分析报告.md
*-实施报告.md
*-优化报告.md
*-完成报告.md
*-指南.md
*-说明.md
功能*报告.md
系统*报告.md
管理后台*报告.md
群组创建*报告.md
防红系统*报告.md
代理商*报告.md
导航*报告.md
登录页面*报告.md
Vue组件*报告.md
DEPLOYMENT_*.md
LOGIN_SYSTEM_*.md
ENHANCED_FEATURES_*.md
LINKHUB_PRO_*.md

# 备份文件
*.backup
*.bak
*.old
*-backup.*
*-old.*
*.vue.backup

# 临时文件
*.tmp
*.temp
*~
.DS_Store
Thumbs.db

# 测试和调试相关
test-*.php
check-*.php
*-test-*.html
browser-test.html
vue-test.html
test-simple.html
test-navigation.html
test-preview*.html
height-overflow-test.html
login-functionality-test.html

# 临时服务器文件
preview-server.js
simple-server.*
*-server.js
*-server.cjs

# 配置文件临时版本
*-rewrite-rules.conf
baota-*.conf

# 日志和缓存
*.log
logs/
cache/
.cache/

# 编辑器临时文件
.vscode/settings.json
.idea/
*.swp
*.swo
*~

# 系统文件
nul
.DS_Store
Thumbs.db
desktop.ini