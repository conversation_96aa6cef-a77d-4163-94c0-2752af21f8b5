/* empty css             *//* empty css                   *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                  *//* empty css                        *//* empty css                       *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                        *//* empty css                         *//* empty css                  *//* empty css                *//* empty css                     */import{l as e,q as a,G as t,A as l,B as r,F as n,Y as s,r as o,M as i,o as d,m as u,E as c,z as m,D as p,K as g,W as _,C as v}from"./vue-vendor-BcnDv-68.js";import{a7 as f,ah as y,a1 as h,V as b,W as w,X as k,at as C,au as V,av as A,aw as j,ax as F,ay as S,az as x,aA as z,aB as U,aC as D,aD as L,aE as T,af as M,ae as B,aF as q,ag as $,u as P,Z as O,s as E,a2 as G,a3 as I,a4 as Q,a5 as R,a6 as K,ai as N,aj as W,aG as Y,ak as Z,aH as H,aI as J,aJ as X,an as ee,aK as ae,_ as te,aL as le,aq as re,a0 as ne}from"./element-plus-C2UshkXo.js";import{S as se}from"./StatCard-CLi1H67J.js";import{a as oe}from"./agent-CxsSQ9z5.js";import{_ as ie}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const de={class:"modern-agent-list"},ue={class:"page-header"},ce={class:"header-content"},me={class:"header-left"},pe={class:"header-icon"},ge={class:"header-actions"},_e={class:"stats-section"},ve={class:"stats-container"},fe={class:"stat-content"},ye={class:"stat-value"},he={class:"stat-label"},be={class:"card-header"},we={class:"header-left"},ke={class:"header-right"},Ce={class:"agent-info"},Ve={class:"agent-details"},Ae={class:"agent-name"},je={class:"agent-code"},Fe={key:0,class:"agent-user"},Se={key:0,class:"no-commission"},xe={key:1,class:"commission-rate"},ze={class:"amount"},Ue={key:0,class:"permanent"},De={key:2,class:"no-expiry"},Le={class:"pagination"};const Te=ie({__name:"AgentList",setup(e,{expose:a}){a();const t=o(!1),l=o(!1),r=o(!0),n=o(1),s=o(20),u=o(!1),c=o("table"),m=o([]),p=o({}),g=o({data:[],total:0}),_=o([]),v=o([{key:"total",label:"总代理商",value:"156",icon:"Avatar",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"ArrowUp",change:"+12%"},{key:"platform",label:"平台代理商",value:"89",icon:"Star",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"ArrowUp",change:"+8%"},{key:"substation",label:"分站代理商",value:"67",icon:"OfficeBuilding",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"ArrowUp",change:"+15%"},{key:"commission",label:"总佣金",value:"¥128,567",icon:"Check",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"ArrowUp",change:"+23.5%"}]),f=i({keyword:"",agent_level:"",agent_type:"",status:""}),y=i({user_id:"",agent_name:"",agent_level:"platform",agent_type:"individual",commission_rate:10,validity_period:"year",custom_end_date:"",remark:""}),h=o(),b=async()=>{try{const e=await oe.getStats();p.value=e.data}catch(e){k.error("加载统计数据失败")}},w=async()=>{try{t.value=!0;const e={page:n.value,limit:s.value,...f},a=await oe.getList(e);g.value=a.data}catch(e){k.error("加载代理商列表失败")}finally{t.value=!1}},P=()=>{n.value=1,w()},O=async()=>{_.value=[{id:1,name:"张三",username:"zhangsan"},{id:2,name:"李四",username:"lisi"}]},E=async e=>{try{await $.confirm(`确定要删除代理商 ${e.agent_name} 吗？`,"确认删除",{type:"warning"}),await oe.delete(e.id),k.success("删除成功"),w(),b()}catch(a){"cancel"!==a&&k.error("删除失败")}},G=async()=>{try{t.value=!0,await new Promise(e=>setTimeout(e,500)),g.value={data:[{id:1,agent_name:"张三代理",agent_code:"AG001",agent_level:"platform",agent_type:"individual",commission_rate:10.5,total_commission:15678.9,status:"active",is_permanent:!0,end_date:null,user:{id:1,username:"zhangsan",name:"张三"}},{id:2,agent_name:"李四企业代理",agent_code:"AG002",agent_level:"substation",agent_type:"enterprise",commission_rate:8,total_commission:8945.3,status:"active",is_permanent:!1,end_date:"2024-12-31",user:{id:2,username:"lisi",name:"李四"}},{id:3,agent_name:"王五渠道代理",agent_code:"AG003",agent_level:"platform",agent_type:"channel",commission_rate:12,total_commission:23456.78,status:"inactive",is_permanent:!1,end_date:"2024-06-30",user:{id:3,username:"wangwu",name:"王五"}}],total:3}}catch(e){k.error("加载代理商列表失败")}finally{t.value=!1}},I=async()=>{try{r.value=!0,await new Promise(e=>setTimeout(e,300)),v.value[0].value="156",v.value[1].value="89",v.value[2].value="67",v.value[3].value="¥128,567"}catch(e){k.error("加载统计数据失败")}finally{r.value=!1}};d(()=>{I(),G()});const Q={loading:t,createLoading:l,statsLoading:r,currentPage:n,pageSize:s,createDialogVisible:u,viewMode:c,selectedAgents:m,agentStats:p,agents:g,availableUsers:_,agentStatCards:v,searchForm:f,createForm:y,createRules:{user_id:[{required:!0,message:"请选择关联用户",trigger:"change"}],agent_name:[{required:!0,message:"请输入代理商名称",trigger:"blur"}],agent_level:[{required:!0,message:"请选择代理商等级",trigger:"change"}],agent_type:[{required:!0,message:"请选择代理商类型",trigger:"change"}],commission_rate:[{required:!0,message:"请输入佣金比例",trigger:"blur"}],validity_period:[{required:!0,message:"请选择有效期",trigger:"change"}]},createFormRef:h,loadAgentStats:b,loadAgents:w,handleQuery:P,resetQuery:()=>{Object.keys(f).forEach(e=>{f[e]=""}),P()},handleSelectionChange:e=>{m.value=e},handleExport:async()=>{try{k.success("导出功能开发中...")}catch(e){k.error("导出失败")}},handleBatchOperation:async()=>{if(0!==m.value.length)try{await $.confirm(`确定要操作选中的 ${m.value.length} 个代理商吗？`,"批量操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),k.success("批量操作成功"),w(),b()}catch(e){"cancel"!==e&&k.error("批量操作失败")}else k.warning("请选择要操作的代理商")},handleAdd:()=>{u.value=!0,O()},editAgent:e=>{k.info(`编辑代理商 ${e.agent_name}`)},toggleStatus:async e=>{try{await $.confirm(`确定要${"active"===e.status?"禁用":"启用"}代理商 ${e.agent_name} 吗？`,"状态切换",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.status="active"===e.status?"inactive":"active",k.success(("active"===e.status?"启用":"禁用")+"成功")}catch(a){"cancel"!==a&&k.error("操作失败")}},handleSizeChange:e=>{s.value=e,w()},handleCurrentChange:e=>{n.value=e,w()},showCreateDialog:()=>{u.value=!0,O()},loadAvailableUsers:O,confirmCreate:async()=>{try{await h.value.validate(),l.value=!0,await oe.create(y),k.success("代理商创建成功"),u.value=!1,w(),b()}catch(e){!1!==e&&k.error("创建代理商失败")}finally{l.value=!1}},viewAgent:e=>{k.info(`查看代理商 ${e.agent_name} 详情`)},handleCommand:({action:e,row:a})=>{switch(e){case"edit":k.info(`编辑代理商 ${a.agent_name}`);break;case"renew":k.info(`续费代理商 ${a.agent_name}`);break;case"status":k.info(`管理代理商 ${a.agent_name} 状态`);break;case"delete":E(a)}},handleDelete:E,formatDate:e=>e?new Date(e).toLocaleDateString("zh-CN"):"",getStatusColor:e=>({active:"success",inactive:"info",suspended:"warning",expired:"danger"}[e]||"info"),getStatusText:e=>({active:"正常",inactive:"未激活",suspended:"暂停",expired:"已过期"}[e]||"未知"),getAgentLevelColor:e=>"platform"===e?"primary":"success",getAgentTypeColor:e=>({individual:"primary",enterprise:"success",channel:"warning"}[e]||"info"),getAgentTypeText:e=>({individual:"个人代理",enterprise:"企业代理",channel:"渠道代理"}[e]||"未知"),getValidityClass:e=>{const a=new Date,t=new Date(e),l=Math.ceil((t-a)/864e5);return l<0?"expired":l<=7?"expiring-soon":"valid"},loadMockAgents:G,loadMockStats:I,ref:o,reactive:i,onMounted:d,get ElMessage(){return k},get ElMessageBox(){return $},get Search(){return q},get Refresh(){return B},get Plus(){return M},get ArrowDown(){return T},get Avatar(){return L},get Check(){return D},get Star(){return U},get OfficeBuilding(){return z},get RefreshLeft(){return x},get Operation(){return S},get Download(){return F},get List(){return j},get Grid(){return A},get UserFilled(){return V},get ArrowUp(){return C},StatCard:se,get agentApi(){return oe}};return Object.defineProperty(Q,"__isScriptSetup",{enumerable:!1,value:!0}),Q}},[["render",function(o,i,d,k,C,V){const A=y,j=h,F=Q,S=I,x=K,z=R,U=G,D=b,L=Z,T=ae,M=W,B=Y,q=X,$=J,se=H,oe=N,ie=ee,Te=le,Me=te,Be=re,qe=ne,$e=w,Pe=f;return u(),e("div",de,[a("div",ue,[a("div",ce,[a("div",me,[a("div",pe,[t(A,{size:"24"},{default:l(()=>[t(k.Avatar)]),_:1})]),i[18]||(i[18]=a("div",{class:"header-text"},[a("h1",null,"代理商管理"),a("p",null,"全面管理平台代理商，包括代理商信息、状态管理、绩效分析和等级管理")],-1))]),a("div",ge,[t(j,{onClick:k.handleExport,class:"action-btn secondary"},{default:l(()=>[t(A,null,{default:l(()=>[t(k.Download)]),_:1}),i[19]||(i[19]=c(" 导出数据 ",-1))]),_:1,__:[19]}),t(j,{onClick:k.handleBatchOperation,class:"action-btn secondary"},{default:l(()=>[t(A,null,{default:l(()=>[t(k.Operation)]),_:1}),i[20]||(i[20]=c(" 批量操作 ",-1))]),_:1,__:[20]}),t(j,{type:"primary",onClick:k.handleAdd,class:"action-btn primary"},{default:l(()=>[t(A,null,{default:l(()=>[t(k.Plus)]),_:1}),i[21]||(i[21]=c(" 新增代理商 ",-1))]),_:1,__:[21]})])])]),a("div",_e,[r((u(),e("div",ve,[(u(!0),e(n,null,s(k.agentStatCards,r=>(u(),e("div",{class:"stat-card",key:r.key},[a("div",{class:"stat-icon",style:P({background:r.color})},[t(A,{size:"20"},{default:l(()=>[(u(),m(p(r.icon)))]),_:2},1024)],4),a("div",fe,[a("div",ye,O(r.value),1),a("div",he,O(r.label),1)]),a("div",{class:E(["stat-trend",r.trend])},[t(A,{size:"14"},{default:l(()=>[(u(),m(p(r.trendIcon)))]),_:2},1024),a("span",null,O(r.change),1)],2)]))),128))])),[[Pe,k.statsLoading]])]),t(D,{class:"filter-card"},{default:l(()=>[t(U,{inline:!0,model:k.searchForm,onSubmit:g(k.handleQuery,["prevent"])},{default:l(()=>[t(S,{label:"关键词"},{default:l(()=>[t(F,{modelValue:k.searchForm.keyword,"onUpdate:modelValue":i[0]||(i[0]=e=>k.searchForm.keyword=e),placeholder:"搜索代理商名称或编码",clearable:"",onKeyup:_(k.handleQuery,["enter"]),class:"search-input"},null,8,["modelValue"])]),_:1}),t(S,{label:"代理商等级"},{default:l(()=>[t(z,{modelValue:k.searchForm.agent_level,"onUpdate:modelValue":i[1]||(i[1]=e=>k.searchForm.agent_level=e),placeholder:"全部等级",clearable:"",class:"filter-select"},{default:l(()=>[t(x,{label:"平台代理商",value:"platform"}),t(x,{label:"分站代理商",value:"substation"})]),_:1},8,["modelValue"])]),_:1}),t(S,{label:"代理商类型"},{default:l(()=>[t(z,{modelValue:k.searchForm.agent_type,"onUpdate:modelValue":i[2]||(i[2]=e=>k.searchForm.agent_type=e),placeholder:"全部类型",clearable:"",class:"filter-select"},{default:l(()=>[t(x,{label:"个人代理",value:"individual"}),t(x,{label:"企业代理",value:"enterprise"}),t(x,{label:"渠道代理",value:"channel"})]),_:1},8,["modelValue"])]),_:1}),t(S,{label:"状态"},{default:l(()=>[t(z,{modelValue:k.searchForm.status,"onUpdate:modelValue":i[3]||(i[3]=e=>k.searchForm.status=e),placeholder:"全部状态",clearable:"",class:"filter-select"},{default:l(()=>[t(x,{label:"正常",value:"active"}),t(x,{label:"未激活",value:"inactive"}),t(x,{label:"暂停",value:"suspended"}),t(x,{label:"已过期",value:"expired"})]),_:1},8,["modelValue"])]),_:1}),t(S,null,{default:l(()=>[t(j,{type:"primary",onClick:k.handleQuery,class:"search-btn"},{default:l(()=>[t(A,null,{default:l(()=>[t(k.Search)]),_:1}),i[22]||(i[22]=c(" 查询 ",-1))]),_:1,__:[22]}),t(j,{onClick:k.resetQuery,class:"reset-btn"},{default:l(()=>[t(A,null,{default:l(()=>[t(k.RefreshLeft)]),_:1}),i[23]||(i[23]=c(" 重置 ",-1))]),_:1,__:[23]})]),_:1})]),_:1},8,["model"])]),_:1}),t(D,{class:"table-card"},{header:l(()=>[a("div",be,[a("div",we,[i[24]||(i[24]=a("h3",null,"代理商列表",-1)),t(L,{size:"small",type:"info"},{default:l(()=>[c("共 "+O(k.agents.total||0)+" 条记录",1)]),_:1})]),a("div",ke,[t(T,null,{default:l(()=>[t(j,{size:"small",type:"table"===k.viewMode?"primary":"",onClick:i[4]||(i[4]=e=>k.viewMode="table")},{default:l(()=>[t(A,null,{default:l(()=>[t(k.List)]),_:1})]),_:1},8,["type"]),t(j,{size:"small",type:"card"===k.viewMode?"primary":"",onClick:i[5]||(i[5]=e=>k.viewMode="card")},{default:l(()=>[t(A,null,{default:l(()=>[t(k.Grid)]),_:1})]),_:1},8,["type"])]),_:1})])])]),default:l(()=>["table"===k.viewMode?r((u(),m(oe,{key:0,data:k.agents.data,onSelectionChange:k.handleSelectionChange,class:"modern-table"},{default:l(()=>[t(M,{type:"selection",width:"55",align:"center"}),t(M,{label:"代理商信息",width:"220"},{default:l(({row:r})=>[a("div",Ce,[t(B,{size:40,class:"agent-avatar"},{default:l(()=>[t(A,null,{default:l(()=>[t(k.Avatar)]),_:1})]),_:1}),a("div",Ve,[a("div",Ae,O(r.agent_name),1),a("div",je,"编码: "+O(r.agent_code),1),r.user?(u(),e("div",Fe,"用户: "+O(r.user.username),1)):v("",!0)])])]),_:1}),t(M,{prop:"agent_level_label",label:"代理商等级"},{default:l(({row:e})=>[t(L,{type:k.getAgentLevelColor(e.agent_level)},{default:l(()=>[c(O("platform"===e.agent_level?"平台代理商":"分站代理商"),1)]),_:2},1032,["type"])]),_:1}),t(M,{prop:"agent_type_label",label:"代理商类型"},{default:l(({row:e})=>[t(L,{type:k.getAgentTypeColor(e.agent_type)},{default:l(()=>[c(O(k.getAgentTypeText(e.agent_type)),1)]),_:2},1032,["type"])]),_:1}),t(M,{prop:"commission_display",label:"佣金设置"},{default:l(({row:a})=>[a.no_commission?(u(),e("span",Se,"不抽佣")):(u(),e("span",xe,O(a.commission_rate)+"%",1))]),_:1}),t(M,{prop:"total_commission",label:"总佣金"},{default:l(({row:e})=>[a("span",ze,"¥"+O(e.total_commission||0),1)]),_:1}),t(M,{prop:"status_label",label:"状态"},{default:l(({row:e})=>[t(L,{type:k.getStatusColor(e.status)},{default:l(()=>[c(O(k.getStatusText(e.status)),1)]),_:2},1032,["type"])]),_:1}),t(M,{prop:"validity_display",label:"有效期"},{default:l(({row:a})=>[a.is_permanent?(u(),e("span",Ue,"永久有效")):a.end_date?(u(),e("span",{key:1,class:E(k.getValidityClass(a.end_date))},O(k.formatDate(a.end_date)),3)):(u(),e("span",De,"未设置"))]),_:1}),t(M,{label:"操作",width:"220",fixed:"right"},{default:l(({row:e})=>[t(j,{link:"",type:"primary",size:"small",onClick:a=>k.viewAgent(e)},{default:l(()=>i[25]||(i[25]=[c(" 查看 ",-1)])),_:2,__:[25]},1032,["onClick"]),t(j,{link:"",type:"info",size:"small",onClick:a=>k.editAgent(e)},{default:l(()=>i[26]||(i[26]=[c(" 编辑 ",-1)])),_:2,__:[26]},1032,["onClick"]),t(j,{link:"",type:"warning",size:"small",onClick:a=>k.toggleStatus(e)},{default:l(()=>[c(O("active"===e.status?"禁用":"启用"),1)]),_:2},1032,["onClick"]),t(se,{onCommand:a=>k.handleCommand(a,e),trigger:"click"},{dropdown:l(()=>[t($,null,{default:l(()=>[t(q,{command:{action:"edit",row:e}},{default:l(()=>i[28]||(i[28]=[c("编辑",-1)])),_:2,__:[28]},1032,["command"]),e.is_permanent?v("",!0):(u(),m(q,{key:0,command:{action:"renew",row:e}},{default:l(()=>i[29]||(i[29]=[c("续费",-1)])),_:2,__:[29]},1032,["command"])),t(q,{command:{action:"status",row:e}},{default:l(()=>i[30]||(i[30]=[c("状态管理",-1)])),_:2,__:[30]},1032,["command"]),t(q,{command:{action:"delete",row:e},divided:""},{default:l(()=>i[31]||(i[31]=[c("删除",-1)])),_:2,__:[31]},1032,["command"])]),_:2},1024)]),default:l(()=>[t(j,{link:"",type:"primary",size:"small"},{default:l(()=>[i[27]||(i[27]=c(" 更多操作",-1)),t(A,{class:"el-icon--right"},{default:l(()=>[t(k.ArrowDown)]),_:1})]),_:1,__:[27]})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[Pe,k.loading]]):v("",!0),a("div",Le,[t(ie,{"current-page":k.currentPage,"onUpdate:currentPage":i[6]||(i[6]=e=>k.currentPage=e),"page-size":k.pageSize,"onUpdate:pageSize":i[7]||(i[7]=e=>k.pageSize=e),total:k.agents.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:k.handleSizeChange,onCurrentChange:k.handleCurrentChange},null,8,["current-page","page-size","total"])])]),_:1}),t($e,{modelValue:k.createDialogVisible,"onUpdate:modelValue":i[17]||(i[17]=e=>k.createDialogVisible=e),title:"新增代理商",width:"600px"},{footer:l(()=>[t(j,{onClick:i[16]||(i[16]=e=>k.createDialogVisible=!1)},{default:l(()=>i[35]||(i[35]=[c("取消",-1)])),_:1,__:[35]}),t(j,{type:"primary",onClick:k.confirmCreate,loading:k.createLoading},{default:l(()=>i[36]||(i[36]=[c(" 确认创建 ",-1)])),_:1,__:[36]},8,["loading"])]),default:l(()=>[t(U,{model:k.createForm,rules:k.createRules,ref:"createFormRef","label-width":"120px"},{default:l(()=>[t(S,{label:"关联用户",prop:"user_id"},{default:l(()=>[t(z,{modelValue:k.createForm.user_id,"onUpdate:modelValue":i[8]||(i[8]=e=>k.createForm.user_id=e),placeholder:"选择用户",filterable:""},{default:l(()=>[(u(!0),e(n,null,s(k.availableUsers,e=>(u(),m(x,{key:e.id,label:`${e.name} (${e.username})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(S,{label:"代理商名称",prop:"agent_name"},{default:l(()=>[t(F,{modelValue:k.createForm.agent_name,"onUpdate:modelValue":i[9]||(i[9]=e=>k.createForm.agent_name=e),placeholder:"请输入代理商名称"},null,8,["modelValue"])]),_:1}),t(S,{label:"代理商等级",prop:"agent_level"},{default:l(()=>[t(Me,{modelValue:k.createForm.agent_level,"onUpdate:modelValue":i[10]||(i[10]=e=>k.createForm.agent_level=e)},{default:l(()=>[t(Te,{label:"platform"},{default:l(()=>i[32]||(i[32]=[c("平台代理商",-1)])),_:1,__:[32]}),t(Te,{label:"substation"},{default:l(()=>i[33]||(i[33]=[c("分站代理商",-1)])),_:1,__:[33]})]),_:1},8,["modelValue"])]),_:1}),t(S,{label:"代理商类型",prop:"agent_type"},{default:l(()=>[t(z,{modelValue:k.createForm.agent_type,"onUpdate:modelValue":i[11]||(i[11]=e=>k.createForm.agent_type=e),placeholder:"选择类型"},{default:l(()=>[t(x,{label:"个人代理",value:"individual"}),t(x,{label:"企业代理",value:"enterprise"}),t(x,{label:"渠道代理",value:"channel"})]),_:1},8,["modelValue"])]),_:1}),t(S,{label:"佣金比例",prop:"commission_rate"},{default:l(()=>[t(Be,{modelValue:k.createForm.commission_rate,"onUpdate:modelValue":i[12]||(i[12]=e=>k.createForm.commission_rate=e),min:0,max:100,precision:2,"controls-position":"right"},null,8,["modelValue"]),i[34]||(i[34]=a("span",{style:{"margin-left":"10px"}},"%",-1))]),_:1,__:[34]}),t(S,{label:"有效期",prop:"validity_period"},{default:l(()=>[t(z,{modelValue:k.createForm.validity_period,"onUpdate:modelValue":i[13]||(i[13]=e=>k.createForm.validity_period=e),placeholder:"选择有效期"},{default:l(()=>[t(x,{label:"1周",value:"week"}),t(x,{label:"1个月",value:"month"}),t(x,{label:"3个月",value:"quarter"}),t(x,{label:"6个月",value:"half_year"}),t(x,{label:"1年",value:"year"}),t(x,{label:"永久",value:"permanent"}),t(x,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),"custom"===k.createForm.validity_period?(u(),m(S,{key:0,label:"自定义结束日期"},{default:l(()=>[t(qe,{modelValue:k.createForm.custom_end_date,"onUpdate:modelValue":i[14]||(i[14]=e=>k.createForm.custom_end_date=e),type:"date",placeholder:"选择结束日期"},null,8,["modelValue"])]),_:1})):v("",!0),t(S,{label:"备注"},{default:l(()=>[t(F,{modelValue:k.createForm.remark,"onUpdate:modelValue":i[15]||(i[15]=e=>k.createForm.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-fdc83703"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/agent/AgentList.vue"]]);export{Te as default};
