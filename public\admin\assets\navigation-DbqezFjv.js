const e={admin:{level:0,name:"超级管理员",canViewRoles:["admin","substation","agent","distributor","group_owner","user"],dataScope:"all",dashboardScope:"global",financeScope:"all_finance",groupCreatePermission:!0,dataExportPermission:!0},substation:{level:1,name:"分站管理员",canViewRoles:["substation","agent","distributor","group_owner","user"],dataScope:"substation_and_below",dashboardScope:"substation",financeScope:"substation_finance",groupCreatePermission:!0,dataExportPermission:!0},agent:{level:2,name:"代理商",canViewRoles:["agent","distributor","group_owner","user"],dataScope:"agent_and_below",dashboardScope:"agent_team",financeScope:"agent_commission",groupCreatePermission:!0,dataExportPermission:!1},distributor:{level:3,name:"分销员",canViewRoles:["distributor","group_owner","user"],dataScope:"distributor_and_below",dashboardScope:"distributor_personal",financeScope:"distributor_commission",groupCreatePermission:!0,dataExportPermission:!1},group_owner:{level:4,name:"群主",canViewRoles:["group_owner","user"],dataScope:"group_owner_and_below",dashboardScope:"group_owner_groups",financeScope:"group_owner_income",groupCreatePermission:!0,dataExportPermission:!1},user:{level:5,name:"普通用户",canViewRoles:["user"],dataScope:"self_only",dashboardScope:"user_personal",financeScope:"user_consumption",groupCreatePermission:!0,dataExportPermission:!1}},o={admin:{allowedRoutes:["*"],defaultRoute:"/admin/dashboard",workbench:"/admin/dashboard"},substation:{allowedRoutes:["/admin/dashboard","/admin/users","/admin/groups","/admin/orders","/admin/finance","/admin/agents","/admin/substations","/admin/distributors","/admin/promotion"],defaultRoute:"/admin/dashboard",workbench:"/admin/dashboard"},agent:{allowedRoutes:["/admin/agents","/admin/agents","/admin/agents","/admin/commission-logs","/admin/agents","/admin/agent-performance","/admin/users","/admin/users","/admin/commission-logs","/admin/promotion"],defaultRoute:"/admin/agents",workbench:"/admin/agents"},distributor:{allowedRoutes:["/admin/distributors","/admin/distributors","/admin/groups","/admin/orders","/admin/commission-logs","/admin/promotion","/admin/users","/admin/users"],defaultRoute:"/admin/distributors",workbench:"/admin/distributors"},group_owner:{allowedRoutes:["/admin/groups","/admin/groups","/admin/templates","/admin/templates","/admin/users","/admin/users","/admin/orders"],defaultRoute:"/admin/groups",workbench:"/admin/groups"},user:{allowedRoutes:["/admin/users","/admin/users","/admin/orders","/admin/groups"],defaultRoute:"/admin/users",workbench:"/admin/users"}};function n(e,n){if(!n||!e)return!1;const a=o[n];if(!a)return!1;if(a.allowedRoutes.includes("*"))return!0;let r;if("string"==typeof e)r=e;else if(e&&"string"==typeof e.path)r=e.path;else{if(!e||"string"!=typeof e.name)return!1;r=`/${e.name.toLowerCase()}`}return"string"==typeof r&&a.allowedRoutes.some(e=>e===r||!!r.startsWith(e+"/"))}function a(e){const n=o[e];return n?.defaultRoute||"/user/center"}function r(e,o){return o&&e?e.filter(e=>!e.meta?.hidden&&(!!n(e,o)&&!(e.children&&e.children.length>0&&(e.children=r(e.children,o),0===e.children.length&&e.redirect)))):[]}const s={admin:"超级管理员",substation:"分站管理员",agent:"代理商",distributor:"分销员",group_owner:"群主",user:"普通用户"};function i(e){return s[e]||"未知角色"}export{n as checkMenuPermission,r as filterRoutesByRole,i as getRoleDisplayName,a as getUserDefaultRoute,s as roleDisplayNames,e as roleHierarchy,o as roleNavigationConfig};
