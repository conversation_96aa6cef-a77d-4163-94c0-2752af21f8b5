<template>
  <div class="notification-template-manager">
    <!-- 模板管理页头 -->
    <div class="template-header">
      <div class="header-left">
        <h3>通知模板管理</h3>
        <p>管理系统中的所有通知模板，支持多渠道和变量配置</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新建模板
        </el-button>
        <el-button type="info" @click="importTemplates">
          <el-icon><Upload /></el-icon>
          导入模板
        </el-button>
        <el-button @click="exportTemplates">
          <el-icon><Download /></el-icon>
          导出模板
        </el-button>
      </div>
    </div>

    <!-- 模板筛选 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="模板类型">
          <el-select v-model="filterForm.type" placeholder="选择类型" clearable style="width: 150px">
            <el-option label="用户通知" value="user" />
            <el-option label="系统通知" value="system" />
            <el-option label="营销通知" value="marketing" />
            <el-option label="安全通知" value="security" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="通知渠道">
          <el-select v-model="filterForm.channel" placeholder="选择渠道" clearable style="width: 150px">
            <el-option label="邮件" value="email" />
            <el-option label="短信" value="sms" />
            <el-option label="微信" value="wechat" />
            <el-option label="系统" value="system" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="filterForm.status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="关键词">
          <el-input v-model="filterForm.keyword" placeholder="搜索模板名称或内容" style="width: 200px" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="applyFilters">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 模板列表 -->
    <el-card class="template-list-card">
      <div class="list-header">
        <span>共找到 {{ filteredTemplates.length }} 个模板</span>
        <div class="view-controls">
          <el-button-group>
            <el-button :type="viewMode === 'grid' ? 'primary' : ''" @click="viewMode = 'grid'">
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
              <el-icon><List /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="template-grid">
        <div v-for="template in paginatedTemplates" :key="template.id" class="template-card">
          <div class="card-header">
            <div class="template-type">
              <el-tag :type="getTypeColor(template.type)" size="small">
                {{ getTypeName(template.type) }}
              </el-tag>
            </div>
            <div class="template-actions">
              <el-dropdown @command="handleTemplateAction">
                <el-button type="text" size="small">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'edit', template}">编辑</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'copy', template}">复制</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'preview', template}">预览</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'delete', template}" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          
          <div class="card-content">
            <h4 class="template-title">{{ template.name }}</h4>
            <p class="template-description">{{ template.description }}</p>
            
            <div class="template-channels">
              <el-tag 
                v-for="channel in template.channels" 
                :key="channel" 
                size="small" 
                class="channel-tag"
              >
                {{ getChannelName(channel) }}
              </el-tag>
            </div>
            
            <div class="template-content-preview">
              {{ template.content }}
            </div>
          </div>
          
          <div class="card-footer">
            <div class="template-stats">
              <span class="stat-item">
                <el-icon><View /></el-icon>
                {{ template.usage_count || 0 }}
              </span>
              <span class="stat-item">
                <el-icon><Clock /></el-icon>
                {{ formatDate(template.updated_at) }}
              </span>
            </div>
            <div class="template-status">
              <el-switch 
                v-model="template.active"
                @change="toggleTemplateStatus(template)"
                size="small"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 表格视图 -->
      <el-table v-else :data="paginatedTemplates" style="width: 100%">
        <el-table-column prop="name" label="模板名称" width="200" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getTypeColor(scope.row.type)" size="small">
              {{ getTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="channels" label="支持渠道" width="150">
          <template #default="scope">
            <el-tag 
              v-for="channel in scope.row.channels" 
              :key="channel" 
              size="small" 
              style="margin-right: 4px;"
            >
              {{ getChannelName(channel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="模板内容" show-overflow-tooltip />
        <el-table-column prop="usage_count" label="使用次数" width="100" />
        <el-table-column prop="active" label="状态" width="80">
          <template #default="scope">
            <el-switch v-model="scope.row.active" @change="toggleTemplateStatus(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="text" size="small" @click="editTemplate(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="previewTemplate(scope.row)">预览</el-button>
            <el-button type="text" size="small" @click="deleteTemplate(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredTemplates.length"
        />
      </div>
    </el-card>

    <!-- 创建/编辑模板对话框 -->
    <el-dialog 
      :title="templateDialog.isEdit ? '编辑模板' : '创建模板'" 
      v-model="templateDialog.visible" 
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form :model="templateForm" :rules="templateRules" ref="templateFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板名称" prop="name">
              <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板类型" prop="type">
              <el-select v-model="templateForm.type" placeholder="选择模板类型" style="width: 100%">
                <el-option label="用户通知" value="user" />
                <el-option label="系统通知" value="system" />
                <el-option label="营销通知" value="marketing" />
                <el-option label="安全通知" value="security" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="模板描述" prop="description">
          <el-input v-model="templateForm.description" type="textarea" :rows="2" placeholder="简要描述模板用途" />
        </el-form-item>
        
        <el-form-item label="支持渠道" prop="channels">
          <el-checkbox-group v-model="templateForm.channels">
            <el-checkbox label="email">邮件</el-checkbox>
            <el-checkbox label="sms">短信</el-checkbox>
            <el-checkbox label="wechat">微信</el-checkbox>
            <el-checkbox label="system">系统</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="模板内容" prop="content">
          <el-input 
            v-model="templateForm.content" 
            type="textarea" 
            :rows="8" 
            placeholder="请输入模板内容，支持变量：{{username}}, {{amount}}, {{time}}等"
          />
        </el-form-item>
        
        <el-form-item label="变量说明">
          <el-input 
            v-model="templateForm.variables" 
            type="textarea" 
            :rows="3" 
            placeholder="变量说明，例如：{{username}}=用户名，{{amount}}=金额"
          />
        </el-form-item>
        
        <el-form-item label="启用状态">
          <el-switch v-model="templateForm.active" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="templateDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveTemplate" :loading="saving">
            {{ templateDialog.isEdit ? '保存修改' : '创建模板' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模板预览对话框 -->
    <el-dialog title="模板预览" v-model="previewDialog.visible" width="600px">
      <div class="template-preview-container">
        <div class="preview-header">
          <h4>{{ previewTemplate?.name }}</h4>
          <el-tag :type="getTypeColor(previewTemplate?.type)" size="small">
            {{ getTypeName(previewTemplate?.type) }}
          </el-tag>
        </div>
        
        <div class="preview-channels">
          <span>支持渠道：</span>
          <el-tag 
            v-for="channel in previewTemplate?.channels" 
            :key="channel" 
            size="small" 
            style="margin-left: 8px;"
          >
            {{ getChannelName(channel) }}
          </el-tag>
        </div>
        
        <div class="preview-content">
          <h5>模板内容：</h5>
          <div class="content-display">{{ previewTemplate?.content }}</div>
        </div>
        
        <div v-if="previewTemplate?.variables" class="preview-variables">
          <h5>变量说明：</h5>
          <div class="variables-display">{{ previewTemplate?.variables }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, Download, Grid, List, MoreFilled, View, Clock } from '@element-plus/icons-vue'

// 响应式数据
const viewMode = ref('grid')
const currentPage = ref(1)
const pageSize = ref(10)
const saving = ref(false)

// 筛选表单
const filterForm = reactive({
  type: '',
  channel: '',
  status: '',
  keyword: ''
})

// 模板数据
const templates = ref([
  {
    id: 1,
    name: '用户注册欢迎通知',
    description: '新用户注册成功后发送的欢迎通知',
    type: 'user',
    channels: ['email', 'system'],
    content: '亲爱的{{username}}，欢迎加入我们的平台！您的账户已成功创建，开始您的精彩之旅吧！',
    variables: '{{username}}=用户名',
    usage_count: 156,
    active: true,
    created_at: '2024-01-01 10:00:00',
    updated_at: '2024-01-15 14:30:00'
  },
  {
    id: 2,
    name: '订单确认通知',
    description: '用户下单成功后的确认通知',
    type: 'user',
    channels: ['email', 'sms', 'system'],
    content: '您的订单{{order_no}}已成功提交，订单金额{{amount}}元，我们将尽快为您处理。',
    variables: '{{order_no}}=订单号，{{amount}}=订单金额',
    usage_count: 234,
    active: true,
    created_at: '2024-01-02 09:15:00',
    updated_at: '2024-01-16 11:20:00'
  },
  {
    id: 3,
    name: '提现成功通知',
    description: '用户提现申请成功处理后的通知',
    type: 'user',
    channels: ['email', 'sms'],
    content: '您的提现申请已成功处理，金额{{amount}}元已转入您的账户，请注意查收。',
    variables: '{{amount}}=提现金额，{{account}}=收款账户',
    usage_count: 89,
    active: true,
    created_at: '2024-01-03 16:00:00',
    updated_at: '2024-01-17 09:45:00'
  },
  {
    id: 4,
    name: '系统维护通知',
    description: '系统维护前的用户通知',
    type: 'system',
    channels: ['email', 'system'],
    content: '系统将于{{maintenance_time}}进行维护，预计持续{{duration}}，期间可能影响正常使用，请您谅解。',
    variables: '{{maintenance_time}}=维护时间，{{duration}}=维护时长',
    usage_count: 12,
    active: false,
    created_at: '2024-01-04 08:30:00',
    updated_at: '2024-01-18 13:15:00'
  },
  {
    id: 5,
    name: '营销推广通知',
    description: '产品促销活动通知',
    type: 'marketing',
    channels: ['email', 'wechat'],
    content: '限时优惠！{{product_name}}现在享受{{discount}}折扣，活动截止{{end_time}}，立即抢购！',
    variables: '{{product_name}}=产品名称，{{discount}}=折扣，{{end_time}}=结束时间',
    usage_count: 67,
    active: true,
    created_at: '2024-01-05 12:00:00',
    updated_at: '2024-01-19 10:30:00'
  }
])

// 模板对话框
const templateDialog = reactive({
  visible: false,
  isEdit: false
})

const templateForm = reactive({
  name: '',
  description: '',
  type: 'user',
  channels: [],
  content: '',
  variables: '',
  active: true
})

const templateRules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
  channels: [{ required: true, message: '请选择支持渠道', trigger: 'change' }],
  content: [{ required: true, message: '请输入模板内容', trigger: 'blur' }]
}

// 预览对话框
const previewDialog = reactive({
  visible: false
})
const previewTemplate = ref(null)

// 计算属性
const filteredTemplates = computed(() => {
  let result = templates.value
  
  if (filterForm.type) {
    result = result.filter(t => t.type === filterForm.type)
  }
  
  if (filterForm.channel) {
    result = result.filter(t => t.channels.includes(filterForm.channel))
  }
  
  if (filterForm.status) {
    const isActive = filterForm.status === 'active'
    result = result.filter(t => t.active === isActive)
  }
  
  if (filterForm.keyword) {
    const keyword = filterForm.keyword.toLowerCase()
    result = result.filter(t => 
      t.name.toLowerCase().includes(keyword) || 
      t.content.toLowerCase().includes(keyword)
    )
  }
  
  return result
})

const paginatedTemplates = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredTemplates.value.slice(start, end)
})

// 辅助方法
const getTypeColor = (type) => {
  const colors = {
    user: 'primary',
    system: 'info',
    marketing: 'warning',
    security: 'danger'
  }
  return colors[type] || 'info'
}

const getTypeName = (type) => {
  const names = {
    user: '用户通知',
    system: '系统通知',
    marketing: '营销通知',
    security: '安全通知'
  }
  return names[type] || type
}

const getChannelName = (channel) => {
  const names = {
    email: '邮件',
    sms: '短信',
    wechat: '微信',
    system: '系统'
  }
  return names[channel] || channel
}

const formatDate = (dateStr) => {
  return dateStr?.slice(0, 16) || ''
}

// 事件处理
const applyFilters = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  Object.assign(filterForm, {
    type: '',
    channel: '',
    status: '',
    keyword: ''
  })
  currentPage.value = 1
}

const showCreateDialog = () => {
  resetTemplateForm()
  templateDialog.isEdit = false
  templateDialog.visible = true
}

const resetTemplateForm = () => {
  Object.assign(templateForm, {
    name: '',
    description: '',
    type: 'user',
    channels: [],
    content: '',
    variables: '',
    active: true
  })
}

const handleTemplateAction = ({ action, template }) => {
  switch (action) {
    case 'edit':
      editTemplate(template)
      break
    case 'copy':
      copyTemplate(template)
      break
    case 'preview':
      previewTemplateDialog(template)
      break
    case 'delete':
      deleteTemplate(template)
      break
  }
}

const editTemplate = (template) => {
  Object.assign(templateForm, template)
  templateDialog.isEdit = true
  templateDialog.visible = true
}

const copyTemplate = (template) => {
  Object.assign(templateForm, {
    ...template,
    name: template.name + ' (副本)',
    id: undefined
  })
  templateDialog.isEdit = false
  templateDialog.visible = true
}

const previewTemplateDialog = (template) => {
  previewTemplate.value = template
  previewDialog.visible = true
}

const deleteTemplate = (template) => {
  ElMessageBox.confirm(`确定要删除模板"${template.name}"吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = templates.value.findIndex(t => t.id === template.id)
    if (index > -1) {
      templates.value.splice(index, 1)
      ElMessage.success('模板删除成功')
    }
  })
}

const saveTemplate = async () => {
  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (templateDialog.isEdit) {
      const index = templates.value.findIndex(t => t.id === templateForm.id)
      if (index > -1) {
        templates.value[index] = { 
          ...templateForm, 
          updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
        }
      }
      ElMessage.success('模板修改成功')
    } else {
      const newTemplate = {
        ...templateForm,
        id: Date.now(),
        usage_count: 0,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
      }
      templates.value.unshift(newTemplate)
      ElMessage.success('模板创建成功')
    }
    
    templateDialog.visible = false
  } catch (error) {
    ElMessage.error('保存模板失败')
  } finally {
    saving.value = false
  }
}

const toggleTemplateStatus = (template) => {
  const statusText = template.active ? '启用' : '禁用'
  ElMessage.success(`模板"${template.name}"已${statusText}`)
}

const importTemplates = () => {
  ElMessage.info('模板导入功能开发中...')
}

const exportTemplates = () => {
  const dataStr = JSON.stringify(templates.value, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `notification-templates-${new Date().toISOString().slice(0, 10)}.json`
  link.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('模板已导出')
}

onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style lang="scss" scoped>
.notification-template-manager {
  padding: 20px;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  
  .header-left {
    h3 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 20px;
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.filter-card {
  margin-bottom: 20px;
}

.template-list-card {
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    span {
      color: #606266;
      font-size: 14px;
    }
  }
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.template-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.15);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .card-content {
    .template-title {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
    
    .template-description {
      margin: 0 0 12px 0;
      color: #606266;
      font-size: 13px;
      line-height: 1.4;
    }
    
    .template-channels {
      margin-bottom: 12px;
      
      .channel-tag {
        margin-right: 6px;
        margin-bottom: 4px;
      }
    }
    
    .template-content-preview {
      background: #f5f7fa;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 13px;
      color: #606266;
      line-height: 1.5;
      margin-bottom: 12px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
    }
  }
  
  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #f0f0f0;
    padding-top: 12px;
    
    .template-stats {
      display: flex;
      gap: 16px;
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #909399;
        font-size: 12px;
        
        .el-icon {
          font-size: 14px;
        }
      }
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.template-preview-container {
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h4 {
      margin: 0;
      color: #303133;
    }
  }
  
  .preview-channels {
    margin-bottom: 16px;
    font-size: 14px;
    color: #606266;
  }
  
  .preview-content, .preview-variables {
    margin-bottom: 16px;
    
    h5 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }
    
    .content-display, .variables-display {
      background: #f5f7fa;
      padding: 12px;
      border-radius: 4px;
      font-size: 14px;
      line-height: 1.6;
      color: #606266;
    }
  }
}
</style>