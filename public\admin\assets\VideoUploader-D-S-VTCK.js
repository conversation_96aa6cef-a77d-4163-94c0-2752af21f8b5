/* empty css             *//* empty css                   *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                  *//* empty css                  *//* empty css                    *//* empty css                       *//* empty css                        */import{l as e,m as l,q as a,C as t,E as o,G as s,A as r,z as u,al as i,r as n,c as d,o as c,I as p,w as v,n as m,M as g}from"./vue-vendor-BcnDv-68.js";import{s as f,ah as y,Z as h,u as b,bn as V,bB as _,c1 as k,c2 as S,c3 as w,c4 as P,by as E,$ as C,_ as T,bg as M,ao as j,a1 as U,a4 as L,a2 as x,a3 as F,al as B,W as q,aa as $,ab as z,bt as I,X as D}from"./element-plus-C2UshkXo.js";import{_ as O,g as R}from"./index-eUTsTR3J.js";import A from"./MediaUploader-CWV5P0gW.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const N={__name:"VideoPlayer",props:{src:{type:String,required:!0},poster:{type:String,default:""},title:{type:String,default:""},description:{type:String,default:""},autoplay:{type:Boolean,default:!1},muted:{type:Boolean,default:!0},loop:{type:Boolean,default:!1},showControls:{type:Boolean,default:!1},showInfo:{type:Boolean,default:!0},preload:{type:String,default:"metadata",validator:e=>["none","metadata","auto"].includes(e)},width:{type:String,default:"100%"},height:{type:String,default:"auto"}},emits:["loadstart","loadeddata","canplay","play","pause","ended","error","timeupdate","volumechange"],setup(e,{expose:l,emit:a}){l(),i(l=>({"4ab3cf75-width":e.width,"4ab3cf75-height":e.height}));const t=e,o=a,s=n(null),r=n(null),u=n(!1),g=n(!1),f=n(!1),y=n(0),h=n(0),b=n(1),C=n(t.muted),T=n(!1),M=n(!1),j=n(0),U=d(()=>/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)),L=d(()=>({mp4:"video/mp4",webm:"video/webm",ogg:"video/ogg",mov:"video/quicktime",avi:"video/x-msvideo"}[t.src.split(".").pop().toLowerCase()]||"video/mp4")),x=d(()=>h.value>0?y.value/h.value*100:0),F=()=>{if(!s.value||!s.value.buffered.length)return;const e=s.value.buffered,l=s.value.currentTime;for(let a=0;a<e.length;a++)if(e.start(a)<=l&&l<=e.end(a)){j.value=e.end(a)/h.value*100;break}};let B=null;const q=()=>{M.value=!0,clearTimeout(B),B=setTimeout(()=>{u.value&&(M.value=!1)},3e3)},$=()=>{u.value&&(M.value=!1)},z=()=>{T.value=!!(document.fullscreenElement||document.webkitFullscreenElement||document.msFullscreenElement)};c(()=>{r.value&&(r.value.addEventListener("mouseenter",q),r.value.addEventListener("mousemove",q),r.value.addEventListener("mouseleave",$)),document.addEventListener("fullscreenchange",z),document.addEventListener("webkitfullscreenchange",z),document.addEventListener("msfullscreenchange",z)}),p(()=>{B&&clearTimeout(B),r.value&&(r.value.removeEventListener("mouseenter",q),r.value.removeEventListener("mousemove",q),r.value.removeEventListener("mouseleave",$)),document.removeEventListener("fullscreenchange",z),document.removeEventListener("webkitfullscreenchange",z),document.removeEventListener("msfullscreenchange",z)}),v(()=>t.src,()=>{f.value=!1,g.value=!0});const I={props:t,emit:o,videoElement:s,videoContainer:r,isPlaying:u,isLoading:g,hasError:f,currentTime:y,duration:h,volume:b,isMuted:C,isFullscreen:T,showCustomControls:M,bufferedPercent:j,isMobile:U,videoType:L,playedPercent:x,togglePlay:()=>{s.value&&(u.value?s.value.pause():s.value.play())},toggleMute:()=>{s.value&&(s.value.muted=!s.value.muted,C.value=s.value.muted)},setVolume:e=>{if(!s.value)return;const l=e.currentTarget.getBoundingClientRect(),a=(e.clientX-l.left)/l.width,t=Math.max(0,Math.min(1,a));s.value.volume=t,b.value=t,t>0&&C.value&&(s.value.muted=!1,C.value=!1)},seekTo:e=>{if(!s.value||0===h.value)return;const l=e.currentTarget.getBoundingClientRect(),a=(e.clientX-l.left)/l.width*h.value;s.value.currentTime=a},toggleFullscreen:()=>{r.value&&(T.value?document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.msExitFullscreen&&document.msExitFullscreen():r.value.requestFullscreen?r.value.requestFullscreen():r.value.webkitRequestFullscreen?r.value.webkitRequestFullscreen():r.value.msRequestFullscreen&&r.value.msRequestFullscreen())},formatTime:e=>{if(isNaN(e))return"00:00";const l=Math.floor(e/60),a=Math.floor(e%60);return`${l.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},updateBuffered:F,retry:()=>{f.value=!1,g.value=!0,s.value&&s.value.load()},onLoadStart:e=>{g.value=!0,f.value=!1,o("loadstart",e)},onLoadedData:e=>{g.value=!1,h.value=s.value.duration,o("loadeddata",e)},onCanPlay:e=>{g.value=!1,o("canplay",e)},onPlay:e=>{u.value=!0,o("play",e)},onPause:e=>{u.value=!1,o("pause",e)},onEnded:e=>{u.value=!1,o("ended",e)},onError:e=>{g.value=!1,f.value=!0,o("error",e)},onTimeUpdate:e=>{y.value=s.value.currentTime,F(),o("timeupdate",e)},onVolumeChange:e=>{b.value=s.value.volume,C.value=s.value.muted,o("volumechange",e)},get controlsTimer(){return B},set controlsTimer(e){B=e},showControls:q,hideControls:$,onFullscreenChange:z,ref:n,computed:d,onMounted:c,onUnmounted:p,watch:v,nextTick:m,get VideoPlay(){return E},get VideoPause(){return P},get Microphone(){return w},get Mute(){return S},get FullScreen(){return k},get Loading(){return _},get Warning(){return V}};return Object.defineProperty(I,"__isScriptSetup",{enumerable:!1,value:!0}),I}},W={class:"video-container",ref:"videoContainer"},X=["src","poster","muted","autoplay","loop","preload"],H=["src","type"],Y={key:0,class:"play-button"},G={class:"controls-bar"},J={class:"controls-left"},Z={class:"time-display"},K={class:"controls-center"},Q={class:"progress-bar"},ee={class:"controls-right"},le={key:0,class:"volume-container"},ae={class:"volume-bar"},te={key:1,class:"loading-overlay"},oe={class:"loading-spinner"},se={key:2,class:"error-overlay"},re={class:"error-icon"},ue={key:0,class:"video-info"},ie={key:0,class:"video-title"},ne={key:1,class:"video-description"};const de=O(N,[["render",function(i,n,d,c,p,v){const m=y;return l(),e("div",{class:f(["video-player",{"is-mobile":c.isMobile}])},[a("div",W,[a("video",{ref:"videoElement",src:d.src,poster:d.poster,muted:d.muted,autoplay:d.autoplay,loop:d.loop,controls:c.showControls,playsinline:!0,"webkit-playsinline":!0,preload:d.preload,onLoadstart:c.onLoadStart,onLoadeddata:c.onLoadedData,onCanplay:c.onCanPlay,onPlay:c.onPlay,onPause:c.onPause,onEnded:c.onEnded,onError:c.onError,onTimeupdate:c.onTimeUpdate,onVolumechange:c.onVolumeChange},[a("source",{src:d.src,type:c.videoType},null,8,H),n[0]||(n[0]=o(" 您的浏览器不支持视频播放 ",-1))],40,X),c.showControls?t("",!0):(l(),e("div",{key:0,class:f(["custom-controls",{show:c.showCustomControls}])},[a("div",{class:"play-pause-overlay",onClick:c.togglePlay},[c.isPlaying?t("",!0):(l(),e("div",Y,[s(m,{size:"60"},{default:r(()=>[s(c.VideoPlay)]),_:1})]))]),a("div",G,[a("div",J,[a("button",{class:"control-btn",onClick:c.togglePlay},[c.isPlaying?(l(),u(m,{key:0},{default:r(()=>[s(c.VideoPause)]),_:1})):(l(),u(m,{key:1},{default:r(()=>[s(c.VideoPlay)]),_:1}))]),a("div",Z,h(c.formatTime(c.currentTime))+" / "+h(c.formatTime(c.duration)),1)]),a("div",K,[a("div",{class:"progress-container",onClick:c.seekTo},[a("div",Q,[a("div",{class:"progress-buffer",style:b({width:`${c.bufferedPercent}%`})},null,4),a("div",{class:"progress-played",style:b({width:`${c.playedPercent}%`})},null,4),a("div",{class:"progress-thumb",style:b({left:`${c.playedPercent}%`})},null,4)])])]),a("div",ee,[a("button",{class:"control-btn",onClick:c.toggleMute},[c.isMuted?(l(),u(m,{key:0},{default:r(()=>[s(c.Mute)]),_:1})):(l(),u(m,{key:1},{default:r(()=>[s(c.Microphone)]),_:1}))]),c.isMobile?t("",!0):(l(),e("div",le,[a("div",{class:"volume-slider",onClick:c.setVolume},[a("div",ae,[a("div",{class:"volume-fill",style:b({width:100*c.volume+"%"})},null,4)])])])),c.isMobile?t("",!0):(l(),e("button",{key:1,class:"control-btn",onClick:c.toggleFullscreen},[s(m,null,{default:r(()=>[s(c.FullScreen)]),_:1})]))])])],2)),c.isLoading?(l(),e("div",te,[a("div",oe,[s(m,{class:"is-loading"},{default:r(()=>[s(c.Loading)]),_:1})]),n[1]||(n[1]=a("div",{class:"loading-text"},"视频加载中...",-1))])):t("",!0),c.hasError?(l(),e("div",se,[a("div",re,[s(m,{size:"48"},{default:r(()=>[s(c.Warning)]),_:1})]),n[2]||(n[2]=a("div",{class:"error-text"},"视频加载失败",-1)),a("button",{class:"retry-btn",onClick:c.retry},"重试")])):t("",!0)],512),d.showInfo&&(d.title||d.description)?(l(),e("div",ue,[d.title?(l(),e("h3",ie,h(d.title),1)):t("",!0),d.description?(l(),e("p",ne,h(d.description),1)):t("",!0)])):t("",!0)],2)}],["__scopeId","data-v-4ab3cf75"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/VideoPlayer.vue"]]),ce={class:"video-uploader"},pe={key:0,class:"upload-section"},ve={key:0,class:"upload-dragger"},me={class:"upload-hint"},ge={key:1,class:"upload-progress"},fe={key:1,class:"url-section"},ye={key:2,class:"video-preview"},he={class:"preview-header"},be={class:"preview-actions"},Ve={class:"preview-content"},_e={class:"video-info"},ke={class:"info-item"},Se={class:"value"},we={key:0,class:"info-item"},Pe={class:"value"},Ee={key:1,class:"info-item"},Ce={class:"value"};const Te=O({__name:"VideoUploader",props:{modelValue:{type:[String,Object],default:""},maxSize:{type:Number,default:100},accept:{type:String,default:"video/*"}},emits:["update:modelValue","change"],setup(e,{expose:l,emit:a}){l();const t=e,o=a,s=n(),r=n("upload"),u=n(!1),i=n(0),c=n(""),p=n(!1),m=n(!1),f=g({poster:"",autoplay:!1,loop:!1,muted:!1}),y=d(()=>"/api/upload/video"),h=d(()=>({Authorization:`Bearer ${R()}`})),b=d(()=>({type:"video"})),V=d(()=>"string"==typeof t.modelValue?t.modelValue:t.modelValue&&t.modelValue.url?t.modelValue.url:""),_=d(()=>"object"==typeof t.modelValue&&t.modelValue.poster?t.modelValue.poster:f.poster),k=d(()=>V.value?V.value.includes("qq.com")?"腾讯视频":V.value.includes("youku.com")?"优酷":V.value.includes("iqiyi.com")?"爱奇艺":V.value.includes("bilibili.com")?"B站":V.value.includes("youtube.com")?"YouTube":V.value.startsWith("http")?"在线链接":"本地上传":""),S=d(()=>"object"==typeof t.modelValue&&t.modelValue.title?t.modelValue.title:""),w=d(()=>"object"==typeof t.modelValue&&t.modelValue.duration?P(t.modelValue.duration):""),P=e=>{const l=Math.floor(e/3600),a=Math.floor(e%3600/60),t=Math.floor(e%60);return l>0?`${l}:${a.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`:`${a}:${t.toString().padStart(2,"0")}`};v(()=>t.modelValue,e=>{"object"==typeof e&&e&&Object.assign(f,e)},{immediate:!0,deep:!0});const C={props:t,emit:o,uploadRef:s,uploadType:r,uploading:u,uploadProgress:i,videoUrlInput:c,urlValidating:p,settingsVisible:m,videoSettings:f,uploadUrl:y,uploadHeaders:h,uploadData:b,videoUrl:V,videoPoster:_,videoSource:k,videoTitle:S,videoDuration:w,beforeUpload:e=>{if(!e.type.startsWith("video/"))return D.error("请上传视频文件"),!1;return e.size/1024/1024<t.maxSize?(u.value=!0,i.value=0,!0):(D.error(`视频大小不能超过 ${t.maxSize}MB`),!1)},handleProgress:e=>{i.value=Math.round(e.percent)},handleSuccess:e=>{if(u.value=!1,i.value=0,e.success){const l={url:e.data.url,poster:e.data.poster||"",title:e.data.title||"",duration:e.data.duration||0,...f};o("update:modelValue",l),o("change",l),D.success("视频上传成功")}else D.error(e.message||"上传失败")},handleError:()=>{u.value=!1,i.value=0,D.error("视频上传失败，请重试")},handleUrlInput:async()=>{if(c.value.trim()){p.value=!0;try{const e=await fetch("/api/video/validate",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${R()}`},body:JSON.stringify({url:c.value})}),l=await e.json();if(l.success){const e={url:c.value,poster:l.data.poster||"",title:l.data.title||"",duration:l.data.duration||0,...f};o("update:modelValue",e),o("change",e),D.success("视频链接验证成功")}else D.error(l.message||"视频链接无效")}catch(e){D.error("链接验证失败，请检查网络连接")}finally{p.value=!1}}},handleEdit:()=>{"object"==typeof t.modelValue&&Object.assign(f,t.modelValue),m.value=!0},handleRemove:()=>{o("update:modelValue",""),o("change",""),c.value="",D.success("视频已删除")},saveSettings:()=>{const e={..."object"==typeof t.modelValue?t.modelValue:{url:t.modelValue},...f};o("update:modelValue",e),o("change",e),m.value=!1,D.success("设置已保存")},formatDuration:P,ref:n,reactive:g,computed:d,watch:v,get ElMessage(){return D},get VideoPlay(){return E},get Link(){return I},get Edit(){return z},get Delete(){return $},VideoPlayer:de,MediaUploader:A,get getToken(){return R}};return Object.defineProperty(C,"__isScriptSetup",{enumerable:!1,value:!0}),C}},[["render",function(u,i,n,d,c,p){const v=C,m=T,g=y,f=M,b=j,V=U,_=L,k=F,S=B,w=x,P=q;return l(),e("div",ce,[s(m,{modelValue:d.uploadType,"onUpdate:modelValue":i[0]||(i[0]=e=>d.uploadType=e),class:"upload-type-selector"},{default:r(()=>[s(v,{label:"upload"},{default:r(()=>i[8]||(i[8]=[o("本地上传",-1)])),_:1,__:[8]}),s(v,{label:"url"},{default:r(()=>i[9]||(i[9]=[o("在线链接",-1)])),_:1,__:[9]})]),_:1},8,["modelValue"]),"upload"===d.uploadType?(l(),e("div",pe,[s(b,{ref:"uploadRef",class:"video-upload",action:d.uploadUrl,headers:d.uploadHeaders,data:d.uploadData,accept:n.accept,"before-upload":d.beforeUpload,"on-success":d.handleSuccess,"on-error":d.handleError,"on-progress":d.handleProgress,"show-file-list":!1,drag:""},{default:r(()=>[d.videoUrl?t("",!0):(l(),e("div",ve,[s(g,{class:"upload-icon"},{default:r(()=>[s(d.VideoPlay)]),_:1}),i[10]||(i[10]=a("div",{class:"upload-text"},[o("将视频拖到此处，或"),a("em",null,"点击上传")],-1)),a("div",me,"支持 MP4、AVI、MOV 格式，大小不超过 "+h(n.maxSize)+"MB",1)])),d.uploading?(l(),e("div",ge,[s(f,{percentage:d.uploadProgress},null,8,["percentage"]),i[11]||(i[11]=a("div",{class:"progress-text"},"正在上传视频...",-1))])):t("",!0)]),_:1},8,["action","headers","data","accept"])])):(l(),e("div",fe,[s(_,{modelValue:d.videoUrlInput,"onUpdate:modelValue":i[1]||(i[1]=e=>d.videoUrlInput=e),placeholder:"请输入视频链接（支持腾讯视频、优酷、B站等）",onBlur:d.handleUrlInput},{prepend:r(()=>[s(g,null,{default:r(()=>[s(d.Link)]),_:1})]),append:r(()=>[s(V,{onClick:d.handleUrlInput,loading:d.urlValidating},{default:r(()=>i[12]||(i[12]=[o(" 验证 ",-1)])),_:1,__:[12]},8,["loading"])]),_:1},8,["modelValue"]),i[13]||(i[13]=a("div",{class:"url-tip"}," 支持的视频平台：腾讯视频、优酷、爱奇艺、B站、YouTube等 ",-1))])),d.videoUrl?(l(),e("div",ye,[a("div",he,[i[16]||(i[16]=a("span",{class:"preview-title"},"视频预览",-1)),a("div",be,[s(V,{onClick:d.handleEdit,size:"small",type:"primary",plain:""},{default:r(()=>[s(g,null,{default:r(()=>[s(d.Edit)]),_:1}),i[14]||(i[14]=o(" 编辑 ",-1))]),_:1,__:[14]}),s(V,{onClick:d.handleRemove,size:"small",type:"danger",plain:""},{default:r(()=>[s(g,null,{default:r(()=>[s(d.Delete)]),_:1}),i[15]||(i[15]=o(" 删除 ",-1))]),_:1,__:[15]})])]),a("div",Ve,[s(d.VideoPlayer,{src:d.videoUrl,poster:d.videoPoster,width:320,height:180,controls:""},null,8,["src","poster"]),a("div",_e,[a("div",ke,[i[17]||(i[17]=a("span",{class:"label"},"视频源：",-1)),a("span",Se,h(d.videoSource),1)]),d.videoTitle?(l(),e("div",we,[i[18]||(i[18]=a("span",{class:"label"},"标题：",-1)),a("span",Pe,h(d.videoTitle),1)])):t("",!0),d.videoDuration?(l(),e("div",Ee,[i[19]||(i[19]=a("span",{class:"label"},"时长：",-1)),a("span",Ce,h(d.videoDuration),1)])):t("",!0)])])])):t("",!0),s(P,{modelValue:d.settingsVisible,"onUpdate:modelValue":i[7]||(i[7]=e=>d.settingsVisible=e),title:"视频设置",width:"500px","append-to-body":""},{footer:r(()=>[s(V,{onClick:i[6]||(i[6]=e=>d.settingsVisible=!1)},{default:r(()=>i[20]||(i[20]=[o("取消",-1)])),_:1,__:[20]}),s(V,{type:"primary",onClick:d.saveSettings},{default:r(()=>i[21]||(i[21]=[o("确定",-1)])),_:1,__:[21]})]),default:r(()=>[s(w,{model:d.videoSettings,"label-width":"80px"},{default:r(()=>[s(k,{label:"封面图"},{default:r(()=>[s(d.MediaUploader,{modelValue:d.videoSettings.poster,"onUpdate:modelValue":i[2]||(i[2]=e=>d.videoSettings.poster=e),type:"image",limit:1,accept:"image/*","list-type":"picture-card"},null,8,["modelValue"])]),_:1}),s(k,{label:"自动播放"},{default:r(()=>[s(S,{modelValue:d.videoSettings.autoplay,"onUpdate:modelValue":i[3]||(i[3]=e=>d.videoSettings.autoplay=e)},null,8,["modelValue"])]),_:1}),s(k,{label:"循环播放"},{default:r(()=>[s(S,{modelValue:d.videoSettings.loop,"onUpdate:modelValue":i[4]||(i[4]=e=>d.videoSettings.loop=e)},null,8,["modelValue"])]),_:1}),s(k,{label:"静音播放"},{default:r(()=>[s(S,{modelValue:d.videoSettings.muted,"onUpdate:modelValue":i[5]||(i[5]=e=>d.videoSettings.muted=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-b2e48738"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/VideoUploader.vue"]]);export{Te as default};
