<template>
  <div class="modern-order-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><Tickets /></el-icon>
          </div>
          <div class="header-text">
            <h1>订单管理</h1>
            <p>全面管理平台订单，包括订单查询、支付处理、退款管理和数据统计</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleExport" class="action-btn secondary">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button @click="handleBatchOperation" class="action-btn secondary">
            <el-icon><Operation /></el-icon>
            批量操作
          </el-button>
          <el-button type="primary" @click="handleFilter" class="action-btn primary">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选与搜索 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="listQuery" @submit.prevent="handleFilter">
        <el-form-item label="关键词">
          <el-input
            v-model="listQuery.keyword"
            placeholder="订单号、用户名"
            clearable
            @keyup.enter="handleFilter"
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="listQuery.status" placeholder="全部状态" clearable class="filter-select">
            <el-option label="全部" value="" />
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="已退款" value="refunded" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式">
          <el-select v-model="listQuery.payment_method" placeholder="全部支付方式" clearable class="filter-select">
            <el-option label="全部" value="" />
            <el-option label="微信支付" value="wechat" />
            <el-option label="支付宝" value="alipay" />
            <el-option label="QQ钱包" value="qqpay" />
            <el-option label="银行卡" value="bank" />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="date-picker"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter" class="search-btn">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetQuery" class="reset-btn">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-container" v-loading="statsLoading">
        <div class="stat-card" v-for="stat in orderStatCards" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h3>订单列表</h3>
            <el-tag size="small" type="info">共 {{ total }} 条记录</el-tag>
          </div>
          <div class="header-right">
            <el-button-group>
              <el-button size="small" :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                <el-icon><List /></el-icon>
              </el-button>
              <el-button size="small" :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
                <el-icon><Grid /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="订单号" width="180">
          <template #default="{ row }">
            <div class="order-no">
              <span>{{ row.order_no }}</span>
              <el-button 
                type="text" 
                size="small" 
                @click="copyToClipboard(row.order_no)"
                class="copy-btn"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用户信息" width="150">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :src="row.user?.avatar" size="small">
                {{ row.user?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <div class="user-details">
                <div class="username">{{ row.user?.username }}</div>
                <div class="user-id">ID: {{ row.user_id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="群组信息" width="200">
          <template #default="{ row }">
            <div class="group-info" v-if="row.wechat_group">
              <div class="group-name">{{ row.wechat_group.name }}</div>
              <div class="group-price">¥{{ row.wechat_group.price }}</div>
            </div>
            <span v-else class="no-group">-</span>
          </template>
        </el-table-column>
        <el-table-column label="订单金额" width="100">
          <template #default="{ row }">
            <span class="order-amount">¥{{ row.amount.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="支付方式" width="100">
          <template #default="{ row }">
            <el-tag :type="getPaymentMethodTagType(row.payment_method)" v-if="row.payment_method">
              {{ getPaymentMethodText(row.payment_method) }}
            </el-tag>
            <span v-else class="no-payment">-</span>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="支付时间" width="160">
          <template #default="{ row }">
            {{ row.paid_at ? formatDate(row.paid_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click="handleRefund(row)"
              v-if="row.status === 'paid'"
            >
              退款
            </el-button>
            <el-dropdown @command="handleCommand">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`detail-${row.id}`">订单详情</el-dropdown-item>
                  <el-dropdown-item :command="`payment-${row.id}`" v-if="row.status === 'pending'">支付信息</el-dropdown-item>
                  <el-dropdown-item :command="`cancel-${row.id}`" v-if="row.status === 'pending'">取消订单</el-dropdown-item>
                  <el-dropdown-item :command="`resend-${row.id}`" v-if="row.status === 'paid'">重发通知</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="listQuery.page"
          v-model:page-size="listQuery.limit"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model="detailDialogVisible"
      :order-data="currentOrder"
    />

    <!-- 退款对话框 -->
    <RefundDialog
      v-model="refundDialogVisible"
      :order-data="currentOrder"
      @success="handleRefundSuccess"
    />

    <!-- 支付信息对话框 -->
    <PaymentInfoDialog
      v-model="paymentDialogVisible"
      :order-data="currentOrder"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Tickets, Money, Check, TrendCharts, Search, Download, ArrowDown, DocumentCopy,
  Operation, Refresh, RefreshLeft, List, Grid, ArrowUp
} from '@element-plus/icons-vue'
import StatCard from '@/components/dashboard/StatCard.vue'
import OrderDetailDialog from '@/components/OrderDetailDialog.vue'
import RefundDialog from '@/components/RefundDialog.vue'
import PaymentInfoDialog from '@/components/PaymentInfoDialog.vue'
import { getOrderList, cancelOrder, refundOrder, getOrderStats } from '@/api/order'
import { formatDate } from '@/utils/format'

// 响应式数据
const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const statsLoading = ref(false)
const detailDialogVisible = ref(false)
const refundDialogVisible = ref(false)
const paymentDialogVisible = ref(false)
const currentOrder = ref({})
const multipleSelection = ref([])
const dateRange = ref([])
const viewMode = ref('table') // 视图模式：table 或 card

// 订单统计卡片数据 - 与其他页面保持一致的设计
const orderStatCards = ref([
  {
    key: 'total',
    label: '总订单数',
    value: '0',
    icon: 'Tickets',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+125'
  },
  {
    key: 'amount',
    label: '总交易额',
    value: '¥0',
    icon: 'Money',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+28.5%'
  },
  {
    key: 'paid',
    label: '已支付订单',
    value: '0',
    icon: 'Check',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+18.2%'
  },
  {
    key: 'success_rate',
    label: '支付成功率',
    value: '0%',
    icon: 'TrendCharts',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+2.1%'
  }
])

// 统计数据
const stats = ref({
  total_orders: 0,
  total_amount: 0,
  paid_orders: 0,
  success_rate: 0
})

// 查询参数
const listQuery = reactive({
  page: 1,
  limit: 20,
  keyword: '',
  status: '',
  payment_method: '',
  start_date: '',
  end_date: ''
})

// 获取订单列表
const getList = async () => {
  listLoading.value = true
  try {
    const { data } = await getOrderList(listQuery)
    list.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    listLoading.value = false
  }
}

// 获取统计数据
const getStats = async () => {
  statsLoading.value = true
  try {
    console.log('加载订单统计数据...')
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟统计数据
    const mockStats = {
      total_orders: 1568,
      total_amount: 234567.89,
      paid_orders: 1234,
      success_rate: 78.5
    }
    
    stats.value = mockStats
    
    // 更新统计卡片数据
    orderStatCards.value[0].value = mockStats.total_orders.toString()
    orderStatCards.value[1].value = '¥' + mockStats.total_amount.toLocaleString()
    orderStatCards.value[2].value = mockStats.paid_orders.toString()
    orderStatCards.value[3].value = mockStats.success_rate.toFixed(1) + '%'
    
    console.log('订单统计数据加载完成')
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

// 重置查询条件
const resetQuery = () => {
  listQuery.page = 1
  listQuery.limit = 20
  listQuery.keyword = ''
  listQuery.status = ''
  listQuery.payment_method = ''
  listQuery.start_date = ''
  listQuery.end_date = ''
  dateRange.value = []
  handleFilter()
}

// 筛选
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

// 日期范围变化
const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    listQuery.start_date = dates[0]
    listQuery.end_date = dates[1]
  } else {
    listQuery.start_date = ''
    listQuery.end_date = ''
  }
}

// 查看订单
const handleView = (row) => {
  currentOrder.value = { ...row }
  detailDialogVisible.value = true
}

// 退款处理
const handleRefund = (row) => {
  currentOrder.value = { ...row }
  refundDialogVisible.value = true
}

// 查看支付信息
const handlePaymentInfo = (row) => {
  currentOrder.value = { ...row }
  paymentDialogVisible.value = true
}

// 取消订单
const handleCancel = async (orderId) => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await cancelOrder(orderId)
    ElMessage.success('订单取消成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消订单失败')
    }
  }
}

// 重发通知
const handleResendNotification = async (orderId) => {
  try {
    await ElMessageBox.confirm('确定要重新发送通知吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    ElMessage.success('通知发送成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('发送通知失败')
    }
  }
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('订单号已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 下拉菜单命令处理
const handleCommand = (command) => {
  const [action, orderId] = command.split('-')
  const id = parseInt(orderId)
  const order = list.value.find(o => o.id === id)
  
  switch (action) {
    case 'detail':
      handleView(order)
      break
    case 'payment':
      handlePaymentInfo(order)
      break
    case 'cancel':
      handleCancel(id)
      break
    case 'resend':
      handleResendNotification(id)
      break
  }
}

// 导出数据
const handleExport = async () => {
  try {
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 批量操作
const handleBatchOperation = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请先选择要操作的订单')
    return
  }
  
  ElMessageBox.confirm('请选择批量操作类型', '批量操作', {
    distinguishCancelAndClose: true,
    confirmButtonText: '批量取消',
    cancelButtonText: '批量导出'
  }).then(() => {
    batchCancelOrders()
  }).catch((action) => {
    if (action === 'cancel') {
      batchExportOrders()
    }
  })
}

// 批量取消订单
const batchCancelOrders = async () => {
  try {
    const orderIds = multipleSelection.value
      .filter(order => order.status === 'pending')
      .map(order => order.id)
    
    if (orderIds.length === 0) {
      ElMessage.warning('没有可取消的订单')
      return
    }
    
    ElMessage.success('批量取消成功')
    getList()
  } catch (error) {
    ElMessage.error('批量取消失败')
  }
}

// 批量导出订单
const batchExportOrders = async () => {
  try {
    const orderIds = multipleSelection.value.map(order => order.id)
    ElMessage.success('批量导出成功')
  } catch (error) {
    ElMessage.error('批量导出失败')
  }
}

// 选择变化
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 分页
const handleSizeChange = (val) => {
  listQuery.limit = val
  getList()
}

const handleCurrentChange = (val) => {
  listQuery.page = val
  getList()
}

// 退款成功回调
const handleRefundSuccess = () => {
  getList()
  getStats()
}

// 导航到列表
const navigateToList = () => {
  getList()
}

// 工具函数
const getPaymentMethodTagType = (method) => {
  const types = {
    wechat: 'success',
    alipay: 'primary',
    qqpay: 'warning',
    bank: 'info'
  }
  return types[method] || 'info'
}

const getPaymentMethodText = (method) => {
  const texts = {
    wechat: '微信支付',
    alipay: '支付宝',
    qqpay: 'QQ钱包',
    bank: '银行卡'
  }
  return texts[method] || '未知'
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'success',
    cancelled: 'info',
    refunded: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return texts[status] || '未知'
}

// 初始化
onMounted(() => {
  getList()
  getStats()
})
</script>

<style lang="scss" scoped>
.modern-order-list {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  // 页面头部样式
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          height: 40px;
          padding: 0 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }

  // 筛选卡片样式
  .filter-card {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .search-input {
      width: 250px;
    }
    
    .filter-select {
      width: 150px;
    }
    
    .date-picker {
      width: 300px;
    }
    
    .search-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      }
    }
    
    .reset-btn {
      background: #f5f7fa;
      border-color: #dcdfe6;
      color: #606266;
      border-radius: 8px;
      
      &:hover {
        background: #ecf5ff;
        border-color: #409eff;
        color: #409eff;
      }
    }
  }

  // 统计卡片区域
  .stats-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 表格卡片样式
  .table-card {
    max-width: 1400px;
    margin: 0 auto 40px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
          
          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
          
          .el-tag {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: none;
          }
        }
      }
    }
    
    :deep(.el-card__body) {
      padding: 0;
    }
    
    :deep(.el-table) {
      .el-table__header th {
        background: #fafbfc !important;
        border-bottom: 1px solid #e4e7ed;
        font-weight: 600;
        color: #606266;
        font-size: 13px;
        padding: 16px 12px;
      }
      
      .el-table__body tr {
        transition: all 0.3s ease;
        
        &:hover {
          background: #f8f9ff !important;
        }
        
        td {
          border-bottom: 1px solid #f0f2f5;
          padding: 16px 12px;
        }
      }
    }
    
    .pagination-container {
      padding: 32px 16px;
      text-align: center;
    }
  }
  
  // 订单特定样式
  .order-no {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .copy-btn {
      padding: 0;
      min-height: auto;
      
      &:hover {
        color: #409eff;
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .user-details {
      .username {
        font-weight: 600;
        color: #303133;
        font-size: 14px;
        margin-bottom: 2px;
      }
      
      .user-id {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .group-info {
    .group-name {
      font-weight: 600;
      color: #303133;
      margin-bottom: 2px;
    }
    
    .group-price {
      font-size: 12px;
      color: #f56c6c;
      font-weight: 600;
    }
  }

  .no-group,
  .no-payment {
    color: #c0c4cc;
    font-style: italic;
  }

  .order-amount {
    color: #f56c6c;
    font-weight: 700;
    font-size: 16px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .modern-order-list {
    .stats-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .modern-order-list {
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .stats-container {
      grid-template-columns: 1fr;
    }
    
    .filter-card {
      .el-form {
        .el-form-item {
          width: 100%;
          margin-bottom: 16px;
          
          .search-input,
          .filter-select,
          .date-picker {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>