{"name": "linkhub-admin", "version": "1.0.0", "private": true, "type": "module", "description": "晨鑫流量变现 管理后台", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "test": "vitest", "test:unit": "vitest run", "test:unit:watch": "vitest watch", "test:unit:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:all": "npm run test:unit && npm run test:e2e", "test:ci": "npm run test:coverage && npm run test:e2e", "playwright:install": "playwright install"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.4.0", "chart.js": "^4.4.0", "date-fns": "^4.1.0", "dayjs": "^1.11.0", "echarts": "^5.4.0", "element-plus": "^2.3.0", "glob": "^11.0.3", "js-cookie": "^3.0.0", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "pinia": "^2.1.7", "qrcode": "^1.5.4", "sortablejs-vue3": "^1.2.11", "vue": "^3.4.21", "vue-echarts": "^6.6.0", "vue-router": "^4.2.0"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@playwright/test": "^1.54.2", "@vitejs/plugin-vue": "^5.2.0", "@vitest/coverage-v8": "^1.6.1", "@vitest/ui": "^1.6.1", "@vue/test-utils": "^2.4.6", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "happy-dom": "^12.10.3", "jsdom": "^23.2.0", "rollup": "^3.0.0", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.62.0", "terser": "^5.32.0", "unplugin-auto-import": "^0.16.0", "unplugin-vue-components": "^0.25.0", "vite": "^5.2.0", "vite-plugin-compression": "^0.5.1", "vitest": "^1.6.1"}}