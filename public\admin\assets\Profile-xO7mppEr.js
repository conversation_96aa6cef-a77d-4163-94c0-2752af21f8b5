/* empty css             *//* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css               *//* empty css                   *//* empty css                  *//* empty css               *//* empty css                 *//* empty css                *//* empty css                     *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css                 */import{l as e,m as a,G as t,A as l,q as s,C as r,K as o,E as i,al as n,r as d,c as u,M as c,o as p,w as m,z as g,F as f,Y as _}from"./vue-vendor-BcnDv-68.js";import{_ as v,g as w,u as h,b as y,v as b,e as S,t as k,f as V,h as F,i as U,j as x,k as P,l as j}from"./index-eUTsTR3J.js";import{bg as A,ah as C,bf as z,a1 as L,s as M,ao as D,W as H,aa as E,ab as R,af as N,ag as T,X as B,U as I,bv as Y,bT as q,aQ as $,ae as G,bU as K,b8 as O,b3 as Q,a9 as W,Y as Z,V as J,a2 as X,a3 as ee,a4 as ae,ak as te,_ as le,aL as se,a0 as re,Z as oe,bV as ie,aZ as ne,u as de,al as ue,ap as ce,ai as pe,aj as me}from"./element-plus-C2UshkXo.js";/* empty css                  *//* empty css                    */import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const ge={__name:"AvatarUpload",props:{modelValue:{type:String,default:""},size:{type:Number,default:100},maxSize:{type:Number,default:2},enablePreview:{type:Boolean,default:!0}},emits:["update:modelValue","success","error"],setup(e,{expose:a,emit:t}){a(),n(a=>({"78d6afc3-`${size}px`":`${e.size}px`}));const l=e,s=t,r=d(!1),o=d(0),i=d(!1),c=u({get:()=>l.modelValue,set:e=>s("update:modelValue",e)}),p=u(()=>"/api/upload/avatar"),m=u(()=>({Authorization:`Bearer ${w()}`})),g={props:l,emit:s,uploading:r,uploadProgress:o,showPreview:i,avatarUrl:c,uploadUrl:p,uploadHeaders:m,beforeUpload:e=>{if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(e.type))return B.error("头像只能是 JPG、PNG、GIF 或 WebP 格式!"),!1;return e.size/1024/1024<l.maxSize?(r.value=!0,o.value=0,!0):(B.error(`头像大小不能超过 ${l.maxSize}MB!`),!1)},handleProgress:e=>{o.value=Math.round(e.loaded/e.total*100)},handleSuccess:e=>{r.value=!1,o.value=0,e.success?(c.value=e.data.url,B.success("头像上传成功!"),s("success",e.data)):(B.error(e.message||"头像上传失败!"),s("error",e))},handleError:e=>{r.value=!1,o.value=0,B.error("头像上传失败!"),s("error",e)},triggerUpload:()=>{const e=document.querySelector('.avatar-uploader input[type="file"]');e&&e.click()},removeAvatar:()=>{T.confirm("确定要删除当前头像吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{c.value="",s("update:modelValue",""),B.success("头像已删除")}).catch(()=>{})},downloadAvatar:()=>{if(c.value){const e=document.createElement("a");e.href=c.value,e.download="avatar.jpg",e.click()}},previewAvatar:()=>{c.value&&l.enablePreview&&(i.value=!0)},ref:d,computed:u,get ElMessage(){return B},get ElMessageBox(){return T},get Plus(){return N},get Edit(){return R},get Delete(){return E},get getToken(){return w}};return Object.defineProperty(g,"__isScriptSetup",{enumerable:!1,value:!0}),g}},fe={class:"avatar-upload-container"},_e=["src"],ve={key:1,class:"upload-progress"},we={key:2,class:"avatar-placeholder"},he={key:3,class:"avatar-overlay"},ye={class:"overlay-actions"},be={class:"avatar-preview"},Se=["src"];const ke=v(ge,[["render",function(n,d,u,c,p,m){const g=A,f=C,_=L,v=z,w=D,h=H;return a(),e("div",fe,[t(w,{class:"avatar-uploader",action:c.uploadUrl,headers:c.uploadHeaders,"show-file-list":!1,"on-success":c.handleSuccess,"on-error":c.handleError,"before-upload":c.beforeUpload,"on-progress":c.handleProgress,accept:"image/jpeg,image/jpg,image/png,image/gif,image/webp"},{default:l(()=>[s("div",{class:M(["avatar-wrapper",{uploading:c.uploading}])},[c.avatarUrl&&!c.uploading?(a(),e("img",{key:0,src:c.avatarUrl,class:"avatar",alt:"头像"},null,8,_e)):c.uploading?(a(),e("div",ve,[t(g,{type:"circle",percentage:c.uploadProgress,width:Math.min(u.size-20,80),"stroke-width":4},null,8,["percentage","width"]),d[2]||(d[2]=s("div",{class:"progress-text"},"上传中...",-1))])):(a(),e("div",we,[t(f,{class:"avatar-uploader-icon"},{default:l(()=>[t(c.Plus)]),_:1}),d[3]||(d[3]=s("div",{class:"upload-text"},"点击上传头像",-1))])),c.avatarUrl&&!c.uploading?(a(),e("div",he,[s("div",ye,[t(v,{content:"更换头像",placement:"top"},{default:l(()=>[t(_,{type:"primary",size:"small",circle:"",onClick:o(c.triggerUpload,["stop"])},{default:l(()=>[t(f,null,{default:l(()=>[t(c.Edit)]),_:1})]),_:1})]),_:1}),t(v,{content:"删除头像",placement:"top"},{default:l(()=>[t(_,{type:"danger",size:"small",circle:"",onClick:o(c.removeAvatar,["stop"])},{default:l(()=>[t(f,null,{default:l(()=>[t(c.Delete)]),_:1})]),_:1})]),_:1})])])):r("",!0)],2)]),_:1},8,["action","headers"]),t(h,{modelValue:c.showPreview,"onUpdate:modelValue":d[1]||(d[1]=e=>c.showPreview=e),title:"头像预览",width:"400px",center:""},{footer:l(()=>[t(_,{onClick:d[0]||(d[0]=e=>c.showPreview=!1)},{default:l(()=>d[4]||(d[4]=[i("关闭",-1)])),_:1,__:[4]}),t(_,{type:"primary",onClick:c.downloadAvatar},{default:l(()=>d[5]||(d[5]=[i("下载",-1)])),_:1,__:[5]})]),default:l(()=>[s("div",be,[s("img",{src:c.avatarUrl,alt:"头像预览"},null,8,Se)])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-78d6afc3"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/AvatarUpload.vue"]]),Ve={class:"app-container"},Fe={class:"card-header"},Ue={class:"balance"},xe={class:"card-header"},Pe={key:0,class:"password-strength"},je={class:"strength-bar"},Ae={key:0,class:"strength-suggestions"},Ce={class:"stat-item"},ze={class:"stat-value"},Le={class:"stat-item"},Me={class:"stat-value"},De={class:"stat-item"},He={class:"stat-value"},Ee={class:"stat-item"},Re={class:"stat-value"},Ne={class:"card-header"},Te={class:"security-settings"},Be={class:"setting-item"},Ie={class:"setting-item"},Ye={class:"setting-item"},qe={class:"card-header"},$e={class:"login-info"},Ge={class:"login-item"},Ke={class:"login-time"},Oe={class:"login-item"},Qe={class:"login-ip"},We={class:"login-item"},Ze={class:"login-device"};const Je=v({__name:"Profile",setup(e,{expose:a}){a();const t=h(),l=d(),s=d(),r=d(!1),o=d(!1),i=d({id:null,username:"",nickname:"",real_name:"",email:"",phone:"",avatar:"",role:"",gender:0,birthday:"",bio:"",status:"active",balance:0,email_verified:!1,phone_verified:!1,created_at:"",last_login_at:"",last_login_ip:"",last_device:""}),n=d({current_password:"",password:"",password_confirmation:""}),g=d({total_orders:0,total_amount:0,total_commission:0,children_count:0}),f=d({show:!1,level:"weak",percentage:0,text:"弱",suggestions:[]}),_=d({two_factor_enabled:!1,login_notification:!0}),v=d(!1),A=d(!1),C=d(!1),z=d([]),L=d([]),M=d([]),D=d("/api/v1/upload"),H=d({Authorization:`Bearer ${w()}`}),E=c({nickname:[{required:!0,message:"昵称不能为空",trigger:"blur"},{min:2,max:20,message:"昵称长度为2-20个字符",trigger:"blur"}],real_name:[{min:2,max:10,message:"真实姓名长度为2-10个字符",trigger:"blur"}],email:[{type:"email",message:"邮箱格式不正确",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}],bio:[{max:200,message:"个人简介不能超过200个字符",trigger:"blur"}]}),R=c({current_password:[{required:!0,message:"当前密码不能为空",trigger:"blur"}],password:[{required:!0,message:"新密码不能为空",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"},{validator:(e,a,t)=>{a&&a===n.value.current_password?t(new Error("新密码不能与当前密码相同")):t()},trigger:"blur"}],password_confirmation:[{required:!0,message:"确认密码不能为空",trigger:"blur"},{validator:(e,a,t)=>{a!==n.value.password?t(new Error("两次输入的密码不一致")):t()},trigger:"blur"}]}),I=async()=>{try{const{data:e}=await y();e.success&&(i.value={...e.data.user},g.value=e.data.stats||{})}catch(e){B.error("获取用户信息失败")}},Z=()=>{n.value={current_password:"",password:"",password_confirmation:""},s.value?.resetFields()},J=e=>{if(!e)return void(f.value.show=!1);const a=b(e);f.value={show:!0,level:a.strength,percentage:a.score/5*100,text:"strong"===a.strength?"强":"medium"===a.strength?"中":"弱",suggestions:a.feedback}},X=async()=>{try{const{data:e}=await F();e.success&&(L.value=e.data)}catch(e){L.value=[{id:1,ip_address:"127.0.0.1",location:"本地",device:"Chrome 浏览器",last_activity:"刚刚",is_current:!0}]}},ee=async()=>{try{const{data:e}=await S({limit:20});e.success&&(M.value=e.data)}catch(e){M.value=[{login_time:(new Date).toLocaleString("zh-CN"),ip_address:"127.0.0.1",location:"本地",device:"Chrome 浏览器",status:"success"},{login_time:new Date(Date.now()-864e5).toLocaleString("zh-CN"),ip_address:"*************",location:"内网",device:"Chrome 浏览器",status:"success"}]}},ae=()=>{z.value=[{created_at:(new Date).toLocaleString("zh-CN"),type_text:"充值",amount:100,description:"在线充值",balance_after:1e3},{created_at:new Date(Date.now()-864e5).toLocaleString("zh-CN"),type_text:"消费",amount:-50,description:"购买服务",balance_after:900},{created_at:new Date(Date.now()-1728e5).toLocaleString("zh-CN"),type_text:"返佣",amount:25.5,description:"推荐奖励",balance_after:950}]},te=()=>{v.value&&ae()},le=()=>{A.value&&X()},se=()=>{C.value&&ee()};p(()=>{I(),m(v,te),m(A,le),m(C,se)});const re={userStore:t,profileFormRef:l,passwordFormRef:s,profileLoading:r,passwordLoading:o,profileForm:i,passwordForm:n,userStats:g,passwordStrength:f,securitySettings:_,showBalanceHistory:v,showActiveSessions:A,showLoginHistory:C,balanceHistory:z,activeSessions:L,loginHistory:M,uploadUrl:D,uploadHeaders:H,profileRules:E,passwordRules:R,getUserInfo:I,updateProfile:async()=>{try{await l.value.validate(),r.value=!0;const{data:e}=await j({nickname:i.value.nickname,real_name:i.value.real_name,email:i.value.email,phone:i.value.phone,avatar:i.value.avatar,gender:i.value.gender,birthday:i.value.birthday,bio:i.value.bio});e.success&&(B.success("个人信息更新成功"),await t.getUserInfo(),await I())}catch(e){B.error(e.response?.data?.message||"更新失败")}finally{r.value=!1}},changePassword:async()=>{try{await s.value.validate(),o.value=!0;const{data:e}=await P({current_password:n.value.current_password,password:n.value.password,password_confirmation:n.value.password_confirmation});e.success&&(B.success("密码修改成功"),Z())}catch(e){}finally{o.value=!1}},resetPasswordForm:Z,handleAvatarSuccess:e=>{i.value.avatar=e.url,t.userInfo&&(t.userInfo.avatar=e.url),B.success("头像上传成功!")},handleAvatarError:e=>{B.error("头像上传失败，请重试")},getRoleTagType:e=>({admin:"danger",substation:"warning",distributor:"success",user:"info"}[e]),getRoleName:e=>({admin:"超级管理员",substation:"分站管理员",distributor:"分销商",user:"普通用户"}[e]),formatAmount:e=>new Intl.NumberFormat("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}).format(e||0),formatDate:e=>e?new Date(e).toLocaleString("zh-CN"):"未知",disabledDate:e=>e.getTime()>Date.now(),resetProfileForm:()=>{I()},checkPasswordStrength:J,generateRandomPassword:()=>{const e="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";let a="";a+="ABCDEFGHIJKLMNOPQRSTUVWXYZ"[Math.floor(26*Math.random())],a+="abcdefghijklmnopqrstuvwxyz"[Math.floor(26*Math.random())],a+="0123456789"[Math.floor(10*Math.random())],a+="!@#$%^&*"[Math.floor(8*Math.random())];for(let t=4;t<12;t++)a+=e.charAt(Math.floor(70*Math.random()));a=a.split("").sort(()=>Math.random()-.5).join(""),n.value.password=a,n.value.password_confirmation=a,J(a),B.success("随机密码已生成，请确认后保存")},handleTwoFactorToggle:async e=>{try{if(e)B.info("两步验证功能开发中..."),_.value.two_factor_enabled=!1;else try{const{data:e}=await U({two_factor_enabled:!1});e.success&&B.success("已关闭两步验证")}catch(a){B.info("安全设置已保存到本地")}}catch(t){_.value.two_factor_enabled=!e,B.error("设置失败")}},handleLoginNotificationToggle:async e=>{try{const{data:a}=await U({login_notification:e});a.success&&B.success(e?"已开启登录提醒":"已关闭登录提醒")}catch(a){_.value.login_notification=!e,B.error("设置失败")}},loadActiveSessions:X,terminateSession:async e=>{try{await T.confirm("确定要终止这个会话吗？","确认操作");const{data:a}=await V(e);a.success&&(B.success("会话已终止"),X())}catch(a){"cancel"!==a&&B.error("终止会话失败")}},terminateAllSessions:async()=>{try{await T.confirm("确定要终止所有其他会话吗？这将强制其他设备重新登录。","确认操作",{type:"warning"});const{data:e}=await k();e.success&&(B.success("已终止所有其他会话"),X())}catch(e){"cancel"!==e&&B.error("操作失败")}},loadLoginHistory:ee,loadBalanceHistory:ae,watchShowBalanceHistory:te,watchShowActiveSessions:le,watchShowLoginHistory:se,ref:d,reactive:c,onMounted:p,computed:u,watch:m,get getInfo(){return y},get updateProfileApi(){return j},get changePasswordApi(){return P},get getSecuritySettings(){return x},get updateSecuritySettings(){return U},get getActiveSessions(){return F},get terminateSessionApi(){return V},get terminateAllSessionsApi(){return k},get getLoginLogs(){return S},get validatePasswordStrength(){return b},get useUserStore(){return h},get ElMessage(){return B},get ElMessageBox(){return T},get getToken(){return w},get Plus(){return N},get User(){return W},get Lock(){return Q},get Key(){return O},get EditPen(){return K},get Refresh(){return G},get Clock(){return $},get Location(){return q},get Monitor(){return Y},AvatarUpload:ke};return Object.defineProperty(re,"__isScriptSetup",{enumerable:!1,value:!0}),re}},[["render",function(o,n,d,u,c,p){const m=C,v=ie,w=ee,h=z,y=ae,b=te,S=se,k=le,V=re,F=L,U=X,x=J,P=Z,j=ne,A=I,D=ue,E=ce,R=me,N=pe,T=H;return a(),e("div",Ve,[t(A,{gutter:20},{default:l(()=>[t(P,{span:12},{default:l(()=>[t(x,{class:"profile-card"},{header:l(()=>[s("div",Fe,[n[23]||(n[23]=s("span",null,"个人信息",-1)),t(v,{value:u.profileForm.nickname?0:1,hidden:u.profileForm.nickname,type:"warning"},{default:l(()=>[t(m,null,{default:l(()=>[t(u.User)]),_:1})]),_:1},8,["value","hidden"])])]),default:l(()=>[t(U,{ref:"profileFormRef",model:u.profileForm,rules:u.profileRules,"label-width":"100px"},{default:l(()=>[t(w,{label:"头像"},{default:l(()=>[t(u.AvatarUpload,{modelValue:u.profileForm.avatar,"onUpdate:modelValue":n[0]||(n[0]=e=>u.profileForm.avatar=e),size:120,"max-size":5,"enable-preview":!0,onSuccess:u.handleAvatarSuccess,onError:u.handleAvatarError},null,8,["modelValue"])]),_:1}),t(w,{label:"用户名",prop:"username"},{default:l(()=>[t(y,{modelValue:u.profileForm.username,"onUpdate:modelValue":n[1]||(n[1]=e=>u.profileForm.username=e),readonly:""},{suffix:l(()=>[t(h,{content:"用户名不可修改",placement:"top"},{default:l(()=>[t(m,{class:"readonly-icon"},{default:l(()=>[t(u.Lock)]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"昵称",prop:"nickname"},{default:l(()=>[t(y,{modelValue:u.profileForm.nickname,"onUpdate:modelValue":n[2]||(n[2]=e=>u.profileForm.nickname=e),placeholder:"请输入昵称",maxlength:"20","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(w,{label:"真实姓名",prop:"real_name"},{default:l(()=>[t(y,{modelValue:u.profileForm.real_name,"onUpdate:modelValue":n[3]||(n[3]=e=>u.profileForm.real_name=e),placeholder:"请输入真实姓名",maxlength:"10"},null,8,["modelValue"])]),_:1}),t(w,{label:"邮箱",prop:"email"},{default:l(()=>[t(y,{modelValue:u.profileForm.email,"onUpdate:modelValue":n[4]||(n[4]=e=>u.profileForm.email=e),placeholder:"请输入邮箱"},{suffix:l(()=>[u.profileForm.email_verified?(a(),g(b,{key:0,type:"success",size:"small"},{default:l(()=>n[24]||(n[24]=[i("已验证",-1)])),_:1,__:[24]})):(a(),g(b,{key:1,type:"warning",size:"small"},{default:l(()=>n[25]||(n[25]=[i("未验证",-1)])),_:1,__:[25]}))]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"手机号",prop:"phone"},{default:l(()=>[t(y,{modelValue:u.profileForm.phone,"onUpdate:modelValue":n[5]||(n[5]=e=>u.profileForm.phone=e),placeholder:"请输入手机号"},{suffix:l(()=>[u.profileForm.phone_verified?(a(),g(b,{key:0,type:"success",size:"small"},{default:l(()=>n[26]||(n[26]=[i("已验证",-1)])),_:1,__:[26]})):(a(),g(b,{key:1,type:"warning",size:"small"},{default:l(()=>n[27]||(n[27]=[i("未验证",-1)])),_:1,__:[27]}))]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"性别",prop:"gender"},{default:l(()=>[t(k,{modelValue:u.profileForm.gender,"onUpdate:modelValue":n[6]||(n[6]=e=>u.profileForm.gender=e)},{default:l(()=>[t(S,{label:1},{default:l(()=>n[28]||(n[28]=[i("男",-1)])),_:1,__:[28]}),t(S,{label:2},{default:l(()=>n[29]||(n[29]=[i("女",-1)])),_:1,__:[29]}),t(S,{label:0},{default:l(()=>n[30]||(n[30]=[i("保密",-1)])),_:1,__:[30]})]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"生日",prop:"birthday"},{default:l(()=>[t(V,{modelValue:u.profileForm.birthday,"onUpdate:modelValue":n[7]||(n[7]=e=>u.profileForm.birthday=e),type:"date",placeholder:"选择生日",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":u.disabledDate},null,8,["modelValue"])]),_:1}),t(w,{label:"个人简介",prop:"bio"},{default:l(()=>[t(y,{modelValue:u.profileForm.bio,"onUpdate:modelValue":n[8]||(n[8]=e=>u.profileForm.bio=e),type:"textarea",placeholder:"介绍一下自己...",rows:3,maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(w,{label:"角色"},{default:l(()=>[t(b,{type:u.getRoleTagType(u.profileForm.role)},{default:l(()=>[i(oe(u.getRoleName(u.profileForm.role)),1)]),_:1},8,["type"])]),_:1}),t(w,{label:"账户状态"},{default:l(()=>[t(b,{type:"active"===u.profileForm.status?"success":"danger"},{default:l(()=>[i(oe("active"===u.profileForm.status?"正常":"已禁用"),1)]),_:1},8,["type"])]),_:1}),t(w,{label:"余额"},{default:l(()=>[s("span",Ue,"¥ "+oe(u.formatAmount(u.profileForm.balance)),1),t(F,{type:"text",size:"small",onClick:n[9]||(n[9]=e=>u.showBalanceHistory=!0)},{default:l(()=>n[31]||(n[31]=[i("查看明细",-1)])),_:1,__:[31]})]),_:1}),t(w,{label:"注册时间"},{default:l(()=>[s("span",null,oe(u.formatDate(u.profileForm.created_at)),1)]),_:1}),t(w,null,{default:l(()=>[t(F,{type:"primary",onClick:u.updateProfile,loading:u.profileLoading},{default:l(()=>[t(m,null,{default:l(()=>[t(u.EditPen)]),_:1}),n[32]||(n[32]=i(" 更新信息 ",-1))]),_:1,__:[32]},8,["loading"]),t(F,{onClick:u.resetProfileForm},{default:l(()=>n[33]||(n[33]=[i("重置",-1)])),_:1,__:[33]})]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1}),t(P,{span:12},{default:l(()=>[t(x,{class:"password-card"},{header:l(()=>[s("div",xe,[n[34]||(n[34]=s("span",null,"修改密码",-1)),t(m,{class:"security-icon"},{default:l(()=>[t(u.Key)]),_:1})])]),default:l(()=>[t(U,{ref:"passwordFormRef",model:u.passwordForm,rules:u.passwordRules,"label-width":"100px"},{default:l(()=>[t(j,{title:"安全提示",type:"info",description:"为了账户安全，建议定期更换密码，密码应包含大小写字母、数字和特殊字符。",closable:!1,class:"security-alert"}),t(w,{label:"当前密码",prop:"current_password"},{default:l(()=>[t(y,{modelValue:u.passwordForm.current_password,"onUpdate:modelValue":n[10]||(n[10]=e=>u.passwordForm.current_password=e),type:"password",placeholder:"请输入当前密码","show-password":"",autocomplete:"current-password"},null,8,["modelValue"])]),_:1}),t(w,{label:"新密码",prop:"password"},{default:l(()=>[t(y,{modelValue:u.passwordForm.password,"onUpdate:modelValue":n[11]||(n[11]=e=>u.passwordForm.password=e),type:"password",placeholder:"请输入新密码(至少6位)","show-password":"",autocomplete:"new-password",onInput:u.checkPasswordStrength},null,8,["modelValue"]),u.passwordStrength.show?(a(),e("div",Pe,[s("div",je,[s("div",{class:M(["strength-fill","strength-"+u.passwordStrength.level]),style:de({width:u.passwordStrength.percentage+"%"})},null,6)]),s("div",{class:M(["strength-text","strength-"+u.passwordStrength.level])},oe(u.passwordStrength.text),3),u.passwordStrength.suggestions.length?(a(),e("div",Ae,[s("ul",null,[(a(!0),e(f,null,_(u.passwordStrength.suggestions,t=>(a(),e("li",{key:t},oe(t),1))),128))])])):r("",!0)])):r("",!0)]),_:1}),t(w,{label:"确认密码",prop:"password_confirmation"},{default:l(()=>[t(y,{modelValue:u.passwordForm.password_confirmation,"onUpdate:modelValue":n[12]||(n[12]=e=>u.passwordForm.password_confirmation=e),type:"password",placeholder:"请再次输入新密码","show-password":"",autocomplete:"new-password"},null,8,["modelValue"])]),_:1}),t(w,null,{default:l(()=>[t(F,{type:"primary",onClick:u.changePassword,loading:u.passwordLoading},{default:l(()=>[t(m,null,{default:l(()=>[t(u.Lock)]),_:1}),n[35]||(n[35]=i(" 修改密码 ",-1))]),_:1,__:[35]},8,["loading"]),t(F,{onClick:u.resetPasswordForm},{default:l(()=>n[36]||(n[36]=[i("重置",-1)])),_:1,__:[36]}),t(F,{type:"info",plain:"",onClick:u.generateRandomPassword},{default:l(()=>[t(m,null,{default:l(()=>[t(u.Refresh)]),_:1}),n[37]||(n[37]=i(" 生成随机密码 ",-1))]),_:1,__:[37]})]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:20,class:"mt-4"},{default:l(()=>[t(P,{span:24},{default:l(()=>[t(x,null,{header:l(()=>n[38]||(n[38]=[s("div",{class:"card-header"},[s("span",null,"账户统计")],-1)])),default:l(()=>[t(A,{gutter:20,class:"stats-row"},{default:l(()=>[t(P,{span:6},{default:l(()=>[s("div",Ce,[s("div",ze,oe(u.userStats.total_orders||0),1),n[39]||(n[39]=s("div",{class:"stat-label"},"总订单数",-1))])]),_:1}),t(P,{span:6},{default:l(()=>[s("div",Le,[s("div",Me,oe(u.userStats.total_amount||0)+" 元",1),n[40]||(n[40]=s("div",{class:"stat-label"},"总消费金额",-1))])]),_:1}),t(P,{span:6},{default:l(()=>[s("div",De,[s("div",He,oe(u.userStats.total_commission||0)+" 元",1),n[41]||(n[41]=s("div",{class:"stat-label"},"总佣金收入",-1))])]),_:1}),t(P,{span:6},{default:l(()=>[s("div",Ee,[s("div",Re,oe(u.userStats.children_count||0),1),n[42]||(n[42]=s("div",{class:"stat-label"},"下级用户数",-1))])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),t(A,{gutter:20,class:"mt-4"},{default:l(()=>[t(P,{span:12},{default:l(()=>[t(x,{class:"security-card"},{header:l(()=>[s("div",Ne,[n[43]||(n[43]=s("span",null,"安全设置",-1)),t(m,null,{default:l(()=>[t(u.Lock)]),_:1})])]),default:l(()=>[s("div",Te,[s("div",Be,[n[44]||(n[44]=s("div",{class:"setting-info"},[s("div",{class:"setting-title"},"两步验证"),s("div",{class:"setting-desc"},"使用手机验证码提高账户安全性")],-1)),t(D,{modelValue:u.securitySettings.two_factor_enabled,"onUpdate:modelValue":n[13]||(n[13]=e=>u.securitySettings.two_factor_enabled=e),onChange:u.handleTwoFactorToggle},null,8,["modelValue"])]),t(E),s("div",Ie,[n[45]||(n[45]=s("div",{class:"setting-info"},[s("div",{class:"setting-title"},"登录提醒"),s("div",{class:"setting-desc"},"新设备登录时发送邮件通知")],-1)),t(D,{modelValue:u.securitySettings.login_notification,"onUpdate:modelValue":n[14]||(n[14]=e=>u.securitySettings.login_notification=e),onChange:u.handleLoginNotificationToggle},null,8,["modelValue"])]),t(E),s("div",Ye,[n[47]||(n[47]=s("div",{class:"setting-info"},[s("div",{class:"setting-title"},"会话管理"),s("div",{class:"setting-desc"},"查看和管理活跃会话")],-1)),t(F,{size:"small",onClick:n[15]||(n[15]=e=>u.showActiveSessions=!0)},{default:l(()=>n[46]||(n[46]=[i("管理会话",-1)])),_:1,__:[46]})])])]),_:1})]),_:1}),t(P,{span:12},{default:l(()=>[t(x,{class:"login-records-card"},{header:l(()=>[s("div",qe,[n[49]||(n[49]=s("span",null,"登录记录",-1)),t(F,{type:"text",size:"small",onClick:n[16]||(n[16]=e=>u.showLoginHistory=!0)},{default:l(()=>n[48]||(n[48]=[i(" 查看更多 ",-1)])),_:1,__:[48]})])]),default:l(()=>[s("div",$e,[s("div",Ge,[t(m,{class:"login-icon"},{default:l(()=>[t(u.Clock)]),_:1}),s("div",null,[s("div",Ke,oe(u.formatDate(u.profileForm.last_login_at)||"从未登录"),1),n[50]||(n[50]=s("div",{class:"login-detail"},"最后登录时间",-1))])]),s("div",Oe,[t(m,{class:"login-icon"},{default:l(()=>[t(u.Location)]),_:1}),s("div",null,[s("div",Qe,oe(u.profileForm.last_login_ip||"未知"),1),n[51]||(n[51]=s("div",{class:"login-detail"},"最后登录IP",-1))])]),s("div",We,[t(m,{class:"login-icon"},{default:l(()=>[t(u.Monitor)]),_:1}),s("div",null,[s("div",Ze,oe(u.profileForm.last_device||"未知设备"),1),n[52]||(n[52]=s("div",{class:"login-detail"},"最后使用设备",-1))])])])]),_:1})]),_:1})]),_:1}),t(T,{modelValue:u.showBalanceHistory,"onUpdate:modelValue":n[18]||(n[18]=e=>u.showBalanceHistory=e),title:"余额明细",width:"60%"},{footer:l(()=>[t(F,{onClick:n[17]||(n[17]=e=>u.showBalanceHistory=!1)},{default:l(()=>n[53]||(n[53]=[i("关闭",-1)])),_:1,__:[53]})]),default:l(()=>[t(N,{data:u.balanceHistory,style:{width:"100%"}},{default:l(()=>[t(R,{prop:"created_at",label:"时间",width:"180"}),t(R,{prop:"type_text",label:"类型",width:"120"}),t(R,{label:"金额",width:"120"},{default:l(e=>[s("span",{class:M(e.row.amount>0?"income":"expense")},oe(e.row.amount>0?"+":"")+oe(e.row.amount),3)]),_:1}),t(R,{prop:"description",label:"说明"}),t(R,{prop:"balance_after",label:"余额"})]),_:1},8,["data"])]),_:1},8,["modelValue"]),t(T,{modelValue:u.showActiveSessions,"onUpdate:modelValue":n[20]||(n[20]=e=>u.showActiveSessions=e),title:"活跃会话",width:"70%"},{footer:l(()=>[t(F,{onClick:n[19]||(n[19]=e=>u.showActiveSessions=!1)},{default:l(()=>n[56]||(n[56]=[i("关闭",-1)])),_:1,__:[56]}),t(F,{type:"danger",onClick:u.terminateAllSessions},{default:l(()=>n[57]||(n[57]=[i("终止所有其他会话",-1)])),_:1,__:[57]})]),default:l(()=>[t(N,{data:u.activeSessions,style:{width:"100%"}},{default:l(()=>[t(R,{prop:"ip_address",label:"IP地址",width:"140"}),t(R,{prop:"location",label:"位置",width:"120"}),t(R,{prop:"device",label:"设备"}),t(R,{prop:"last_activity",label:"最后活动",width:"160"}),t(R,{label:"操作",width:"100"},{default:l(e=>[e.row.is_current?(a(),g(b,{key:1,type:"success",size:"small"},{default:l(()=>n[55]||(n[55]=[i("当前会话",-1)])),_:1,__:[55]})):(a(),g(F,{key:0,type:"danger",size:"small",onClick:a=>u.terminateSession(e.row.id)},{default:l(()=>n[54]||(n[54]=[i(" 终止 ",-1)])),_:2,__:[54]},1032,["onClick"]))]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),t(T,{modelValue:u.showLoginHistory,"onUpdate:modelValue":n[22]||(n[22]=e=>u.showLoginHistory=e),title:"登录历史",width:"70%"},{footer:l(()=>[t(F,{onClick:n[21]||(n[21]=e=>u.showLoginHistory=!1)},{default:l(()=>n[58]||(n[58]=[i("关闭",-1)])),_:1,__:[58]})]),default:l(()=>[t(N,{data:u.loginHistory,style:{width:"100%"}},{default:l(()=>[t(R,{prop:"login_time",label:"登录时间",width:"160"}),t(R,{prop:"ip_address",label:"IP地址",width:"140"}),t(R,{prop:"location",label:"位置",width:"120"}),t(R,{prop:"device",label:"设备/浏览器"}),t(R,{label:"状态",width:"80"},{default:l(e=>[t(b,{type:"success"===e.row.status?"success":"danger",size:"small"},{default:l(()=>[i(oe("success"===e.row.status?"成功":"失败"),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-aad7e8b8"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/user/Profile.vue"]]);export{Je as default};
