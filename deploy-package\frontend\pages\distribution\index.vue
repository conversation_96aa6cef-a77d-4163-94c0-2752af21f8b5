<script setup>
// 页面需要认证
definePageMeta({
  middleware: 'auth'
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">分销中心</h1>
          <p class="text-gray-600 mt-1">管理您的推广链接，查看团队收益和佣金统计</p>
        </div>
        <div class="flex items-center space-x-4">
          <button 
            @click="showCreateLinkModal = true"
            class="btn-primary"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            生成推广链接
          </button>
          <button 
            @click="refreshData"
            :disabled="loading"
            class="btn btn-secondary"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            {{ loading ? '刷新中...' : '刷新数据' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 分销统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-blue-100 text-sm">推广链接</p>
            <p class="text-3xl font-bold mt-2">{{ stats.total_links || 0 }}</p>
            <p class="text-blue-100 text-sm mt-1">活跃链接: {{ stats.active_links || 0 }}</p>
          </div>
          <div class="p-3 bg-white bg-opacity-20 rounded-full">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-green-100 text-sm">团队成员</p>
            <p class="text-3xl font-bold mt-2">{{ stats.team_members || 0 }}</p>
            <p class="text-green-100 text-sm mt-1">本月新增: {{ stats.new_members || 0 }}</p>
          </div>
          <div class="p-3 bg-white bg-opacity-20 rounded-full">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-purple-100 text-sm">推广收益</p>
            <p class="text-3xl font-bold mt-2">¥{{ stats.total_earnings?.toFixed(2) || '0.00' }}</p>
            <p class="text-purple-100 text-sm mt-1">本月收益: ¥{{ stats.month_earnings?.toFixed(2) || '0.00' }}</p>
          </div>
          <div class="p-3 bg-white bg-opacity-20 rounded-full">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-orange-600 to-orange-700 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-orange-100 text-sm">访问量</p>
            <p class="text-3xl font-bold mt-2">{{ stats.total_visits || 0 }}</p>
            <p class="text-orange-100 text-sm mt-1">今日访问: {{ stats.today_visits || 0 }}</p>
          </div>
          <div class="p-3 bg-white bg-opacity-20 rounded-full">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 推广链接管理 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">推广链接管理</h2>
          <div class="flex items-center space-x-4">
            <select 
              v-model="linkFilter" 
              @change="loadLinks"
              class="text-sm border border-gray-300 rounded px-3 py-2"
            >
              <option value="">全部状态</option>
              <option value="active">活跃</option>
              <option value="inactive">已停用</option>
            </select>
          </div>
        </div>
      </div>

      <div class="p-6">
        <div v-if="linksLoading" class="text-center py-8">
          <div class="spinner mx-auto mb-4"></div>
          <p class="text-gray-600">加载中...</p>
        </div>

        <div v-else-if="!links.length" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
          </svg>
          <p class="text-gray-500 mt-2">暂无推广链接</p>
          <button 
            @click="showCreateLinkModal = true"
            class="btn-primary mt-4"
          >
            创建推广链接
          </button>
        </div>

        <div v-else class="space-y-4">
          <div v-for="link in links" :key="link.id" class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <img 
                      :src="link.group?.cover_image || '/default-group.png'" 
                      :alt="link.group?.title"
                      class="w-10 h-10 rounded-lg border border-gray-300"
                    />
                  </div>
                  <div class="flex-1">
                    <h3 class="font-semibold text-gray-900">{{ link.group?.title || '通用推广' }}</h3>
                    <p class="text-sm text-gray-500">{{ link.description || '推广链接' }}</p>
                  </div>
                </div>
                
                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-600">
                  <span>访问量: {{ link.visits || 0 }}</span>
                  <span>转化: {{ link.conversions || 0 }}</span>
                  <span>收益: ¥{{ link.earnings?.toFixed(2) || '0.00' }}</span>
                  <span>创建时间: {{ formatDate(link.created_at) }}</span>
                </div>
                
                <div class="mt-3 flex items-center space-x-2">
                  <div class="flex-1 bg-gray-100 rounded-md p-2">
                    <code class="text-sm break-all">{{ link.url }}</code>
                  </div>
                  <button 
                    @click="copyLink(link.url)"
                    class="btn btn-secondary"
                  >
                    复制
                  </button>
                  <button 
                    @click="shareLink(link)"
                    class="btn btn-secondary"
                  >
                    分享
                  </button>
                </div>
              </div>
              
              <div class="flex items-center space-x-2 ml-4">
                <span 
                  :class="[
                    'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                    link.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]"
                >
                  {{ link.status === 'active' ? '活跃' : '已停用' }}
                </span>
                
                <div class="relative">
                  <button 
                    @click="toggleLinkActions(link.id)"
                    class="p-2 text-gray-400 hover:text-gray-600"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                    </svg>
                  </button>
                  
                  <div v-if="showLinkActions === link.id" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                    <div class="py-1">
                      <button 
                        @click="viewLinkStats(link)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        查看统计
                      </button>
                      <button 
                        @click="editLink(link)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        编辑链接
                      </button>
                      <button 
                        @click="toggleLinkStatus(link)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        {{ link.status === 'active' ? '停用' : '启用' }}
                      </button>
                      <button 
                        @click="deleteLink(link)"
                        class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                      >
                        删除链接
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 团队管理 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">团队管理</h2>
          <div class="flex items-center space-x-4">
            <select 
              v-model="teamFilter" 
              @change="loadTeamMembers"
              class="text-sm border border-gray-300 rounded px-3 py-2"
            >
              <option value="">全部级别</option>
              <option value="1">一级推广</option>
              <option value="2">二级推广</option>
              <option value="3">三级推广</option>
            </select>
          </div>
        </div>
      </div>

      <div class="p-6">
        <div v-if="teamLoading" class="text-center py-8">
          <div class="spinner mx-auto mb-4"></div>
          <p class="text-gray-600">加载中...</p>
        </div>

        <div v-else-if="!teamMembers.length" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
          <p class="text-gray-500 mt-2">暂无团队成员</p>
        </div>

        <div v-else class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  成员信息
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  推广级别
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  贡献订单
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  贡献收益
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  加入时间
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="member in teamMembers" :key="member.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img 
                        :src="member.avatar || '/default-avatar.png'" 
                        :alt="member.nickname || member.username || member.phone || member.email || '用户头像'"
                        class="h-10 w-10 rounded-full"
                      />
                    </div>
                    <div class="ml-4">
                      <!-- 这里优先显示昵称，其次用户名、手机号、邮箱，避免使用不存在的 name 字段 -->
                      <div class="text-sm font-medium text-gray-900">
                        {{ member.nickname || member.username || member.phone || member.email || '未命名用户' }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ member.phone || member.email || '无联系方式' }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span 
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getLevelClass(member.level)"
                  >
                    {{ getLevelText(member.level) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ member.orders_count || 0 }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ¥{{ member.total_commission?.toFixed(2) || '0.00' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(member.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span 
                    :class="[
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      member.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ member.status === 1 ? '活跃' : '非活跃' }}
                    <!-- 
                      说明：
                      member.status 字段为数字类型（如 1 表示活跃，0 表示非活跃），
                      这里用数字做判断，避免类型不匹配的报错。
                      如后端返回的 status 字段为其他数字，请根据实际情况调整判断条件。
                    -->
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 创建推广链接弹窗 -->
    <div v-if="showCreateLinkModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">创建推广链接</h3>
          <button 
            @click="showCreateLinkModal = false"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">选择推广群组</label>
            <select 
              v-model="newLink.group_id" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option :value="null">通用推广链接</option>
              <option v-for="group in availableGroups" :key="group.id" :value="group.id">
                {{ group.title }} - ¥{{ group.price }}
              </option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">链接描述</label>
            <input 
              v-model="newLink.description" 
              type="text" 
              placeholder="为这个推广链接添加描述"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">佣金比例</label>
            <select 
              v-model="newLink.commission_rate" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="0.1">10%</option>
              <option value="0.15">15%</option>
              <option value="0.2">20%</option>
              <option value="0.25">25%</option>
              <option value="0.3">30%</option>
            </select>
          </div>
          
          <div class="flex space-x-4">
            <button 
              @click="showCreateLinkModal = false"
              class="flex-1 btn btn-secondary"
            >
              取消
            </button>
            <button 
              @click="createLink"
              :disabled="createLinkLoading"
              class="flex-1 btn-primary"
            >
              {{ createLinkLoading ? '创建中...' : '创建链接' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useApi } from '~/composables/useApi'
import type { User, WechatGroup, PromotionLink } from '~/types/auth'

// 为团队成员创建一个更具体的类型，继承自User并添加统计字段
interface TeamMember extends User {
  orders_count?: number;
  total_commission?: number;
}

// 页面元数据
definePageMeta({
  middleware: 'auth',
  layout: 'default'
})

// 页面头部
useHead({
  title: '分销中心 - 晨鑫流量变现系统',
  meta: [
    { name: 'description', content: '分销中心，管理您的推广链接，查看团队收益和佣金统计' }
  ]
})

const api = useApi()

// 响应式数据
const loading = ref(false)
const linksLoading = ref(false)
const teamLoading = ref(false)
const createLinkLoading = ref(false)

// 弹窗状态
const showCreateLinkModal = ref(false)
const showLinkActions = ref(null)

// 筛选条件
const linkFilter = ref('')
const teamFilter = ref('')

// 统计数据
const stats = ref({
  total_links: 0,
  active_links: 0,
  team_members: 0,
  new_members: 0,
  total_earnings: 0,
  month_earnings: 0,
  total_visits: 0,
  today_visits: 0
})

// 推广链接
const links = ref<PromotionLink[]>([])

// 团队成员
const teamMembers = ref<TeamMember[]>([])

// 可用群组
const availableGroups = ref<WechatGroup[]>([])

// 新建链接表单
const newLink = ref<{
  group_id: number | null;
  description: string;
  commission_rate: string;
}>({
  group_id: null,
  description: '',
  commission_rate: '0.15'
})

// 工具函数
const formatDate = (date?: string) => {
  if (!date) return ''
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(new Date(date))
}

const getLevelClass = (level?: number) => {
  if (level === undefined) return 'bg-gray-100 text-gray-800';
  const classes: { [key: string]: string } = {
    '1': 'bg-blue-100 text-blue-800',
    '2': 'bg-green-100 text-green-800',
    '3': 'bg-purple-100 text-purple-800'
  }
  return classes[String(level)] || 'bg-gray-100 text-gray-800'
}

const getLevelText = (level?: number) => {
  if (level === undefined) return '未知级别';
  const texts: { [key: string]: string } = {
    '1': '一级推广',
    '2': '二级推广',
    '3': '三级推广'
  }
  return texts[String(level)] || '未知级别'
}

// 数据加载函数
const loadStats = async () => {
  try {
    const response = await api.distribution.getStats()
    if (response.success) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadLinks = async () => {
  linksLoading.value = true
  try {
    const params = {
      per_page: 50,
      status: linkFilter.value
    }
    const response = await api.distribution.getLinks(params)
    // 修正：API返回的是分页对象，我们需要的是其中的data数组
    links.value = response.data.data || []
  } catch (error) {
    console.error('加载推广链接失败:', error)
  } finally {
    linksLoading.value = false
  }
}

const loadTeamMembers = async () => {
  teamLoading.value = true
  try {
    const params = {
      per_page: 50,
      level: teamFilter.value || undefined // 确保空字符串不会发送到后端
    }
    const response = await api.distribution.getTeamMembers(params)
    // 修正：API返回的是分页对象，我们需要的是其中的data数组
    teamMembers.value = response.data.data || []
  } catch (error) {
    console.error('加载团队成员失败:', error)
  } finally {
    teamLoading.value = false
  }
}

const loadAvailableGroups = async () => {
  try {
    const response = await api.groups.list({ per_page: 100 })
    // 修正：API返回的是分页对象，我们需要的是其中的data数组
    availableGroups.value = response.data.data || []
  } catch (error) {
    console.error('加载群组列表失败:', error)
  }
}

// 链接操作
const createLink = async () => {
  createLinkLoading.value = true
  try {
    const response = await api.distribution.createLink(newLink.value)
    if (response.success) {
      alert('推广链接创建成功')
      showCreateLinkModal.value = false
      
      // 重置表单
      newLink.value = {
        group_id: null,
        description: '',
        commission_rate: '0.15'
      }
      
      // 刷新数据
      await loadLinks()
      await loadStats()
    } else {
      alert(response.message || '创建推广链接失败')
    }
  } catch (error) {
    console.error('创建推广链接失败:', error)
    alert('创建推广链接失败，请重试')
  } finally {
    createLinkLoading.value = false
  }
}

const copyLink = async (url: string) => {
  try {
    await navigator.clipboard.writeText(url)
    alert('链接已复制到剪贴板')
  } catch (error) {
    console.error('复制链接失败:', error)
    alert('复制链接失败，请手动复制')
  }
}

const shareLink = (link: PromotionLink) => {
  if (navigator.share) {
    navigator.share({
      title: `推广 ${link.group?.title || '优质群组'}`,
      text: `邀请您加入 ${link.group?.title || '优质群组'}`,
      url: link.url
    })
  } else {
    copyLink(link.url)
  }
}

const toggleLinkActions = (linkId: number) => {
  showLinkActions.value = showLinkActions.value === linkId ? null : linkId
}

const viewLinkStats = (link: PromotionLink) => {
  // 跳转到链接统计页面
  navigateTo(`/distribution/stats/${link.id}`)
}

const editLink = (link: PromotionLink) => {
  // 编辑链接逻辑
  console.log('编辑链接:', link)
}

const toggleLinkStatus = async (link: PromotionLink) => {
  try {
    const newStatus = link.status === 'active' ? 'inactive' : 'active'
    const response = await api.distribution.updateLinkStatus(link.id, newStatus)
    if (response.success) {
      link.status = newStatus
      alert(`链接已${newStatus === 'active' ? '启用' : '停用'}`)
    } else {
      alert(response.message || '操作失败')
    }
  } catch (error) {
    console.error('切换链接状态失败:', error)
    alert('操作失败，请重试')
  }
}

const deleteLink = async (link: PromotionLink) => {
  if (!confirm('确定要删除这个推广链接吗？')) return
  
  try {
    const response = await api.distribution.deleteLink(link.id)
    if (response.success) {
      alert('推广链接已删除')
      await loadLinks()
      await loadStats()
    } else {
      alert(response.message || '删除失败')
    }
  } catch (error) {
    console.error('删除链接失败:', error)
    alert('删除失败，请重试')
  }
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadLinks(),
      loadTeamMembers(),
      loadAvailableGroups()
    ])
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(async () => {
  await refreshData()
})

// 点击外部关闭操作菜单
onMounted(() => {
  document.addEventListener('click', (e) => {
    if (!(e.target as Element).closest('.relative')) {
      showLinkActions.value = null
    }
  })
})
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

.spinner {
  @apply w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin;
}
</style> 