<template>
  <div class="modern-user-add">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><UserFilled /></el-icon>
          </div>
          <div class="header-text">
            <h1>添加用户</h1>
            <p>创建新的系统用户，配置角色权限和基本信息</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="$router.go(-1)" class="action-btn secondary">
            <el-icon><ArrowLeft /></el-icon>
            返回列表
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting" class="action-btn primary">
            <el-icon><Check /></el-icon>
            保存用户
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-container">
      <div class="form-container">
        <!-- 基本信息卡片 -->
        <el-card class="form-card" shadow="never">
          <template #header>
            <div class="card-header">
              <div class="card-title">
                <el-icon><User /></el-icon>
                <span>基本信息</span>
              </div>
              <div class="card-subtitle">用户的基本身份信息</div>
            </div>
          </template>

          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="100px"
            size="large"
            class="modern-form"
          >
            <!-- 头像上传区域 -->
            <div class="avatar-section">
              <div class="avatar-upload">
                <el-avatar :size="80" :src="form.avatar" class="user-avatar">
                  <el-icon size="40"><UserFilled /></el-icon>
                </el-avatar>
                <div class="upload-actions">
                  <el-button size="small" type="primary" plain>
                    <el-icon><Camera /></el-icon>
                    上传头像
                  </el-button>
                  <p class="upload-tip">支持 JPG、PNG 格式，建议尺寸 200x200</p>
                </div>
              </div>
            </div>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="用户名" prop="username">
                  <el-input 
                    v-model="form.username" 
                    placeholder="请输入用户名"
                    prefix-icon="User"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="真实姓名" prop="realName">
                  <el-input 
                    v-model="form.realName" 
                    placeholder="请输入真实姓名"
                    prefix-icon="UserFilled"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="邮箱地址" prop="email">
                  <el-input 
                    v-model="form.email" 
                    placeholder="请输入邮箱地址"
                    prefix-icon="Message"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号码" prop="phone">
                  <el-input 
                    v-model="form.phone" 
                    placeholder="请输入手机号码"
                    prefix-icon="Phone"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 角色权限卡片 -->
        <el-card class="form-card" shadow="never">
          <template #header>
            <div class="card-header">
              <div class="card-title">
                <el-icon><Lock /></el-icon>
                <span>角色权限</span>
              </div>
              <div class="card-subtitle">配置用户的角色和权限范围</div>
            </div>
          </template>

          <el-form
            :model="form"
            :rules="rules"
            label-width="100px"
            size="large"
            class="modern-form"
          >
            <el-form-item label="用户角色" prop="role">
              <div class="role-selection">
                <el-radio-group v-model="form.role" class="role-group">
                  <div class="role-option" v-for="role in roleOptions" :key="role.value">
                    <el-radio :label="role.value" class="role-radio">
                      <div class="role-content">
                        <div class="role-icon">
                          <el-icon :size="20" :color="role.color">
                            <component :is="role.icon" />
                          </el-icon>
                        </div>
                        <div class="role-info">
                          <div class="role-name">{{ role.label }}</div>
                          <div class="role-desc">{{ role.description }}</div>
                        </div>
                      </div>
                    </el-radio>
                  </div>
                </el-radio-group>
              </div>
            </el-form-item>

            <el-form-item label="所属部门" prop="department">
              <el-select 
                v-model="form.department" 
                placeholder="请选择所属部门"
                style="width: 100%"
                clearable
              >
                <el-option label="技术部" value="tech" />
                <el-option label="运营部" value="operation" />
                <el-option label="市场部" value="marketing" />
                <el-option label="客服部" value="service" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 账户安全卡片 -->
        <el-card class="form-card" shadow="never">
          <template #header>
            <div class="card-header">
              <div class="card-title">
                <el-icon><Lock /></el-icon>
                <span>账户安全</span>
              </div>
              <div class="card-subtitle">设置登录密码和账户状态</div>
            </div>
          </template>

          <el-form
            :model="form"
            :rules="rules"
            label-width="100px"
            size="large"
            class="modern-form"
          >
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="登录密码" prop="password">
                  <el-input 
                    v-model="form.password" 
                    type="password" 
                    placeholder="请输入登录密码"
                    prefix-icon="Lock"
                    show-password
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input 
                    v-model="form.confirmPassword" 
                    type="password" 
                    placeholder="请再次输入密码"
                    prefix-icon="Lock"
                    show-password
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="账户状态" prop="status">
              <el-radio-group v-model="form.status" class="status-group">
                <el-radio label="active" class="status-radio active">
                  <div class="status-content">
                    <el-icon color="#67c23a"><CircleCheckFilled /></el-icon>
                    <span>启用</span>
                    <small>用户可以正常登录使用</small>
                  </div>
                </el-radio>
                <el-radio label="inactive" class="status-radio inactive">
                  <div class="status-content">
                    <el-icon color="#f56c6c"><CircleCloseFilled /></el-icon>
                    <span>禁用</span>
                    <small>用户无法登录系统</small>
                  </div>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 附加信息卡片 -->
        <el-card class="form-card" shadow="never">
          <template #header>
            <div class="card-header">
              <div class="card-title">
                <el-icon><Document /></el-icon>
                <span>附加信息</span>
              </div>
              <div class="card-subtitle">用户的其他相关信息</div>
            </div>
          </template>

          <el-form
            :model="form"
            label-width="100px"
            size="large"
            class="modern-form"
          >
            <el-form-item label="备注信息">
              <el-input 
                v-model="form.remark" 
                type="textarea" 
                :rows="4"
                placeholder="请输入备注信息，如用户的特殊说明、联系方式等"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 操作按钮区域 -->
        <div class="form-actions">
          <el-button @click="$router.go(-1)" size="large" class="action-button cancel">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button @click="handleReset" size="large" class="action-button reset">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting" size="large" class="action-button submit">
            <el-icon><Check /></el-icon>
            保存用户
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, Check, Close, RefreshLeft, UserFilled, User, Lock, 
  Document, Camera, Phone, Message, CircleCheckFilled, CircleCloseFilled,
  Star, Avatar, Management, Share
} from '@element-plus/icons-vue'

const router = useRouter()
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)

// 角色选项配置
const roleOptions = [
  {
    value: 'admin',
    label: '超级管理员',
    description: '拥有系统所有权限，可以管理所有功能',
    icon: 'Star',
    color: '#f56c6c'
  },
  {
    value: 'substation',
    label: '分站管理员',
    description: '管理分站运营，拥有分站内所有权限',
    icon: 'Management',
    color: '#e6a23c'
  },
  {
    value: 'agent',
    label: '代理商',
    description: '管理下级分销员，拥有团队管理权限',
    icon: 'Avatar',
    color: '#409eff'
  },
  {
    value: 'distributor',
    label: '分销员',
    description: '推广群组链接，获得佣金收益',
    icon: 'Share',
    color: '#67c23a'
  },
  {
    value: 'group_owner',
    label: '群主',
    description: '管理自己的群组，发布群组内容',
    icon: 'UserFilled',
    color: '#909399'
  },
  {
    value: 'user',
    label: '普通用户',
    description: '基础用户权限，可以加入群组',
    icon: 'User',
    color: '#909399'
  }
]

// 表单数据
const form = reactive({
  username: '',
  realName: '',
  email: '',
  phone: '',
  role: 'user',
  department: '',
  password: '',
  confirmPassword: '',
  status: 'active',
  avatar: '',
  remark: ''
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '姓名长度在 2 到 10 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择用户角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入登录密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/, message: '密码必须包含大小写字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== form.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    username: '',
    realName: '',
    email: '',
    phone: '',
    role: 'user',
    department: '',
    password: '',
    confirmPassword: '',
    status: 'active',
    avatar: '',
    remark: ''
  })
  ElMessage.info('表单已重置')
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success('用户创建成功！')
    router.push('/user/list')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单信息是否填写正确')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.modern-user-add {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  // 页面头部样式
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          height: 40px;
          padding: 0 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }
  
  // 内容容器
  .content-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 24px 40px;
    
    .form-container {
      display: flex;
      flex-direction: column;
      gap: 24px;
      
      // 表单卡片样式
      .form-card {
        border-radius: 16px;
        border: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        :deep(.el-card__header) {
          background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
          border-bottom: 1px solid #e4e7ed;
          padding: 20px 24px;
          
          .card-header {
            .card-title {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 16px;
              font-weight: 600;
              color: #303133;
              margin-bottom: 4px;
              
              .el-icon {
                color: #667eea;
              }
            }
            
            .card-subtitle {
              font-size: 13px;
              color: #909399;
              margin: 0;
            }
          }
        }
        
        :deep(.el-card__body) {
          padding: 32px 24px;
        }
      }
      
      // 现代表单样式
      .modern-form {
        :deep(.el-form-item) {
          margin-bottom: 24px;
          
          .el-form-item__label {
            font-weight: 500;
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
          }
          
          .el-input {
            .el-input__wrapper {
              border-radius: 8px;
              border: 1px solid #dcdfe6;
              transition: all 0.3s ease;
              
              &:hover {
                border-color: #c0c4cc;
              }
              
              &.is-focus {
                border-color: #667eea;
                box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
              }
            }
          }
          
          .el-select {
            width: 100%;
            
            .el-input__wrapper {
              border-radius: 8px;
            }
          }
          
          .el-textarea {
            .el-textarea__inner {
              border-radius: 8px;
              border: 1px solid #dcdfe6;
              transition: all 0.3s ease;
              
              &:hover {
                border-color: #c0c4cc;
              }
              
              &:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
              }
            }
          }
        }
      }
      
      // 头像上传区域
      .avatar-section {
        display: flex;
        justify-content: center;
        margin-bottom: 32px;
        
        .avatar-upload {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 16px;
          
          .user-avatar {
            border: 4px solid #f0f2ff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            
            &:hover {
              transform: scale(1.05);
              box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            }
          }
          
          .upload-actions {
            text-align: center;
            
            .el-button {
              border-radius: 20px;
              padding: 8px 16px;
              font-size: 13px;
            }
            
            .upload-tip {
              margin: 8px 0 0 0;
              font-size: 12px;
              color: #909399;
              line-height: 1.4;
            }
          }
        }
      }
      
      // 角色选择样式
      .role-selection {
        .role-group {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 16px;
          
          .role-option {
            .role-radio {
              width: 100%;
              margin: 0;
              
              :deep(.el-radio__input) {
                display: none;
              }
              
              :deep(.el-radio__label) {
                padding: 0;
                width: 100%;
              }
              
              .role-content {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 16px;
                border: 2px solid #e4e7ed;
                border-radius: 12px;
                background: white;
                cursor: pointer;
                transition: all 0.3s ease;
                
                &:hover {
                  border-color: #667eea;
                  background: #f8f9ff;
                  transform: translateY(-2px);
                  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
                }
                
                .role-icon {
                  width: 40px;
                  height: 40px;
                  border-radius: 10px;
                  background: #f5f7fa;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  transition: all 0.3s ease;
                }
                
                .role-info {
                  flex: 1;
                  
                  .role-name {
                    font-size: 14px;
                    font-weight: 600;
                    color: #303133;
                    margin-bottom: 4px;
                  }
                  
                  .role-desc {
                    font-size: 12px;
                    color: #909399;
                    line-height: 1.4;
                  }
                }
              }
              
              &.is-checked {
                .role-content {
                  border-color: #667eea;
                  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
                  
                  .role-icon {
                    background: #667eea;
                    color: white;
                  }
                  
                  .role-name {
                    color: #667eea;
                  }
                }
              }
            }
          }
        }
      }
      
      // 状态选择样式
      .status-group {
        display: flex;
        gap: 16px;
        
        .status-radio {
          flex: 1;
          margin: 0;
          
          :deep(.el-radio__input) {
            display: none;
          }
          
          :deep(.el-radio__label) {
            padding: 0;
            width: 100%;
          }
          
          .status-content {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            border: 2px solid #e4e7ed;
            border-radius: 12px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            }
            
            span {
              font-weight: 500;
              color: #303133;
            }
            
            small {
              font-size: 12px;
              color: #909399;
              margin-left: auto;
            }
          }
          
          &.is-checked {
            &.active .status-content {
              border-color: #67c23a;
              background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            }
            
            &.inactive .status-content {
              border-color: #f56c6c;
              background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
            }
          }
        }
      }
      
      // 操作按钮区域
      .form-actions {
        display: flex;
        justify-content: center;
        gap: 16px;
        padding: 32px 0;
        border-top: 1px solid #e4e7ed;
        margin-top: 24px;
        
        .action-button {
          min-width: 120px;
          height: 44px;
          border-radius: 22px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.cancel {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.reset {
            background: #fff7e6;
            border-color: #ffd666;
            color: #e6a23c;
            
            &:hover {
              background: #fff2d9;
              border-color: #ffcc33;
              transform: translateY(-2px);
            }
          }
          
          &.submit {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
            
            &:active {
              transform: translateY(0);
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modern-user-add {
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .content-container {
      padding: 0 16px 40px;
      
      .form-container .form-card :deep(.el-card__body) {
        padding: 24px 16px;
      }
      
      .role-selection .role-group {
        grid-template-columns: 1fr;
      }
      
      .status-group {
        flex-direction: column;
      }
      
      .form-actions {
        flex-direction: column;
        
        .action-button {
          width: 100%;
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-card {
  animation: fadeInUp 0.6s ease-out;
  
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
}
</style>
