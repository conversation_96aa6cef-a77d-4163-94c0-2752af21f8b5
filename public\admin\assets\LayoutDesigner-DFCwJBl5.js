/* empty css             *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                        */import{k as e,J as t,r as n,c as o,w as i,I as a,z as r,m as l,A as s,t as c,l as u,F as d,Y as p,D as h,q as f,G as v,E as g,C as m,K as b,M as y}from"./vue-vendor-BcnDv-68.js";import{s as w,ah as S,a1 as D,Z as _,aK as E,al as C,a3 as T,a4 as x,ap as M,cf as O,aq as P,a2 as A,bv as I,bw as k,a9 as N,by as R,b2 as j,ad as X,bA as Y,aa as V,ac as F,bu as B,av as L,X as U}from"./element-plus-C2UshkXo.js";import{_ as H}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";function z(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function W(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?z(Object(n),!0).forEach(function(t){$(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):z(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function G(e){return G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},G(e)}function $(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function q(){return q=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},q.apply(this,arguments)}function K(e,t){if(null==e)return{};var n,o,i=function(e,t){if(null==e)return{};var n,o,i={},a=Object.keys(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function J(e){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(e)}var Z=J(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Q=J(/Edge/i),ee=J(/firefox/i),te=J(/safari/i)&&!J(/chrome/i)&&!J(/android/i),ne=J(/iP(ad|od|hone)/i),oe=J(/chrome/i)&&J(/android/i),ie={capture:!1,passive:!1};function ae(e,t,n){e.addEventListener(t,n,!Z&&ie)}function re(e,t,n){e.removeEventListener(t,n,!Z&&ie)}function le(e,t){if(t){if(">"===t[0]&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch(n){return!1}return!1}}function se(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function ce(e,t,n,o){if(e){n=n||document;do{if(null!=t&&(">"===t[0]?e.parentNode===n&&le(e,t):le(e,t))||o&&e===n)return e;if(e===n)break}while(e=se(e))}return null}var ue,de=/\s+/g;function pe(e,t,n){if(e&&t)if(e.classList)e.classList[n?"add":"remove"](t);else{var o=(" "+e.className+" ").replace(de," ").replace(" "+t+" "," ");e.className=(o+(n?" "+t:"")).replace(de," ")}}function he(e,t,n){var o=e&&e.style;if(o){if(void 0===n)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(n=e.currentStyle),void 0===t?n:n[t];t in o||-1!==t.indexOf("webkit")||(t="-webkit-"+t),o[t]=n+("string"==typeof n?"":"px")}}function fe(e,t){var n="";if("string"==typeof e)n=e;else do{var o=he(e,"transform");o&&"none"!==o&&(n=o+" "+n)}while(!t&&(e=e.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(n)}function ve(e,t,n){if(e){var o=e.getElementsByTagName(t),i=0,a=o.length;if(n)for(;i<a;i++)n(o[i],i);return o}return[]}function ge(){var e=document.scrollingElement;return e||document.documentElement}function me(e,t,n,o,i){if(e.getBoundingClientRect||e===window){var a,r,l,s,c,u,d;if(e!==window&&e.parentNode&&e!==ge()?(r=(a=e.getBoundingClientRect()).top,l=a.left,s=a.bottom,c=a.right,u=a.height,d=a.width):(r=0,l=0,s=window.innerHeight,c=window.innerWidth,u=window.innerHeight,d=window.innerWidth),(t||n)&&e!==window&&(i=i||e.parentNode,!Z))do{if(i&&i.getBoundingClientRect&&("none"!==he(i,"transform")||n&&"static"!==he(i,"position"))){var p=i.getBoundingClientRect();r-=p.top+parseInt(he(i,"border-top-width")),l-=p.left+parseInt(he(i,"border-left-width")),s=r+a.height,c=l+a.width;break}}while(i=i.parentNode);if(o&&e!==window){var h=fe(i||e),f=h&&h.a,v=h&&h.d;h&&(s=(r/=v)+(u/=v),c=(l/=f)+(d/=f))}return{top:r,left:l,bottom:s,right:c,width:d,height:u}}}function be(e,t,n){for(var o=_e(e,!0),i=me(e)[t];o;){if(!(i>=me(o)[n]))return o;if(o===ge())break;o=_e(o,!1)}return!1}function ye(e,t,n,o){for(var i=0,a=0,r=e.children;a<r.length;){if("none"!==r[a].style.display&&r[a]!==xt.ghost&&(o||r[a]!==xt.dragged)&&ce(r[a],n.draggable,e,!1)){if(i===t)return r[a];i++}a++}return null}function we(e,t){for(var n=e.lastElementChild;n&&(n===xt.ghost||"none"===he(n,"display")||t&&!le(n,t));)n=n.previousElementSibling;return n||null}function Se(e,t){var n=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)"TEMPLATE"===e.nodeName.toUpperCase()||e===xt.clone||t&&!le(e,t)||n++;return n}function De(e){var t=0,n=0,o=ge();if(e)do{var i=fe(e),a=i.a,r=i.d;t+=e.scrollLeft*a,n+=e.scrollTop*r}while(e!==o&&(e=e.parentNode));return[t,n]}function _e(e,t){if(!e||!e.getBoundingClientRect)return ge();var n=e,o=!1;do{if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var i=he(n);if(n.clientWidth<n.scrollWidth&&("auto"==i.overflowX||"scroll"==i.overflowX)||n.clientHeight<n.scrollHeight&&("auto"==i.overflowY||"scroll"==i.overflowY)){if(!n.getBoundingClientRect||n===document.body)return ge();if(o||t)return n;o=!0}}}while(n=n.parentNode);return ge()}function Ee(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}function Ce(e,t){return function(){if(!ue){var n=arguments;1===n.length?e.call(this,n[0]):e.apply(this,n),ue=setTimeout(function(){ue=void 0},t)}}}function Te(e,t,n){e.scrollLeft+=t,e.scrollTop+=n}function xe(e){var t=window.Polymer,n=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):n?n(e).clone(!0)[0]:e.cloneNode(!0)}function Me(e,t,n){var o={};return Array.from(e.children).forEach(function(i){var a,r,l,s;if(ce(i,t.draggable,e,!1)&&!i.animated&&i!==n){var c=me(i);o.left=Math.min(null!==(a=o.left)&&void 0!==a?a:1/0,c.left),o.top=Math.min(null!==(r=o.top)&&void 0!==r?r:1/0,c.top),o.right=Math.max(null!==(l=o.right)&&void 0!==l?l:-1/0,c.right),o.bottom=Math.max(null!==(s=o.bottom)&&void 0!==s?s:-1/0,c.bottom)}}),o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}var Oe="Sortable"+(new Date).getTime();function Pe(){var e,t=[];return{captureAnimationState:function(){(t=[],this.options.animation)&&[].slice.call(this.el.children).forEach(function(e){if("none"!==he(e,"display")&&e!==xt.ghost){t.push({target:e,rect:me(e)});var n=W({},t[t.length-1].rect);if(e.thisAnimationDuration){var o=fe(e,!0);o&&(n.top-=o.f,n.left-=o.e)}e.fromRect=n}})},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice(function(e,t){for(var n in e)if(e.hasOwnProperty(n))for(var o in t)if(t.hasOwnProperty(o)&&t[o]===e[n][o])return Number(n);return-1}(t,{target:e}),1)},animateAll:function(n){var o=this;if(!this.options.animation)return clearTimeout(e),void("function"==typeof n&&n());var i=!1,a=0;t.forEach(function(e){var t=0,n=e.target,r=n.fromRect,l=me(n),s=n.prevFromRect,c=n.prevToRect,u=e.rect,d=fe(n,!0);d&&(l.top-=d.f,l.left-=d.e),n.toRect=l,n.thisAnimationDuration&&Ee(s,l)&&!Ee(r,l)&&(u.top-l.top)/(u.left-l.left)===(r.top-l.top)/(r.left-l.left)&&(t=function(e,t,n,o){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-n.top,2)+Math.pow(t.left-n.left,2))*o.animation}(u,s,c,o.options)),Ee(l,r)||(n.prevFromRect=r,n.prevToRect=l,t||(t=o.options.animation),o.animate(n,u,l,t)),t&&(i=!0,a=Math.max(a,t),clearTimeout(n.animationResetTimer),n.animationResetTimer=setTimeout(function(){n.animationTime=0,n.prevFromRect=null,n.fromRect=null,n.prevToRect=null,n.thisAnimationDuration=null},t),n.thisAnimationDuration=t)}),clearTimeout(e),i?e=setTimeout(function(){"function"==typeof n&&n()},a):"function"==typeof n&&n(),t=[]},animate:function(e,t,n,o){if(o){he(e,"transition",""),he(e,"transform","");var i=fe(this.el),a=i&&i.a,r=i&&i.d,l=(t.left-n.left)/(a||1),s=(t.top-n.top)/(r||1);e.animatingX=!!l,e.animatingY=!!s,he(e,"transform","translate3d("+l+"px,"+s+"px,0)"),this.forRepaintDummy=function(e){return e.offsetWidth}(e),he(e,"transition","transform "+o+"ms"+(this.options.easing?" "+this.options.easing:"")),he(e,"transform","translate3d(0,0,0)"),"number"==typeof e.animated&&clearTimeout(e.animated),e.animated=setTimeout(function(){he(e,"transition",""),he(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1},o)}}}}var Ae=[],Ie={initializeByDefault:!0},ke={mount:function(e){for(var t in Ie)Ie.hasOwnProperty(t)&&!(t in e)&&(e[t]=Ie[t]);Ae.forEach(function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),Ae.push(e)},pluginEvent:function(e,t,n){var o=this;this.eventCanceled=!1,n.cancel=function(){o.eventCanceled=!0};var i=e+"Global";Ae.forEach(function(o){t[o.pluginName]&&(t[o.pluginName][i]&&t[o.pluginName][i](W({sortable:t},n)),t.options[o.pluginName]&&t[o.pluginName][e]&&t[o.pluginName][e](W({sortable:t},n)))})},initializePlugins:function(e,t,n,o){for(var i in Ae.forEach(function(o){var i=o.pluginName;if(e.options[i]||o.initializeByDefault){var a=new o(e,t,e.options);a.sortable=e,a.options=e.options,e[i]=a,q(n,a.defaults)}}),e.options)if(e.options.hasOwnProperty(i)){var a=this.modifyOption(e,i,e.options[i]);void 0!==a&&(e.options[i]=a)}},getEventProperties:function(e,t){var n={};return Ae.forEach(function(o){"function"==typeof o.eventProperties&&q(n,o.eventProperties.call(t[o.pluginName],e))}),n},modifyOption:function(e,t,n){var o;return Ae.forEach(function(i){e[i.pluginName]&&i.optionListeners&&"function"==typeof i.optionListeners[t]&&(o=i.optionListeners[t].call(e[i.pluginName],n))}),o}};var Ne=["evt"],Re=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=n.evt,i=K(n,Ne);ke.pluginEvent.bind(xt)(e,t,W({dragEl:Xe,parentEl:Ye,ghostEl:Ve,rootEl:Fe,nextEl:Be,lastDownEl:Le,cloneEl:Ue,cloneHidden:He,dragStarted:ot,putSortable:Ke,activeSortable:xt.active,originalEvent:o,oldIndex:ze,oldDraggableIndex:Ge,newIndex:We,newDraggableIndex:$e,hideGhostForTarget:_t,unhideGhostForTarget:Et,cloneNowHidden:function(){He=!0},cloneNowShown:function(){He=!1},dispatchSortableEvent:function(e){je({sortable:t,name:e,originalEvent:o})}},i))};function je(e){!function(e){var t=e.sortable,n=e.rootEl,o=e.name,i=e.targetEl,a=e.cloneEl,r=e.toEl,l=e.fromEl,s=e.oldIndex,c=e.newIndex,u=e.oldDraggableIndex,d=e.newDraggableIndex,p=e.originalEvent,h=e.putSortable,f=e.extraEventProperties;if(t=t||n&&n[Oe]){var v,g=t.options,m="on"+o.charAt(0).toUpperCase()+o.substr(1);!window.CustomEvent||Z||Q?(v=document.createEvent("Event")).initEvent(o,!0,!0):v=new CustomEvent(o,{bubbles:!0,cancelable:!0}),v.to=r||n,v.from=l||n,v.item=i||n,v.clone=a,v.oldIndex=s,v.newIndex=c,v.oldDraggableIndex=u,v.newDraggableIndex=d,v.originalEvent=p,v.pullMode=h?h.lastPutMode:void 0;var b=W(W({},f),ke.getEventProperties(o,t));for(var y in b)v[y]=b[y];n&&n.dispatchEvent(v),g[m]&&g[m].call(t,v)}}(W({putSortable:Ke,cloneEl:Ue,targetEl:Xe,rootEl:Fe,oldIndex:ze,oldDraggableIndex:Ge,newIndex:We,newDraggableIndex:$e},e))}var Xe,Ye,Ve,Fe,Be,Le,Ue,He,ze,We,Ge,$e,qe,Ke,Je,Ze,Qe,et,tt,nt,ot,it,at,rt,lt,st=!1,ct=!1,ut=[],dt=!1,pt=!1,ht=[],ft=!1,vt=[],gt="undefined"!=typeof document,mt=ne,bt=Q||Z?"cssFloat":"float",yt=gt&&!oe&&!ne&&"draggable"in document.createElement("div"),wt=function(){if(gt){if(Z)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto","auto"===e.style.pointerEvents}}(),St=function(e,t){var n=he(e),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),i=ye(e,0,t),a=ye(e,1,t),r=i&&he(i),l=a&&he(a),s=r&&parseInt(r.marginLeft)+parseInt(r.marginRight)+me(i).width,c=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+me(a).width;if("flex"===n.display)return"column"===n.flexDirection||"column-reverse"===n.flexDirection?"vertical":"horizontal";if("grid"===n.display)return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&r.float&&"none"!==r.float){var u="left"===r.float?"left":"right";return!a||"both"!==l.clear&&l.clear!==u?"horizontal":"vertical"}return i&&("block"===r.display||"flex"===r.display||"table"===r.display||"grid"===r.display||s>=o&&"none"===n[bt]||a&&"none"===n[bt]&&s+c>o)?"vertical":"horizontal"},Dt=function(e){function t(e,n){return function(o,i,a,r){var l=o.options.group.name&&i.options.group.name&&o.options.group.name===i.options.group.name;if(null==e&&(n||l))return!0;if(null==e||!1===e)return!1;if(n&&"clone"===e)return e;if("function"==typeof e)return t(e(o,i,a,r),n)(o,i,a,r);var s=(n?o:i).options.group.name;return!0===e||"string"==typeof e&&e===s||e.join&&e.indexOf(s)>-1}}var n={},o=e.group;o&&"object"==G(o)||(o={name:o}),n.name=o.name,n.checkPull=t(o.pull,!0),n.checkPut=t(o.put),n.revertClone=o.revertClone,e.group=n},_t=function(){!wt&&Ve&&he(Ve,"display","none")},Et=function(){!wt&&Ve&&he(Ve,"display","")};gt&&!oe&&document.addEventListener("click",function(e){if(ct)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),ct=!1,!1},!0);var Ct=function(e){if(Xe){e=e.touches?e.touches[0]:e;var t=(i=e.clientX,a=e.clientY,ut.some(function(e){var t=e[Oe].options.emptyInsertThreshold;if(t&&!we(e)){var n=me(e),o=i>=n.left-t&&i<=n.right+t,l=a>=n.top-t&&a<=n.bottom+t;return o&&l?r=e:void 0}}),r);if(t){var n={};for(var o in e)e.hasOwnProperty(o)&&(n[o]=e[o]);n.target=n.rootEl=t,n.preventDefault=void 0,n.stopPropagation=void 0,t[Oe]._onDragOver(n)}}var i,a,r},Tt=function(e){Xe&&Xe.parentNode[Oe]._isOutsideThisEl(e.target)};function xt(e,t){if(!e||!e.nodeType||1!==e.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=q({},t),e[Oe]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return St(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(e,t){e.setData("Text",t.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==xt.supportPointer&&"PointerEvent"in window&&(!te||ne),emptyInsertThreshold:5};for(var o in ke.initializePlugins(this,e,n),n)!(o in t)&&(t[o]=n[o]);for(var i in Dt(t),this)"_"===i.charAt(0)&&"function"==typeof this[i]&&(this[i]=this[i].bind(this));this.nativeDraggable=!t.forceFallback&&yt,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?ae(e,"pointerdown",this._onTapStart):(ae(e,"mousedown",this._onTapStart),ae(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(ae(e,"dragover",this),ae(e,"dragenter",this)),ut.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),q(this,Pe())}function Mt(e,t,n,o,i,a,r,l){var s,c,u=e[Oe],d=u.options.onMove;return!window.CustomEvent||Z||Q?(s=document.createEvent("Event")).initEvent("move",!0,!0):s=new CustomEvent("move",{bubbles:!0,cancelable:!0}),s.to=t,s.from=e,s.dragged=n,s.draggedRect=o,s.related=i||t,s.relatedRect=a||me(t),s.willInsertAfter=l,s.originalEvent=r,e.dispatchEvent(s),d&&(c=d.call(u,s,r)),c}function Ot(e){e.draggable=!1}function Pt(){ft=!1}function At(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,n=t.length,o=0;n--;)o+=t.charCodeAt(n);return o.toString(36)}function It(e){return setTimeout(e,0)}function kt(e){return clearTimeout(e)}xt.prototype={constructor:xt,_isOutsideThisEl:function(e){this.el.contains(e)||e===this.el||(it=null)},_getDirection:function(e,t){return"function"==typeof this.options.direction?this.options.direction.call(this,e,t,Xe):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,n=this.el,o=this.options,i=o.preventOnFilter,a=e.type,r=e.touches&&e.touches[0]||e.pointerType&&"touch"===e.pointerType&&e,l=(r||e).target,s=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||l,c=o.filter;if(function(e){vt.length=0;var t=e.getElementsByTagName("input"),n=t.length;for(;n--;){var o=t[n];o.checked&&vt.push(o)}}(n),!Xe&&!(/mousedown|pointerdown/.test(a)&&0!==e.button||o.disabled)&&!s.isContentEditable&&(this.nativeDraggable||!te||!l||"SELECT"!==l.tagName.toUpperCase())&&!((l=ce(l,o.draggable,n,!1))&&l.animated||Le===l)){if(ze=Se(l),Ge=Se(l,o.draggable),"function"==typeof c){if(c.call(this,e,l,this))return je({sortable:t,rootEl:s,name:"filter",targetEl:l,toEl:n,fromEl:n}),Re("filter",t,{evt:e}),void(i&&e.preventDefault())}else if(c&&(c=c.split(",").some(function(o){if(o=ce(s,o.trim(),n,!1))return je({sortable:t,rootEl:o,name:"filter",targetEl:l,fromEl:n,toEl:n}),Re("filter",t,{evt:e}),!0})))return void(i&&e.preventDefault());o.handle&&!ce(s,o.handle,n,!1)||this._prepareDragStart(e,r,l)}}},_prepareDragStart:function(e,t,n){var o,i=this,a=i.el,r=i.options,l=a.ownerDocument;if(n&&!Xe&&n.parentNode===a){var s=me(n);if(Fe=a,Ye=(Xe=n).parentNode,Be=Xe.nextSibling,Le=n,qe=r.group,xt.dragged=Xe,Je={target:Xe,clientX:(t||e).clientX,clientY:(t||e).clientY},tt=Je.clientX-s.left,nt=Je.clientY-s.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,Xe.style["will-change"]="all",o=function(){Re("delayEnded",i,{evt:e}),xt.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!ee&&i.nativeDraggable&&(Xe.draggable=!0),i._triggerDragStart(e,t),je({sortable:i,name:"choose",originalEvent:e}),pe(Xe,r.chosenClass,!0))},r.ignore.split(",").forEach(function(e){ve(Xe,e.trim(),Ot)}),ae(l,"dragover",Ct),ae(l,"mousemove",Ct),ae(l,"touchmove",Ct),r.supportPointer?(ae(l,"pointerup",i._onDrop),!this.nativeDraggable&&ae(l,"pointercancel",i._onDrop)):(ae(l,"mouseup",i._onDrop),ae(l,"touchend",i._onDrop),ae(l,"touchcancel",i._onDrop)),ee&&this.nativeDraggable&&(this.options.touchStartThreshold=4,Xe.draggable=!0),Re("delayStart",this,{evt:e}),!r.delay||r.delayOnTouchOnly&&!t||this.nativeDraggable&&(Q||Z))o();else{if(xt.eventCanceled)return void this._onDrop();r.supportPointer?(ae(l,"pointerup",i._disableDelayedDrag),ae(l,"pointercancel",i._disableDelayedDrag)):(ae(l,"mouseup",i._disableDelayedDrag),ae(l,"touchend",i._disableDelayedDrag),ae(l,"touchcancel",i._disableDelayedDrag)),ae(l,"mousemove",i._delayedDragTouchMoveHandler),ae(l,"touchmove",i._delayedDragTouchMoveHandler),r.supportPointer&&ae(l,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(o,r.delay)}}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){Xe&&Ot(Xe),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;re(e,"mouseup",this._disableDelayedDrag),re(e,"touchend",this._disableDelayedDrag),re(e,"touchcancel",this._disableDelayedDrag),re(e,"pointerup",this._disableDelayedDrag),re(e,"pointercancel",this._disableDelayedDrag),re(e,"mousemove",this._delayedDragTouchMoveHandler),re(e,"touchmove",this._delayedDragTouchMoveHandler),re(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||"touch"==e.pointerType&&e,!this.nativeDraggable||t?this.options.supportPointer?ae(document,"pointermove",this._onTouchMove):ae(document,t?"touchmove":"mousemove",this._onTouchMove):(ae(Xe,"dragend",this),ae(Fe,"dragstart",this._onDragStart));try{document.selection?It(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(n){}},_dragStarted:function(e,t){if(st=!1,Fe&&Xe){Re("dragStarted",this,{evt:t}),this.nativeDraggable&&ae(document,"dragover",Tt);var n=this.options;!e&&pe(Xe,n.dragClass,!1),pe(Xe,n.ghostClass,!0),xt.active=this,e&&this._appendGhost(),je({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(Ze){this._lastX=Ze.clientX,this._lastY=Ze.clientY,_t();for(var e=document.elementFromPoint(Ze.clientX,Ze.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(Ze.clientX,Ze.clientY))!==t;)t=e;if(Xe.parentNode[Oe]._isOutsideThisEl(e),t)do{if(t[Oe]){if(t[Oe]._onDragOver({clientX:Ze.clientX,clientY:Ze.clientY,target:e,rootEl:t})&&!this.options.dragoverBubble)break}e=t}while(t=se(t));Et()}},_onTouchMove:function(e){if(Je){var t=this.options,n=t.fallbackTolerance,o=t.fallbackOffset,i=e.touches?e.touches[0]:e,a=Ve&&fe(Ve,!0),r=Ve&&a&&a.a,l=Ve&&a&&a.d,s=mt&&lt&&De(lt),c=(i.clientX-Je.clientX+o.x)/(r||1)+(s?s[0]-ht[0]:0)/(r||1),u=(i.clientY-Je.clientY+o.y)/(l||1)+(s?s[1]-ht[1]:0)/(l||1);if(!xt.active&&!st){if(n&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<n)return;this._onDragStart(e,!0)}if(Ve){a?(a.e+=c-(Qe||0),a.f+=u-(et||0)):a={a:1,b:0,c:0,d:1,e:c,f:u};var d="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");he(Ve,"webkitTransform",d),he(Ve,"mozTransform",d),he(Ve,"msTransform",d),he(Ve,"transform",d),Qe=c,et=u,Ze=i}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!Ve){var e=this.options.fallbackOnBody?document.body:Fe,t=me(Xe,!0,mt,!0,e),n=this.options;if(mt){for(lt=e;"static"===he(lt,"position")&&"none"===he(lt,"transform")&&lt!==document;)lt=lt.parentNode;lt!==document.body&&lt!==document.documentElement?(lt===document&&(lt=ge()),t.top+=lt.scrollTop,t.left+=lt.scrollLeft):lt=ge(),ht=De(lt)}pe(Ve=Xe.cloneNode(!0),n.ghostClass,!1),pe(Ve,n.fallbackClass,!0),pe(Ve,n.dragClass,!0),he(Ve,"transition",""),he(Ve,"transform",""),he(Ve,"box-sizing","border-box"),he(Ve,"margin",0),he(Ve,"top",t.top),he(Ve,"left",t.left),he(Ve,"width",t.width),he(Ve,"height",t.height),he(Ve,"opacity","0.8"),he(Ve,"position",mt?"absolute":"fixed"),he(Ve,"zIndex","100000"),he(Ve,"pointerEvents","none"),xt.ghost=Ve,e.appendChild(Ve),he(Ve,"transform-origin",tt/parseInt(Ve.style.width)*100+"% "+nt/parseInt(Ve.style.height)*100+"%")}},_onDragStart:function(e,t){var n=this,o=e.dataTransfer,i=n.options;Re("dragStart",this,{evt:e}),xt.eventCanceled?this._onDrop():(Re("setupClone",this),xt.eventCanceled||((Ue=xe(Xe)).removeAttribute("id"),Ue.draggable=!1,Ue.style["will-change"]="",this._hideClone(),pe(Ue,this.options.chosenClass,!1),xt.clone=Ue),n.cloneId=It(function(){Re("clone",n),xt.eventCanceled||(n.options.removeCloneOnHide||Fe.insertBefore(Ue,Xe),n._hideClone(),je({sortable:n,name:"clone"}))}),!t&&pe(Xe,i.dragClass,!0),t?(ct=!0,n._loopId=setInterval(n._emulateDragOver,50)):(re(document,"mouseup",n._onDrop),re(document,"touchend",n._onDrop),re(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",i.setData&&i.setData.call(n,o,Xe)),ae(document,"drop",n),he(Xe,"transform","translateZ(0)")),st=!0,n._dragStartId=It(n._dragStarted.bind(n,t,e)),ae(document,"selectstart",n),ot=!0,window.getSelection().removeAllRanges(),te&&he(document.body,"user-select","none"))},_onDragOver:function(e){var t,n,o,i,a=this.el,r=e.target,l=this.options,s=l.group,c=xt.active,u=qe===s,d=l.sort,p=Ke||c,h=this,f=!1;if(!ft){if(void 0!==e.preventDefault&&e.cancelable&&e.preventDefault(),r=ce(r,l.draggable,a,!0),O("dragOver"),xt.eventCanceled)return f;if(Xe.contains(e.target)||r.animated&&r.animatingX&&r.animatingY||h._ignoreWhileAnimating===r)return A(!1);if(ct=!1,c&&!l.disabled&&(u?d||(o=Ye!==Fe):Ke===this||(this.lastPutMode=qe.checkPull(this,c,Xe,e))&&s.checkPut(this,c,Xe,e))){if(i="vertical"===this._getDirection(e,r),t=me(Xe),O("dragOverValid"),xt.eventCanceled)return f;if(o)return Ye=Fe,P(),this._hideClone(),O("revert"),xt.eventCanceled||(Be?Fe.insertBefore(Xe,Be):Fe.appendChild(Xe)),A(!0);var v=we(a,l.draggable);if(!v||function(e,t,n){var o=me(we(n.el,n.options.draggable)),i=Me(n.el,n.options,Ve),a=10;return t?e.clientX>i.right+a||e.clientY>o.bottom&&e.clientX>o.left:e.clientY>i.bottom+a||e.clientX>o.right&&e.clientY>o.top}(e,i,this)&&!v.animated){if(v===Xe)return A(!1);if(v&&a===e.target&&(r=v),r&&(n=me(r)),!1!==Mt(Fe,a,Xe,t,r,n,e,!!r))return P(),v&&v.nextSibling?a.insertBefore(Xe,v.nextSibling):a.appendChild(Xe),Ye=a,I(),A(!0)}else if(v&&function(e,t,n){var o=me(ye(n.el,0,n.options,!0)),i=Me(n.el,n.options,Ve),a=10;return t?e.clientX<i.left-a||e.clientY<o.top&&e.clientX<o.right:e.clientY<i.top-a||e.clientY<o.bottom&&e.clientX<o.left}(e,i,this)){var g=ye(a,0,l,!0);if(g===Xe)return A(!1);if(n=me(r=g),!1!==Mt(Fe,a,Xe,t,r,n,e,!1))return P(),a.insertBefore(Xe,g),Ye=a,I(),A(!0)}else if(r.parentNode===a){n=me(r);var m,b,y,w=Xe.parentNode!==a,S=!function(e,t,n){var o=n?e.left:e.top,i=n?e.right:e.bottom,a=n?e.width:e.height,r=n?t.left:t.top,l=n?t.right:t.bottom,s=n?t.width:t.height;return o===r||i===l||o+a/2===r+s/2}(Xe.animated&&Xe.toRect||t,r.animated&&r.toRect||n,i),D=i?"top":"left",_=be(r,"top","top")||be(Xe,"top","top"),E=_?_.scrollTop:void 0;if(it!==r&&(b=n[D],dt=!1,pt=!S&&l.invertSwap||w),m=function(e,t,n,o,i,a,r,l){var s=o?e.clientY:e.clientX,c=o?n.height:n.width,u=o?n.top:n.left,d=o?n.bottom:n.right,p=!1;if(!r)if(l&&rt<c*i){if(!dt&&(1===at?s>u+c*a/2:s<d-c*a/2)&&(dt=!0),dt)p=!0;else if(1===at?s<u+rt:s>d-rt)return-at}else if(s>u+c*(1-i)/2&&s<d-c*(1-i)/2)return function(e){return Se(Xe)<Se(e)?1:-1}(t);if((p=p||r)&&(s<u+c*a/2||s>d-c*a/2))return s>u+c/2?1:-1;return 0}(e,r,n,i,S?1:l.swapThreshold,null==l.invertedSwapThreshold?l.swapThreshold:l.invertedSwapThreshold,pt,it===r),0!==m){var C=Se(Xe);do{C-=m,y=Ye.children[C]}while(y&&("none"===he(y,"display")||y===Ve))}if(0===m||y===r)return A(!1);it=r,at=m;var T=r.nextElementSibling,x=!1,M=Mt(Fe,a,Xe,t,r,n,e,x=1===m);if(!1!==M)return 1!==M&&-1!==M||(x=1===M),ft=!0,setTimeout(Pt,30),P(),x&&!T?a.appendChild(Xe):r.parentNode.insertBefore(Xe,x?T:r),_&&Te(_,0,E-_.scrollTop),Ye=Xe.parentNode,void 0===b||pt||(rt=Math.abs(b-me(r)[D])),I(),A(!0)}if(a.contains(Xe))return A(!1)}return!1}function O(l,s){Re(l,h,W({evt:e,isOwner:u,axis:i?"vertical":"horizontal",revert:o,dragRect:t,targetRect:n,canSort:d,fromSortable:p,target:r,completed:A,onMove:function(n,o){return Mt(Fe,a,Xe,t,n,me(n),e,o)},changed:I},s))}function P(){O("dragOverAnimationCapture"),h.captureAnimationState(),h!==p&&p.captureAnimationState()}function A(t){return O("dragOverCompleted",{insertion:t}),t&&(u?c._hideClone():c._showClone(h),h!==p&&(pe(Xe,Ke?Ke.options.ghostClass:c.options.ghostClass,!1),pe(Xe,l.ghostClass,!0)),Ke!==h&&h!==xt.active?Ke=h:h===xt.active&&Ke&&(Ke=null),p===h&&(h._ignoreWhileAnimating=r),h.animateAll(function(){O("dragOverAnimationComplete"),h._ignoreWhileAnimating=null}),h!==p&&(p.animateAll(),p._ignoreWhileAnimating=null)),(r===Xe&&!Xe.animated||r===a&&!r.animated)&&(it=null),l.dragoverBubble||e.rootEl||r===document||(Xe.parentNode[Oe]._isOutsideThisEl(e.target),!t&&Ct(e)),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),f=!0}function I(){We=Se(Xe),$e=Se(Xe,l.draggable),je({sortable:h,name:"change",toEl:a,newIndex:We,newDraggableIndex:$e,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){re(document,"mousemove",this._onTouchMove),re(document,"touchmove",this._onTouchMove),re(document,"pointermove",this._onTouchMove),re(document,"dragover",Ct),re(document,"mousemove",Ct),re(document,"touchmove",Ct)},_offUpEvents:function(){var e=this.el.ownerDocument;re(e,"mouseup",this._onDrop),re(e,"touchend",this._onDrop),re(e,"pointerup",this._onDrop),re(e,"pointercancel",this._onDrop),re(e,"touchcancel",this._onDrop),re(document,"selectstart",this)},_onDrop:function(e){var t=this.el,n=this.options;We=Se(Xe),$e=Se(Xe,n.draggable),Re("drop",this,{evt:e}),Ye=Xe&&Xe.parentNode,We=Se(Xe),$e=Se(Xe,n.draggable),xt.eventCanceled||(st=!1,pt=!1,dt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),kt(this.cloneId),kt(this._dragStartId),this.nativeDraggable&&(re(document,"drop",this),re(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),te&&he(document.body,"user-select",""),he(Xe,"transform",""),e&&(ot&&(e.cancelable&&e.preventDefault(),!n.dropBubble&&e.stopPropagation()),Ve&&Ve.parentNode&&Ve.parentNode.removeChild(Ve),(Fe===Ye||Ke&&"clone"!==Ke.lastPutMode)&&Ue&&Ue.parentNode&&Ue.parentNode.removeChild(Ue),Xe&&(this.nativeDraggable&&re(Xe,"dragend",this),Ot(Xe),Xe.style["will-change"]="",ot&&!st&&pe(Xe,Ke?Ke.options.ghostClass:this.options.ghostClass,!1),pe(Xe,this.options.chosenClass,!1),je({sortable:this,name:"unchoose",toEl:Ye,newIndex:null,newDraggableIndex:null,originalEvent:e}),Fe!==Ye?(We>=0&&(je({rootEl:Ye,name:"add",toEl:Ye,fromEl:Fe,originalEvent:e}),je({sortable:this,name:"remove",toEl:Ye,originalEvent:e}),je({rootEl:Ye,name:"sort",toEl:Ye,fromEl:Fe,originalEvent:e}),je({sortable:this,name:"sort",toEl:Ye,originalEvent:e})),Ke&&Ke.save()):We!==ze&&We>=0&&(je({sortable:this,name:"update",toEl:Ye,originalEvent:e}),je({sortable:this,name:"sort",toEl:Ye,originalEvent:e})),xt.active&&(null!=We&&-1!==We||(We=ze,$e=Ge),je({sortable:this,name:"end",toEl:Ye,originalEvent:e}),this.save())))),this._nulling()},_nulling:function(){Re("nulling",this),Fe=Xe=Ye=Ve=Be=Ue=Le=He=Je=Ze=ot=We=$e=ze=Ge=it=at=Ke=qe=xt.dragged=xt.ghost=xt.clone=xt.active=null,vt.forEach(function(e){e.checked=!0}),vt.length=Qe=et=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":Xe&&(this._onDragOver(e),function(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move");e.cancelable&&e.preventDefault()}(e));break;case"selectstart":e.preventDefault()}},toArray:function(){for(var e,t=[],n=this.el.children,o=0,i=n.length,a=this.options;o<i;o++)ce(e=n[o],a.draggable,this.el,!1)&&t.push(e.getAttribute(a.dataIdAttr)||At(e));return t},sort:function(e,t){var n={},o=this.el;this.toArray().forEach(function(e,t){var i=o.children[t];ce(i,this.options.draggable,o,!1)&&(n[e]=i)},this),t&&this.captureAnimationState(),e.forEach(function(e){n[e]&&(o.removeChild(n[e]),o.appendChild(n[e]))}),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return ce(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var n=this.options;if(void 0===t)return n[e];var o=ke.modifyOption(this,e,t);n[e]=void 0!==o?o:t,"group"===e&&Dt(n)},destroy:function(){Re("destroy",this);var e=this.el;e[Oe]=null,re(e,"mousedown",this._onTapStart),re(e,"touchstart",this._onTapStart),re(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(re(e,"dragover",this),re(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(e){e.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),ut.splice(ut.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!He){if(Re("hideClone",this),xt.eventCanceled)return;he(Ue,"display","none"),this.options.removeCloneOnHide&&Ue.parentNode&&Ue.parentNode.removeChild(Ue),He=!0}},_showClone:function(e){if("clone"===e.lastPutMode){if(He){if(Re("showClone",this),xt.eventCanceled)return;Xe.parentNode!=Fe||this.options.group.revertClone?Be?Fe.insertBefore(Ue,Be):Fe.appendChild(Ue):Fe.insertBefore(Ue,Xe),this.options.group.revertClone&&this.animate(Xe,Ue),he(Ue,"display",""),He=!1}}else this._hideClone()}},gt&&ae(document,"touchmove",function(e){(xt.active||st)&&e.cancelable&&e.preventDefault()}),xt.utils={on:ae,off:re,css:he,find:ve,is:function(e,t){return!!ce(e,t,e,!1)},extend:function(e,t){if(e&&t)for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},throttle:Ce,closest:ce,toggleClass:pe,clone:xe,index:Se,nextTick:It,cancelNextTick:kt,detectDirection:St,getChild:ye,expando:Oe},xt.get=function(e){return e[Oe]},xt.mount=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];t[0].constructor===Array&&(t=t[0]),t.forEach(function(e){if(!e.prototype||!e.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(e));e.utils&&(xt.utils=W(W({},xt.utils),e.utils)),ke.mount(e)})},xt.create=function(e,t){return new xt(e,t)},xt.version="1.15.6";var Nt,Rt,jt,Xt,Yt,Vt,Ft=[],Bt=!1;function Lt(){Ft.forEach(function(e){clearInterval(e.pid)}),Ft=[]}function Ut(){clearInterval(Vt)}var Ht=Ce(function(e,t,n,o){if(t.scroll){var i,a=(e.touches?e.touches[0]:e).clientX,r=(e.touches?e.touches[0]:e).clientY,l=t.scrollSensitivity,s=t.scrollSpeed,c=ge(),u=!1;Rt!==n&&(Rt=n,Lt(),Nt=t.scroll,i=t.scrollFn,!0===Nt&&(Nt=_e(n,!0)));var d=0,p=Nt;do{var h=p,f=me(h),v=f.top,g=f.bottom,m=f.left,b=f.right,y=f.width,w=f.height,S=void 0,D=void 0,_=h.scrollWidth,E=h.scrollHeight,C=he(h),T=h.scrollLeft,x=h.scrollTop;h===c?(S=y<_&&("auto"===C.overflowX||"scroll"===C.overflowX||"visible"===C.overflowX),D=w<E&&("auto"===C.overflowY||"scroll"===C.overflowY||"visible"===C.overflowY)):(S=y<_&&("auto"===C.overflowX||"scroll"===C.overflowX),D=w<E&&("auto"===C.overflowY||"scroll"===C.overflowY));var M=S&&(Math.abs(b-a)<=l&&T+y<_)-(Math.abs(m-a)<=l&&!!T),O=D&&(Math.abs(g-r)<=l&&x+w<E)-(Math.abs(v-r)<=l&&!!x);if(!Ft[d])for(var P=0;P<=d;P++)Ft[P]||(Ft[P]={});Ft[d].vx==M&&Ft[d].vy==O&&Ft[d].el===h||(Ft[d].el=h,Ft[d].vx=M,Ft[d].vy=O,clearInterval(Ft[d].pid),0==M&&0==O||(u=!0,Ft[d].pid=setInterval(function(){o&&0===this.layer&&xt.active._onTouchMove(Yt);var t=Ft[this.layer].vy?Ft[this.layer].vy*s:0,n=Ft[this.layer].vx?Ft[this.layer].vx*s:0;"function"==typeof i&&"continue"!==i.call(xt.dragged.parentNode[Oe],n,t,e,Yt,Ft[this.layer].el)||Te(Ft[this.layer].el,n,t)}.bind({layer:d}),24))),d++}while(t.bubbleScroll&&p!==c&&(p=_e(p,!1)));Bt=u}},30),zt=function(e){var t=e.originalEvent,n=e.putSortable,o=e.dragEl,i=e.activeSortable,a=e.dispatchSortableEvent,r=e.hideGhostForTarget,l=e.unhideGhostForTarget;if(t){var s=n||i;r();var c=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,u=document.elementFromPoint(c.clientX,c.clientY);l(),s&&!s.el.contains(u)&&(a("spill"),this.onSpill({dragEl:o,putSortable:n}))}};function Wt(){}function Gt(){}Wt.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,n=e.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var o=ye(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(t,o):this.sortable.el.appendChild(t),this.sortable.animateAll(),n&&n.animateAll()},drop:zt},q(Wt,{pluginName:"revertOnSpill"}),Gt.prototype={onSpill:function(e){var t=e.dragEl,n=e.putSortable||this.sortable;n.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),n.animateAll()},drop:zt},q(Gt,{pluginName:"removeOnSpill"}),xt.mount(new function(){function e(){for(var e in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===e.charAt(0)&&"function"==typeof this[e]&&(this[e]=this[e].bind(this))}return e.prototype={dragStarted:function(e){var t=e.originalEvent;this.sortable.nativeDraggable?ae(document,"dragover",this._handleAutoScroll):this.options.supportPointer?ae(document,"pointermove",this._handleFallbackAutoScroll):t.touches?ae(document,"touchmove",this._handleFallbackAutoScroll):ae(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var t=e.originalEvent;this.options.dragOverBubble||t.rootEl||this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?re(document,"dragover",this._handleAutoScroll):(re(document,"pointermove",this._handleFallbackAutoScroll),re(document,"touchmove",this._handleFallbackAutoScroll),re(document,"mousemove",this._handleFallbackAutoScroll)),Ut(),Lt(),clearTimeout(ue),ue=void 0},nulling:function(){Yt=Rt=Nt=Bt=Vt=jt=Xt=null,Ft.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,t){var n=this,o=(e.touches?e.touches[0]:e).clientX,i=(e.touches?e.touches[0]:e).clientY,a=document.elementFromPoint(o,i);if(Yt=e,t||this.options.forceAutoScrollFallback||Q||Z||te){Ht(e,this.options,a,t);var r=_e(a,!0);!Bt||Vt&&o===jt&&i===Xt||(Vt&&Ut(),Vt=setInterval(function(){var a=_e(document.elementFromPoint(o,i),!0);a!==r&&(r=a,Lt()),Ht(e,n.options,a,t)},10),jt=o,Xt=i)}else{if(!this.options.bubbleScroll||_e(a,!0)===ge())return void Lt();Ht(e,this.options,_e(a,!1),!1)}}},q(e,{pluginName:"scroll",initializeByDefault:!0})}),xt.mount(Gt,Wt);const $t=e({__name:"Sortable",props:{options:{type:Object,default:null,required:!1},list:{type:[Array,Object],default:[],required:!0},itemKey:{type:[String,Function],default:"",required:!0},tag:{type:String,default:"div",required:!1}},emits:["choose","unchoose","start","end","add","update","sort","remove","filter","move","clone","change"],setup(e,{expose:f,emit:v}){const g=e,m=v,b=t(),y=n(!1),S=n(null),D=n(null),_=o(()=>"string"==typeof g.itemKey?e=>e[g.itemKey]:g.itemKey);return f({containerRef:S,sortable:D,isDragging:y}),i(S,e=>{e&&(D.value=new xt(e,{...g.options,onChoose:e=>m("choose",e),onUnchoose:e=>m("unchoose",e),onStart:e=>{y.value=!0,m("start",e)},onEnd:e=>{setTimeout(()=>{y.value=!1,m("end",e)})},onAdd:e=>m("add",e),onUpdate:e=>m("update",e),onSort:e=>m("sort",e),onRemove:e=>m("remove",e),onFilter:e=>m("filter",e),onMove:(e,t)=>"onMoveCapture"in b?b.onMoveCapture(e,t):m("move",e,t),onClone:e=>m("clone",e),onChange:e=>m("change",e)}))}),i(()=>g.options,e=>{if(e&&D?.value)for(const t in e)D.value.option(t,e[t])}),a(()=>{D.value&&(D.value.destroy(),S.value=null,D.value=null)}),(t,n)=>(l(),r(h(t.$props.tag),{ref_key:"containerRef",ref:S,class:w(t.$props.class)},{default:s(()=>[c(t.$slots,"header"),(l(!0),u(d,null,p(e.list,(e,n)=>c(t.$slots,"item",{key:_.value(e),element:e,index:n})),128)),c(t.$slots,"footer")]),_:3},8,["class"]))}}),qt={__name:"LayoutDesigner",props:{modelValue:{type:Object,default:()=>({sections:[]})}},emits:["update:modelValue","change"],setup(e,{expose:t,emit:a}){t();const r=e,l=a,s=n("mobile"),c=n(null),u=n([]),d=n([{id:"banner",name:"顶部横幅",description:"展示主要信息的横幅区域",icon:"Picture",type:"banner",available:!0},{id:"info",name:"基础信息",description:"群组名称、价格等基础信息",icon:"Document",type:"info",available:!0},{id:"content",name:"详细介绍",description:"富文本内容展示区域",icon:"Document",type:"content",available:!0},{id:"gallery",name:"图片展示",description:"多图片轮播展示",icon:"Picture",type:"gallery",available:!0},{id:"video",name:"视频播放",description:"视频内容播放区域",icon:"VideoPlay",type:"video",available:!0},{id:"members",name:"成员展示",description:"虚拟成员头像展示",icon:"User",type:"members",available:!0}]),p=o(()=>c.value?u.value.find(e=>e.id===c.value):null),h=e=>{const t={id:`${e.type}_${Date.now()}`,type:e.type,name:e.name,visible:!0,order:u.value.length+1,style:{backgroundColor:"#ffffff",padding:20,margin:0},config:f(e.type)};u.value.push(t),v(t.id),m()},f=e=>({banner:{height:200,showOverlay:!0},info:{showPrice:!0,showDescription:!0},content:{maxHeight:300},gallery:{columns:3,showThumbnails:!0},video:{autoplay:!1,controls:!0},members:{maxCount:12,showCount:!0}}[e]||{}),v=e=>{c.value=e},g=(e,t)=>{const n=u.value.find(t=>t.id===e);n&&(Object.assign(n,t),m())},m=()=>{const e={sections:u.value,viewMode:s.value};l("update:modelValue",e),l("change",e)};i(()=>r.modelValue,e=>{e&&e.sections&&(u.value=[...e.sections])},{immediate:!0,deep:!0});const b={props:r,emit:l,viewMode:s,activeSection:c,layoutSections:u,availableComponents:d,activeSectionData:p,handleDragStart:(e,t)=>{e.dataTransfer.setData("component",JSON.stringify(t))},handleDragOver:e=>{e.preventDefault()},handleDrop:e=>{e.preventDefault();const t=JSON.parse(e.dataTransfer.getData("component"));h(t)},addSection:h,getDefaultConfig:f,selectSection:v,updateSection:g,updateActiveSection:e=>{c.value&&g(c.value,e)},updateSectionStyle:(e,t)=>{if(c.value){const n=u.value.find(e=>e.id===c.value);n&&(n.style[e]=t,m())}},removeSection:e=>{const t=u.value.findIndex(t=>t.id===e);t>-1&&(u.value.splice(t,1),c.value===e&&(c.value=null),m())},handleSectionChange:()=>{m()},resetLayout:()=>{u.value=[],c.value=null,m(),U.success("布局已重置")},previewLayout:()=>{l("preview",{sections:u.value,viewMode:s.value})},getSectionIcon:e=>({banner:"Picture",info:"Document",content:"Document",gallery:"Picture",video:"VideoPlay",members:"User"}[e]||"Document"),getSectionComponent:e=>`Section${e.charAt(0).toUpperCase()+e.slice(1)}Preview`,getSectionPropertyComponent:e=>`Section${e.charAt(0).toUpperCase()+e.slice(1)}Properties`,updateModelValue:m,ref:n,reactive:y,computed:o,watch:i,get ElMessage(){return U},get Grid(){return L},get RefreshRight(){return B},get View(){return F},get Delete(){return V},get InfoFilled(){return Y},get Picture(){return X},get Document(){return j},get VideoPlay(){return R},get User(){return N},get Iphone(){return k},get Monitor(){return I},get Sortable(){return $t}};return Object.defineProperty(b,"__isScriptSetup",{enumerable:!1,value:!0}),b}},Kt={class:"layout-designer"},Jt={class:"designer-header"},Zt={class:"header-title"},Qt={class:"header-actions"},en={class:"layout-area"},tn={class:"component-library"},nn={class:"component-list"},on=["onDragstart"],an={class:"component-icon"},rn={class:"component-info"},ln={class:"component-name"},sn={class:"component-desc"},cn={class:"design-area"},un={class:"design-canvas"},dn={class:"canvas-header"},pn={class:"canvas-tools"},hn=["onClick"],fn={class:"section-header"},vn={class:"section-info"},gn={class:"section-name"},mn={class:"section-controls"},bn={class:"section-content"},yn={key:0,class:"empty-canvas"},wn={class:"property-panel"},Sn={key:0,class:"property-content"},Dn={key:1,class:"no-selection"};const _n=H(qt,[["render",function(e,t,n,o,i,a){const c=S,y=D,I=E,k=C,N=x,R=T,j=M,X=O,Y=P,V=A;return l(),u("div",Kt,[f("div",Jt,[f("div",Zt,[v(c,null,{default:s(()=>[v(o.Grid)]),_:1}),t[14]||(t[14]=f("span",null,"落地页布局设计",-1))]),f("div",Qt,[v(y,{onClick:o.resetLayout,size:"small",type:"warning",plain:""},{default:s(()=>[v(c,null,{default:s(()=>[v(o.RefreshRight)]),_:1}),t[15]||(t[15]=g(" 重置布局 ",-1))]),_:1,__:[15]}),v(y,{onClick:o.previewLayout,size:"small",type:"primary",plain:""},{default:s(()=>[v(c,null,{default:s(()=>[v(o.View)]),_:1}),t[16]||(t[16]=g(" 预览效果 ",-1))]),_:1,__:[16]})])]),f("div",en,[f("div",tn,[t[17]||(t[17]=f("div",{class:"library-title"},"可用组件",-1)),f("div",nn,[(l(!0),u(d,null,p(o.availableComponents,e=>(l(),u("div",{key:e.id,class:w(["component-item",{disabled:!e.available}]),draggable:"true",onDragstart:t=>o.handleDragStart(t,e)},[f("div",an,[v(c,null,{default:s(()=>[(l(),r(h(e.icon)))]),_:2},1024)]),f("div",rn,[f("div",ln,_(e.name),1),f("div",sn,_(e.description),1)])],42,on))),128))])]),f("div",cn,[f("div",un,[f("div",dn,[t[18]||(t[18]=f("span",{class:"canvas-title"},"落地页预览",-1)),f("div",pn,[v(I,{size:"small"},{default:s(()=>[v(y,{type:"mobile"===o.viewMode?"primary":"",onClick:t[0]||(t[0]=e=>o.viewMode="mobile")},{default:s(()=>[v(c,null,{default:s(()=>[v(o.Iphone)]),_:1})]),_:1},8,["type"]),v(y,{type:"tablet"===o.viewMode?"primary":"",onClick:t[1]||(t[1]=e=>o.viewMode="tablet")},{default:s(()=>[v(c,null,{default:s(()=>[v(o.Monitor)]),_:1})]),_:1},8,["type"]),v(y,{type:"desktop"===o.viewMode?"primary":"",onClick:t[2]||(t[2]=e=>o.viewMode="desktop")},{default:s(()=>[v(c,null,{default:s(()=>[v(o.Monitor)]),_:1})]),_:1},8,["type"])]),_:1})])]),f("div",{class:w(["canvas-content",`view-${o.viewMode}`]),onDrop:o.handleDrop,onDragover:o.handleDragOver},[v(o.Sortable,{modelValue:o.layoutSections,"onUpdate:modelValue":t[3]||(t[3]=e=>o.layoutSections=e),options:{group:"layout",animation:200,ghostClass:"ghost"},class:"section-list",onChange:o.handleSectionChange},{item:s(({element:e,index:t})=>[f("div",{class:w(["layout-section",{active:o.activeSection===e.id,hidden:!e.visible}]),onClick:t=>o.selectSection(e.id)},[f("div",fn,[f("div",vn,[v(c,null,{default:s(()=>[(l(),r(h(o.getSectionIcon(e.type))))]),_:2},1024),f("span",gn,_(e.name),1)]),f("div",mn,[v(k,{modelValue:e.visible,"onUpdate:modelValue":t=>e.visible=t,size:"small",onChange:t=>o.updateSection(e.id,{visible:e.visible})},null,8,["modelValue","onUpdate:modelValue","onChange"]),v(y,{onClick:b(t=>o.removeSection(e.id),["stop"]),size:"small",type:"danger",text:""},{default:s(()=>[v(c,null,{default:s(()=>[v(o.Delete)]),_:1})]),_:2},1032,["onClick"])])]),f("div",bn,[(l(),r(h(o.getSectionComponent(e.type)),{section:e,preview:!0,onUpdate:t=>o.updateSection(e.id,t)},null,40,["section","onUpdate"]))])],10,hn)]),_:1},8,["modelValue"]),0===o.layoutSections.length?(l(),u("div",yn,[v(c,{class:"empty-icon"},{default:s(()=>[v(o.Grid)]),_:1}),t[19]||(t[19]=f("div",{class:"empty-text"},"从左侧拖拽组件到此处开始设计",-1))])):m("",!0)],34)])]),f("div",wn,[t[22]||(t[22]=f("div",{class:"panel-title"},"属性设置",-1)),o.activeSection?(l(),u("div",Sn,[v(V,{model:o.activeSectionData,"label-width":"80px",size:"small"},{default:s(()=>[v(R,{label:"组件名称"},{default:s(()=>[v(N,{modelValue:o.activeSectionData.name,"onUpdate:modelValue":t[4]||(t[4]=e=>o.activeSectionData.name=e),onInput:t[5]||(t[5]=e=>o.updateActiveSection({name:o.activeSectionData.name}))},null,8,["modelValue"])]),_:1}),v(R,{label:"显示状态"},{default:s(()=>[v(k,{modelValue:o.activeSectionData.visible,"onUpdate:modelValue":t[6]||(t[6]=e=>o.activeSectionData.visible=e),onChange:t[7]||(t[7]=e=>o.updateActiveSection({visible:o.activeSectionData.visible}))},null,8,["modelValue"])]),_:1}),v(j,{"content-position":"left"},{default:s(()=>t[20]||(t[20]=[g("样式设置",-1)])),_:1,__:[20]}),v(R,{label:"背景色"},{default:s(()=>[v(X,{modelValue:o.activeSectionData.style.backgroundColor,"onUpdate:modelValue":t[8]||(t[8]=e=>o.activeSectionData.style.backgroundColor=e),onChange:t[9]||(t[9]=e=>o.updateSectionStyle("backgroundColor",o.activeSectionData.style.backgroundColor))},null,8,["modelValue"])]),_:1}),v(R,{label:"内边距"},{default:s(()=>[v(Y,{modelValue:o.activeSectionData.style.padding,"onUpdate:modelValue":t[10]||(t[10]=e=>o.activeSectionData.style.padding=e),min:0,max:100,onChange:t[11]||(t[11]=e=>o.updateSectionStyle("padding",o.activeSectionData.style.padding))},null,8,["modelValue"])]),_:1}),v(R,{label:"外边距"},{default:s(()=>[v(Y,{modelValue:o.activeSectionData.style.margin,"onUpdate:modelValue":t[12]||(t[12]=e=>o.activeSectionData.style.margin=e),min:0,max:100,onChange:t[13]||(t[13]=e=>o.updateSectionStyle("margin",o.activeSectionData.style.margin))},null,8,["modelValue"])]),_:1}),(l(),r(h(o.getSectionPropertyComponent(o.activeSectionData.type)),{section:o.activeSectionData,onUpdate:o.updateActiveSection},null,40,["section"]))]),_:1},8,["model"])])):(l(),u("div",Dn,[v(c,{class:"no-selection-icon"},{default:s(()=>[v(o.InfoFilled)]),_:1}),t[21]||(t[21]=f("div",{class:"no-selection-text"},"请选择一个组件进行编辑",-1))]))])])])}],["__scopeId","data-v-af762b5c"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/LayoutDesigner.vue"]]);export{_n as default};
