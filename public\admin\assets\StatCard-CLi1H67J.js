/* empty css             */import{l as e,m as t,q as a,C as s,G as r,A as l,z as i,D as n,r as d,c}from"./vue-vendor-BcnDv-68.js";import{ah as o,Z as u,s as p,bc as v,bd as f,be as m}from"./element-plus-C2UshkXo.js";import{_ as g}from"./index-eUTsTR3J.js";const _={__name:"StatCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},unit:{type:String,default:""},change:{type:Number,default:void 0},trend:{type:String,default:"flat",validator:e=>["up","down","flat"].includes(e)},icon:{type:String,required:!0},color:{type:String,default:"primary",validator:e=>["primary","success","warning","info","danger"].includes(e)},clickable:{type:Boolean,default:!1}},emits:["click"],setup(e,{expose:t,emit:a}){t();const s=e,r=a,l=d(null),i=c(()=>"number"==typeof s.value?s.value>=1e4?(s.value/1e4).toFixed(1)+"W":s.value>=1e3?(s.value/1e3).toFixed(1)+"K":s.value.toLocaleString():s.value),n=c(()=>({"trend--up":"up"===s.trend,"trend--down":"down"===s.trend,"trend--flat":"flat"===s.trend})),o=e=>{const t=l.value;if(!t)return;const a=e.currentTarget.getBoundingClientRect(),s=Math.max(a.width,a.height),r=e.clientX-a.left-s/2,i=e.clientY-a.top-s/2;t.style.width=t.style.height=s+"px",t.style.left=r+"px",t.style.top=i+"px",t.classList.add("ripple-active"),setTimeout(()=>{t.classList.remove("ripple-active")},600)},u={props:s,emit:r,rippleRef:l,formattedValue:i,trendClass:n,handleClick:e=>{s.clickable&&(o(e),r("click"))},createRipple:o,computed:c,ref:d,get CaretTop(){return m},get CaretBottom(){return f},get Minus(){return v}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}},b={class:"stat-card__content"},y={class:"stat-card__icon"},h={class:"icon-wrapper"},k={class:"stat-card__data"},C={class:"stat-value"},S={class:"value-number"},x={key:0,class:"value-unit"},w={class:"stat-title"},j={key:0,class:"stat-card__trend"},q={class:"trend-value"},B={class:"stat-card__ripple",ref:"rippleRef"};const M=g(_,[["render",function(d,c,v,f,m,g){const _=o;return t(),e("div",{class:p(["stat-card",[`stat-card--${v.color}`,{"stat-card--clickable":v.clickable}]]),onClick:f.handleClick},[c[1]||(c[1]=a("div",{class:"stat-card__bg"},[a("div",{class:"bg-pattern"}),a("div",{class:"bg-gradient"})],-1)),a("div",b,[a("div",y,[a("div",h,[r(_,null,{default:l(()=>[(t(),i(n(v.icon)))]),_:1})])]),a("div",k,[a("div",C,[a("span",S,u(f.formattedValue),1),v.unit?(t(),e("span",x,u(v.unit),1)):s("",!0)]),a("div",w,u(v.title),1)]),void 0!==v.change?(t(),e("div",j,[a("div",{class:p(["trend-indicator",f.trendClass])},[r(_,{class:"trend-icon"},{default:l(()=>["up"===v.trend?(t(),i(f.CaretTop,{key:0})):s("",!0),"down"===v.trend?(t(),i(f.CaretBottom,{key:1})):s("",!0),"flat"===v.trend?(t(),i(f.Minus,{key:2})):s("",!0)]),_:1}),a("span",q,u(Math.abs(v.change))+"%",1)],2),c[0]||(c[0]=a("div",{class:"trend-label"},"较昨日",-1))])):s("",!0)]),c[2]||(c[2]=a("div",{class:"stat-card__hover-effect"},null,-1)),a("div",B,null,512)],2)}],["__scopeId","data-v-b5d816cd"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/dashboard/StatCard.vue"]]);export{M as S};
