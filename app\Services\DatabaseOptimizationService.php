<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

/**
 * 数据库优化服务
 * 提供数据库优化、清理和维护功能
 */
class DatabaseOptimizationService
{
    /**
     * 运行所有优化
     *
     * @return array 优化结果
     */
    public function runAllOptimizations()
    {
        $results = [];
        
        try {
            $results['analyze_tables'] = $this->analyzeTables();
            $results['optimize_tables'] = $this->optimizeTables();
            $results['cleanup_expired_data'] = $this->cleanupExpiredData();
            $results['vacuum_tables'] = $this->vacuumTables();
            
            Log::info('数据库优化完成', $results);
        } catch (\Exception $e) {
            Log::error('数据库优化失败', ['error' => $e->getMessage()]);
            $results['error'] = $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * 分析表
     *
     * @return array 分析结果
     */
    public function analyzeTables()
    {
        $results = [];
        $tables = $this->getTables();
        
        foreach ($tables as $table) {
            try {
                app(SecureDatabaseService::class)->analyzeTable($table);
                $results[$table] = 'success';
            } catch (\Exception $e) {
                $results[$table] = 'error: ' . $e->getMessage();
            }
        }
        
        return $results;
    }
    
    /**
     * 优化表
     *
     * @return array 优化结果
     */
    public function optimizeTables()
    {
        $results = [];
        $tables = $this->getTables();
        
        foreach ($tables as $table) {
            try {
                app(SecureDatabaseService::class)->optimizeTable($table);
                $results[$table] = 'success';
            } catch (\Exception $e) {
                $results[$table] = 'error: ' . $e->getMessage();
            }
        }
        
        return $results;
    }
    
    /**
     * 清理过期数据
     *
     * @return array 清理结果
     */
    public function cleanupExpiredData()
    {
        $results = [];
        
        // 清理过期短链接
        if (Schema::hasTable('short_links')) {
            $count = DB::table('short_links')
                ->where('expires_at', '<', now())
                ->where('status', 'active')
                ->update(['status' => 'expired']);
            
            $results['short_links'] = "{$count} 条记录已更新";
        }
        
        // 清理过期访问日志（保留90天）
        if (Schema::hasTable('link_access_logs')) {
            $count = DB::table('link_access_logs')
                ->where('created_at', '<', now()->subDays(90))
                ->delete();
            
            $results['link_access_logs'] = "{$count} 条记录已删除";
        }
        
        // 清理过期操作日志（保留30天）
        if (Schema::hasTable('operation_logs')) {
            $count = DB::table('operation_logs')
                ->where('created_at', '<', now()->subDays(30))
                ->delete();
            
            $results['operation_logs'] = "{$count} 条记录已删除";
        }
        
        // 清理过期性能日志（保留7天）
        if (Schema::hasTable('performance_logs')) {
            $count = DB::table('performance_logs')
                ->where('created_at', '<', now()->subDays(7))
                ->delete();
            
            $results['performance_logs'] = "{$count} 条记录已删除";
        }
        
        return $results;
    }
    
    /**
     * 整理表空间
     *
     * @return array 整理结果
     */
    public function vacuumTables()
    {
        $results = [];
        
        // 检查数据库类型
        $driver = DB::connection()->getDriverName();
        
        if ($driver === 'mysql') {
            // MySQL不支持VACUUM，使用OPTIMIZE TABLE代替
            return $this->optimizeTables();
        } elseif ($driver === 'pgsql') {
            // PostgreSQL支持VACUUM
            try {
                DB::statement('VACUUM ANALYZE');
                $results['vacuum'] = 'success';
            } catch (\Exception $e) {
                $results['vacuum'] = 'error: ' . $e->getMessage();
            }
        } elseif ($driver === 'sqlite') {
            // SQLite支持VACUUM
            try {
                DB::statement('VACUUM');
                $results['vacuum'] = 'success';
            } catch (\Exception $e) {
                $results['vacuum'] = 'error: ' . $e->getMessage();
            }
        } else {
            $results['vacuum'] = 'not supported for ' . $driver;
        }
        
        return $results;
    }
    
    /**
     * 获取所有表名
     *
     * @return array 表名列表
     */
    protected function getTables()
    {
        $tables = [];
        
        // 获取数据库类型
        $driver = DB::connection()->getDriverName();
        
        if ($driver === 'mysql') {
            // MySQL
            $dbTables = DB::select('SHOW TABLES');
            $column = 'Tables_in_' . DB::connection()->getDatabaseName();
            
            foreach ($dbTables as $table) {
                $tables[] = $table->$column;
            }
        } elseif ($driver === 'pgsql') {
            // PostgreSQL
            $dbTables = DB::select("SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname = 'public'");
            
            foreach ($dbTables as $table) {
                $tables[] = $table->tablename;
            }
        } elseif ($driver === 'sqlite') {
            // SQLite
            $dbTables = DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
            
            foreach ($dbTables as $table) {
                $tables[] = $table->name;
            }
        }
        
        return $tables;
    }
    
    /**
     * 获取表统计信息
     *
     * @return array 表统计信息
     */
    public function getTableStatistics()
    {
        $statistics = [];
        $tables = $this->getTables();
        
        foreach ($tables as $table) {
            try {
                // 获取记录数
                $count = DB::table($table)->count();
                
                // 获取表大小（仅MySQL）
                $size = 0;
                if (DB::connection()->getDriverName() === 'mysql') {
                    $result = DB::select("
                        SELECT 
                            data_length + index_length AS size,
                            table_rows,
                            data_free
                        FROM information_schema.TABLES
                        WHERE table_schema = ? AND table_name = ?
                    ", [DB::connection()->getDatabaseName(), $table]);
                    
                    if (!empty($result)) {
                        $size = $result[0]->size;
                        $dataFree = $result[0]->data_free;
                    }
                }
                
                $statistics[$table] = [
                    'records' => $count,
                    'size' => $size,
                    'size_formatted' => $this->formatBytes($size),
                    'data_free' => $dataFree ?? 0,
                    'data_free_formatted' => $this->formatBytes($dataFree ?? 0),
                    'fragmentation' => $size > 0 ? round(($dataFree ?? 0) / $size * 100, 2) : 0,
                ];
            } catch (\Exception $e) {
                $statistics[$table] = [
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $statistics;
    }
    
    /**
     * 格式化字节数
     *
     * @param int $bytes 字节数
     * @param int $precision 精度
     * @return string 格式化后的字符串
     */
    protected function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, $precision) . ' ' . $units[$pow];
    }
}