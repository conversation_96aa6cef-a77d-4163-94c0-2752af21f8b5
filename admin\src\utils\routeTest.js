// 路由测试工具 - 用于验证所有路由的可访问性
import router from '@/router/index-optimized.js'
import { sidebarMenuConfig, flattenMenu } from '@/components/layout/SidebarConfig.js'

/**
 * 测试所有路由的可访问性
 * @param {Object} options 测试选项
 * @returns {Promise<Object>} 测试结果
 */
export async function testAllRoutes(options = {}) {
  const {
    timeout = 5000,
    skipTestRoutes = true,
    verbose = false
  } = options

  const results = {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0,
    details: [],
    errors: [],
    summary: {}
  }

  // 获取所有路由
  const allRoutes = extractAllRoutes(router.getRoutes())
  
  // 过滤测试路由
  const routesToTest = skipTestRoutes 
    ? allRoutes.filter(route => !route.path.includes('/test'))
    : allRoutes

  results.total = routesToTest.length

  console.log(`🧪 开始测试 ${results.total} 个路由...`)

  for (const route of routesToTest) {
    try {
      const testResult = await testSingleRoute(route, timeout, verbose)
      results.details.push(testResult)

      if (testResult.status === 'passed') {
        results.passed++
      } else if (testResult.status === 'failed') {
        results.failed++
        results.errors.push({
          path: route.path,
          error: testResult.error
        })
      } else {
        results.skipped++
      }

      // 输出进度
      if (verbose) {
        const icon = testResult.status === 'passed' ? '✅' : 
                    testResult.status === 'failed' ? '❌' : '⏭️'
        console.log(`${icon} ${route.path} - ${testResult.message}`)
      }
    } catch (error) {
      results.failed++
      results.errors.push({
        path: route.path,
        error: error.message
      })
      
      if (verbose) {
        console.error(`❌ ${route.path} - 测试异常:`, error)
      }
    }
  }

  // 生成摘要
  results.summary = generateSummary(results)

  return results
}

/**
 * 测试单个路由
 */
async function testSingleRoute(route, timeout = 5000, verbose = false) {
  const result = {
    path: route.path,
    name: route.name,
    status: 'unknown',
    message: '',
    error: null,
    timestamp: new Date().toISOString(),
    meta: route.meta
  }

  try {
    // 检查路由是否存在
    if (!route.component && !route.redirect) {
      result.status = 'failed'
      result.error = '路由没有定义组件或重定向'
      result.message = '配置错误'
      return result
    }

    // 检查是否为重定向路由
    if (route.redirect) {
      result.status = 'passed'
      result.message = `重定向到: ${route.redirect}`
      return result
    }

    // 检查组件是否可以加载
    if (typeof route.component === 'function') {
      const componentPromise = route.component()
      const component = await Promise.race([
        componentPromise,
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('组件加载超时')), timeout)
        )
      ])

      if (component) {
        result.status = 'passed'
        result.message = '组件加载成功'
      } else {
        result.status = 'failed'
        result.error = '组件加载失败'
        result.message = '组件不存在'
      }
    } else {
      result.status = 'passed'
      result.message = '静态组件'
    }
  } catch (error) {
    result.status = 'failed'
    result.error = error.message
    result.message = '组件加载异常'
  }

  return result
}

/**
 * 提取所有路由
 */
function extractAllRoutes(routes, basePath = '') {
  const allRoutes = []

  routes.forEach(route => {
    const fullPath = basePath ? `${basePath}/${route.path}`.replace(/\/+/g, '/') : route.path

    // 添加当前路由
    allRoutes.push({
      ...route,
      path: fullPath
    })

    // 递归处理子路由
    if (route.children && route.children.length > 0) {
      const childRoutes = extractAllRoutes(route.children, fullPath)
      allRoutes.push(...childRoutes)
    }
  })

  return allRoutes
}

/**
 * 生成测试摘要
 */
function generateSummary(results) {
  const { total, passed, failed, skipped } = results
  const passRate = total > 0 ? ((passed / total) * 100).toFixed(2) : 0

  return {
    passRate: `${passRate}%`,
    status: failed === 0 ? 'SUCCESS' : 'FAILED',
    message: `总计: ${total}, 通过: ${passed}, 失败: ${failed}, 跳过: ${skipped}`,
    recommendations: generateRecommendations(results)
  }
}

/**
 * 生成修复建议
 */
function generateRecommendations(results) {
  const recommendations = []

  // 检查失败的路由
  if (results.failed > 0) {
    recommendations.push({
      type: 'error',
      title: '修复失败的路由',
      items: results.errors.map(error => `${error.path}: ${error.error}`)
    })
  }

  // 检查重复路由
  const pathCounts = {}
  results.details.forEach(detail => {
    pathCounts[detail.path] = (pathCounts[detail.path] || 0) + 1
  })
  
  const duplicates = Object.entries(pathCounts)
    .filter(([path, count]) => count > 1)
    .map(([path]) => path)

  if (duplicates.length > 0) {
    recommendations.push({
      type: 'warning',
      title: '发现重复路由',
      items: duplicates
    })
  }

  // 检查孤儿路由（在菜单中不存在的路由）
  const menuPaths = flattenMenu(sidebarMenuConfig).map(item => item.path)
  const routePaths = results.details.map(detail => detail.path)
  const orphanRoutes = routePaths.filter(path => 
    !menuPaths.includes(path) && 
    !path.includes('/test') && 
    !path.includes('/login') && 
    path !== '/'
  )

  if (orphanRoutes.length > 0) {
    recommendations.push({
      type: 'info',
      title: '孤儿路由（不在菜单中）',
      items: orphanRoutes
    })
  }

  return recommendations
}

/**
 * 测试菜单和路由的一致性
 */
export function testMenuRouteConsistency() {
  const results = {
    inconsistencies: [],
    missing: [],
    extra: [],
    summary: ''
  }

  // 获取菜单中的所有路径
  const menuPaths = flattenMenu(sidebarMenuConfig)
    .map(item => item.path)
    .filter(path => path) // 过滤空路径

  // 获取路由中的所有路径
  const routePaths = extractAllRoutes(router.getRoutes())
    .map(route => route.path)
    .filter(path => 
      !path.includes('/test') && 
      !path.includes('/login') && 
      path !== '/'
    )

  // 检查菜单中存在但路由中不存在的路径
  results.missing = menuPaths.filter(path => !routePaths.includes(path))

  // 检查路由中存在但菜单中不存在的路径
  results.extra = routePaths.filter(path => !menuPaths.includes(path))

  // 生成摘要
  const totalIssues = results.missing.length + results.extra.length
  results.summary = totalIssues === 0 
    ? '✅ 菜单和路由完全一致'
    : `❌ 发现 ${totalIssues} 个一致性问题`

  return results
}

/**
 * 输出格式化的测试报告
 */
export function formatTestReport(results) {
  const { total, passed, failed, skipped, summary, errors } = results

  let report = `
📊 路由测试报告
================

总体统计:
- 总路由数: ${total}
- 通过数: ${passed} ✅
- 失败数: ${failed} ❌
- 跳过数: ${skipped} ⏭️
- 通过率: ${summary.passRate}

整体状态: ${summary.status === 'SUCCESS' ? '✅ 成功' : '❌ 失败'}
${summary.message}
`

  if (errors.length > 0) {
    report += `\n\n❌ 失败详情:\n`
    errors.forEach(error => {
      report += `- ${error.path}: ${error.error}\n`
    })
  }

  if (summary.recommendations && summary.recommendations.length > 0) {
    report += `\n\n💡 修复建议:\n`
    summary.recommendations.forEach(rec => {
      const icon = rec.type === 'error' ? '❌' : rec.type === 'warning' ? '⚠️' : 'ℹ️'
      report += `\n${icon} ${rec.title}:\n`
      rec.items.forEach(item => {
        report += `  - ${item}\n`
      })
    })
  }

  return report
}

/**
 * 一键测试所有功能
 */
export async function runCompleteTest(options = {}) {
  console.log('🚀 开始完整的路由测试...\n')

  // 1. 测试路由可访问性
  console.log('1️⃣ 测试路由可访问性...')
  const routeTestResults = await testAllRoutes({
    verbose: options.verbose || false,
    timeout: options.timeout || 5000
  })

  // 2. 测试菜单一致性
  console.log('\n2️⃣ 测试菜单一致性...')
  const consistencyResults = testMenuRouteConsistency()

  // 3. 输出报告
  console.log('\n' + formatTestReport(routeTestResults))
  
  if (consistencyResults.missing.length > 0 || consistencyResults.extra.length > 0) {
    console.log(`\n📋 菜单一致性报告:`)
    console.log(consistencyResults.summary)
    
    if (consistencyResults.missing.length > 0) {
      console.log(`\n❌ 菜单中存在但路由中缺失:`)
      consistencyResults.missing.forEach(path => console.log(`  - ${path}`))
    }
    
    if (consistencyResults.extra.length > 0) {
      console.log(`\n⚠️ 路由中存在但菜单中缺失:`)
      consistencyResults.extra.forEach(path => console.log(`  - ${path}`))
    }
  }

  return {
    routeTest: routeTestResults,
    consistency: consistencyResults
  }
}

export default {
  testAllRoutes,
  testMenuRouteConsistency,
  formatTestReport,
  runCompleteTest
}