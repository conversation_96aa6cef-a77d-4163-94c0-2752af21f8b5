/* empty css             *//* empty css                   *//* empty css                   *//* empty css                 *//* empty css                        *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css               *//* empty css               *//* empty css                *//* empty css                     *//* empty css                  *//* empty css                  */import{l as e,G as a,A as l,r as t,M as o,a3 as i,o as r,m as n,K as d,W as s,E as u,B as m,z as p,H as c,q as f}from"./vue-vendor-BcnDv-68.js";import{g as _,b as g,c as y,d as v,u as b,a as h}from"./anti-block-CJ1NNk3N.js";import{P as k}from"./index-B0-VWyD5.js";import{_ as w}from"./index-eUTsTR3J.js";import{X as j,V,U as C,W as P,ag as q,a2 as D,a3 as U,a5 as x,a6 as S,a4 as A,a1 as B,Y as L,ai as Q,aj as F,ak as T,Z as N,a7 as I,_ as M,aL as R,aq as E}from"./element-plus-C2UshkXo.js";import"./index-D4AyIzGN.js";import"./utils-SdQ7DxjY.js";/* empty css                      */import"./echarts-D6CUuNS9.js";const K={class:"app-container"},O={class:"dialog-footer"};const W=w({__name:"DomainList",setup(e,{expose:a}){a();const l=t(!0),n=t([]),d=t(!0),s=t(!0),u=t(0),m=t([]),p=t(null),c=o({queryParams:{page:1,limit:10,domain:void 0,domain_type:void 0,status:void 0},dialog:{visible:!1,title:""},form:{},rules:{domain:[{required:!0,message:"域名不能为空",trigger:"blur"}],domain_type:[{required:!0,message:"域名类型不能为空",trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}],priority:[{required:!0,message:"优先级不能为空",trigger:"blur"}]}}),{queryParams:f,dialog:w,form:V,rules:C}=i(c);async function P(){l.value=!0;try{const e=await _(f.value);m.value=e.data.data,u.value=e.data.total}catch(e){j.error("获取域名列表失败")}finally{l.value=!1}}function D(){f.value.page=1,P()}function U(){V.value={domain:void 0,domain_type:"redirect",status:"normal",priority:10,remark:void 0},p.value&&p.value.resetFields()}r(()=>{P()});const x={loading:l,selectedDomains:n,single:d,multiple:s,total:u,domainList:m,domainFormRef:p,data:c,queryParams:f,dialog:w,form:V,rules:C,getDomainTypeName:e=>({redirect:"短链接",landing:"中转页",api:"API服务"}[e]||"未知"),getDomainTypeColor:e=>({redirect:"success",landing:"primary",api:"warning"}[e]||"info"),getStatusName:e=>({normal:"正常",error:"异常",banned:"封禁",stopped:"停用"}[e]||"未知"),getStatusColor:e=>({normal:"success",error:"danger",banned:"danger",stopped:"info"}[e]||"warning"),getList:P,handleQuery:D,resetQuery:function(){f.value={page:1,limit:10,domain:void 0,domain_type:void 0,status:void 0},D()},handleSelectionChange:function(e){n.value=e,d.value=1!==e.length,s.value=!e.length},resetForm:U,handleAdd:function(){U(),w.value.visible=!0,w.value.title="添加域名"},handleUpdate:function(e){U();const a=e||n.value[0];V.value={...a},w.value.visible=!0,w.value.title="修改域名"},handleDelete:async function(e){const a=e?[e.id]:n.value.map(e=>e.id);await q.confirm("是否确认删除选中的域名?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});try{await g({ids:a}),P(),j.success("删除成功")}catch(l){j.error("删除失败")}},handleSingleCheck:async function(e){l.value=!0;try{await y({ids:[e.id]}),j.success("检测任务已提交"),P()}catch(a){j.error("检测失败")}finally{l.value=!1}},handleBatchCheck:async function(){const e=n.value.map(e=>e.id);l.value=!0;try{await y({ids:e}),j.success("批量检测任务已提交"),P()}catch(a){j.error("批量检测失败")}finally{l.value=!1}},cancel:function(){w.value.visible=!1,U()},submitForm:async function(){await p.value.validate();const e=!!V.value.id;try{e?(await b(V.value.id,V.value),j.success("修改成功")):(await h(V.value),j.success("新增成功")),w.value.visible=!1,P()}catch(a){j.error("操作失败")}},ref:t,reactive:o,onMounted:r,toRefs:i,get ElMessage(){return j},get ElMessageBox(){return q},get getDomains(){return _},get addDomain(){return h},get updateDomain(){return b},get deleteDomain(){return v},get checkDomains(){return y},get batchDeleteDomains(){return g},Pagination:k};return Object.defineProperty(x,"__isScriptSetup",{enumerable:!1,value:!0}),x}},[["render",function(t,o,i,r,_,g){const y=S,v=x,b=U,h=A,k=B,w=D,j=V,q=L,W=C,z=F,G=T,H=Q,X=R,Y=M,Z=E,J=P,$=I;return n(),e("div",K,[a(j,{class:"filter-card"},{default:l(()=>[a(w,{inline:!0,model:r.queryParams,onSubmit:d(r.handleQuery,["prevent"])},{default:l(()=>[a(b,{label:"域名类型"},{default:l(()=>[a(v,{modelValue:r.queryParams.domain_type,"onUpdate:modelValue":o[0]||(o[0]=e=>r.queryParams.domain_type=e),placeholder:"全部类型",clearable:""},{default:l(()=>[a(y,{label:"短链接域名",value:"redirect"}),a(y,{label:"中转页域名",value:"landing"}),a(y,{label:"API服务域名",value:"api"})]),_:1},8,["modelValue"])]),_:1}),a(b,{label:"域名状态"},{default:l(()=>[a(v,{modelValue:r.queryParams.status,"onUpdate:modelValue":o[1]||(o[1]=e=>r.queryParams.status=e),placeholder:"全部状态",clearable:""},{default:l(()=>[a(y,{label:"正常",value:"normal"}),a(y,{label:"异常",value:"error"}),a(y,{label:"封禁",value:"banned"}),a(y,{label:"停用",value:"stopped"})]),_:1},8,["modelValue"])]),_:1}),a(b,{label:"域名搜索"},{default:l(()=>[a(h,{modelValue:r.queryParams.domain,"onUpdate:modelValue":o[2]||(o[2]=e=>r.queryParams.domain=e),placeholder:"输入域名关键词",clearable:"",onKeyup:s(r.handleQuery,["enter"])},null,8,["modelValue"])]),_:1}),a(b,null,{default:l(()=>[a(k,{type:"primary",icon:"el-icon-search",onClick:r.handleQuery},{default:l(()=>o[12]||(o[12]=[u("查询",-1)])),_:1,__:[12]}),a(k,{icon:"el-icon-refresh",onClick:r.resetQuery},{default:l(()=>o[13]||(o[13]=[u("重置",-1)])),_:1,__:[13]})]),_:1})]),_:1},8,["model"])]),_:1}),a(W,{gutter:10,class:"mb8"},{default:l(()=>[a(q,{span:1.5},{default:l(()=>[a(k,{type:"primary",plain:"",icon:"el-icon-plus",onClick:r.handleAdd},{default:l(()=>o[14]||(o[14]=[u("新增",-1)])),_:1,__:[14]})]),_:1}),a(q,{span:1.5},{default:l(()=>[a(k,{type:"success",plain:"",icon:"el-icon-edit",disabled:r.single,onClick:o[3]||(o[3]=e=>r.handleUpdate(r.selectedDomains[0]))},{default:l(()=>o[15]||(o[15]=[u("修改",-1)])),_:1,__:[15]},8,["disabled"])]),_:1}),a(q,{span:1.5},{default:l(()=>[a(k,{type:"danger",plain:"",icon:"el-icon-delete",disabled:r.multiple,onClick:r.handleDelete},{default:l(()=>o[16]||(o[16]=[u("删除",-1)])),_:1,__:[16]},8,["disabled"])]),_:1}),a(q,{span:1.5},{default:l(()=>[a(k,{type:"warning",plain:"",icon:"el-icon-magic-stick",disabled:r.multiple,onClick:r.handleBatchCheck},{default:l(()=>o[17]||(o[17]=[u("批量检测",-1)])),_:1,__:[17]},8,["disabled"])]),_:1})]),_:1}),a(j,null,{default:l(()=>[m((n(),p(H,{data:r.domainList,onSelectionChange:r.handleSelectionChange},{default:l(()=>[a(z,{type:"selection",width:"55",align:"center"}),a(z,{label:"域名",prop:"domain",width:"250"}),a(z,{label:"类型",prop:"domain_type",width:"120"},{default:l(e=>[a(G,{type:r.getDomainTypeColor(e.row.domain_type)},{default:l(()=>[u(N(r.getDomainTypeName(e.row.domain_type)),1)]),_:2},1032,["type"])]),_:1}),a(z,{label:"状态",prop:"status",width:"100"},{default:l(e=>[a(G,{type:r.getStatusColor(e.row.status)},{default:l(()=>[u(N(r.getStatusName(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),a(z,{label:"优先级",prop:"priority",width:"100"}),a(z,{label:"备注",prop:"remark","show-overflow-tooltip":""}),a(z,{label:"创建时间",prop:"created_at",width:"160"}),a(z,{label:"操作",width:"180","class-name":"small-padding fixed-width"},{default:l(e=>[a(k,{link:"",type:"primary",icon:"el-icon-edit",onClick:a=>r.handleUpdate(e.row)},{default:l(()=>o[18]||(o[18]=[u("修改",-1)])),_:2,__:[18]},1032,["onClick"]),a(k,{link:"",type:"danger",icon:"el-icon-delete",onClick:a=>r.handleDelete(e.row)},{default:l(()=>o[19]||(o[19]=[u("删除",-1)])),_:2,__:[19]},1032,["onClick"]),a(k,{link:"",type:"warning",icon:"el-icon-magic-stick",onClick:a=>r.handleSingleCheck(e.row)},{default:l(()=>o[20]||(o[20]=[u("检测",-1)])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[$,r.loading]]),m(a(r.Pagination,{total:r.total,page:r.queryParams.page,"onUpdate:page":o[4]||(o[4]=e=>r.queryParams.page=e),limit:r.queryParams.limit,"onUpdate:limit":o[5]||(o[5]=e=>r.queryParams.limit=e),onPagination:r.getList},null,8,["total","page","limit"]),[[c,r.total>0]])]),_:1}),a(J,{title:r.dialog.title,modelValue:r.dialog.visible,"onUpdate:modelValue":o[11]||(o[11]=e=>r.dialog.visible=e),width:"600px","append-to-body":""},{footer:l(()=>[f("div",O,[a(k,{onClick:r.cancel},{default:l(()=>o[23]||(o[23]=[u("取 消",-1)])),_:1,__:[23]}),a(k,{type:"primary",onClick:r.submitForm},{default:l(()=>o[24]||(o[24]=[u("确 定",-1)])),_:1,__:[24]})])]),default:l(()=>[a(w,{ref:"domainFormRef",model:r.form,rules:r.rules,"label-width":"80px"},{default:l(()=>[a(b,{label:"域名",prop:"domain"},{default:l(()=>[a(h,{modelValue:r.form.domain,"onUpdate:modelValue":o[6]||(o[6]=e=>r.form.domain=e),placeholder:"请输入域名，如 domain.com"},null,8,["modelValue"])]),_:1}),a(b,{label:"类型",prop:"domain_type"},{default:l(()=>[a(v,{modelValue:r.form.domain_type,"onUpdate:modelValue":o[7]||(o[7]=e=>r.form.domain_type=e),placeholder:"请选择域名类型"},{default:l(()=>[a(y,{label:"短链接域名",value:"redirect"}),a(y,{label:"中转页域名",value:"landing"}),a(y,{label:"API服务域名",value:"api"})]),_:1},8,["modelValue"])]),_:1}),a(b,{label:"状态",prop:"status"},{default:l(()=>[a(Y,{modelValue:r.form.status,"onUpdate:modelValue":o[8]||(o[8]=e=>r.form.status=e)},{default:l(()=>[a(X,{label:"normal"},{default:l(()=>o[21]||(o[21]=[u("正常",-1)])),_:1,__:[21]}),a(X,{label:"stopped"},{default:l(()=>o[22]||(o[22]=[u("停用",-1)])),_:1,__:[22]})]),_:1},8,["modelValue"])]),_:1}),a(b,{label:"优先级",prop:"priority"},{default:l(()=>[a(Z,{modelValue:r.form.priority,"onUpdate:modelValue":o[9]||(o[9]=e=>r.form.priority=e),min:0,max:100},null,8,["modelValue"])]),_:1}),a(b,{label:"备注",prop:"remark"},{default:l(()=>[a(h,{modelValue:r.form.remark,"onUpdate:modelValue":o[10]||(o[10]=e=>r.form.remark=e),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}],["__scopeId","data-v-319badbb"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/anti-block/DomainList.vue"]]);export{W as default};
