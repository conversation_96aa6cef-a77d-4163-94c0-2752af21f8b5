# 代码审查检查清单

## 📋 概览

**版本**: v1.0  
**更新时间**: 2025-08-08  
**适用范围**: 晨鑫流量变现系统 项目所有代码提交  

---

## 🔍 代码审查流程

### **1. 审查前准备**
- [ ] 确认PR标题清晰描述了变更内容
- [ ] 检查PR描述包含了变更原因和影响范围
- [ ] 验证所有CI/CD检查通过
- [ ] 确认没有合并冲突

### **2. 代码质量审查**

#### **📁 文件和结构**
- [ ] **文件命名规范**: 组件使用PascalCase，工具文件使用camelCase
- [ ] **目录结构**: 文件放置在正确的目录中
- [ ] **文件大小**: 单个文件不超过500行（特殊情况除外）
- [ ] **导入顺序**: 第三方库 → 本地组件 → 工具函数

#### **🎨 代码风格**
- [ ] **缩进一致**: 使用2个空格缩进
- [ ] **命名规范**: 变量和函数使用camelCase
- [ ] **常量命名**: 使用UPPER_SNAKE_CASE
- [ ] **注释质量**: 复杂逻辑有清晰注释

#### **🔧 Vue组件规范**
- [ ] **组件结构**: template → script → style 顺序
- [ ] **Props定义**: 包含类型和默认值
- [ ] **事件命名**: 使用kebab-case (update:model-value)
- [ ] **样式作用域**: 使用scoped或module

### **3. 功能审查**

#### **⚡ 性能考虑**
- [ ] **避免不必要的重渲染**: 使用computed和watch合理
- [ ] **列表渲染**: 使用key属性
- [ ] **图片优化**: 使用适当的格式和大小
- [ ] **懒加载**: 大组件使用动态导入

#### **🛡️ 安全性**
- [ ] **XSS防护**: 用户输入正确转义
- [ ] **CSRF保护**: API请求包含必要的token
- [ ] **权限检查**: 敏感操作有权限验证
- [ ] **数据验证**: 前后端都有数据验证

#### **♿ 可访问性**
- [ ] **语义化HTML**: 使用正确的HTML标签
- [ ] **键盘导航**: 支持Tab键导航
- [ ] **屏幕阅读器**: 重要元素有aria-label
- [ ] **颜色对比**: 确保足够的颜色对比度

### **4. 错误处理审查**

#### **🚨 异常处理**
- [ ] **API错误**: 有适当的错误处理和用户提示
- [ ] **边界情况**: 考虑了空数据、网络错误等情况
- [ ] **错误日志**: 重要错误有日志记录
- [ ] **用户体验**: 错误信息对用户友好

#### **🧪 测试覆盖**
- [ ] **单元测试**: 核心逻辑有单元测试
- [ ] **集成测试**: 重要功能有集成测试
- [ ] **边界测试**: 测试了边界条件
- [ ] **错误测试**: 测试了错误处理逻辑

---

## 🚫 代码清理审查

### **禁止提交的内容**

#### **🚫 调试文件**
- [ ] 没有*-test.html, *-debug.html等调试页面
- [ ] 没有*-fix-*.html等修复验证页面
- [ ] 没有Mock*调试*.html等API调试页面
- [ ] 没有admin-preview*.html等预览测试页面

#### **🚫 临时脚本**
- [ ] 没有debug-*.js, fix-*.js等调试脚本
- [ ] 没有quick-fix.*, temp-*.*等临时文件
- [ ] 没有*.bat, *.ps1等批处理脚本
- [ ] 没有*-deploy.sh, optimize-*.sh等部署脚本

#### **🚫 文档报告**
- [ ] 没有*-report.md, *-analysis.md等报告文件
- [ ] 没有*-修复报告.md, *-检测报告.md等中文报告
- [ ] 没有功能*报告.md, 系统*报告.md等功能报告
- [ ] 没有DEPLOYMENT_*.md等临时文档

#### **🚫 备份文件**
- [ ] 没有*.backup, *.bak, *.old等备份文件
- [ ] 没有*-backup.*, *-old.*等备份文件
- [ ] 没有*.vue.backup等组件备份

#### **🚫 调试代码**
- [ ] 没有console.log()调试输出（保留console.warn/error）
- [ ] 没有debugger语句
- [ ] 没有TODO注释（除非有对应的issue）
- [ ] 没有注释掉的代码块

---

## ✅ 代码质量标准

### **📝 注释标准**

#### **函数注释**
```javascript
/**
 * 上传文件到服务器
 * @param {File} file - 要上传的文件
 * @param {Object} options - 上传选项
 * @param {string} options.url - 上传地址
 * @param {Function} options.onProgress - 进度回调
 * @returns {Promise<Object>} 上传结果
 * @throws {Error} 当文件格式不支持时抛出错误
 */
```

#### **组件注释**
```vue
<script setup>
/**
 * 用户头像组件
 * 
 * 功能：
 * - 显示用户头像
 * - 支持默认头像
 * - 支持头像上传
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-08
 */
</script>
```

### **🎯 命名标准**

#### **组件命名**
```javascript
// ✅ 正确
UserProfile.vue
NavigationSidebar.vue
DataTable.vue

// ❌ 错误
userProfile.vue
navigation-sidebar.vue
dataTable.vue
```

#### **变量命名**
```javascript
// ✅ 正确
const userName = 'John'
const isLoading = false
const API_BASE_URL = 'https://api.example.com'

// ❌ 错误
const user_name = 'John'
const IsLoading = false
const apiBaseUrl = 'https://api.example.com'
```

### **🔄 错误处理标准**

#### **API调用**
```javascript
// ✅ 正确的错误处理
const fetchUserData = async (userId) => {
  try {
    const response = await api.getUser(userId)
    return response.data
  } catch (error) {
    console.error('获取用户数据失败:', error)
    ElMessage.error('加载用户信息失败，请重试')
    throw error
  }
}
```

#### **组件错误边界**
```vue
<script setup>
import { onErrorCaptured } from 'vue'

onErrorCaptured((error, instance, info) => {
  console.error('组件错误:', error)
  // 上报错误到监控系统
  reportError(error, { component: instance, info })
  return false
})
</script>
```

---

## 📊 审查结果

### **审查通过标准**
- ✅ 所有检查项目都已通过
- ✅ 代码风格符合项目规范
- ✅ 功能实现正确且完整
- ✅ 错误处理适当且用户友好
- ✅ 性能和安全性考虑充分

### **审查不通过情况**
- ❌ 存在禁止提交的文件类型
- ❌ 代码中有调试输出或调试语句
- ❌ 命名不符合规范
- ❌ 缺少必要的错误处理
- ❌ 存在明显的性能或安全问题

---

## 📝 审查记录模板

```markdown
## 代码审查记录

**PR编号**: #123
**审查人**: 张三
**审查时间**: 2025-08-08
**变更类型**: 功能开发/Bug修复/重构/文档

### 审查结果
- [ ] 通过
- [ ] 需要修改

### 检查项目
- [x] 文件命名规范
- [x] 代码风格
- [x] 功能实现
- [x] 错误处理
- [x] 性能考虑
- [x] 安全性
- [x] 代码清理

### 发现的问题
1. 问题描述
2. 建议修改方案

### 审查意见
审查意见和建议...

### 后续行动
- [ ] 开发者修改代码
- [ ] 重新审查
- [ ] 合并到主分支
```

---

## 🔧 工具集成

### **ESLint规则**
```json
{
  "rules": {
    "no-console": ["warn", { "allow": ["warn", "error"] }],
    "no-debugger": "error",
    "no-unused-vars": "error",
    "vue/component-name-in-template-casing": ["error", "PascalCase"],
    "vue/prop-name-casing": ["error", "camelCase"]
  }
}
```

### **Prettier配置**
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 100
}
```

---

**文档版本**: v1.0  
**维护团队**: 开发团队  
**下次更新**: 2025-11-08
