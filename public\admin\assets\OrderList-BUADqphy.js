/* empty css             *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                        *//* empty css                         *//* empty css                  *//* empty css                *//* empty css                     *//* empty css                       */import{z as e,m as a,A as t,q as l,G as r,l as d,C as n,E as o,c as s,r as i,M as u,w as c,B as p,F as m,Y as f,o as _,K as g,W as y,D as h}from"./vue-vendor-BcnDv-68.js";import{as as b,Z as v,ak as w,ar as D,ap as C,aj as V,ai as k,a1 as A,W as S,a3 as x,a4 as j,aq as T,a5 as U,a6 as O,_ as P,aL as R,a2 as Q,aZ as q,X as z,br as F,bq as M,ag as B,a7 as I,ah as N,V as L,at as E,av as G,aw as J,az as K,ae as Y,ay as $,a_ as Z,aE as H,ax as W,aF as X,b1 as ee,aC as ae,aT as te,bH as le,a0 as re,u as de,s as ne,aG as oe,aH as se,aI as ie,aJ as ue,an as ce,aK as pe}from"./element-plus-C2UshkXo.js";import{S as me}from"./StatCard-CLi1H67J.js";/* empty css                   *//* empty css                   *//* empty css                             */import{_ as fe}from"./index-eUTsTR3J.js";/* empty css                 *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                         */import{g as _e,a as ge,r as ye,c as he}from"./order-DI2E_fWj.js";import{f as be}from"./format-3eU4VJ9V.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";import"./index-D4AyIzGN.js";const ve={__name:"OrderDetailDialog",props:{modelValue:{type:Boolean,default:!1},order:{type:Object,default:()=>({})}},emits:["update:modelValue","close"],setup(e,{expose:a,emit:t}){a();const l=e,r=t,d=s({get:()=>l.modelValue,set:e=>r("update:modelValue",e)}),n={props:l,emit:r,visible:d,getStatusType:e=>({pending:"warning",paid:"success",shipped:"info",delivered:"success",cancelled:"danger",refunded:"info"}[e]||"info"),getStatusText:e=>({pending:"待支付",paid:"已支付",shipped:"已发货",delivered:"已送达",cancelled:"已取消",refunded:"已退款"}[e]||"未知状态"),handleClose:()=>{r("close"),r("update:modelValue",!1)},handlePrint:()=>{window.print()},ref:i,computed:s};return Object.defineProperty(n,"__isScriptSetup",{enumerable:!1,value:!0}),n}},we={class:"order-detail"},De={class:"amount"},Ce={key:0,class:"order-notes"},Ve={class:"dialog-footer"};const ke=fe(ve,[["render",function(s,i,u,c,p,m){const f=b,_=w,g=D,y=C,h=V,x=k,j=A,T=S;return a(),e(T,{modelValue:c.visible,"onUpdate:modelValue":i[0]||(i[0]=e=>c.visible=e),title:"订单详情",width:"800px","before-close":c.handleClose},{footer:t(()=>[l("span",Ve,[r(j,{onClick:c.handleClose},{default:t(()=>i[3]||(i[3]=[o("关闭",-1)])),_:1,__:[3]}),r(j,{type:"primary",onClick:c.handlePrint},{default:t(()=>i[4]||(i[4]=[o("打印订单",-1)])),_:1,__:[4]})])]),default:t(()=>[l("div",we,[r(g,{title:"订单信息",column:2,border:""},{default:t(()=>[r(f,{label:"订单号"},{default:t(()=>[o(v(u.order.orderNo||"N/A"),1)]),_:1}),r(f,{label:"订单状态"},{default:t(()=>[r(_,{type:c.getStatusType(u.order.status)},{default:t(()=>[o(v(c.getStatusText(u.order.status)),1)]),_:1},8,["type"])]),_:1}),r(f,{label:"用户ID"},{default:t(()=>[o(v(u.order.userId||"N/A"),1)]),_:1}),r(f,{label:"用户名"},{default:t(()=>[o(v(u.order.userName||"N/A"),1)]),_:1}),r(f,{label:"订单金额"},{default:t(()=>[l("span",De,"¥"+v(u.order.amount||"0.00"),1)]),_:1}),r(f,{label:"支付方式"},{default:t(()=>[o(v(u.order.paymentMethod||"N/A"),1)]),_:1}),r(f,{label:"创建时间"},{default:t(()=>[o(v(u.order.createTime||"N/A"),1)]),_:1}),r(f,{label:"更新时间"},{default:t(()=>[o(v(u.order.updateTime||"N/A"),1)]),_:1})]),_:1}),r(y,{"content-position":"left"},{default:t(()=>i[1]||(i[1]=[o("商品信息",-1)])),_:1,__:[1]}),r(x,{data:u.order.items||[],style:{width:"100%"}},{default:t(()=>[r(h,{prop:"productName",label:"商品名称"}),r(h,{prop:"quantity",label:"数量",width:"80"}),r(h,{prop:"price",label:"单价",width:"100"},{default:t(e=>[o(" ¥"+v(e.row.price||"0.00"),1)]),_:1}),r(h,{prop:"total",label:"小计",width:"100"},{default:t(e=>[o(" ¥"+v(e.row.total||"0.00"),1)]),_:1})]),_:1},8,["data"]),u.order.notes?(a(),d("div",Ce,[r(y,{"content-position":"left"},{default:t(()=>i[2]||(i[2]=[o("订单备注",-1)])),_:1,__:[2]}),l("p",null,v(u.order.notes),1)])):n("",!0)])]),_:1},8,["modelValue"])}],["__scopeId","data-v-db2009b2"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/OrderDetailDialog.vue"]]),Ae={__name:"RefundDialog",props:{modelValue:{type:Boolean,default:!1},orderData:{type:Object,default:()=>({})}},emits:["update:modelValue","success","close"],setup(e,{expose:a,emit:t}){a();const l=e,r=t,d=s({get:()=>l.modelValue,set:e=>r("update:modelValue",e)}),n=i(),o=i(!1),p=u({refund_amount:0,refund_reason:"",refund_note:"",refund_method:"original"});c(()=>l.orderData,e=>{e&&e.amount&&(p.refund_amount=e.amount)},{immediate:!0});const m=()=>{r("close"),r("update:modelValue",!1),f()},f=()=>{n.value&&n.value.resetFields(),Object.assign(p,{refund_amount:0,refund_reason:"",refund_note:"",refund_method:"original"})},_={props:l,emit:r,visible:d,refundFormRef:n,loading:o,refundForm:p,rules:{refund_amount:[{required:!0,message:"请输入退款金额",trigger:"blur"},{type:"number",min:.01,message:"退款金额必须大于0.01",trigger:"blur"}],refund_reason:[{required:!0,message:"请选择退款原因",trigger:"change"}]},handleClose:m,resetForm:f,handleSubmit:async()=>{if(n.value)try{await n.value.validate(),o.value=!0,await new Promise(e=>setTimeout(e,2e3)),z.success("退款申请提交成功"),r("success"),m()}catch(e){z.error("退款申请提交失败")}finally{o.value=!1}},ref:i,reactive:u,computed:s,watch:c,get ElMessage(){return z}};return Object.defineProperty(_,"__isScriptSetup",{enumerable:!1,value:!0}),_}},Se={class:"refund-form"},xe={class:"refund-info"},je={class:"dialog-footer"};const Te=fe(Ae,[["render",function(d,n,s,i,u,c){const p=j,m=x,f=T,_=O,g=U,y=R,h=P,b=Q,v=q,w=A,D=S;return a(),e(D,{modelValue:i.visible,"onUpdate:modelValue":n[6]||(n[6]=e=>i.visible=e),title:"订单退款",width:"600px","before-close":i.handleClose},{footer:t(()=>[l("span",je,[r(w,{onClick:i.handleClose},{default:t(()=>n[11]||(n[11]=[o("取消",-1)])),_:1,__:[11]}),r(w,{type:"primary",onClick:i.handleSubmit,loading:i.loading},{default:t(()=>n[12]||(n[12]=[o(" 确认退款 ",-1)])),_:1,__:[12]},8,["loading"])])]),default:t(()=>[l("div",Se,[r(b,{model:i.refundForm,rules:i.rules,ref:"refundFormRef","label-width":"100px"},{default:t(()=>[r(m,{label:"订单号"},{default:t(()=>[r(p,{modelValue:s.orderData.order_no,"onUpdate:modelValue":n[0]||(n[0]=e=>s.orderData.order_no=e),disabled:""},null,8,["modelValue"])]),_:1}),r(m,{label:"订单金额"},{default:t(()=>[r(p,{modelValue:s.orderData.amount,"onUpdate:modelValue":n[1]||(n[1]=e=>s.orderData.amount=e),disabled:""},{prepend:t(()=>n[7]||(n[7]=[o("¥",-1)])),_:1},8,["modelValue"])]),_:1}),r(m,{label:"退款金额",prop:"refund_amount"},{default:t(()=>[r(f,{modelValue:i.refundForm.refund_amount,"onUpdate:modelValue":n[2]||(n[2]=e=>i.refundForm.refund_amount=e),min:.01,max:s.orderData.amount,precision:2,style:{width:"100%"}},null,8,["modelValue","max"])]),_:1}),r(m,{label:"退款原因",prop:"refund_reason"},{default:t(()=>[r(g,{modelValue:i.refundForm.refund_reason,"onUpdate:modelValue":n[3]||(n[3]=e=>i.refundForm.refund_reason=e),placeholder:"请选择退款原因",style:{width:"100%"}},{default:t(()=>[r(_,{label:"用户申请退款",value:"user_request"}),r(_,{label:"商品缺货",value:"out_of_stock"}),r(_,{label:"系统错误",value:"system_error"}),r(_,{label:"重复支付",value:"duplicate_payment"}),r(_,{label:"其他原因",value:"other"})]),_:1},8,["modelValue"])]),_:1}),r(m,{label:"退款说明",prop:"refund_note"},{default:t(()=>[r(p,{modelValue:i.refundForm.refund_note,"onUpdate:modelValue":n[4]||(n[4]=e=>i.refundForm.refund_note=e),type:"textarea",rows:4,placeholder:"请输入退款说明（可选）"},null,8,["modelValue"])]),_:1}),r(m,{label:"退款方式"},{default:t(()=>[r(h,{modelValue:i.refundForm.refund_method,"onUpdate:modelValue":n[5]||(n[5]=e=>i.refundForm.refund_method=e)},{default:t(()=>[r(y,{label:"original"},{default:t(()=>n[8]||(n[8]=[o("原路退回",-1)])),_:1,__:[8]}),r(y,{label:"manual"},{default:t(()=>n[9]||(n[9]=[o("人工处理",-1)])),_:1,__:[9]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),l("div",xe,[r(v,{title:"退款说明",type:"info",closable:!1,"show-icon":""},{default:t(()=>n[10]||(n[10]=[l("ul",null,[l("li",null,"退款将在1-3个工作日内到账"),l("li",null,"原路退回：退款将返回到原支付账户"),l("li",null,"人工处理：需要手动处理退款流程"),l("li",null,"退款成功后将自动发送通知给用户")],-1)])),_:1})])])]),_:1},8,["modelValue"])}],["__scopeId","data-v-fb88221e"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/RefundDialog.vue"]]),Ue={__name:"PaymentInfoDialog",props:{modelValue:{type:Boolean,default:!1},orderData:{type:Object,default:()=>({})}},emits:["update:modelValue","close","refresh"],setup(e,{expose:a,emit:t}){a();const l=e,r=t,d=s({get:()=>l.modelValue,set:e=>r("update:modelValue",e)}),n=i("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="),o=()=>{r("close"),r("update:modelValue",!1)},u={props:l,emit:r,visible:d,paymentQRCode:n,handleClose:o,getStatusType:e=>({pending:"warning",paid:"success",cancelled:"info",refunded:"danger"}[e]||"info"),getStatusText:e=>({pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[e]||"未知状态"),getPaymentMethodType:e=>({wechat:"success",alipay:"primary",qqpay:"warning",bank:"info"}[e]||"info"),getPaymentMethodText:e=>({wechat:"微信支付",alipay:"支付宝",qqpay:"QQ钱包",bank:"银行卡"}[e]||"未知支付方式"),formatDate:e=>e?new Date(e).toLocaleString("zh-CN"):"N/A",sendPaymentReminder:async()=>{try{await B.confirm("确定要发送支付提醒吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),z.success("支付提醒已发送")}catch(e){"cancel"!==e&&z.error("发送提醒失败")}},cancelOrder:async()=>{try{await B.confirm("确定要取消这个订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),z.success("订单已取消"),r("refresh"),o()}catch(e){"cancel"!==e&&z.error("取消订单失败")}},refreshPaymentStatus:async()=>{try{z.success("支付状态已刷新"),r("refresh")}catch(e){z.error("刷新失败")}},ref:i,computed:s,get ElMessage(){return z},get ElMessageBox(){return B}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}},Oe={class:"payment-info"},Pe={class:"amount"},Re={key:0,class:"payment-details"},Qe={class:"payment-timeline"},qe={key:1,class:"pending-payment"},ze={class:"payment-actions"},Fe={key:0,class:"qr-code-section"},Me={class:"qr-code"},Be=["src"],Ie={key:2,class:"cancelled-order"},Ne={key:3,class:"refunded-order"},Le={class:"dialog-footer"};const Ee=fe(Ue,[["render",function(s,i,u,c,p,m){const f=b,_=w,g=D,y=C,h=F,V=M,k=A,x=q,j=S;return a(),e(j,{modelValue:c.visible,"onUpdate:modelValue":i[0]||(i[0]=e=>c.visible=e),title:"支付信息",width:"700px","before-close":c.handleClose},{footer:t(()=>[l("span",Le,[r(k,{onClick:c.handleClose},{default:t(()=>i[19]||(i[19]=[o("关闭",-1)])),_:1,__:[19]}),"pending"===u.orderData.status?(a(),e(k,{key:0,type:"primary",onClick:c.refreshPaymentStatus},{default:t(()=>i[20]||(i[20]=[o(" 刷新支付状态 ",-1)])),_:1,__:[20]})):n("",!0)])]),default:t(()=>[l("div",Oe,[r(g,{title:"订单信息",column:2,border:""},{default:t(()=>[r(f,{label:"订单号"},{default:t(()=>[o(v(u.orderData.order_no||"N/A"),1)]),_:1}),r(f,{label:"订单状态"},{default:t(()=>[r(_,{type:c.getStatusType(u.orderData.status)},{default:t(()=>[o(v(c.getStatusText(u.orderData.status)),1)]),_:1},8,["type"])]),_:1}),r(f,{label:"订单金额"},{default:t(()=>[l("span",Pe,"¥"+v(u.orderData.amount||"0.00"),1)]),_:1}),r(f,{label:"创建时间"},{default:t(()=>[o(v(c.formatDate(u.orderData.created_at)),1)]),_:1})]),_:1}),r(y,{"content-position":"left"},{default:t(()=>i[1]||(i[1]=[o("支付详情",-1)])),_:1,__:[1]}),"paid"===u.orderData.status?(a(),d("div",Re,[r(g,{column:2,border:""},{default:t(()=>[r(f,{label:"支付方式"},{default:t(()=>[r(_,{type:c.getPaymentMethodType(u.orderData.payment_method)},{default:t(()=>[o(v(c.getPaymentMethodText(u.orderData.payment_method)),1)]),_:1},8,["type"])]),_:1}),r(f,{label:"支付时间"},{default:t(()=>[o(v(c.formatDate(u.orderData.paid_at)),1)]),_:1}),r(f,{label:"交易流水号"},{default:t(()=>[o(v(u.orderData.transaction_id||"N/A"),1)]),_:1}),r(f,{label:"支付状态"},{default:t(()=>[r(_,{type:"success"},{default:t(()=>i[2]||(i[2]=[o("支付成功",-1)])),_:1,__:[2]})]),_:1})]),_:1}),l("div",Qe,[i[6]||(i[6]=l("h4",null,"支付流程",-1)),r(V,null,{default:t(()=>[r(h,{timestamp:"订单创建",time:c.formatDate(u.orderData.created_at),type:"primary"},{default:t(()=>i[3]||(i[3]=[o(" 用户创建订单，等待支付 ",-1)])),_:1,__:[3]},8,["time"]),r(h,{timestamp:"支付完成",time:c.formatDate(u.orderData.paid_at),type:"success"},{default:t(()=>i[4]||(i[4]=[o(" 用户完成支付，订单状态更新为已支付 ",-1)])),_:1,__:[4]},8,["time"]),r(h,{timestamp:"订单处理",time:c.formatDate(u.orderData.updated_at),type:"info"},{default:t(()=>i[5]||(i[5]=[o(" 系统处理订单，准备发货 ",-1)])),_:1,__:[5]},8,["time"])]),_:1})])])):"pending"===u.orderData.status?(a(),d("div",qe,[r(x,{title:"订单待支付",type:"warning",closable:!1,"show-icon":""},{default:t(()=>[i[9]||(i[9]=l("p",null,"该订单尚未完成支付，请提醒用户及时支付。",-1)),l("div",ze,[r(k,{type:"primary",size:"small",onClick:c.sendPaymentReminder},{default:t(()=>i[7]||(i[7]=[o(" 发送支付提醒 ",-1)])),_:1,__:[7]}),r(k,{type:"warning",size:"small",onClick:c.cancelOrder},{default:t(()=>i[8]||(i[8]=[o(" 取消订单 ",-1)])),_:1,__:[8]})])]),_:1}),c.paymentQRCode?(a(),d("div",Fe,[i[11]||(i[11]=l("h4",null,"支付二维码",-1)),l("div",Me,[l("img",{src:c.paymentQRCode,alt:"支付二维码"},null,8,Be),i[10]||(i[10]=l("p",null,"用户可扫描此二维码完成支付",-1))])])):n("",!0)])):"cancelled"===u.orderData.status?(a(),d("div",Ie,[r(x,{title:"订单已取消",type:"info",closable:!1,"show-icon":""},{default:t(()=>[i[14]||(i[14]=l("p",null,"该订单已被取消，无法进行支付操作。",-1)),l("p",null,[i[12]||(i[12]=l("strong",null,"取消时间：",-1)),o(v(c.formatDate(u.orderData.cancelled_at)),1)]),l("p",null,[i[13]||(i[13]=l("strong",null,"取消原因：",-1)),o(v(u.orderData.cancel_reason||"用户主动取消"),1)])]),_:1})])):"refunded"===u.orderData.status?(a(),d("div",Ne,[r(x,{title:"订单已退款",type:"danger",closable:!1,"show-icon":""},{default:t(()=>[i[18]||(i[18]=l("p",null,"该订单已完成退款处理。",-1)),l("p",null,[i[15]||(i[15]=l("strong",null,"退款时间：",-1)),o(v(c.formatDate(u.orderData.refunded_at)),1)]),l("p",null,[i[16]||(i[16]=l("strong",null,"退款金额：",-1)),o("¥"+v(u.orderData.refund_amount||u.orderData.amount),1)]),l("p",null,[i[17]||(i[17]=l("strong",null,"退款原因：",-1)),o(v(u.orderData.refund_reason||"用户申请退款"),1)])]),_:1})])):n("",!0)])]),_:1},8,["modelValue"])}],["__scopeId","data-v-ee37795a"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/PaymentInfoDialog.vue"]]),Ge={class:"modern-order-list"},Je={class:"page-header"},Ke={class:"header-content"},Ye={class:"header-left"},$e={class:"header-icon"},Ze={class:"header-actions"},He={class:"stats-section"},We={class:"stats-container"},Xe={class:"stat-content"},ea={class:"stat-value"},aa={class:"stat-label"},ta={class:"card-header"},la={class:"header-left"},ra={class:"header-right"},da={class:"order-no"},na={class:"user-info"},oa={class:"user-details"},sa={class:"username"},ia={class:"user-id"},ua={key:0,class:"group-info"},ca={class:"group-name"},pa={class:"group-price"},ma={key:1,class:"no-group"},fa={class:"order-amount"},_a={key:1,class:"no-payment"},ga={class:"pagination-container"};const ya=fe({__name:"OrderList",setup(e,{expose:a}){a();const t=i([]),l=i(0),r=i(!0),d=i(!1),n=i(!1),o=i(!1),s=i(!1),c=i({}),p=i([]),m=i([]),f=i("table"),g=i([{key:"total",label:"总订单数",value:"0",icon:"Tickets",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"ArrowUp",change:"+125"},{key:"amount",label:"总交易额",value:"¥0",icon:"Money",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"ArrowUp",change:"+28.5%"},{key:"paid",label:"已支付订单",value:"0",icon:"Check",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"ArrowUp",change:"+18.2%"},{key:"success_rate",label:"支付成功率",value:"0%",icon:"TrendCharts",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"ArrowUp",change:"+2.1%"}]),y=i({total_orders:0,total_amount:0,paid_orders:0,success_rate:0}),h=u({page:1,limit:20,keyword:"",status:"",payment_method:"",start_date:"",end_date:""}),b=async()=>{r.value=!0;try{const{data:e}=await _e(h);t.value=e.list,l.value=e.total}catch(e){z.error("获取订单列表失败")}finally{r.value=!1}},v=async()=>{d.value=!0;try{await new Promise(e=>setTimeout(e,300));const e={total_orders:1568,total_amount:234567.89,paid_orders:1234,success_rate:78.5};y.value=e,g.value[0].value=e.total_orders.toString(),g.value[1].value="¥"+e.total_amount.toLocaleString(),g.value[2].value=e.paid_orders.toString(),g.value[3].value=e.success_rate.toFixed(1)+"%"}catch(e){z.error("获取统计数据失败")}finally{d.value=!1}},w=()=>{h.page=1,b()},D=e=>{c.value={...e},n.value=!0},C=e=>{c.value={...e},s.value=!0},V=async e=>{try{await B.confirm("确定要取消这个订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await he(e),z.success("订单取消成功"),b()}catch(a){"cancel"!==a&&z.error("取消订单失败")}},k=async e=>{try{await B.confirm("确定要重新发送通知吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),z.success("通知发送成功")}catch(a){"cancel"!==a&&z.error("发送通知失败")}},A=async()=>{try{if(0===p.value.filter(e=>"pending"===e.status).map(e=>e.id).length)return void z.warning("没有可取消的订单");z.success("批量取消成功"),b()}catch(e){z.error("批量取消失败")}},S=async()=>{try{p.value.map(e=>e.id);z.success("批量导出成功")}catch(e){z.error("批量导出失败")}};_(()=>{b(),v()});const x={list:t,total:l,listLoading:r,statsLoading:d,detailDialogVisible:n,refundDialogVisible:o,paymentDialogVisible:s,currentOrder:c,multipleSelection:p,dateRange:m,viewMode:f,orderStatCards:g,stats:y,listQuery:h,getList:b,getStats:v,resetQuery:()=>{h.page=1,h.limit=20,h.keyword="",h.status="",h.payment_method="",h.start_date="",h.end_date="",m.value=[],w()},handleFilter:w,handleDateChange:e=>{e&&2===e.length?(h.start_date=e[0],h.end_date=e[1]):(h.start_date="",h.end_date="")},handleView:D,handleRefund:e=>{c.value={...e},o.value=!0},handlePaymentInfo:C,handleCancel:V,handleResendNotification:k,copyToClipboard:async e=>{try{await navigator.clipboard.writeText(e),z.success("订单号已复制到剪贴板")}catch(a){z.error("复制失败")}},handleCommand:e=>{const[a,l]=e.split("-"),r=parseInt(l),d=t.value.find(e=>e.id===r);switch(a){case"detail":D(d);break;case"payment":C(d);break;case"cancel":V(r);break;case"resend":k()}},handleExport:async()=>{try{z.success("导出成功")}catch(e){z.error("导出失败")}},handleBatchOperation:()=>{0!==p.value.length?B.confirm("请选择批量操作类型","批量操作",{distinguishCancelAndClose:!0,confirmButtonText:"批量取消",cancelButtonText:"批量导出"}).then(()=>{A()}).catch(e=>{"cancel"===e&&S()}):z.warning("请先选择要操作的订单")},batchCancelOrders:A,batchExportOrders:S,handleSelectionChange:e=>{p.value=e},handleSizeChange:e=>{h.limit=e,b()},handleCurrentChange:e=>{h.page=e,b()},handleRefundSuccess:()=>{b(),v()},navigateToList:()=>{b()},getPaymentMethodTagType:e=>({wechat:"success",alipay:"primary",qqpay:"warning",bank:"info"}[e]||"info"),getPaymentMethodText:e=>({wechat:"微信支付",alipay:"支付宝",qqpay:"QQ钱包",bank:"银行卡"}[e]||"未知"),getStatusTagType:e=>({pending:"warning",paid:"success",cancelled:"info",refunded:"danger"}[e]||"info"),getStatusText:e=>({pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[e]||"未知"),ref:i,onMounted:_,reactive:u,get ElMessage(){return z},get ElMessageBox(){return B},get Tickets(){return le},get Money(){return te},get Check(){return ae},get TrendCharts(){return ee},get Search(){return X},get Download(){return W},get ArrowDown(){return H},get DocumentCopy(){return Z},get Operation(){return $},get Refresh(){return Y},get RefreshLeft(){return K},get List(){return J},get Grid(){return G},get ArrowUp(){return E},StatCard:me,OrderDetailDialog:ke,RefundDialog:Te,PaymentInfoDialog:Ee,get getOrderList(){return _e},get cancelOrder(){return he},get refundOrder(){return ye},get getOrderStats(){return ge},get formatDate(){return be}};return Object.defineProperty(x,"__isScriptSetup",{enumerable:!1,value:!0}),x}},[["render",function(s,i,u,c,_,b){const D=N,C=A,S=j,T=x,P=O,R=U,q=re,z=Q,F=L,M=w,B=pe,E=V,G=oe,J=ue,K=ie,Y=se,$=k,Z=ce,H=I;return a(),d("div",Ge,[l("div",Je,[l("div",Ke,[l("div",Ye,[l("div",$e,[r(D,{size:"24"},{default:t(()=>[r(c.Tickets)]),_:1})]),i[11]||(i[11]=l("div",{class:"header-text"},[l("h1",null,"订单管理"),l("p",null,"全面管理平台订单，包括订单查询、支付处理、退款管理和数据统计")],-1))]),l("div",Ze,[r(C,{onClick:c.handleExport,class:"action-btn secondary"},{default:t(()=>[r(D,null,{default:t(()=>[r(c.Download)]),_:1}),i[12]||(i[12]=o(" 导出数据 ",-1))]),_:1,__:[12]}),r(C,{onClick:c.handleBatchOperation,class:"action-btn secondary"},{default:t(()=>[r(D,null,{default:t(()=>[r(c.Operation)]),_:1}),i[13]||(i[13]=o(" 批量操作 ",-1))]),_:1,__:[13]}),r(C,{type:"primary",onClick:c.handleFilter,class:"action-btn primary"},{default:t(()=>[r(D,null,{default:t(()=>[r(c.Refresh)]),_:1}),i[14]||(i[14]=o(" 刷新数据 ",-1))]),_:1,__:[14]})])])]),r(F,{class:"filter-card"},{default:t(()=>[r(z,{inline:!0,model:c.listQuery,onSubmit:g(c.handleFilter,["prevent"])},{default:t(()=>[r(T,{label:"关键词"},{default:t(()=>[r(S,{modelValue:c.listQuery.keyword,"onUpdate:modelValue":i[0]||(i[0]=e=>c.listQuery.keyword=e),placeholder:"订单号、用户名",clearable:"",onKeyup:y(c.handleFilter,["enter"]),class:"search-input"},null,8,["modelValue"])]),_:1}),r(T,{label:"订单状态"},{default:t(()=>[r(R,{modelValue:c.listQuery.status,"onUpdate:modelValue":i[1]||(i[1]=e=>c.listQuery.status=e),placeholder:"全部状态",clearable:"",class:"filter-select"},{default:t(()=>[r(P,{label:"全部",value:""}),r(P,{label:"待支付",value:"pending"}),r(P,{label:"已支付",value:"paid"}),r(P,{label:"已取消",value:"cancelled"}),r(P,{label:"已退款",value:"refunded"})]),_:1},8,["modelValue"])]),_:1}),r(T,{label:"支付方式"},{default:t(()=>[r(R,{modelValue:c.listQuery.payment_method,"onUpdate:modelValue":i[2]||(i[2]=e=>c.listQuery.payment_method=e),placeholder:"全部支付方式",clearable:"",class:"filter-select"},{default:t(()=>[r(P,{label:"全部",value:""}),r(P,{label:"微信支付",value:"wechat"}),r(P,{label:"支付宝",value:"alipay"}),r(P,{label:"QQ钱包",value:"qqpay"}),r(P,{label:"银行卡",value:"bank"})]),_:1},8,["modelValue"])]),_:1}),r(T,{label:"日期范围"},{default:t(()=>[r(q,{modelValue:c.dateRange,"onUpdate:modelValue":i[3]||(i[3]=e=>c.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",class:"date-picker",onChange:c.handleDateChange},null,8,["modelValue"])]),_:1}),r(T,null,{default:t(()=>[r(C,{type:"primary",onClick:c.handleFilter,class:"search-btn"},{default:t(()=>[r(D,null,{default:t(()=>[r(c.Search)]),_:1}),i[15]||(i[15]=o(" 查询 ",-1))]),_:1,__:[15]}),r(C,{onClick:c.resetQuery,class:"reset-btn"},{default:t(()=>[r(D,null,{default:t(()=>[r(c.RefreshLeft)]),_:1}),i[16]||(i[16]=o(" 重置 ",-1))]),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),_:1}),l("div",He,[p((a(),d("div",We,[(a(!0),d(m,null,f(c.orderStatCards,n=>(a(),d("div",{class:"stat-card",key:n.key},[l("div",{class:"stat-icon",style:de({background:n.color})},[r(D,{size:"20"},{default:t(()=>[(a(),e(h(n.icon)))]),_:2},1024)],4),l("div",Xe,[l("div",ea,v(n.value),1),l("div",aa,v(n.label),1)]),l("div",{class:ne(["stat-trend",n.trend])},[r(D,{size:"14"},{default:t(()=>[(a(),e(h(n.trendIcon)))]),_:2},1024),l("span",null,v(n.change),1)],2)]))),128))])),[[H,c.statsLoading]])]),r(F,{class:"table-card"},{header:t(()=>[l("div",ta,[l("div",la,[i[17]||(i[17]=l("h3",null,"订单列表",-1)),r(M,{size:"small",type:"info"},{default:t(()=>[o("共 "+v(c.total)+" 条记录",1)]),_:1})]),l("div",ra,[r(B,null,{default:t(()=>[r(C,{size:"small",type:"table"===c.viewMode?"primary":"",onClick:i[4]||(i[4]=e=>c.viewMode="table")},{default:t(()=>[r(D,null,{default:t(()=>[r(c.List)]),_:1})]),_:1},8,["type"]),r(C,{size:"small",type:"card"===c.viewMode?"primary":"",onClick:i[5]||(i[5]=e=>c.viewMode="card")},{default:t(()=>[r(D,null,{default:t(()=>[r(c.Grid)]),_:1})]),_:1},8,["type"])]),_:1})])])]),default:t(()=>[p((a(),e($,{data:c.list,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",onSelectionChange:c.handleSelectionChange},{default:t(()=>[r(E,{type:"selection",width:"55"}),r(E,{label:"订单号",width:"180"},{default:t(({row:e})=>[l("div",da,[l("span",null,v(e.order_no),1),r(C,{type:"text",size:"small",onClick:a=>c.copyToClipboard(e.order_no),class:"copy-btn"},{default:t(()=>[r(D,null,{default:t(()=>[r(c.DocumentCopy)]),_:1})]),_:2},1032,["onClick"])])]),_:1}),r(E,{label:"用户信息",width:"150"},{default:t(({row:e})=>[l("div",na,[r(G,{src:e.user?.avatar,size:"small"},{default:t(()=>[o(v(e.user?.username?.charAt(0).toUpperCase()),1)]),_:2},1032,["src"]),l("div",oa,[l("div",sa,v(e.user?.username),1),l("div",ia,"ID: "+v(e.user_id),1)])])]),_:1}),r(E,{label:"群组信息",width:"200"},{default:t(({row:e})=>[e.wechat_group?(a(),d("div",ua,[l("div",ca,v(e.wechat_group.name),1),l("div",pa,"¥"+v(e.wechat_group.price),1)])):(a(),d("span",ma,"-"))]),_:1}),r(E,{label:"订单金额",width:"100"},{default:t(({row:e})=>[l("span",fa,"¥"+v(e.amount.toFixed(2)),1)]),_:1}),r(E,{label:"支付方式",width:"100"},{default:t(({row:l})=>[l.payment_method?(a(),e(M,{key:0,type:c.getPaymentMethodTagType(l.payment_method)},{default:t(()=>[o(v(c.getPaymentMethodText(l.payment_method)),1)]),_:2},1032,["type"])):(a(),d("span",_a,"-"))]),_:1}),r(E,{label:"订单状态",width:"100"},{default:t(({row:e})=>[r(M,{type:c.getStatusTagType(e.status)},{default:t(()=>[o(v(c.getStatusText(e.status)),1)]),_:2},1032,["type"])]),_:1}),r(E,{label:"创建时间",width:"160"},{default:t(({row:e})=>[o(v(c.formatDate(e.created_at)),1)]),_:1}),r(E,{label:"支付时间",width:"160"},{default:t(({row:e})=>[o(v(e.paid_at?c.formatDate(e.paid_at):"-"),1)]),_:1}),r(E,{label:"操作",width:"200",fixed:"right"},{default:t(({row:l})=>[r(C,{type:"primary",size:"small",onClick:e=>c.handleView(l)},{default:t(()=>i[18]||(i[18]=[o(" 查看 ",-1)])),_:2,__:[18]},1032,["onClick"]),"paid"===l.status?(a(),e(C,{key:0,type:"success",size:"small",onClick:e=>c.handleRefund(l)},{default:t(()=>i[19]||(i[19]=[o(" 退款 ",-1)])),_:2,__:[19]},1032,["onClick"])):n("",!0),r(Y,{onCommand:c.handleCommand},{dropdown:t(()=>[r(K,null,{default:t(()=>[r(J,{command:`detail-${l.id}`},{default:t(()=>i[21]||(i[21]=[o("订单详情",-1)])),_:2,__:[21]},1032,["command"]),"pending"===l.status?(a(),e(J,{key:0,command:`payment-${l.id}`},{default:t(()=>i[22]||(i[22]=[o("支付信息",-1)])),_:2,__:[22]},1032,["command"])):n("",!0),"pending"===l.status?(a(),e(J,{key:1,command:`cancel-${l.id}`},{default:t(()=>i[23]||(i[23]=[o("取消订单",-1)])),_:2,__:[23]},1032,["command"])):n("",!0),"paid"===l.status?(a(),e(J,{key:2,command:`resend-${l.id}`},{default:t(()=>i[24]||(i[24]=[o("重发通知",-1)])),_:2,__:[24]},1032,["command"])):n("",!0)]),_:2},1024)]),default:t(()=>[r(C,{type:"info",size:"small"},{default:t(()=>[i[20]||(i[20]=o(" 更多",-1)),r(D,{class:"el-icon--right"},{default:t(()=>[r(c.ArrowDown)]),_:1})]),_:1,__:[20]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[H,c.listLoading]]),l("div",ga,[r(Z,{"current-page":c.listQuery.page,"onUpdate:currentPage":i[6]||(i[6]=e=>c.listQuery.page=e),"page-size":c.listQuery.limit,"onUpdate:pageSize":i[7]||(i[7]=e=>c.listQuery.limit=e),"page-sizes":[10,20,30,50],total:c.total,background:"",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:c.handleSizeChange,onCurrentChange:c.handleCurrentChange},null,8,["current-page","page-size","total"])])]),_:1}),r(c.OrderDetailDialog,{modelValue:c.detailDialogVisible,"onUpdate:modelValue":i[8]||(i[8]=e=>c.detailDialogVisible=e),"order-data":c.currentOrder},null,8,["modelValue","order-data"]),r(c.RefundDialog,{modelValue:c.refundDialogVisible,"onUpdate:modelValue":i[9]||(i[9]=e=>c.refundDialogVisible=e),"order-data":c.currentOrder,onSuccess:c.handleRefundSuccess},null,8,["modelValue","order-data"]),r(c.PaymentInfoDialog,{modelValue:c.paymentDialogVisible,"onUpdate:modelValue":i[10]||(i[10]=e=>c.paymentDialogVisible=e),"order-data":c.currentOrder},null,8,["modelValue","order-data"])])}],["__scopeId","data-v-29937d67"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/orders/OrderList.vue"]]);export{ya as default};
