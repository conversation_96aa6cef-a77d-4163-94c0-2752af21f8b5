import{s as e}from"./index-eUTsTR3J.js";import{f as t}from"./chunk-KZPPZA2C-C8HwxGb3.js";const r=(e=>{const r=[],a=["startup","finance","tech","education","other"],s=["active","paused","full","pending"];for(let o=1;o<=e;o++)r.push({id:o,name:t.company.name()+"交流群",description:t.lorem.sentence(5),avatar:t.image.avatar(),owner_name:t.person.fullName(),owner_role:"群主",category:t.helpers.arrayElement(a),price:t.helpers.arrayElement([0,0,0,9.9,19.9,29.9]),current_members:t.number.int({min:50,max:500}),max_members:500,health_score:t.number.int({min:30,max:100}),status:t.helpers.arrayElement(s),created_at:t.date.past().toISOString()});return r})(123),a={getGroupList(e){const{page:t=1,limit:a=10,keyword:s,status:o,category:i}=e;let n=r.filter(e=>{let t=!0;return!s||e.name.includes(s)||(e.owner_name||"").includes(s)||(t=!1),o&&e.status!==o&&(t=!1),i&&e.category!==i&&(t=!1),t});const m=(t-1)*a,c=t*a,u=n.slice(m,c);return Promise.resolve({code:0,data:{list:u,total:n.length},message:"成功"})},getGroupStats:()=>Promise.resolve({code:0,data:{total_groups:r.length,active_groups:r.filter(e=>"active"===e.status).length,total_members:r.reduce((e,t)=>e+t.current_members,0),total_revenue:r.reduce((e,t)=>e+t.price*t.current_members,0)/100},message:"成功"}),getGroupDetail(e){const t=r.find(t=>t.id===e);return Promise.resolve({code:0,data:t||null,message:"成功"})},deleteGroup(e){const t=r.findIndex(t=>t.id===e);return-1!==t&&r.splice(t,1),Promise.resolve({code:0,message:"删除成功"})},updateGroupStatus(e,t){const a=r.find(t=>t.id===e);return a&&(a.status=t),Promise.resolve({code:0,message:"状态更新成功"})},getTemplates(e={}){const{page:t=1,per_page:r=10,keyword:a,category:s,is_preset:o,is_active:i}=e;let n=[{id:1,template_name:"高端商务群模板",template_code:"BUSINESS_001",description:"适用于商务交流、项目合作等高端群组",category:"business",category_name:"商务类",is_preset:!0,is_active:!0,usage_count:156,cover_image_url:"https://picsum.photos/200/120?random=1",creator:{username:"系统"},created_at:"2024-01-15T10:00:00Z",template_data:{title:"{{city}}商务精英交流群",description:"汇聚{{city}}地区商务精英，分享商机，共创未来",price:99,member_limit:500,virtual_members:328,virtual_orders:89,virtual_income:15680.5},sort_order:100},{id:2,template_name:"副业赚钱群模板",template_code:"SIDEJOB_001",description:"专为副业创业者设计的群组模板",category:"finance",category_name:"财经类",is_preset:!0,is_active:!0,usage_count:289,cover_image_url:"https://picsum.photos/200/120?random=2",creator:{username:"系统"},created_at:"2024-01-10T14:30:00Z",template_data:{title:"{{city}}副业赚钱交流群",description:"分享副业项目，交流赚钱经验，实现财务自由",price:58,member_limit:300,virtual_members:267,virtual_orders:156,virtual_income:8960},sort_order:90},{id:3,template_name:"学习成长群模板",template_code:"STUDY_001",description:"知识分享、技能提升类群组模板",category:"education",category_name:"教育类",is_preset:!1,is_active:!0,usage_count:78,cover_image_url:"https://picsum.photos/200/120?random=3",creator:{username:"admin"},created_at:"2024-01-20T09:15:00Z",template_data:{title:"{{city}}学习成长交流群",description:"一起学习，共同成长，分享知识与经验",price:29,member_limit:200,virtual_members:145,virtual_orders:67,virtual_income:1943},sort_order:80},{id:4,template_name:"健身运动群模板",template_code:"FITNESS_001",description:"健身爱好者交流群组模板",category:"lifestyle",category_name:"生活类",is_preset:!1,is_active:!0,usage_count:45,cover_image_url:"https://picsum.photos/200/120?random=4",creator:{username:"admin"},created_at:"2024-01-25T16:45:00Z",template_data:{title:"{{city}}健身运动交流群",description:"分享健身经验，制定运动计划，一起变得更健康",price:39,member_limit:150,virtual_members:89,virtual_orders:34,virtual_income:1326},sort_order:70}];a&&(n=n.filter(e=>e.template_name.includes(a)||e.description.includes(a)||e.template_code.includes(a))),s&&(n=n.filter(e=>e.category===s)),"boolean"==typeof o&&(n=n.filter(e=>e.is_preset===o)),"boolean"==typeof i&&(n=n.filter(e=>e.is_active===i));const m=n.length,c=(t-1)*r,u=c+r,l=n.slice(c,u);return Promise.resolve({code:200,data:{data:l,total:m,current_page:t,per_page:r,last_page:Math.ceil(m/r)},message:"获取成功"})},getTemplate(e){return this.getTemplates().then(t=>{const r=t.data.data.find(t=>t.id==e);return r?Promise.resolve({code:200,data:r,message:"获取成功"}):Promise.resolve({code:404,data:null,message:"模板不存在"})})},getTemplateCategories:()=>Promise.resolve({code:200,data:{business:"商务类",finance:"财经类",education:"教育类",lifestyle:"生活类",entertainment:"娱乐类",technology:"科技类"},message:"获取成功"}),createTemplate(e){const t={id:Date.now(),...e,usage_count:0,creator:{username:"admin"},created_at:(new Date).toISOString(),is_preset:!1};return Promise.resolve({code:200,data:t,message:"创建成功"})},updateTemplate:(e,t)=>Promise.resolve({code:200,data:{id:e,...t},message:"更新成功"}),deleteTemplate:e=>Promise.resolve({code:200,data:null,message:"删除成功"}),copyTemplate(e){return this.getTemplate(e).then(e=>{if(200===e.code){const t=e.data,r={...t,id:Date.now(),template_name:t.template_name+" (副本)",usage_count:0,created_at:(new Date).toISOString(),is_preset:!1};return Promise.resolve({code:200,data:r,message:"复制成功"})}return e})},toggleTemplateStatus:(e,t)=>Promise.resolve({code:200,data:{id:e,is_active:t},message:"状态更新成功"})};function s(e){return a.getGroupList(e)}function o(){return a.getGroupStats()}function i(e){return a.createGroup(e)}function n(t,r){return e({url:`/admin/groups/${t}`,method:"put",data:r})}function m(e){return a.deleteGroup(e)}function c(e,t){return a.updateGroupStatus(e,t)}function u(e){return a.exportGroups(e)}function l(e){return a.getTemplates(e)}function d(e){return a.getTemplate(e)}function p(e){return a.createTemplate(e)}function _(e,t){return a.updateTemplate(e,t)}function g(e){return a.deleteTemplate(e)}function v(){return a.getTemplateCategories()}function f(e,t){return a.toggleTemplateStatus(e,t)}function y(e,t){return a.updateGroupQrCode(e,t)}export{v as a,d as b,p as c,g as d,n as e,i as f,l as g,y as h,s as i,o as j,u as k,c as l,m,f as t,_ as u};
