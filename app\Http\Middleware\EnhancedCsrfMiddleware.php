<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Support\Facades\Log;

/**
 * 增强的CSRF保护中间件
 * 提供更强的CSRF攻击防护机制
 */
class EnhancedCsrfMiddleware extends Middleware
{
    /**
     * 不需要CSRF验证的URI
     */
    protected $except = [
        'api/payment/notify/*',
        'api/webhook/*',
        'api/callback/*',
    ];

    /**
     * 需要严格CSRF验证的敏感操作路由
     */
    protected $strictRoutes = [
        'admin/*',
        'api/users/*/delete',
        'api/orders/*/cancel',
        'api/payments/*',
        'api/settings/*',
        'api/system/*',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // API路由使用JWT认证，跳过CSRF
        if ($this->isApiRoute($request)) {
            return $this->handleApiRequest($request, $next);
        }

        // 检查是否为敏感操作
        if ($this->isSensitiveOperation($request)) {
            return $this->handleSensitiveOperation($request, $next);
        }

        return parent::handle($request, $next);
    }

    /**
     * 处理API请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    protected function handleApiRequest($request, Closure $next)
    {
        // 对于API请求，检查Referer和Origin头
        if (!$this->validateRequestHeaders($request)) {
            Log::warning('API请求头验证失败', [
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
                'referer' => $request->header('referer'),
                'origin' => $request->header('origin'),
            ]);

            return response()->json([
                'success' => false,
                'message' => '请求来源验证失败'
            ], 403);
        }

        return $next($request);
    }

    /**
     * 处理敏感操作
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    protected function handleSensitiveOperation($request, Closure $next)
    {
        try {
            // 严格的CSRF验证
            $this->verifyCsrfToken($request);
            
            // 额外的安全检查
            if (!$this->validateRequestHeaders($request)) {
                throw new TokenMismatchException('请求头验证失败');
            }

            // 记录敏感操作
            Log::info('敏感操作通过CSRF验证', [
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
            ]);

            return $next($request);
        } catch (TokenMismatchException $e) {
            Log::warning('CSRF验证失败 - 敏感操作', [
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
                'referer' => $request->header('referer'),
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'CSRF令牌验证失败'
                ], 419);
            }

            return redirect()->back()->withErrors(['csrf' => 'CSRF令牌验证失败，请刷新页面重试']);
        }
    }

    /**
     * 验证请求头
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function validateRequestHeaders($request): bool
    {
        $allowedOrigins = [
            config('app.url'),
            'http://localhost:8000',
            'https://localhost:8000',
        ];

        // 添加配置中的允许域名
        if (config('app.allowed_origins')) {
            $allowedOrigins = array_merge($allowedOrigins, config('app.allowed_origins'));
        }

        $origin = $request->header('origin');
        $referer = $request->header('referer');

        // 检查Origin头
        if ($origin && !$this->isAllowedOrigin($origin, $allowedOrigins)) {
            return false;
        }

        // 检查Referer头
        if ($referer && !$this->isAllowedReferer($referer, $allowedOrigins)) {
            return false;
        }

        return true;
    }

    /**
     * 检查是否为API路由
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function isApiRoute($request): bool
    {
        return $request->is('api/*');
    }

    /**
     * 检查是否为敏感操作
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function isSensitiveOperation($request): bool
    {
        foreach ($this->strictRoutes as $route) {
            if ($request->is($route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查Origin是否被允许
     *
     * @param  string  $origin
     * @param  array   $allowedOrigins
     * @return bool
     */
    protected function isAllowedOrigin($origin, $allowedOrigins): bool
    {
        return in_array($origin, $allowedOrigins, true);
    }

    /**
     * 检查Referer是否被允许
     *
     * @param  string  $referer
     * @param  array   $allowedOrigins
     * @return bool
     */
    protected function isAllowedReferer($referer, $allowedOrigins): bool
    {
        foreach ($allowedOrigins as $allowedOrigin) {
            if (strpos($referer, $allowedOrigin) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取令牌不匹配的响应
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Session\TokenMismatchException  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function tokensMatch($request)
    {
        $token = $this->getTokenFromRequest($request);

        return is_string($request->session()->token()) &&
               is_string($token) &&
               hash_equals($request->session()->token(), $token);
    }
}