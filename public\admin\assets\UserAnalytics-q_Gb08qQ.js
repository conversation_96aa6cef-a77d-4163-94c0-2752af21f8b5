/* empty css             *//* empty css                   *//* empty css                     *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                  *//* empty css                       *//* empty css                       *//* empty css                        *//* empty css               *//* empty css                */import{l as e,G as a,A as t,M as s,r as l,o as n,m as i,q as r,E as o,F as d,Y as c}from"./vue-vendor-BcnDv-68.js";import{H as u}from"./echarts-D6CUuNS9.js";import{_ as p}from"./index-eUTsTR3J.js";import{U as v,V as m,W as g,X as _,Y as h,Z as f,_ as y,$ as b,u as C,s as V,a0 as w,a1 as D,a2 as x,a3 as S,a4 as j,a5 as k,a6 as U}from"./element-plus-C2UshkXo.js";import"./utils-SdQ7DxjY.js";const A={class:"app-container"},F={class:"stat-content"},P={class:"stat-data"},z={class:"stat-number"},O={class:"stat-change"},M={class:"change-text"},E={class:"stat-content"},L={class:"stat-data"},R={class:"stat-number"},q={class:"stat-change"},G={class:"change-text"},I={class:"stat-content"},B={class:"stat-data"},N={class:"stat-number"},Y={class:"stat-change"},Q={class:"change-text"},T={class:"stat-content"},W={class:"stat-data"},X={class:"stat-number"},Z={class:"stat-change"},$={class:"change-text"},H={class:"card-header"},J={class:"region-list"},K={class:"region-info"},ee={class:"region-name"},ae={class:"region-count"},te={class:"region-bar"},se={class:"device-stats"},le={class:"device-info"},ne={class:"device-name"},ie={class:"device-count"},re={class:"device-percent"},oe={class:"card-header"},de={class:"behavior-card"},ce={class:"feature-list"},ue={class:"feature-rank"},pe={class:"feature-info"},ve={class:"feature-name"},me={class:"feature-usage"},ge={class:"feature-bar"},_e={class:"behavior-card"},he={class:"behavior-card"},fe={class:"retention-table"},ye={class:"retention-rate"},be={class:"retention-rate"},Ce={class:"card-header"},Ve={class:"portrait-section"},we={class:"portrait-section"},De={class:"age-distribution"},xe={class:"age-range"},Se={class:"age-bar"},je={class:"age-percent"},ke={class:"segment-conditions"},Ue={class:"segment-preview"},Ae={class:"dialog-footer"};const Fe=p({__name:"UserAnalytics",setup(e,{expose:a}){a();const t=s({total_users:12543,new_this_month:1245,active_users:8976,active_rate:71.5,vip_users:456,vip_rate:3.6,avg_user_value:1256.78,lifetime_value:3456.78}),i=l([{name:"北京",count:2543,percentage:85},{name:"上海",count:2134,percentage:71},{name:"广州",count:1876,percentage:63},{name:"深圳",count:1654,percentage:55},{name:"其他",count:4336,percentage:45}]),r=l([{type:"mobile",name:"手机",icon:"el-icon-mobile-phone",count:8543,percentage:68},{type:"desktop",name:"电脑",icon:"el-icon-monitor",count:3210,percentage:26},{type:"tablet",name:"平板",icon:"el-icon-tablet",count:790,percentage:6}]),o=l([{name:"用户登录",usage:15643,percentage:100},{name:"查看商品",usage:12453,percentage:80},{name:"下单购买",usage:8976,percentage:57},{name:"分享推广",usage:6543,percentage:42},{name:"提现申请",usage:3456,percentage:22}]),d=l([{date:"2024-01-01",new_users:123,day1_retention:85,day7_retention:45},{date:"2024-01-02",new_users:156,day1_retention:78,day7_retention:42},{date:"2024-01-03",new_users:134,day1_retention:82,day7_retention:48}]),c=l([{range:"18-25",percentage:35,color:"#409EFF"},{range:"26-35",percentage:42,color:"#67C23A"},{range:"36-45",percentage:18,color:"#E6A23C"},{range:"46+",percentage:5,color:"#F56C6C"}]),p=l("30d"),v=l([]),m=l({}),g=l({}),h=l({}),f=l({}),y=l({}),b=s({visible:!1}),C=s({name:"",conditions:[{field:"",operator:"",value:""}]}),V=s({count:0,percentage:0}),w=()=>{D(),g.value={tooltip:{trigger:"item"},series:[{name:"用户活跃度",type:"pie",radius:["30%","70%"],data:[{value:6543,name:"高活跃"},{value:2433,name:"中活跃"},{value:3567,name:"低活跃"}]}]},h.value={tooltip:{trigger:"item"},series:[{name:"用户等级",type:"pie",radius:"60%",data:[{value:8543,name:"普通用户"},{value:3210,name:"银牌用户"},{value:790,name:"金牌用户"},{value:200,name:"VIP用户"}]}]},f.value={xAxis:{type:"category",data:["00:00","04:00","08:00","12:00","16:00","20:00"]},yAxis:{type:"value"},series:[{data:[120,200,150,800,700,500],type:"line",smooth:!0}]},y.value={xAxis:{type:"category",data:["0-100","100-500","500-1000","1000-5000","5000+"]},yAxis:{type:"value"},series:[{data:[1200,3400,2800,1600,400],type:"bar"}]}},D=()=>{const e=[],a=[],t=[];for(let s=("7d"===p.value?7:"30d"===p.value?30:90)-1;s>=0;s--){const l=new Date;l.setDate(l.getDate()-s),e.push(l.toLocaleDateString("zh-CN",{month:"short",day:"numeric"})),a.push(Math.floor(100*Math.random())+50),t.push(Math.floor(200*Math.random())+100)}m.value={tooltip:{trigger:"axis"},legend:{data:["新增用户","活跃用户"]},xAxis:{type:"category",data:e},yAxis:{type:"value"},series:[{name:"新增用户",type:"line",data:a,itemStyle:{color:"#409EFF"}},{name:"活跃用户",type:"line",data:t,itemStyle:{color:"#67C23A"}}]}};n(()=>{w()});const x={userStats:t,regionData:i,deviceData:r,featureUsage:o,retentionData:d,ageData:c,growthPeriod:p,behaviorDateRange:v,growthChartOptions:m,activityChartOptions:g,levelChartOptions:h,timeAnalysisChart:f,consumptionChart:y,segmentDialog:b,segmentForm:C,segmentPreview:V,initCharts:w,updateGrowthChart:D,updateBehaviorData:()=>{_.success("行为数据已更新")},showUserSegmentDialog:()=>{b.visible=!0},addCondition:()=>{C.conditions.push({field:"",operator:"",value:""})},removeCondition:e=>{C.conditions.splice(e,1)},createSegment:()=>{_.success("用户分群创建成功"),b.visible=!1},ref:l,reactive:s,onMounted:n,get ElMessage(){return _},get VChart(){return u}};return Object.defineProperty(x,"__isScriptSetup",{enumerable:!1,value:!0}),x}},[["render",function(s,l,n,u,p,_){const Fe=m,Pe=h,ze=v,Oe=b,Me=y,Ee=w,Le=D,Re=j,qe=S,Ge=U,Ie=k,Be=x,Ne=g;return i(),e("div",A,[a(ze,{gutter:20},{default:t(()=>[a(Pe,{span:6},{default:t(()=>[a(Fe,{class:"stat-card total-users"},{default:t(()=>[r("div",F,[l[6]||(l[6]=r("div",{class:"stat-icon"},[r("i",{class:"el-icon-user"})],-1)),r("div",P,[r("div",z,f(u.userStats.total_users.toLocaleString()),1),l[5]||(l[5]=r("div",{class:"stat-label"},"总用户数",-1)),r("div",O,[r("span",M,"本月新增 "+f(u.userStats.new_this_month),1)])])])]),_:1})]),_:1}),a(Pe,{span:6},{default:t(()=>[a(Fe,{class:"stat-card active-users"},{default:t(()=>[r("div",E,[l[8]||(l[8]=r("div",{class:"stat-icon"},[r("i",{class:"el-icon-star-on"})],-1)),r("div",L,[r("div",R,f(u.userStats.active_users.toLocaleString()),1),l[7]||(l[7]=r("div",{class:"stat-label"},"活跃用户",-1)),r("div",q,[r("span",G,"活跃率 "+f(u.userStats.active_rate)+"%",1)])])])]),_:1})]),_:1}),a(Pe,{span:6},{default:t(()=>[a(Fe,{class:"stat-card vip-users"},{default:t(()=>[r("div",I,[l[10]||(l[10]=r("div",{class:"stat-icon"},[r("i",{class:"el-icon-trophy"})],-1)),r("div",B,[r("div",N,f(u.userStats.vip_users.toLocaleString()),1),l[9]||(l[9]=r("div",{class:"stat-label"},"VIP用户",-1)),r("div",Y,[r("span",Q,"占比 "+f(u.userStats.vip_rate)+"%",1)])])])]),_:1})]),_:1}),a(Pe,{span:6},{default:t(()=>[a(Fe,{class:"stat-card avg-value"},{default:t(()=>[r("div",T,[l[12]||(l[12]=r("div",{class:"stat-icon"},[r("i",{class:"el-icon-coin"})],-1)),r("div",W,[r("div",X,"¥"+f(u.userStats.avg_user_value),1),l[11]||(l[11]=r("div",{class:"stat-label"},"用户均值",-1)),r("div",Z,[r("span",$,"LTV ¥"+f(u.userStats.lifetime_value),1)])])])]),_:1})]),_:1})]),_:1}),a(ze,{gutter:20,style:{"margin-top":"20px"}},{default:t(()=>[a(Pe,{span:12},{default:t(()=>[a(Fe,null,{header:t(()=>[r("div",H,[l[16]||(l[16]=r("span",null,"📈 用户增长趋势",-1)),a(Me,{modelValue:u.growthPeriod,"onUpdate:modelValue":l[0]||(l[0]=e=>u.growthPeriod=e),size:"small",onChange:u.updateGrowthChart},{default:t(()=>[a(Oe,{label:"7d"},{default:t(()=>l[13]||(l[13]=[o("7天",-1)])),_:1,__:[13]}),a(Oe,{label:"30d"},{default:t(()=>l[14]||(l[14]=[o("30天",-1)])),_:1,__:[14]}),a(Oe,{label:"90d"},{default:t(()=>l[15]||(l[15]=[o("90天",-1)])),_:1,__:[15]})]),_:1},8,["modelValue"])])]),default:t(()=>[a(u.VChart,{class:"chart",option:u.growthChartOptions,autoresize:""},null,8,["option"])]),_:1})]),_:1}),a(Pe,{span:12},{default:t(()=>[a(Fe,null,{header:t(()=>l[17]||(l[17]=[r("div",{class:"card-header"},[r("span",null,"🎯 用户活跃度")],-1)])),default:t(()=>[a(u.VChart,{class:"chart",option:u.activityChartOptions,autoresize:""},null,8,["option"])]),_:1})]),_:1})]),_:1}),a(ze,{gutter:20,style:{"margin-top":"20px"}},{default:t(()=>[a(Pe,{span:8},{default:t(()=>[a(Fe,null,{header:t(()=>l[18]||(l[18]=[r("div",{class:"card-header"},[r("span",null,"🌍 地域分布")],-1)])),default:t(()=>[r("div",J,[(i(!0),e(d,null,c(u.regionData,a=>(i(),e("div",{class:"region-item",key:a.name},[r("div",K,[r("span",ee,f(a.name),1),r("span",ae,f(a.count)+"人",1)]),r("div",te,[r("div",{class:"region-progress",style:C({width:a.percentage+"%"})},null,4)])]))),128))])]),_:1})]),_:1}),a(Pe,{span:8},{default:t(()=>[a(Fe,null,{header:t(()=>l[19]||(l[19]=[r("div",{class:"card-header"},[r("span",null,"👥 用户等级分布")],-1)])),default:t(()=>[a(u.VChart,{class:"chart",option:u.levelChartOptions,autoresize:""},null,8,["option"])]),_:1})]),_:1}),a(Pe,{span:8},{default:t(()=>[a(Fe,null,{header:t(()=>l[20]||(l[20]=[r("div",{class:"card-header"},[r("span",null,"📱 设备类型")],-1)])),default:t(()=>[r("div",se,[(i(!0),e(d,null,c(u.deviceData,a=>(i(),e("div",{class:"device-item",key:a.type},[r("div",{class:V(["device-icon",a.type])},[r("i",{class:V(a.icon)},null,2)],2),r("div",le,[r("div",ne,f(a.name),1),r("div",ie,f(a.count)+"人",1),r("div",re,f(a.percentage)+"%",1)])]))),128))])]),_:1})]),_:1})]),_:1}),a(Fe,{style:{"margin-top":"20px"}},{header:t(()=>[r("div",oe,[l[21]||(l[21]=r("span",null,"🔍 用户行为分析",-1)),r("div",null,[a(Ee,{modelValue:u.behaviorDateRange,"onUpdate:modelValue":l[1]||(l[1]=e=>u.behaviorDateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",size:"small",onChange:u.updateBehaviorData},null,8,["modelValue"])])])]),default:t(()=>[a(ze,{gutter:20},{default:t(()=>[a(Pe,{span:8},{default:t(()=>[r("div",de,[l[22]||(l[22]=r("h4",null,"🚀 功能使用排行",-1)),r("div",ce,[(i(!0),e(d,null,c(u.featureUsage,(a,t)=>(i(),e("div",{class:"feature-item",key:a.name},[r("div",ue,f(t+1),1),r("div",pe,[r("div",ve,f(a.name),1),r("div",me,f(a.usage)+"次使用",1)]),r("div",ge,[r("div",{class:"feature-progress",style:C({width:a.percentage+"%"})},null,4)])]))),128))])])]),_:1}),a(Pe,{span:8},{default:t(()=>[r("div",_e,[l[23]||(l[23]=r("h4",null,"⏰ 活跃时段分析",-1)),a(u.VChart,{class:"mini-chart",option:u.timeAnalysisChart,autoresize:""},null,8,["option"])])]),_:1}),a(Pe,{span:8},{default:t(()=>[r("div",he,[l[25]||(l[25]=r("h4",null,"📊 用户留存分析",-1)),r("div",fe,[l[24]||(l[24]=r("div",{class:"retention-header"},[r("span",null,"时期"),r("span",null,"新增用户"),r("span",null,"次日留存"),r("span",null,"7日留存")],-1)),(i(!0),e(d,null,c(u.retentionData,a=>(i(),e("div",{class:"retention-row",key:a.date},[r("span",null,f(a.date),1),r("span",null,f(a.new_users),1),r("span",ye,f(a.day1_retention)+"%",1),r("span",be,f(a.day7_retention)+"%",1)]))),128))])])]),_:1})]),_:1})]),_:1}),a(Fe,{style:{"margin-top":"20px"}},{header:t(()=>[r("div",Ce,[l[27]||(l[27]=r("span",null,"👤 用户画像分析",-1)),a(Le,{type:"primary",onClick:u.showUserSegmentDialog},{default:t(()=>l[26]||(l[26]=[o("创建用户分群",-1)])),_:1,__:[26]})])]),default:t(()=>[a(ze,{gutter:20},{default:t(()=>[a(Pe,{span:12},{default:t(()=>[r("div",Ve,[l[28]||(l[28]=r("h4",null,"📈 消费能力分布",-1)),a(u.VChart,{class:"chart",option:u.consumptionChart,autoresize:""},null,8,["option"])])]),_:1}),a(Pe,{span:12},{default:t(()=>[r("div",we,[l[29]||(l[29]=r("h4",null,"🎂 年龄段分布",-1)),r("div",De,[(i(!0),e(d,null,c(u.ageData,a=>(i(),e("div",{class:"age-item",key:a.range},[r("div",xe,f(a.range),1),r("div",Se,[r("div",{class:"age-progress",style:C({width:a.percentage+"%",background:a.color})},null,4)]),r("div",je,f(a.percentage)+"%",1)]))),128))])])]),_:1})]),_:1})]),_:1}),a(Ne,{title:"创建用户分群",modelValue:u.segmentDialog.visible,"onUpdate:modelValue":l[4]||(l[4]=e=>u.segmentDialog.visible=e),width:"800px"},{footer:t(()=>[r("div",Ae,[a(Le,{onClick:l[3]||(l[3]=e=>u.segmentDialog.visible=!1)},{default:t(()=>l[31]||(l[31]=[o("取消",-1)])),_:1,__:[31]}),a(Le,{type:"primary",onClick:u.createSegment},{default:t(()=>l[32]||(l[32]=[o("创建分群",-1)])),_:1,__:[32]})])]),default:t(()=>[a(Be,{model:u.segmentForm,"label-width":"100px"},{default:t(()=>[a(qe,{label:"分群名称"},{default:t(()=>[a(Re,{modelValue:u.segmentForm.name,"onUpdate:modelValue":l[2]||(l[2]=e=>u.segmentForm.name=e),placeholder:"请输入分群名称"},null,8,["modelValue"])]),_:1}),a(qe,{label:"分群条件"},{default:t(()=>[r("div",ke,[(i(!0),e(d,null,c(u.segmentForm.conditions,(s,l)=>(i(),e("div",{class:"condition-item",key:l},[a(Ie,{modelValue:s.field,"onUpdate:modelValue":e=>s.field=e,placeholder:"选择字段"},{default:t(()=>[a(Ge,{label:"注册时间",value:"register_time"}),a(Ge,{label:"最后登录",value:"last_login"}),a(Ge,{label:"消费金额",value:"consumption"}),a(Ge,{label:"用户等级",value:"level"}),a(Ge,{label:"地域",value:"region"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),a(Ie,{modelValue:s.operator,"onUpdate:modelValue":e=>s.operator=e,placeholder:"条件"},{default:t(()=>[a(Ge,{label:"等于",value:"="}),a(Ge,{label:"大于",value:">"}),a(Ge,{label:"小于",value:"<"}),a(Ge,{label:"包含",value:"in"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),a(Re,{modelValue:s.value,"onUpdate:modelValue":e=>s.value=e,placeholder:"值"},null,8,["modelValue","onUpdate:modelValue"]),a(Le,{type:"danger",onClick:e=>u.removeCondition(l),icon:"el-icon-delete",circle:""},null,8,["onClick"])]))),128)),a(Le,{type:"primary",onClick:u.addCondition,icon:"el-icon-plus"},{default:t(()=>l[30]||(l[30]=[o("添加条件",-1)])),_:1,__:[30]})])]),_:1}),a(qe,{label:"预览结果"},{default:t(()=>[r("div",Ue,[r("span",null,"预计匹配用户数："+f(u.segmentPreview.count),1),r("span",null,"占总用户比例："+f(u.segmentPreview.percentage)+"%",1)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-7e456d56"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/user/UserAnalytics.vue"]]);export{Fe as default};
