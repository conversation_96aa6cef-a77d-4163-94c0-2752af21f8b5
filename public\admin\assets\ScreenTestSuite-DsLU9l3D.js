import{l as e,q as s,C as t,F as n,Y as i,ag as a,r,c as o,o as l,I as c,m as u}from"./vue-vendor-BcnDv-68.js";import{_ as d}from"./index-eUTsTR3J.js";import{Z as h,s as m,X as p}from"./element-plus-C2UshkXo.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";class g{constructor(){this.issues=[],this.recommendations=[]}async runDiagnostics(){return this.issues=[],this.recommendations=[],this.checkViewportSize(),this.checkScrollbars(),this.checkOverflow(),this.checkZIndex(),this.checkFlexLayout(),this.checkGridLayout(),this.checkResponsiveBreakpoints(),{issues:this.issues,recommendations:this.recommendations,summary:this.generateSummary()}}checkViewportSize(){const e={width:window.innerWidth,height:window.innerHeight,devicePixelRatio:window.devicePixelRatio||1};return e.width<1200&&(this.issues.push({type:"warning",message:`屏幕宽度较小 (${e.width}px)，建议使用1200px以上的屏幕`,element:"viewport"}),this.recommendations.push("考虑使用响应式布局或移动端优化版本")),e.height<800&&(this.issues.push({type:"warning",message:`屏幕高度较小 (${e.height}px)，可能导致内容显示不完整`,element:"viewport"}),this.recommendations.push("启用垂直滚动或调整布局密度")),e}checkScrollbars(){const e=document.body,s=e.scrollWidth>e.clientWidth;e.scrollHeight,e.clientHeight;s&&(this.issues.push({type:"error",message:"检测到水平滚动条，可能导致布局错乱",element:"body"}),this.recommendations.push("检查元素宽度设置，确保不超过100vw"))}checkOverflow(){document.querySelectorAll(".data-screen, .ultra-data-screen, .enhanced-data-screen, .optimized-data-screen").forEach((e,s)=>{const t=e.getBoundingClientRect(),n=window.getComputedStyle(e);t.width>window.innerWidth&&this.issues.push({type:"error",message:`数据大屏 ${s+1} 宽度溢出 (${t.width}px > ${window.innerWidth}px)`,element:e}),t.height>window.innerHeight&&"auto"!==n.overflow&&this.issues.push({type:"warning",message:`数据大屏 ${s+1} 高度溢出且无滚动 (${t.height}px > ${window.innerHeight}px)`,element:e})})}checkZIndex(){document.querySelectorAll('[style*="z-index"], .fullscreen-mode').forEach(e=>{const s=window.getComputedStyle(e).zIndex;"auto"!==s&&parseInt(s)})}checkFlexLayout(){document.querySelectorAll(".data-screen, .screen-content, .charts-grid").forEach(e=>{if("flex"===window.getComputedStyle(e).display){const s=Array.from(e.children);0===s.reduce((e,s)=>e+(parseFloat(window.getComputedStyle(s).flexGrow)||0),0)&&s.length>0&&this.issues.push({type:"info",message:"Flex容器中没有flex-grow元素，可能导致空间分配问题",element:e})}})}checkGridLayout(){document.querySelectorAll(".metrics-grid, .charts-grid").forEach(e=>{if("grid"===window.getComputedStyle(e).display){e.getBoundingClientRect();0===e.children.length&&this.issues.push({type:"warning",message:"Grid容器为空",element:e})}})}checkResponsiveBreakpoints(){const e=window.innerWidth,s={xs:480,sm:768,md:1024,lg:1200,xl:1400,xxl:1600,xxxl:1920,xxxxl:2560};let t="xs";for(const[n,i]of Object.entries(s))e>=i&&(t=n);Array.from(document.styleSheets).flatMap(e=>{try{return Array.from(e.cssRules||[])}catch(s){return[]}}).filter(e=>e.type===CSSRule.MEDIA_RULE).some(e=>e.conditionText.includes(`${s[t]}px`))||this.issues.push({type:"warning",message:`当前断点 ${t} 可能缺少对应的CSS媒体查询规则`,element:"css"})}autoFix(){const e=[],s=document.body;s.scrollWidth>s.clientWidth&&(s.style.overflowX="hidden",e.push("隐藏水平滚动条"));return document.querySelectorAll(".data-screen, .ultra-data-screen, .enhanced-data-screen, .optimized-data-screen").forEach(s=>{const t=s.getBoundingClientRect();t.width>window.innerWidth&&(s.style.width="100vw",s.style.maxWidth="100vw",e.push("修复数据大屏宽度溢出")),t.height>window.innerHeight&&(s.style.height="100vh",s.style.overflowY="auto",e.push("修复数据大屏高度溢出"))}),e}generateSummary(){const e=this.issues.filter(e=>"error"===e.type).length,s=this.issues.filter(e=>"warning"===e.type).length,t=this.issues.filter(e=>"info"===e.type).length;return{total:this.issues.length,errors:e,warnings:s,info:t,status:e>0?"error":s>0?"warning":"success"}}printReport(){const e=this.generateSummary();return this.issues.length>0&&this.issues.forEach((e,s)=>{"error"===e.type||e.type}),this.recommendations.length>0&&this.recommendations.forEach((e,s)=>{}),e}}function w(){const e=new g;return{runDiagnostics:async()=>{const s=await e.runDiagnostics();return e.printReport(),s},autoFix:()=>e.autoFix(),diagnostics:e}}const v=()=>(new g).autoFix(),y={class:"screen-test-suite"},x={class:"screen-info"},f={class:"info-card"},k={class:"info-grid"},S={class:"info-item"},b={class:"value"},R={class:"info-item"},$={class:"value"},C={class:"info-item"},D={class:"info-item"},A={class:"value"},E={class:"version-tests"},F={class:"test-grid"},I={class:"version-header"},L={class:"version-icon"},j={class:"version-info"},z={class:"version-features"},V={class:"feature-list"},W={class:"version-actions"},_=["onClick","disabled"],B=["onClick"],P={key:0,class:"test-result"},T={class:"result-header"},O={class:"result-details"},q={key:0,class:"issues"},H={key:1,class:"recommendations"},U={class:"global-actions"},G=["disabled"],M={key:0,class:"test-logs"},Z={class:"log-container"},X={class:"log-time"},Y={class:"log-message"};const J=d({__name:"ScreenTestSuite",setup(e,{expose:s}){s();const t=a(),{runDiagnostics:n,autoFix:i}=w(),u=r(!1),d=r(""),h=r({}),m=r([]),g=r({width:window.innerWidth,height:window.innerHeight,devicePixelRatio:window.devicePixelRatio||1}),y=o(()=>{const e=g.value.width;return e>=2560?"xxxxl":e>=1920?"xxxl":e>=1600?"xxl":e>=1400?"xl":e>=1200?"lg":e>=768?"md":e>=480?"sm":"xs"}),x=o(()=>{const e=y.value;return["xxxxl","xxxl"].includes(e)?"ultra":["xxl","xl"].includes(e)?"optimized":["lg","md"].includes(e)?"enhanced":"classic"}),f=r([{key:"optimized",name:"OptimizedDataScreen",description:"最新优化版本，完整响应式支持",icon:"🚀",path:"/data-screen/optimized",features:["响应式","自动修复","全屏模式","诊断工具"],status:"excellent"},{key:"ultra",name:"UltraDataScreen",description:"3D动画效果，适合大屏展示",icon:"⚡",path:"/data-screen/ultra",features:["3D动画","高级视觉","大屏优化"],status:"good"},{key:"enhanced",name:"EnhancedDataScreen",description:"现代化设计，平衡性能和视觉",icon:"🌟",path:"/data-screen/enhanced",features:["现代设计","性能优化","视觉平衡"],status:"good"},{key:"classic",name:"DataScreen",description:"经典版本，稳定可靠",icon:"🛡️",path:"/data-screen",features:["稳定可靠","兼容性好","轻量级"],status:"stable"}]),k=()=>{g.value={width:window.innerWidth,height:window.innerHeight,devicePixelRatio:window.devicePixelRatio||1}},S=(e,s="info")=>{m.value.unshift({id:Date.now(),time:(new Date).toLocaleTimeString(),message:e,type:s}),m.value.length>50&&(m.value=m.value.slice(0,50))},b=e=>({excellent:"优秀",good:"良好",stable:"稳定",warning:"警告",error:"错误"}[e]||"未知"),R=async e=>{u.value=!0,d.value=e.key;try{S(`开始测试 ${e.name}`,"info"),await new Promise(e=>setTimeout(e,1e3));const s=await n();h.value[e.key]=s;const t=s.summary.status;S(`${e.name} 测试完成 - ${b(t)}`,t),"success"===t?p.success(`${e.name} 显示正常`):p.warning(`${e.name} 发现 ${s.summary.total} 个问题`)}catch(s){S(`${e.name} 测试失败: ${s.message}`,"error"),p.error("测试失败")}finally{u.value=!1,d.value=""}};l(()=>{k(),window.addEventListener("resize",k),S("数据大屏测试套件已启动","info")}),c(()=>{window.removeEventListener("resize",k)});const $={router:t,runDiagnostics:n,autoFix:i,testing:u,currentTest:d,testResults:h,logs:m,screenInfo:g,currentBreakpoint:y,recommendedVersion:x,versions:f,updateScreenInfo:k,addLog:S,getStatusText:b,testVersion:R,testAllVersions:async()=>{for(const e of f.value)await R(e),await new Promise(e=>setTimeout(e,500));S("所有版本测试完成","success"),p.success("所有版本测试完成")},autoFixAll:()=>{try{const e=v();e.length>0?(S(`应用了 ${e.length} 个自动修复`,"success"),p.success(`已应用 ${e.length} 个修复`)):(S("未发现需要修复的问题","info"),p.info("未发现需要修复的问题"))}catch(e){S(`自动修复失败: ${e.message}`,"error"),p.error("自动修复失败")}},openVersion:e=>{window.open(e.path,"_blank")},exportReport:()=>{const e={screenInfo:g.value,currentBreakpoint:y.value,recommendedVersion:x.value,testResults:h.value,logs:m.value,timestamp:(new Date).toISOString()},s=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),t=URL.createObjectURL(s),n=document.createElement("a");n.href=t,n.download=`data-screen-test-report-${Date.now()}.json`,n.click(),URL.revokeObjectURL(t),S("测试报告已导出","success"),p.success("测试报告已导出")},ref:r,computed:o,onMounted:l,onUnmounted:c,get ElMessage(){return p},get useRouter(){return a},get useScreenDiagnostics(){return w},get quickFix(){return v}};return Object.defineProperty($,"__isScriptSetup",{enumerable:!1,value:!0}),$}},[["render",function(a,r,o,l,c,d){return u(),e("div",y,[r[10]||(r[10]=s("div",{class:"test-header"},[s("h2",null,"📊 数据大屏显示测试套件"),s("p",null,"测试不同版本数据大屏在当前屏幕下的显示效果")],-1)),s("div",x,[s("div",f,[r[4]||(r[4]=s("h3",null,"🖥️ 屏幕信息",-1)),s("div",k,[s("div",S,[r[0]||(r[0]=s("span",{class:"label"},"分辨率:",-1)),s("span",b,h(l.screenInfo.width)+" × "+h(l.screenInfo.height),1)]),s("div",R,[r[1]||(r[1]=s("span",{class:"label"},"设备像素比:",-1)),s("span",$,h(l.screenInfo.devicePixelRatio),1)]),s("div",C,[r[2]||(r[2]=s("span",{class:"label"},"当前断点:",-1)),s("span",{class:m(["value breakpoint",l.currentBreakpoint])},h(l.currentBreakpoint),3)]),s("div",D,[r[3]||(r[3]=s("span",{class:"label"},"推荐版本:",-1)),s("span",A,h(l.recommendedVersion),1)])])])]),s("div",E,[r[8]||(r[8]=s("h3",null,"🎯 版本测试",-1)),s("div",F,[(u(!0),e(n,null,i(l.versions,a=>(u(),e("div",{key:a.key,class:m(["version-card",{recommended:a.key===l.recommendedVersion}])},[s("div",I,[s("div",L,h(a.icon),1),s("div",j,[s("h4",null,h(a.name),1),s("p",null,h(a.description),1)]),s("div",{class:m(["version-status",a.status])},h(l.getStatusText(a.status)),3)]),s("div",z,[s("div",V,[(u(!0),e(n,null,i(a.features,s=>(u(),e("span",{key:s,class:"feature-tag"},h(s),1))),128))])]),s("div",W,[s("button",{class:"test-btn",onClick:e=>l.testVersion(a),disabled:l.testing},h(l.testing&&l.currentTest===a.key?"测试中...":"测试显示"),9,_),s("button",{class:"preview-btn",onClick:e=>l.openVersion(a)}," 预览 ",8,B)]),l.testResults[a.key]?(u(),e("div",P,[s("div",T,[s("span",{class:m(["result-status",l.testResults[a.key].status])},h("success"===l.testResults[a.key].status?"✅":"warning"===l.testResults[a.key].status?"⚠️":"❌"),3),r[5]||(r[5]=s("span",null,"测试结果",-1))]),s("div",O,[l.testResults[a.key].issues.length>0?(u(),e("div",q,[r[6]||(r[6]=s("strong",null,"发现问题:",-1)),s("ul",null,[(u(!0),e(n,null,i(l.testResults[a.key].issues,s=>(u(),e("li",{key:s.message},h(s.message),1))),128))])])):t("",!0),l.testResults[a.key].recommendations.length>0?(u(),e("div",H,[r[7]||(r[7]=s("strong",null,"优化建议:",-1)),s("ul",null,[(u(!0),e(n,null,i(l.testResults[a.key].recommendations,s=>(u(),e("li",{key:s},h(s),1))),128))])])):t("",!0)])])):t("",!0)],2))),128))])]),s("div",U,[s("button",{class:"action-btn primary",onClick:l.testAllVersions,disabled:l.testing}," 🔍 测试所有版本 ",8,G),s("button",{class:"action-btn",onClick:l.autoFixAll}," 🔧 自动修复问题 "),s("button",{class:"action-btn",onClick:l.exportReport}," 📄 导出报告 ")]),l.logs.length>0?(u(),e("div",M,[r[9]||(r[9]=s("h3",null,"📝 测试日志",-1)),s("div",Z,[(u(!0),e(n,null,i(l.logs,t=>(u(),e("div",{key:t.id,class:m(["log-item",t.type])},[s("span",X,h(t.time),1),s("span",Y,h(t.message),1)],2))),128))])])):t("",!0)])}],["__scopeId","data-v-b45f1bfc"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/dev-tools/ScreenTestSuite.vue"]]);export{J as default};
