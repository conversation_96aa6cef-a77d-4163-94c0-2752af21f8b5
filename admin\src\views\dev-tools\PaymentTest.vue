<template>
  <div class="payment-test">
    <div class="page-header">
      <h1>支付功能测试</h1>
      <p>测试所有支付相关功能是否正常工作</p>
    </div>

    <div class="test-sections">
      <!-- API测试 -->
      <el-card class="test-card">
        <template #header>
          <h3>API功能测试</h3>
        </template>
        
        <div class="test-buttons">
          <el-button type="primary" @click="testGetConfig" :loading="testing.config">
            测试获取支付配置
          </el-button>
          <el-button type="success" @click="testUpdateConfig" :loading="testing.update">
            测试更新支付配置
          </el-button>
          <el-button type="warning" @click="testToggleMethod" :loading="testing.toggle">
            测试切换支付方式
          </el-button>
          <el-button type="info" @click="testChannelTest" :loading="testing.channel">
            测试支付通道
          </el-button>
          <el-button type="danger" @click="testGetStats" :loading="testing.stats">
            测试获取统计数据
          </el-button>
        </div>
      </el-card>

      <!-- 测试结果 -->
      <el-card class="test-card">
        <template #header>
          <h3>测试结果</h3>
        </template>
        
        <div class="test-results">
          <div v-for="(result, index) in testResults" :key="index" class="test-result">
            <div class="result-header">
              <span class="result-time">{{ result.time }}</span>
              <el-tag :type="result.success ? 'success' : 'danger'">
                {{ result.success ? '成功' : '失败' }}
              </el-tag>
            </div>
            <div class="result-content">
              <strong>{{ result.test }}</strong>: {{ result.message }}
            </div>
            <div v-if="result.data" class="result-data">
              <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 当前配置状态 -->
      <el-card class="test-card">
        <template #header>
          <h3>当前支付配置状态</h3>
        </template>
        
        <div class="config-status">
          <div class="status-item">
            <strong>支付宝:</strong>
            <el-tag :type="currentConfig.alipay?.enabled ? 'success' : 'info'">
              {{ currentConfig.alipay?.enabled ? '已启用' : '未启用' }}
            </el-tag>
          </div>
          <div class="status-item">
            <strong>微信支付:</strong>
            <el-tag :type="currentConfig.wechat?.enabled ? 'success' : 'info'">
              {{ currentConfig.wechat?.enabled ? '已启用' : '未启用' }}
            </el-tag>
          </div>
          <div class="status-item">
            <strong>易支付:</strong>
            <el-tag :type="currentConfig.easypay?.enabled ? 'success' : 'info'">
              {{ currentConfig.easypay?.enabled ? '已启用' : '未启用' }}
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getPaymentConfig,
  updatePaymentConfig,
  togglePaymentMethod,
  testPaymentChannel,
  getPaymentStats
} from '@/api/payment'

// 响应式数据
const testing = reactive({
  config: false,
  update: false,
  toggle: false,
  channel: false,
  stats: false
})

const testResults = ref([])
const currentConfig = ref({})

// 测试方法
const addTestResult = (test, success, message, data = null) => {
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    test,
    success,
    message,
    data
  })
  
  // 只保留最近10条结果
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10)
  }
}

const testGetConfig = async () => {
  testing.config = true
  try {
    const response = await getPaymentConfig()
    currentConfig.value = response.data
    addTestResult('获取支付配置', true, '成功获取支付配置', response.data)
    ElMessage.success('获取支付配置成功')
  } catch (error) {
    addTestResult('获取支付配置', false, error.message)
    ElMessage.error('获取支付配置失败')
  } finally {
    testing.config = false
  }
}

const testUpdateConfig = async () => {
  testing.update = true
  try {
    const testConfig = {
      alipay: { enabled: true, app_id: 'test123' },
      wechat: { enabled: false }
    }
    const response = await updatePaymentConfig(testConfig)
    addTestResult('更新支付配置', true, '成功更新支付配置', response.data)
    ElMessage.success('更新支付配置成功')
  } catch (error) {
    addTestResult('更新支付配置', false, error.message)
    ElMessage.error('更新支付配置失败')
  } finally {
    testing.update = false
  }
}

const testToggleMethod = async () => {
  testing.toggle = true
  try {
    const response = await togglePaymentMethod('alipay', true)
    addTestResult('切换支付方式', true, '成功切换支付宝状态', response.data)
    ElMessage.success('切换支付方式成功')
  } catch (error) {
    addTestResult('切换支付方式', false, error.message)
    ElMessage.error('切换支付方式失败')
  } finally {
    testing.toggle = false
  }
}

const testChannelTest = async () => {
  testing.channel = true
  try {
    const testData = {
      amount: 0.01,
      subject: '测试支付',
      out_trade_no: `test_${Date.now()}`
    }
    const response = await testPaymentChannel('alipay', testData)
    addTestResult('支付通道测试', true, '支付通道测试成功', response.data)
    ElMessage.success('支付通道测试成功')
  } catch (error) {
    addTestResult('支付通道测试', false, error.message)
    ElMessage.error('支付通道测试失败')
  } finally {
    testing.channel = false
  }
}

const testGetStats = async () => {
  testing.stats = true
  try {
    const response = await getPaymentStats()
    addTestResult('获取支付统计', true, '成功获取支付统计数据', response.data)
    ElMessage.success('获取支付统计成功')
  } catch (error) {
    addTestResult('获取支付统计', false, error.message)
    ElMessage.error('获取支付统计失败')
  } finally {
    testing.stats = false
  }
}

// 页面加载时自动测试获取配置
onMounted(() => {
  testGetConfig()
})
</script>

<style scoped>
.payment-test {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.page-header p {
  margin: 0;
  color: #7f8c8d;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.test-card {
  width: 100%;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.test-results {
  max-height: 400px;
  overflow-y: auto;
}

.test-result {
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 10px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-time {
  font-size: 12px;
  color: #999;
}

.result-content {
  margin-bottom: 8px;
}

.result-data {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.result-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.config-status {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
