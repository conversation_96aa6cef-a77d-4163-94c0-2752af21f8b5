/* empty css             *//* empty css                   *//* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                  *//* empty css                  *//* empty css               *//* empty css                        *//* empty css                 *//* empty css                *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                  */import{l as e,q as a,G as t,C as l,A as i,B as r,F as s,Y as o,r as n,M as d,a3 as u,o as c,m as p,E as m,K as g,W as v,z as f,D as _,H as b}from"./vue-vendor-BcnDv-68.js";import{a7 as y,X as h,ah as w,a1 as k,V as C,aZ as V,W as j,aP as U,b1 as D,aS as P,bz as S,av as q,aw as x,at as L,aa as M,bI as A,b9 as T,az as F,aF as $,af as z,ax as G,au as I,ag as Q,a2 as B,a3 as O,a4 as E,a5 as R,a6 as N,Z as K,u as W,s as Z,ai as H,aj as X,aG as Y,ak as J,al as ee,aK as ae}from"./element-plus-C2UshkXo.js";import{a as te}from"./index-D4AyIzGN.js";import{P as le}from"./index-B0-VWyD5.js";import{_ as ie}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";/* empty css                      */import"./echarts-D6CUuNS9.js";const re=e=>te.get("/admin/distributors",{params:e}),se=()=>te.get("/admin/distributors/stats"),oe=e=>te.post("/admin/distributors",e),ne=(e,a)=>te.put(`/admin/distributors/${e}`,a),de=e=>te.delete(`/admin/distributors/${e}`),ue=()=>te.get("/admin/distribution-groups"),ce=(e,a)=>te.put(`/admin/distributors/${e}/level`,a),pe=(e,a)=>te.put(`/admin/distributors/${e}/group`,a),me=(e,a)=>te.put(`/admin/distributors/${e}/status`,a),ge={class:"modern-distributor-list"},ve={class:"page-header"},fe={class:"header-content"},_e={class:"header-left"},be={class:"header-icon"},ye={class:"header-actions"},he={key:0,class:"batch-actions"},we={class:"batch-buttons"},ke={class:"stats-section"},Ce={class:"stats-container"},Ve={class:"stat-content"},je={class:"stat-value"},Ue={class:"stat-label"},De={class:"card-header"},Pe={class:"header-left"},Se={class:"header-right"},qe={class:"distributor-info"},xe={class:"distributor-details"},Le={class:"distributor-name"},Me={class:"distributor-email"},Ae={class:"distributor-id"},Te={class:"dialog-footer"};const Fe=ie({__name:"DistributorList",setup(e,{expose:a}){a();const t=n(!0),l=n(!0),i=n([]),r=n(!0),s=n(!0),o=n(0),p=n([]),m=n([]),g=n(null),v=n("table"),f=n([{key:"total",label:"总分销员",value:"0",icon:"UserFilled",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"ArrowUp",change:"+12%"},{key:"today",label:"今日新增",value:"0",icon:"Plus",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"ArrowUp",change:"+8"},{key:"commission",label:"累计佣金",value:"¥0",icon:"CreditCard",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"ArrowUp",change:"+15%"},{key:"children",label:"总下级数",value:"0",icon:"Share",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"ArrowUp",change:"+89"}]),_=d({stats:{},queryParams:{page:1,limit:10,keyword:void 0,distribution_group_id:void 0,level:void 0},dialog:{visible:!1,title:"",type:"add"},form:{},rules:{name:[{required:!0,message:"用户名不能为空",trigger:"blur"}],email:[{required:!0,message:"邮箱不能为空",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],password:[{required:!0,message:"密码不能为空",trigger:"blur"}],distribution_group_id:[{required:!0,message:"必须选择一个分销组",trigger:"change"}],level:[{required:!0,message:"必须选择一个等级",trigger:"change"}]}}),{stats:b,queryParams:y,dialog:w,form:k,rules:C}=u(_),V=e=>e?parseFloat(e).toFixed(2):"0.00";async function j(){t.value=!0;try{await new Promise(e=>setTimeout(e,500)),p.value=[{id:1,name:"张三",email:"<EMAIL>",level:1,status:"active",distribution_group:{name:"华东区"},children_count:15,total_commission:8650.5,created_at:"2024-01-15 10:30:00"},{id:2,name:"李四",email:"<EMAIL>",level:2,status:"active",distribution_group:{name:"华南区"},children_count:23,total_commission:12450.8,created_at:"2024-01-10 14:20:00"},{id:3,name:"王五",email:"<EMAIL>",level:3,status:"inactive",distribution_group:null,children_count:8,total_commission:5230.2,created_at:"2024-01-08 09:15:00"}],o.value=3}catch(e){h.error("加载分销员列表失败")}finally{t.value=!1}}async function B(){l.value=!0;try{await new Promise(e=>setTimeout(e,300)),b.value={total_distributors:156,new_distributors_today:8,total_commission:285650.5,total_children:89},f.value[0].value=b.value.total_distributors.toString(),f.value[1].value=b.value.new_distributors_today.toString(),f.value[2].value="¥"+V(b.value.total_commission),f.value[3].value=b.value.total_children.toString()}catch(e){h.error("加载统计数据失败")}finally{l.value=!1}}async function O(){try{await new Promise(e=>setTimeout(e,200)),m.value=[{id:1,name:"华东区"},{id:2,name:"华南区"},{id:3,name:"华北区"},{id:4,name:"西南区"}]}catch(e){h.error("加载分销组选项失败")}}function E(){y.value.page=1,j()}function R(){k.value={name:"",email:"",password:"",status:"active",level:1,distribution_group_id:null},g.value&&g.value.resetFields()}c(()=>{j(),B(),O()});const N={loading:t,statsLoading:l,selectedDistributors:i,single:r,multiple:s,total:o,distributorList:p,groupOptions:m,formRef:g,viewMode:v,distributorStatCards:f,levelMap:{1:"初级分销员",2:"中级分销员",3:"高级分销员",4:"金牌分销员"},data:_,stats:b,queryParams:y,dialog:w,form:k,rules:C,formatNumber:V,getLevelTagType:e=>({1:"info",2:"success",3:"warning",4:"danger"}[e]||"info"),getList:j,getStats:B,fetchGroupOptions:O,handleQuery:E,resetQuery:function(){y.value={page:1,limit:10,keyword:void 0,distribution_group_id:void 0,level:void 0},E()},handleSelectionChange:function(e){const a=Array.isArray(e)?e:[];i.value=a,r.value=1!==a.length,s.value=!a.length},resetForm:R,handleExport:async()=>{try{h.success("导出功能开发中...")}catch(e){h.error("导出失败")}},handleBatchDelete:async()=>{if(0!==i.value.length)try{await Q.confirm(`确定要删除选中的 ${i.value.length} 个分销商吗？`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),h.success("批量删除成功"),j(),B()}catch(e){"cancel"!==e&&h.error("批量删除失败")}else h.warning("请选择要删除的分销商")},handleAdd:function(){R(),w.value={visible:!0,title:"新增分销员",type:"add"}},handleUpdate:function(e){R();const a=e||i.value[0];k.value={...a,password:""},w.value={visible:!0,title:`编辑分销员 - ${a.name}`,type:"edit"}},handleAssignGroup:function(e){const a=e||i.value[0];k.value={id:a.id,name:a.name,distribution_group_id:a.distribution_group_id},w.value={visible:!0,title:`分配分组 - ${a.name}`,type:"assign_group"}},handleUpgrade:function(e){const a=e||i.value[0];k.value={id:a.id,name:a.name,level:a.level},w.value={visible:!0,title:`等级变更 - ${a.name}`,type:"upgrade"}},handleDelete:async function(e){const a=e?[e.id]:i.value.map(e=>e.id);await Q.confirm(`是否确认删除ID为"${a.join(",")}"的分销员?`,"警告",{type:"warning"}),await de({ids:a}),h.success("删除成功"),j(),B()},handleStatusChange:async function(e){const a="active"===e.status?"启用":"禁用";try{await Q.confirm(`确认要"${a}"分销员"${e.name}"吗?`,"警告",{type:"warning"}),await me(e.id,{status:e.status}),h.success(a+"成功")}catch{e.status="active"===e.status?"inactive":"active"}},cancel:function(){w.value.visible=!1,R()},submitForm:async function(){await g.value.validate();try{switch(w.value.type){case"add":await oe(k.value),h.success("新增成功");break;case"edit":await ne(k.value.id,k.value),h.success("修改成功");break;case"assign_group":await pe(k.value.id,{distribution_group_id:k.value.distribution_group_id}),h.success("分配成功");break;case"upgrade":await ce(k.value.id,{level:k.value.level}),h.success("等级变更成功")}w.value.visible=!1,j(),B()}catch(e){}},ref:n,reactive:d,onMounted:c,toRefs:u,get ElMessage(){return h},get ElMessageBox(){return Q},get UserFilled(){return I},get Download(){return G},get Plus(){return z},get Search(){return $},get RefreshLeft(){return F},get Setting(){return T},get Top(){return A},get Delete(){return M},get ArrowUp(){return L},get List(){return x},get Grid(){return q},get Share(){return S},get CreditCard(){return P},get TrendCharts(){return D},get Medal(){return U},get getDistributors(){return re},get getDistributorStats(){return se},get addDistributor(){return oe},get updateDistributor(){return ne},get deleteDistributor(){return de},get getDistributionGroups(){return ue},get updateDistributorLevel(){return ce},get updateDistributorGroup(){return pe},get updateDistributorStatus(){return me},Pagination:le};return Object.defineProperty(N,"__isScriptSetup",{enumerable:!1,value:!0}),N}},[["render",function(n,d,u,c,h,U){const D=w,P=k,S=E,q=O,x=N,L=R,M=B,A=C,T=V,F=J,$=ae,z=X,G=Y,I=ee,Q=H,te=j,le=y;return p(),e("div",ge,[a("div",ve,[a("div",fe,[a("div",_e,[a("div",be,[t(D,{size:"24"},{default:i(()=>[t(c.UserFilled)]),_:1})]),d[16]||(d[16]=a("div",{class:"header-text"},[a("h1",null,"分销商管理"),a("p",null,"全面管理分销商信息，包括等级管理、分组分配和业绩统计")],-1))]),a("div",ye,[t(P,{onClick:c.handleExport,class:"action-btn secondary"},{default:i(()=>[t(D,null,{default:i(()=>[t(c.Download)]),_:1}),d[17]||(d[17]=m(" 导出数据 ",-1))]),_:1,__:[17]}),t(P,{type:"primary",onClick:c.handleAdd,class:"action-btn primary"},{default:i(()=>[t(D,null,{default:i(()=>[t(c.Plus)]),_:1}),d[18]||(d[18]=m(" 新增分销商 ",-1))]),_:1,__:[18]})])])]),t(A,{class:"filter-card"},{default:i(()=>[t(M,{inline:!0,model:c.queryParams,onSubmit:g(c.handleQuery,["prevent"])},{default:i(()=>[t(q,{label:"名称/邮箱"},{default:i(()=>[t(S,{modelValue:c.queryParams.keyword,"onUpdate:modelValue":d[0]||(d[0]=e=>c.queryParams.keyword=e),placeholder:"分销员名称或邮箱",clearable:"",onKeyup:v(c.handleQuery,["enter"]),class:"search-input"},null,8,["modelValue"])]),_:1}),t(q,{label:"分销组"},{default:i(()=>[t(L,{modelValue:c.queryParams.distribution_group_id,"onUpdate:modelValue":d[1]||(d[1]=e=>c.queryParams.distribution_group_id=e),placeholder:"全部分销组",clearable:"",class:"filter-select"},{default:i(()=>[(p(!0),e(s,null,o(c.groupOptions,e=>(p(),f(x,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(q,{label:"分销等级"},{default:i(()=>[t(L,{modelValue:c.queryParams.level,"onUpdate:modelValue":d[2]||(d[2]=e=>c.queryParams.level=e),placeholder:"全部分销等级",clearable:"",class:"filter-select"},{default:i(()=>[(p(),e(s,null,o(c.levelMap,(e,a)=>t(x,{key:a,label:e,value:a},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(q,null,{default:i(()=>[t(P,{type:"primary",onClick:c.handleQuery,class:"search-btn"},{default:i(()=>[t(D,null,{default:i(()=>[t(c.Search)]),_:1}),d[19]||(d[19]=m(" 查询 ",-1))]),_:1,__:[19]}),t(P,{onClick:c.resetQuery,class:"reset-btn"},{default:i(()=>[t(D,null,{default:i(()=>[t(c.RefreshLeft)]),_:1}),d[20]||(d[20]=m(" 重置 ",-1))]),_:1,__:[20]})]),_:1})]),_:1},8,["model"])]),_:1}),c.selectedDistributors.length>0?(p(),e("div",he,[t(T,{type:"info",closable:!1,class:"selection-alert"},{title:i(()=>[m(" 已选择 "+K(c.selectedDistributors.length)+" 个分销商 ",1)]),_:1}),a("div",we,[t(P,{type:"info",onClick:d[3]||(d[3]=e=>c.handleAssignGroup(c.selectedDistributors[0])),disabled:c.single},{default:i(()=>[t(D,null,{default:i(()=>[t(c.Setting)]),_:1}),d[21]||(d[21]=m(" 分配分组 ",-1))]),_:1,__:[21]},8,["disabled"]),t(P,{type:"warning",onClick:d[4]||(d[4]=e=>c.handleUpgrade(c.selectedDistributors[0])),disabled:c.single},{default:i(()=>[t(D,null,{default:i(()=>[t(c.Top)]),_:1}),d[22]||(d[22]=m(" 等级变更 ",-1))]),_:1,__:[22]},8,["disabled"]),t(P,{type:"danger",onClick:c.handleBatchDelete},{default:i(()=>[t(D,null,{default:i(()=>[t(c.Delete)]),_:1}),d[23]||(d[23]=m(" 批量删除 ",-1))]),_:1,__:[23]})])])):l("",!0),a("div",ke,[r((p(),e("div",Ce,[(p(!0),e(s,null,o(c.distributorStatCards,l=>(p(),e("div",{class:"stat-card",key:l.key},[a("div",{class:"stat-icon",style:W({background:l.color})},[t(D,{size:"20"},{default:i(()=>[(p(),f(_(l.icon)))]),_:2},1024)],4),a("div",Ve,[a("div",je,K(l.value),1),a("div",Ue,K(l.label),1)]),a("div",{class:Z(["stat-trend",l.trend])},[t(D,{size:"14"},{default:i(()=>[(p(),f(_(l.trendIcon)))]),_:2},1024),a("span",null,K(l.change),1)],2)]))),128))])),[[le,c.statsLoading]])]),t(A,{class:"table-card"},{header:i(()=>[a("div",De,[a("div",Pe,[d[24]||(d[24]=a("h3",null,"分销商列表",-1)),t(F,{size:"small",type:"info"},{default:i(()=>[m("共 "+K(c.total)+" 条记录",1)]),_:1})]),a("div",Se,[t($,null,{default:i(()=>[t(P,{size:"small",type:"table"===c.viewMode?"primary":"",onClick:d[5]||(d[5]=e=>c.viewMode="table")},{default:i(()=>[t(D,null,{default:i(()=>[t(c.List)]),_:1})]),_:1},8,["type"]),t(P,{size:"small",type:"card"===c.viewMode?"primary":"",onClick:d[6]||(d[6]=e=>c.viewMode="card")},{default:i(()=>[t(D,null,{default:i(()=>[t(c.Grid)]),_:1})]),_:1},8,["type"])]),_:1})])])]),default:i(()=>["table"===c.viewMode?r((p(),f(Q,{key:0,data:c.distributorList,onSelectionChange:c.handleSelectionChange,class:"modern-table"},{default:i(()=>[t(z,{type:"selection",width:"55",align:"center"}),t(z,{label:"分销员信息",width:"220"},{default:i(({row:e})=>[a("div",qe,[t(G,{size:40,class:"distributor-avatar"},{default:i(()=>[t(D,null,{default:i(()=>[t(c.UserFilled)]),_:1})]),_:1}),a("div",xe,[a("div",Le,K(e.name),1),a("div",Me,K(e.email),1),a("div",Ae,"ID: "+K(e.id),1)])])]),_:1}),t(z,{label:"分销等级",align:"center"},{default:i(({row:e})=>[t(F,{type:c.getLevelTagType(e.level)},{default:i(()=>[m(K(c.levelMap[e.level]||"未知"),1)]),_:2},1032,["type"])]),_:1}),t(z,{label:"分销组",align:"center"},{default:i(({row:e})=>[a("span",null,K(e.distribution_group?e.distribution_group.name:"未分配"),1)]),_:1}),t(z,{label:"团队",align:"center"},{default:i(({row:e})=>[a("div",null,"下级数: "+K(e.children_count||0),1)]),_:1}),t(z,{label:"业绩",align:"center"},{default:i(({row:e})=>[a("div",null,"累计佣金: ¥"+K(c.formatNumber(e.total_commission)),1)]),_:1}),t(z,{label:"状态",align:"center"},{default:i(({row:e})=>[t(I,{modelValue:e.status,"onUpdate:modelValue":a=>e.status=a,"active-value":"active","inactive-value":"inactive",onChange:a=>c.handleStatusChange(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(z,{label:"注册时间",prop:"created_at",width:"160"}),t(z,{label:"操作",width:"220",fixed:"right"},{default:i(({row:e})=>[t(P,{link:"",type:"primary",icon:"el-icon-edit",onClick:a=>c.handleUpdate(e)},{default:i(()=>d[25]||(d[25]=[m("编辑",-1)])),_:2,__:[25]},1032,["onClick"]),t(P,{link:"",type:"info",icon:"el-icon-s-operation",onClick:a=>c.handleAssignGroup(e)},{default:i(()=>d[26]||(d[26]=[m("分配",-1)])),_:2,__:[26]},1032,["onClick"]),t(P,{link:"",type:"warning",icon:"el-icon-top",onClick:a=>c.handleUpgrade(e)},{default:i(()=>d[27]||(d[27]=[m("等级",-1)])),_:2,__:[27]},1032,["onClick"]),t(P,{link:"",type:"danger",icon:"el-icon-delete",onClick:a=>c.handleDelete(e)},{default:i(()=>d[28]||(d[28]=[m("删除",-1)])),_:2,__:[28]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[le,c.loading]]):l("",!0),r(t(c.Pagination,{total:c.total,page:c.queryParams.page,"onUpdate:page":d[7]||(d[7]=e=>c.queryParams.page=e),limit:c.queryParams.limit,"onUpdate:limit":d[8]||(d[8]=e=>c.queryParams.limit=e),onPagination:c.getList},null,8,["total","page","limit"]),[[b,c.total>0]])]),_:1}),t(te,{title:c.dialog.title,modelValue:c.dialog.visible,"onUpdate:modelValue":d[15]||(d[15]=e=>c.dialog.visible=e),width:"600px","append-to-body":""},{footer:i(()=>[a("div",Te,[t(P,{onClick:c.cancel},{default:i(()=>d[29]||(d[29]=[m("取 消",-1)])),_:1,__:[29]}),t(P,{type:"primary",onClick:c.submitForm},{default:i(()=>d[30]||(d[30]=[m("确 定",-1)])),_:1,__:[30]})])]),default:i(()=>[t(M,{ref:"formRef",model:c.form,rules:c.rules,"label-width":"100px"},{default:i(()=>["add"===c.dialog.type||"edit"===c.dialog.type?(p(),e(s,{key:0},[t(q,{label:"用户名",prop:"name"},{default:i(()=>[t(S,{modelValue:c.form.name,"onUpdate:modelValue":d[9]||(d[9]=e=>c.form.name=e),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),t(q,{label:"邮箱",prop:"email"},{default:i(()=>[t(S,{modelValue:c.form.email,"onUpdate:modelValue":d[10]||(d[10]=e=>c.form.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),"add"===c.dialog.type?(p(),f(q,{key:0,label:"密码",prop:"password"},{default:i(()=>[t(S,{modelValue:c.form.password,"onUpdate:modelValue":d[11]||(d[11]=e=>c.form.password=e),type:"password",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})):l("",!0),"edit"===c.dialog.type?(p(),f(q,{key:1,label:"重置密码"},{default:i(()=>[t(S,{modelValue:c.form.password,"onUpdate:modelValue":d[12]||(d[12]=e=>c.form.password=e),type:"password",placeholder:"留空则不修改密码"},null,8,["modelValue"])]),_:1})):l("",!0)],64)):l("",!0),"assign_group"===c.dialog.type?(p(),e(s,{key:1},[t(q,{label:"分销商"},{default:i(()=>[a("span",null,K(c.form.name),1)]),_:1}),t(q,{label:"分销组",prop:"distribution_group_id"},{default:i(()=>[t(L,{modelValue:c.form.distribution_group_id,"onUpdate:modelValue":d[13]||(d[13]=e=>c.form.distribution_group_id=e),placeholder:"请选择分销组",style:{width:"100%"}},{default:i(()=>[(p(!0),e(s,null,o(c.groupOptions,e=>(p(),f(x,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})],64)):l("",!0),"upgrade"===c.dialog.type?(p(),e(s,{key:2},[t(q,{label:"分销商"},{default:i(()=>[a("span",null,K(c.form.name),1)]),_:1}),t(q,{label:"当前等级"},{default:i(()=>[t(F,{type:c.getLevelTagType(c.form.level)},{default:i(()=>[m(K(c.levelMap[c.form.level]||"未知"),1)]),_:1},8,["type"])]),_:1}),t(q,{label:"新等级",prop:"level"},{default:i(()=>[t(L,{modelValue:c.form.level,"onUpdate:modelValue":d[14]||(d[14]=e=>c.form.level=e),placeholder:"请选择新等级",style:{width:"100%"}},{default:i(()=>[(p(),e(s,null,o(c.levelMap,(e,a)=>t(x,{key:a,label:e,value:Number(a)},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})],64)):l("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}],["__scopeId","data-v-08a45ffe"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/distribution/DistributorList.vue"]]);export{Fe as default};
