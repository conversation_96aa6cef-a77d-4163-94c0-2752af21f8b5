<template>
  <div class="ultra-data-screen" :class="{ 'fullscreen': isFullscreen }">
    <!-- 动态背景系统 -->
    <div class="dynamic-background">
      <!-- 网格背景 -->
      <div class="grid-background"></div>
      
      <!-- 粒子系统 -->
      <div class="particle-field">
        <div v-for="i in 60" :key="i" 
             class="particle" 
             :style="getParticleStyle(i)">
        </div>
      </div>
      
      <!-- 能量波纹 -->
      <div class="energy-waves">
        <div class="wave wave-1"></div>
        <div class="wave wave-2"></div>
        <div class="wave wave-3"></div>
      </div>
      
      <!-- 数据流线条 -->
      <svg class="data-streams" viewBox="0 0 1920 1080">
        <defs>
          <linearGradient id="streamGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0" />
            <stop offset="50%" style="stop-color:#06b6d4;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:0" />
          </linearGradient>
          <filter id="streamGlow">
            <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        <!-- 动态数据流 -->
        <path d="M0,300 Q480,200 960,300 T1920,300" 
              stroke="url(#streamGradient)" 
              stroke-width="3" 
              fill="none" 
              filter="url(#streamGlow)">
          <animate attributeName="stroke-dasharray" 
                   values="0,2000;2000,0;0,2000" 
                   dur="8s" 
                   repeatCount="indefinite"/>
        </path>
        
        <path d="M0,600 Q640,500 1280,600 T1920,600" 
              stroke="url(#streamGradient)" 
              stroke-width="2" 
              fill="none" 
              filter="url(#streamGlow)">
          <animate attributeName="stroke-dasharray" 
                   values="0,1500;1500,0;0,1500" 
                   dur="6s" 
                   repeatCount="indefinite"/>
        </path>
      </svg>
    </div>

    <!-- 顶部控制面板 -->
    <div class="control-panel">
      <div class="panel-left">
        <!-- 系统Logo -->
        <div class="system-logo">
          <div class="logo-container">
            <div class="logo-core">
              <div class="core-inner"></div>
              <div class="core-ring"></div>
            </div>
            <div class="logo-text">
              <h1>晨鑫流量变现系统</h1>
              <span>智能数据中心</span>
            </div>
          </div>
        </div>
        
        <!-- 系统状态 -->
        <div class="system-status">
          <div class="status-item">
            <div class="status-dot online"></div>
            <span>系统正常</span>
          </div>
          <div class="status-item">
            <div class="sync-indicator">
              <div class="sync-pulse"></div>
            </div>
            <span>实时同步</span>
          </div>
        </div>
      </div>

      <div class="panel-center">
        <div class="main-title">
          <h2>实时运营数据大屏</h2>
          <div class="title-decoration">
            <div class="deco-line"></div>
            <div class="deco-dot"></div>
            <div class="deco-line"></div>
          </div>
        </div>
      </div>

      <div class="panel-right">
        <!-- 时间显示 -->
        <div class="time-section">
          <div class="current-time">{{ currentTime }}</div>
          <div class="current-date">{{ currentDate }}</div>
        </div>
        
        <!-- 控制按钮 -->
        <div class="control-actions">
          <button class="action-btn" @click="toggleFullscreen" :title="isFullscreen ? '退出全屏' : '全屏模式'">
            <i class="icon" :class="isFullscreen ? 'icon-compress' : 'icon-expand'"></i>
          </button>
          <button class="action-btn" @click="refreshAllData" :class="{ loading: isRefreshing }">
            <i class="icon icon-refresh"></i>
          </button>
          <button class="action-btn" @click="exportScreenData">
            <i class="icon icon-download"></i>
          </button>
          <button class="action-btn" @click="openSettings">
            <i class="icon icon-settings"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 核心KPI指标区 -->
    <div class="kpi-section">
      <div class="kpi-grid">
        <div v-for="kpi in kpiMetrics" 
             :key="kpi.key" 
             class="kpi-card"
             :style="{ '--kpi-color': kpi.color }">
          <div class="kpi-header">
            <div class="kpi-icon">
              <i :class="kpi.icon"></i>
            </div>
            <div class="kpi-trend" :class="kpi.trend.type">
              <i :class="kpi.trend.icon"></i>
              <span>{{ kpi.trend.value }}%</span>
            </div>
          </div>
          
          <div class="kpi-content">
            <div class="kpi-value">
              <CountUp :end="kpi.value" :duration="2" />
              <span class="kpi-unit">{{ kpi.unit }}</span>
            </div>
            <div class="kpi-label">{{ kpi.label }}</div>
            <div class="kpi-subtitle">{{ kpi.subtitle }}</div>
          </div>
          
          <div class="kpi-chart">
            <div class="mini-sparkline">
              <svg viewBox="0 0 100 30" class="sparkline-svg">
                <polyline 
                  :points="generateSparklinePoints(kpi.chartData)"
                  fill="none"
                  :stroke="kpi.color"
                  stroke-width="2"
                  opacity="0.8"/>
                <circle v-for="(point, index) in kpi.chartData" 
                        :key="index"
                        :cx="index * (100 / (kpi.chartData.length - 1))"
                        :cy="30 - (point / 100 * 30)"
                        r="1.5"
                        :fill="kpi.color"
                        opacity="0.6">
                  <animate attributeName="r" 
                           values="1.5;2.5;1.5" 
                           dur="2s" 
                           :begin="`${index * 0.2}s`"
                           repeatCount="indefinite"/>
                </circle>
              </svg>
            </div>
          </div>
          
          <!-- 进度环 -->
          <div class="kpi-progress">
            <svg viewBox="0 0 60 60" class="progress-ring">
              <circle cx="30" cy="30" r="25" 
                      fill="none" 
                      stroke="rgba(255,255,255,0.1)" 
                      stroke-width="4"/>
              <circle cx="30" cy="30" r="25" 
                      fill="none" 
                      :stroke="kpi.color" 
                      stroke-width="4"
                      stroke-dasharray="157"
                      :stroke-dashoffset="157 - (157 * kpi.progress / 100)"
                      class="progress-circle">
                <animate attributeName="stroke-dashoffset" 
                         :values="`157;${157 - (157 * kpi.progress / 100)}`"
                         dur="2s" 
                         fill="freeze"/>
              </circle>
            </svg>
            <div class="progress-text">{{ kpi.progress }}%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要图表区域 -->
    <div class="charts-grid">
      <!-- 收入趋势分析 -->
      <div class="chart-panel revenue-chart">
        <div class="panel-header">
          <div class="header-left">
            <h3>收入趋势分析</h3>
            <div class="chart-meta">
              <span class="meta-item">总收入: ¥{{ totalRevenue.toLocaleString() }}</span>
              <span class="meta-item">增长率: +{{ revenueGrowth }}%</span>
            </div>
          </div>
          <div class="header-right">
            <div class="time-selector">
              <button v-for="period in timePeriods" 
                      :key="period.value"
                      :class="['time-btn', { active: selectedPeriod === period.value }]"
                      @click="selectedPeriod = period.value">
                {{ period.label }}
              </button>
            </div>
          </div>
        </div>
        
        <div class="panel-content">
          <div class="revenue-visualization">
            <canvas ref="revenueCanvas" class="chart-canvas"></canvas>
            <div class="chart-overlay">
              <div class="data-points">
                <div v-for="(point, index) in revenueData" 
                     :key="index"
                     class="data-point"
                     :style="{ 
                       left: (index / (revenueData.length - 1)) * 100 + '%',
                       bottom: (point.value / maxRevenue) * 100 + '%'
                     }">
                  <div class="point-marker"></div>
                  <div class="point-tooltip">
                    <div class="tooltip-date">{{ point.date }}</div>
                    <div class="tooltip-value">¥{{ point.value.toLocaleString() }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户活跃度雷达图 -->
      <div class="chart-panel activity-radar">
        <div class="panel-header">
          <h3>用户活跃度分析</h3>
          <div class="activity-summary">
            <span class="summary-value">{{ overallActivity }}%</span>
            <span class="summary-label">综合活跃度</span>
          </div>
        </div>
        
        <div class="panel-content">
          <div class="radar-container">
            <svg viewBox="0 0 300 300" class="radar-svg">
              <!-- 雷达网格 -->
              <g class="radar-grid">
                <circle v-for="i in 5" 
                        :key="i"
                        cx="150" cy="150" 
                        :r="i * 25"
                        fill="none"
                        stroke="rgba(59, 130, 246, 0.2)"
                        stroke-width="1"/>
                
                <!-- 雷达轴线 -->
                <g class="radar-axes">
                  <line v-for="(axis, index) in radarAxes" 
                        :key="index"
                        x1="150" y1="150"
                        :x2="150 + Math.cos((index * 60 - 90) * Math.PI / 180) * 125"
                        :y2="150 + Math.sin((index * 60 - 90) * Math.PI / 180) * 125"
                        stroke="rgba(59, 130, 246, 0.3)"
                        stroke-width="1"/>
                </g>
              </g>
              
              <!-- 数据区域 -->
              <polygon :points="radarPoints"
                       fill="rgba(59, 130, 246, 0.2)"
                       stroke="#3b82f6"
                       stroke-width="2"
                       class="radar-area">
                <animate attributeName="opacity" 
                         values="0.2;0.4;0.2" 
                         dur="3s" 
                         repeatCount="indefinite"/>
              </polygon>
              
              <!-- 数据点 -->
              <circle v-for="(point, index) in radarDataPoints" 
                      :key="index"
                      :cx="point.x" 
                      :cy="point.y" 
                      r="4"
                      fill="#06b6d4"
                      class="radar-point">
                <animate attributeName="r" 
                         values="4;6;4" 
                         dur="2s" 
                         :begin="`${index * 0.3}s`"
                         repeatCount="indefinite"/>
              </circle>
              
              <!-- 轴标签 -->
              <text v-for="(axis, index) in radarAxes" 
                    :key="index"
                    :x="150 + Math.cos((index * 60 - 90) * Math.PI / 180) * 140"
                    :y="150 + Math.sin((index * 60 - 90) * Math.PI / 180) * 140"
                    text-anchor="middle"
                    dominant-baseline="middle"
                    fill="#94a3b8"
                    font-size="12"
                    class="axis-label">
                {{ axis.label }}
              </text>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template><script 
setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const currentTime = ref('')
const currentDate = ref('')
const selectedPeriod = ref('7d')
const isFullscreen = ref(false)
const isRefreshing = ref(false)

// 时间周期选项
const timePeriods = [
  { label: '今日', value: '1d' },
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' }
]

// KPI指标数据
const kpiMetrics = ref([
  {
    key: 'revenue',
    label: '总收入',
    subtitle: '较昨日',
    value: 1254890,
    unit: '元',
    icon: 'icon-money-circle',
    color: '#3b82f6',
    progress: 85,
    trend: { type: 'up', icon: 'icon-arrow-up', value: 12.5 },
    chartData: [65, 72, 68, 78, 85, 82, 90, 88, 95, 92]
  },
  {
    key: 'users',
    label: '活跃用户',
    subtitle: '在线用户',
    value: 8652,
    unit: '人',
    icon: 'icon-user-group',
    color: '#06b6d4',
    progress: 78,
    trend: { type: 'up', icon: 'icon-arrow-up', value: 8.3 },
    chartData: [45, 52, 48, 58, 65, 62, 70, 68, 75, 78]
  },
  {
    key: 'orders',
    label: '订单总数',
    subtitle: '今日新增',
    value: 15428,
    unit: '单',
    icon: 'icon-shopping-bag',
    color: '#8b5cf6',
    progress: 92,
    trend: { type: 'up', icon: 'icon-arrow-up', value: 15.2 },
    chartData: [55, 62, 58, 68, 75, 72, 80, 85, 88, 92]
  },
  {
    key: 'conversion',
    label: '转化率',
    subtitle: '平均转化',
    value: 23.8,
    unit: '%',
    icon: 'icon-trending-up',
    color: '#10b981',
    progress: 67,
    trend: { type: 'down', icon: 'icon-arrow-down', value: 2.1 },
    chartData: [72, 68, 65, 62, 58, 60, 63, 65, 67, 67]
  },
  {
    key: 'satisfaction',
    label: '满意度',
    subtitle: '用户评价',
    value: 94.2,
    unit: '%',
    icon: 'icon-heart',
    color: '#f59e0b',
    progress: 94,
    trend: { type: 'up', icon: 'icon-arrow-up', value: 3.7 },
    chartData: [88, 89, 91, 90, 92, 93, 94, 93, 94, 94]
  },
  {
    key: 'retention',
    label: '留存率',
    subtitle: '7日留存',
    value: 76.5,
    unit: '%',
    icon: 'icon-refresh',
    color: '#ef4444',
    progress: 76,
    trend: { type: 'up', icon: 'icon-arrow-up', value: 5.8 },
    chartData: [68, 70, 72, 71, 73, 74, 75, 76, 76, 76]
  }
])

// 收入数据
const revenueData = ref([
  { date: '01-01', value: 125000 },
  { date: '01-02', value: 138000 },
  { date: '01-03', value: 142000 },
  { date: '01-04', value: 156000 },
  { date: '01-05', value: 168000 },
  { date: '01-06', value: 175000 },
  { date: '01-07', value: 189000 }
])

// 雷达图数据
const radarAxes = ref([
  { label: '新用户', value: 85 },
  { label: '活跃度', value: 78 },
  { label: '留存率', value: 76 },
  { label: '付费率', value: 82 },
  { label: '推荐度', value: 88 },
  { label: '满意度', value: 94 }
])

// 计算属性
const totalRevenue = computed(() => {
  return revenueData.value.reduce((sum, item) => sum + item.value, 0)
})

const revenueGrowth = computed(() => {
  const current = revenueData.value[revenueData.value.length - 1]?.value || 0
  const previous = revenueData.value[revenueData.value.length - 2]?.value || 0
  return previous > 0 ? Math.round(((current - previous) / previous) * 100) : 0
})

const maxRevenue = computed(() => {
  return Math.max(...revenueData.value.map(item => item.value))
})

const overallActivity = computed(() => {
  const sum = radarAxes.value.reduce((acc, axis) => acc + axis.value, 0)
  return Math.round(sum / radarAxes.value.length)
})

const radarPoints = computed(() => {
  return radarAxes.value.map((axis, index) => {
    const angle = (index * 60 - 90) * Math.PI / 180
    const radius = (axis.value / 100) * 125
    const x = 150 + Math.cos(angle) * radius
    const y = 150 + Math.sin(angle) * radius
    return `${x},${y}`
  }).join(' ')
})

const radarDataPoints = computed(() => {
  return radarAxes.value.map((axis, index) => {
    const angle = (index * 60 - 90) * Math.PI / 180
    const radius = (axis.value / 100) * 125
    return {
      x: 150 + Math.cos(angle) * radius,
      y: 150 + Math.sin(angle) * radius
    }
  })
})

// 方法
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    weekday: 'long'
  })
}

const getParticleStyle = (index) => {
  const colors = ['#3b82f6', '#06b6d4', '#8b5cf6', '#10b981', '#f59e0b']
  return {
    left: Math.random() * 100 + '%',
    top: Math.random() * 100 + '%',
    animationDelay: Math.random() * 5 + 's',
    animationDuration: (Math.random() * 3 + 2) + 's',
    backgroundColor: colors[index % colors.length]
  }
}

const generateSparklinePoints = (data) => {
  return data.map((value, index) => {
    const x = index * (100 / (data.length - 1))
    const y = 30 - (value / 100 * 30)
    return `${x},${y}`
  }).join(' ')
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const refreshAllData = async () => {
  isRefreshing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 更新KPI数据
    kpiMetrics.value.forEach(kpi => {
      const change = (Math.random() - 0.5) * 20
      kpi.trend.value = Math.abs(Math.round(change * 10) / 10)
      kpi.trend.type = change > 0 ? 'up' : 'down'
      kpi.progress = Math.max(50, Math.min(100, kpi.progress + change))
    })
    
    // 更新雷达图数据
    radarAxes.value.forEach(axis => {
      axis.value = Math.max(60, Math.min(100, axis.value + (Math.random() - 0.5) * 10))
    })
    
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    isRefreshing.value = false
  }
}

const exportScreenData = () => {
  try {
    const exportData = {
      timestamp: new Date().toISOString(),
      kpiMetrics: kpiMetrics.value,
      revenueData: revenueData.value,
      radarData: radarAxes.value,
      summary: {
        totalRevenue: totalRevenue.value,
        revenueGrowth: revenueGrowth.value,
        overallActivity: overallActivity.value
      }
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
      type: 'application/json' 
    })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `数据大屏导出_${new Date().toISOString().slice(0, 10)}.json`
    link.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
  }
}

const openSettings = () => {
  ElMessage.info('设置面板开发中...')
}

// 生命周期
let timeInterval = null
let dataUpdateInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
  
  // 模拟实时数据更新
  dataUpdateInterval = setInterval(() => {
    // 随机更新一个KPI指标
    const randomIndex = Math.floor(Math.random() * kpiMetrics.value.length)
    const kpi = kpiMetrics.value[randomIndex]
    const change = (Math.random() - 0.5) * 2
    kpi.value = Math.max(0, kpi.value + Math.round(kpi.value * change / 100))
  }, 5000)
})

onUnmounted(() => {
  if (timeInterval) clearInterval(timeInterval)
  if (dataUpdateInterval) clearInterval(dataUpdateInterval)
})

// CountUp组件
const CountUp = {
  props: ['end', 'duration'],
  setup(props) {
    const displayValue = ref(0)
    
    onMounted(() => {
      const start = 0
      const end = props.end
      const duration = (props.duration || 2) * 1000
      const startTime = Date.now()
      
      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)
        
        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4)
        displayValue.value = Math.round(start + (end - start) * easeOutQuart)
        
        if (progress < 1) {
          requestAnimationFrame(animate)
        }
      }
      
      animate()
    })
    
    return { displayValue }
  },
  template: '<span>{{ displayValue.toLocaleString() }}</span>'
}
</script>

<style scoped>
@import '@/styles/data-screen-responsive.css';
.ultra-data-screen {
  height: 100vh;
  background:
    radial-gradient(ellipse at top left, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse at top right, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse at bottom, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0a0f1c 0%, #1a1f2e 25%, #0f1419 50%, #1e293b 75%, #0a0f1c 100%);
  color: #e2e8f0;
  position: relative;
  overflow: hidden;
  font-family: 'Inter', 'SF Pro Display', system-ui, sans-serif;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
}

.ultra-data-screen.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

/* 动态背景系统 */
.dynamic-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(59, 130, 246, 0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.08) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: gridFloat 25s linear infinite;
}

@keyframes gridFloat {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

.particle-field {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  animation: particleFloat 8s ease-in-out infinite;
  box-shadow: 0 0 6px currentColor;
}

@keyframes particleFloat {
  0%, 100% { 
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0;
  }
  10% { opacity: 1; }
  50% { 
    transform: translateY(-30px) translateX(15px) scale(1.2);
    opacity: 0.8;
  }
  90% { opacity: 1; }
}

.energy-waves {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.wave {
  position: absolute;
  border-radius: 50%;
  border: 1px solid;
  animation: waveExpand 6s ease-out infinite;
}

.wave-1 {
  top: 20%;
  left: 10%;
  width: 200px;
  height: 200px;
  border-color: rgba(59, 130, 246, 0.3);
  animation-delay: 0s;
}

.wave-2 {
  top: 60%;
  right: 20%;
  width: 150px;
  height: 150px;
  border-color: rgba(6, 182, 212, 0.3);
  animation-delay: 2s;
}

.wave-3 {
  bottom: 30%;
  left: 50%;
  width: 180px;
  height: 180px;
  border-color: rgba(139, 92, 246, 0.3);
  animation-delay: 4s;
}

@keyframes waveExpand {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.data-streams {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

/* 控制面板 */
.control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: rgba(10, 15, 28, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 10;
}

.panel-left {
  display: flex;
  align-items: center;
  gap: 32px;
}

.system-logo .logo-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-core {
  position: relative;
  width: 48px;
  height: 48px;
}

.core-inner {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #3b82f6, #06b6d4, #8b5cf6);
  border-radius: 50%;
  animation: coreRotate 10s linear infinite;
  position: relative;
}

.core-inner::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  background: #0a0f1c;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.core-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid transparent;
  border-top-color: rgba(59, 130, 246, 0.6);
  border-right-color: rgba(6, 182, 212, 0.6);
  border-radius: 50%;
  animation: ringRotate 3s linear infinite;
}

@keyframes coreRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes ringRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(-360deg); }
}

.logo-text h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 800;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-text span {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 24px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #94a3b8;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-dot.online {
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.sync-indicator {
  position: relative;
  width: 16px;
  height: 16px;
}

.sync-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #06b6d4;
  border-radius: 50%;
  animation: syncPulse 1.5s ease-in-out infinite;
}

.sync-pulse::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid rgba(6, 182, 212, 0.3);
  border-radius: 50%;
  animation: syncRing 1.5s ease-in-out infinite;
}

@keyframes syncPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(0.8); opacity: 0.7; }
}

@keyframes syncRing {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(1.5); opacity: 0; }
}

.panel-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.main-title h2 {
  margin: 0;
  font-size: 32px;
  font-weight: 900;
  text-align: center;
  background: linear-gradient(135deg, #60a5fa, #34d399, #a78bfa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(96, 165, 250, 0.3);
  animation: titleGlow 4s ease-in-out infinite;
}

@keyframes titleGlow {
  0%, 100% { 
    filter: drop-shadow(0 0 10px rgba(96, 165, 250, 0.4));
  }
  50% { 
    filter: drop-shadow(0 0 20px rgba(96, 165, 250, 0.8));
  }
}

.title-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 8px;
}

.deco-line {
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
}

.deco-dot {
  width: 6px;
  height: 6px;
  background: #3b82f6;
  border-radius: 50%;
  animation: dotPulse 2s ease-in-out infinite;
}

@keyframes dotPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.5); }
}

.panel-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

.time-section {
  text-align: right;
}

.current-time {
  font-size: 24px;
  font-weight: 700;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.current-date {
  font-size: 14px;
  color: #64748b;
  margin-top: 4px;
  font-weight: 500;
}

.control-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 40px;
  height: 40px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 10px;
  color: #60a5fa;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  color: #93c5fd;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn.loading {
  animation: buttonSpin 1s linear infinite;
}

@keyframes buttonSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* KPI指标区 */
.kpi-section {
  padding: 32px;
  position: relative;
  z-index: 5;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 24px;
}

.kpi-card {
  background: rgba(10, 15, 28, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 20px;
  padding: 24px;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  min-height: 200px;
}

.kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--kpi-color), transparent);
  border-radius: 20px 20px 0 0;
}

.kpi-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, var(--kpi-color)10, transparent 50%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.kpi-card:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(59, 130, 246, 0.2);
  border-color: var(--kpi-color);
}

.kpi-card:hover::after {
  opacity: 1;
}

.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.kpi-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--kpi-color), transparent);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.kpi-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 6px 10px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.kpi-trend.up {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.kpi-trend.down {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.kpi-content {
  margin-bottom: 20px;
}

.kpi-value {
  font-size: 36px;
  font-weight: 900;
  color: #e2e8f0;
  margin-bottom: 8px;
  text-shadow: 0 0 10px rgba(226, 232, 240, 0.3);
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.kpi-unit {
  font-size: 16px;
  color: #94a3b8;
  font-weight: 500;
}

.kpi-label {
  font-size: 16px;
  color: #cbd5e1;
  font-weight: 600;
  margin-bottom: 4px;
}

.kpi-subtitle {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.kpi-chart {
  position: absolute;
  right: 20px;
  bottom: 60px;
  width: 100px;
  height: 40px;
}

.mini-sparkline {
  width: 100%;
  height: 100%;
}

.sparkline-svg {
  width: 100%;
  height: 100%;
  overflow: visible;
}

.kpi-progress {
  position: absolute;
  right: 20px;
  bottom: 20px;
  width: 50px;
  height: 50px;
}

.progress-ring {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-circle {
  transition: stroke-dashoffset 2s ease-in-out;
  filter: drop-shadow(0 0 4px currentColor);
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  font-weight: 600;
  color: #94a3b8;
}

/* 图表网格 */
.charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  padding: 0 32px 32px;
  position: relative;
  z-index: 5;
}

.chart-panel {
  background: rgba(10, 15, 28, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-panel:hover {
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  background: rgba(15, 23, 42, 0.5);
}

.header-left h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
  color: #e2e8f0;
}

.chart-meta {
  display: flex;
  gap: 24px;
}

.meta-item {
  font-size: 14px;
  color: #94a3b8;
  font-weight: 500;
}

.time-selector {
  display: flex;
  gap: 8px;
}

.time-btn {
  padding: 8px 16px;
  background: transparent;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.time-btn:hover,
.time-btn.active {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  color: #60a5fa;
}

.activity-summary {
  text-align: right;
}

.summary-value {
  display: block;
  font-size: 32px;
  font-weight: 800;
  color: #60a5fa;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
}

.summary-label {
  font-size: 14px;
  color: #94a3b8;
  font-weight: 500;
}

.panel-content {
  padding: 28px;
}

/* 收入可视化 */
.revenue-visualization {
  position: relative;
  height: 300px;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

.chart-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.data-points {
  position: relative;
  width: 100%;
  height: 100%;
}

.data-point {
  position: absolute;
  transform: translate(-50%, 50%);
}

.point-marker {
  width: 8px;
  height: 8px;
  background: #06b6d4;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(6, 182, 212, 0.6);
  animation: pointPulse 2s ease-in-out infinite;
}

@keyframes pointPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.3); opacity: 0.8; }
}

.point-tooltip {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.data-point:hover .point-tooltip {
  opacity: 1;
}

.tooltip-date {
  color: #94a3b8;
  margin-bottom: 2px;
}

.tooltip-value {
  color: #e2e8f0;
  font-weight: 600;
}

/* 雷达图 */
.radar-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.radar-svg {
  width: 300px;
  height: 300px;
}

.radar-area {
  transition: all 0.3s ease;
}

.radar-point {
  transition: all 0.3s ease;
}

.axis-label {
  font-weight: 600;
  text-shadow: 0 0 4px rgba(148, 163, 184, 0.5);
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .kpi-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1200px) {
  .control-panel {
    flex-direction: column;
    gap: 16px;
    padding: 16px 24px;
  }
  
  .panel-left,
  .panel-right {
    width: 100%;
    justify-content: center;
  }
  
  .kpi-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .kpi-section {
    padding: 24px;
  }
  
  .charts-grid {
    padding: 0 24px 24px;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .ultra-data-screen {
    padding: 0;
  }
  
  .control-panel {
    padding: 12px 16px;
  }
  
  .main-title h2 {
    font-size: 24px;
  }
  
  .kpi-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .kpi-section {
    padding: 16px;
  }
  
  .charts-grid {
    padding: 0 16px 16px;
    gap: 12px;
  }
  
  .kpi-card {
    padding: 16px;
    min-height: 160px;
  }
  
  .kpi-value {
    font-size: 28px;
  }
  
  .panel-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .time-selector {
    width: 100%;
    justify-content: space-between;
  }
  
  .time-btn {
    flex: 1;
    text-align: center;
  }
}

/* 图标字体 */
.icon-money-circle::before { content: '💰'; }
.icon-user-group::before { content: '👥'; }
.icon-shopping-bag::before { content: '🛍️'; }
.icon-trending-up::before { content: '📈'; }
.icon-heart::before { content: '❤️'; }
.icon-refresh::before { content: '🔄'; }
.icon-arrow-up::before { content: '↗️'; }
.icon-arrow-down::before { content: '↘️'; }
.icon-expand::before { content: '⛶'; }
.icon-compress::before { content: '⛶'; }
.icon-refresh::before { content: '🔄'; }
.icon-download::before { content: '⬇️'; }
.icon-settings::before { content: '⚙️'; }
</style>