/* empty css             *//* empty css                   *//* empty css                         *//* empty css                 *//* empty css               *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css               *//* empty css                *//* empty css                        *//* empty css                  */import{l as e,m as a,r as l,w as t,n as s,o as n,I as i,q as r,G as o,A as c,F as d,Y as u,ag as p,E as f,z as v,D as h}from"./vue-vendor-BcnDv-68.js";import{u as m,ah as _,a1 as g,U as b,V as y,W as w,X as C,aB as k,bF as x,bz as j,bG as T,aO as D,ae as F,ax as S,b4 as P,aP as R,bH as A,be as M,b1 as z,aS as O,aR as U,aT as V,Z as q,s as I,Y as E,aK as W,ai as L,aj as Q,ak as G,aX as H,aY as Z,aZ as K}from"./element-plus-C2UshkXo.js";import{L as X}from"./LineChart-tPEDyfYE.js";import{i as Y,e as B}from"./echarts-D6CUuNS9.js";import{_ as N}from"./index-eUTsTR3J.js";import{f as $,h as J,i as ee,j as ae}from"./finance-CGlhYFvA.js";import{f as le}from"./format-3eU4VJ9V.js";import"./utils-SdQ7DxjY.js";import"./chunk-KZPPZA2C-C8HwxGb3.js";const te=N({__name:"PieChart",props:{data:{type:Object,required:!0},options:{type:Object,default:()=>({})},height:{type:Number,default:300}},setup(e,{expose:a}){a();const r=e,o=l();let c=null;const d=()=>{o.value&&(c=Y(o.value),u())},u=()=>{if(!c||!r.data)return;const e=r.data.datasets?.[0];if(!e)return;const a=r.data.labels?.map((a,l)=>({name:a,value:e.data[l],itemStyle:{color:e.backgroundColor[l]}}))||[],l={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:r.data.labels||[]},series:[{name:"数据",type:"pie",radius:"50%",data:a,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}],...r.options};c.setOption(l)},p=()=>{c&&c.resize()};t(()=>r.data,()=>{s(()=>{u()})},{deep:!0}),t(()=>r.options,()=>{s(()=>{u()})},{deep:!0}),n(()=>{s(()=>{d(),window.addEventListener("resize",p)})}),i(()=>{c&&c.dispose(),window.removeEventListener("resize",p)});const f={props:r,chartRef:o,get chartInstance(){return c},set chartInstance(e){c=e},initChart:d,updateChart:u,resizeChart:p,ref:l,onMounted:n,onUnmounted:i,watch:t,nextTick:s,get echarts(){return B}};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}},[["render",function(l,t,s,n,i,r){return a(),e("div",{ref:"chartRef",style:m({height:s.height+"px"})},null,4)}],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/Charts/PieChart.vue"]]),se={class:"modern-finance-dashboard"},ne={class:"page-header"},ie={class:"header-content"},re={class:"header-left"},oe={class:"header-icon"},ce={class:"header-actions"},de={class:"stats-section"},ue={class:"stats-container"},pe={class:"stat-content"},fe={class:"stat-value"},ve={class:"stat-label"},he={class:"card-header"},me={class:"chart-controls"},_e={class:"chart-content"},ge={class:"chart-content"},be={class:"quick-actions"},ye={class:"action-grid"},we={class:"card-header"},Ce={class:"recent-transactions"},ke={class:"transaction-info"},xe={class:"transaction-title"},je={class:"transaction-time"},Te={class:"card-header"},De={class:"money-text"},Fe={class:"money-text"},Se={class:"money-text"},Pe={class:"money-text"},Re={class:"money-text total"},Ae={class:"help-content"},Me={class:"help-section"},ze={class:"feature-item"},Oe={class:"feature-icon"},Ue={class:"feature-item"},Ve={class:"feature-icon"},qe={class:"feature-item"},Ie={class:"feature-icon"},Ee={class:"feature-item"},We={class:"feature-icon"},Le={class:"feature-item"},Qe={class:"feature-icon"},Ge={class:"feature-item"},He={class:"feature-icon"},Ze={class:"help-section"},Ke={class:"help-section"},Xe={class:"revenue-sources"},Ye={class:"source-item"},Be={class:"source-icon",style:{background:"#1890ff"}},Ne={class:"source-item"},$e={class:"source-icon",style:{background:"#52c41a"}},Je={class:"source-item"},ea={class:"source-icon",style:{background:"#faad14"}},aa={class:"source-item"},la={class:"source-icon",style:{background:"#f5222d"}},ta={class:"help-section"},sa={class:"guide-content"},na={class:"guide-content"},ia={class:"guide-content"},ra={class:"guide-content"},oa={class:"help-section"},ca={class:"risk-alerts"},da={class:"help-section"};const ua=N({__name:"FinanceDashboard",setup(e,{expose:a}){a();const t=p(),s=l({total_revenue:0,total_commission:0,pending_withdrawal:0,today_revenue:0,revenue_growth:0,commission_growth:0,today_growth:0,pending_count:0}),i=l([{key:"revenue",label:"总收入",value:"¥89,234",icon:"Money",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"CaretTop",change:"+18.5%"},{key:"commission",label:"总佣金",value:"¥23,456",icon:"Wallet",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"CaretTop",change:"+12.3%"},{key:"pending",label:"待提现",value:"¥5,678",icon:"CreditCard",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"CaretTop",change:"23笔申请"},{key:"today",label:"今日收入",value:"¥1,234",icon:"TrendCharts",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"CaretTop",change:"+25.8%"}]),r=l("7d"),o=l([]),c=l([]),d=l(!1),u=l(["view-data"]),f=l([]),v=l([{metric:"总收入",color:"primary",description:"平台所有收入来源的总和",calculation:"群组收入 + 佣金收入 + 分站收入 + 其他收入",significance:"反映平台整体盈利能力和业务规模"},{metric:"总佣金",color:"success",description:"支付给分销商的佣金总额",calculation:"所有分销商佣金的累计金额",significance:"体现分销体系的活跃度和激励效果"},{metric:"待提现",color:"warning",description:"用户申请但尚未处理的提现金额",calculation:"所有待审核提现申请的金额总和",significance:"反映资金流动性需求和风险控制情况"},{metric:"今日收入",color:"info",description:"当日产生的收入总额",calculation:"当日所有收入来源的实时统计",significance:"监控日常运营状况和收入波动"}]),h=l({labels:[],datasets:[{label:"收入",data:[],borderColor:"#1890ff",backgroundColor:"rgba(24, 144, 255, 0.1)",fill:!0,tension:.4}]}),m=l({labels:["群组收入","佣金收入","分站收入","其他收入"],datasets:[{data:[0,0,0,0],backgroundColor:["#1890ff","#52c41a","#faad14","#f5222d"]}]}),_=l({responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{callback:function(e){return"¥"+(e/1e3).toFixed(1)+"k"}}}}}),g=l({responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}}),b=async()=>{try{const{data:e}=await ae();s.value=e.overview,c.value=e.details}catch(e){C.error("获取财务统计失败")}},y=async()=>{try{const{data:e}=await ee({period:r.value});h.value=e.revenue,m.value=e.source}catch(e){}},w=async()=>{try{const{data:e}=await J({limit:10});o.value=e}catch(e){}},q=async()=>{await Promise.all([b(),y(),w()]),C.success("数据刷新成功")};n(()=>{q()});const I={router:t,stats:s,financeStatCards:i,selectedPeriod:r,recentTransactions:o,financeStats:c,showHelpDialog:d,activeGuides:u,activeFAQ:f,financeMetrics:v,timePeriods:[{label:"7天",value:"7d"},{label:"30天",value:"30d"},{label:"90天",value:"90d"},{label:"1年",value:"1y"}],revenueChartData:h,sourceChartData:m,chartOptions:_,pieChartOptions:g,fetchStats:b,fetchChartData:y,fetchRecentTransactions:w,refreshData:q,navigateToTransactions:()=>{t.push("/admin/finance/transactions")},navigateToCommissions:()=>{t.push("/admin/finance/commission")},navigateToWithdrawals:()=>{t.push("/admin/finance/withdraw")},handleExportReport:async()=>{try{await $(),C.success("报表导出成功")}catch(e){C.error("导出失败")}},formatMoney:e=>e>=1e4?(e/1e4).toFixed(1)+"W":e>=1e3?(e/1e3).toFixed(1)+"K":e.toFixed(2),ref:l,onMounted:n,get useRouter(){return p},get ElMessage(){return C},get Money(){return V},get Wallet(){return U},get CreditCard(){return O},get TrendCharts(){return z},get CaretTop(){return M},get Tickets(){return A},get Medal(){return R},get Upload(){return P},get Download(){return S},get Refresh(){return F},get QuestionFilled(){return D},get Comment(){return T},get Share(){return j},get Shop(){return x},get Star(){return k},LineChart:X,PieChart:te,get getFinanceStats(){return ae},get getFinanceCharts(){return ee},get getRecentTransactions(){return J},get exportFinanceReport(){return $},get formatDate(){return le}};return Object.defineProperty(I,"__isScriptSetup",{enumerable:!1,value:!0}),I}},[["render",function(l,t,s,n,i,p){const C=_,k=g,x=W,j=y,T=E,D=b,F=Q,S=L,P=G,R=K,A=Z,M=H,z=w;return a(),e("div",se,[r("div",ne,[r("div",ie,[r("div",re,[r("div",oe,[o(C,{size:"24"},{default:c(()=>[o(n.Money)]),_:1})]),t[4]||(t[4]=r("div",{class:"header-text"},[r("h1",null,"财务管理"),r("p",null,"全面掌握平台财务状况，实时监控收支情况，高效管理资金流转")],-1))]),r("div",ce,[o(k,{onClick:n.handleExportReport,class:"action-btn secondary"},{default:c(()=>[o(C,null,{default:c(()=>[o(n.Download)]),_:1}),t[5]||(t[5]=f(" 导出报表 ",-1))]),_:1,__:[5]}),o(k,{onClick:t[0]||(t[0]=e=>n.showHelpDialog=!0),class:"action-btn secondary"},{default:c(()=>[o(C,null,{default:c(()=>[o(n.QuestionFilled)]),_:1}),t[6]||(t[6]=f(" 功能说明 ",-1))]),_:1,__:[6]}),o(k,{type:"primary",onClick:n.refreshData,class:"action-btn primary"},{default:c(()=>[o(C,null,{default:c(()=>[o(n.Refresh)]),_:1}),t[7]||(t[7]=f(" 刷新数据 ",-1))]),_:1,__:[7]})])])]),r("div",de,[r("div",ue,[(a(!0),e(d,null,u(n.financeStatCards,l=>(a(),e("div",{class:"stat-card",key:l.key},[r("div",{class:"stat-icon",style:m({background:l.color})},[o(C,{size:"20"},{default:c(()=>[(a(),v(h(l.icon)))]),_:2},1024)],4),r("div",pe,[r("div",fe,q(l.value),1),r("div",ve,q(l.label),1)]),r("div",{class:I(["stat-trend",l.trend])},[o(C,{size:"14"},{default:c(()=>[(a(),v(h(l.trendIcon)))]),_:2},1024),r("span",null,q(l.change),1)],2)]))),128))])]),o(D,{gutter:20,class:"chart-section"},{default:c(()=>[o(T,{span:16},{default:c(()=>[o(j,null,{header:c(()=>[r("div",he,[t[8]||(t[8]=r("h3",null,"收入趋势分析",-1)),r("div",me,[o(x,null,{default:c(()=>[(a(),e(d,null,u(n.timePeriods,e=>o(k,{key:e.value,type:n.selectedPeriod===e.value?"primary":"default",size:"small",onClick:a=>{n.selectedPeriod=e.value,n.fetchChartData()}},{default:c(()=>[f(q(e.label),1)]),_:2},1032,["type","onClick"])),64))]),_:1})])])]),default:c(()=>[r("div",_e,[o(n.LineChart,{data:n.revenueChartData,options:n.chartOptions,height:300},null,8,["data","options"])])]),_:1})]),_:1}),o(T,{span:8},{default:c(()=>[o(j,null,{header:c(()=>t[9]||(t[9]=[r("div",{class:"card-header"},[r("h3",null,"收入来源分布")],-1)])),default:c(()=>[r("div",ge,[o(n.PieChart,{data:n.sourceChartData,options:n.pieChartOptions,height:300},null,8,["data","options"])])]),_:1})]),_:1})]),_:1}),o(D,{gutter:20,class:"action-section"},{default:c(()=>[o(T,{span:12},{default:c(()=>[o(j,null,{header:c(()=>t[10]||(t[10]=[r("div",{class:"card-header"},[r("h3",null,"快捷操作")],-1)])),default:c(()=>[r("div",be,[r("div",ye,[r("div",{class:"action-item",onClick:n.navigateToTransactions},[o(C,null,{default:c(()=>[o(n.Tickets)]),_:1}),t[11]||(t[11]=r("span",null,"交易记录",-1))]),r("div",{class:"action-item",onClick:n.navigateToCommissions},[o(C,null,{default:c(()=>[o(n.Medal)]),_:1}),t[12]||(t[12]=r("span",null,"佣金明细",-1))]),r("div",{class:"action-item",onClick:n.navigateToWithdrawals},[o(C,null,{default:c(()=>[o(n.Upload)]),_:1}),t[13]||(t[13]=r("span",null,"提现管理",-1))]),r("div",{class:"action-item",onClick:n.handleExportReport},[o(C,null,{default:c(()=>[o(n.Download)]),_:1}),t[14]||(t[14]=r("span",null,"导出报表",-1))])])])]),_:1})]),_:1}),o(T,{span:12},{default:c(()=>[o(j,null,{header:c(()=>[r("div",we,[t[16]||(t[16]=r("h3",null,"最新交易",-1)),o(k,{type:"text",onClick:n.navigateToTransactions},{default:c(()=>t[15]||(t[15]=[f("查看全部",-1)])),_:1,__:[15]})])]),default:c(()=>[r("div",Ce,[(a(!0),e(d,null,u(n.recentTransactions,l=>(a(),e("div",{key:l.id,class:"transaction-item"},[r("div",ke,[r("div",xe,q(l.title),1),r("div",je,q(n.formatDate(l.created_at)),1)]),r("div",{class:I(["transaction-amount","income"===l.type?"positive":"negative"])},q("income"===l.type?"+":"-")+"¥"+q(l.amount.toFixed(2)),3)]))),128))])]),_:1})]),_:1})]),_:1}),o(j,null,{header:c(()=>[r("div",Te,[t[18]||(t[18]=r("h3",null,"财务统计详情",-1)),r("div",null,[o(k,{type:"primary",size:"small",onClick:n.refreshData},{default:c(()=>[o(C,null,{default:c(()=>[o(n.Refresh)]),_:1}),t[17]||(t[17]=f(" 刷新数据 ",-1))]),_:1,__:[17]})])])]),default:c(()=>[o(S,{data:n.financeStats,border:""},{default:c(()=>[o(F,{label:"统计项目",prop:"name",width:"150"}),o(F,{label:"今日",width:"120"},{default:c(({row:e})=>[r("span",De,"¥"+q(e.today.toFixed(2)),1)]),_:1}),o(F,{label:"本周",width:"120"},{default:c(({row:e})=>[r("span",Fe,"¥"+q(e.week.toFixed(2)),1)]),_:1}),o(F,{label:"本月",width:"120"},{default:c(({row:e})=>[r("span",Se,"¥"+q(e.month.toFixed(2)),1)]),_:1}),o(F,{label:"本年",width:"120"},{default:c(({row:e})=>[r("span",Pe,"¥"+q(e.year.toFixed(2)),1)]),_:1}),o(F,{label:"总计"},{default:c(({row:e})=>[r("span",Re,"¥"+q(e.total.toFixed(2)),1)]),_:1}),o(F,{label:"增长率",width:"100"},{default:c(({row:e})=>[r("span",{class:I(e.growth>=0?"positive-growth":"negative-growth")},q(e.growth>=0?"+":"")+q(e.growth.toFixed(1))+"% ",3)]),_:1})]),_:1},8,["data"])]),_:1}),o(z,{modelValue:n.showHelpDialog,"onUpdate:modelValue":t[3]||(t[3]=e=>n.showHelpDialog=e),title:"财务管理工作台功能说明",width:"1000px",class:"help-dialog"},{default:c(()=>[r("div",Ae,[t[53]||(t[53]=r("div",{class:"help-section"},[r("h3",null,"💰 功能概述"),r("p",null,"财务管理工作台是平台资金管理的核心系统，提供全面的财务数据统计、收支分析、提现管理等功能，帮助您实时掌握平台财务状况，做出明智的财务决策。")],-1)),r("div",Me,[t[25]||(t[25]=r("h3",null,"🚀 核心功能模块",-1)),o(D,{gutter:20},{default:c(()=>[o(T,{span:8},{default:c(()=>[r("div",ze,[r("div",Oe,[o(C,null,{default:c(()=>[o(n.TrendCharts)]),_:1})]),t[19]||(t[19]=r("div",{class:"feature-content"},[r("h4",null,"财务概览"),r("p",null,"实时显示总收入、总佣金、待提现等关键财务指标")],-1))])]),_:1}),o(T,{span:8},{default:c(()=>[r("div",Ue,[r("div",Ve,[o(C,null,{default:c(()=>[o(n.TrendCharts)]),_:1})]),t[20]||(t[20]=r("div",{class:"feature-content"},[r("h4",null,"数据分析"),r("p",null,"收入趋势图表、来源分布分析、增长率统计")],-1))])]),_:1}),o(T,{span:8},{default:c(()=>[r("div",qe,[r("div",Ie,[o(C,null,{default:c(()=>[o(n.Tickets)]),_:1})]),t[21]||(t[21]=r("div",{class:"feature-content"},[r("h4",null,"交易管理"),r("p",null,"查看所有交易记录，支持筛选和导出功能")],-1))])]),_:1}),o(T,{span:8},{default:c(()=>[r("div",Ee,[r("div",We,[o(C,null,{default:c(()=>[o(n.Medal)]),_:1})]),t[22]||(t[22]=r("div",{class:"feature-content"},[r("h4",null,"佣金管理"),r("p",null,"佣金明细查询、结算记录、分成比例管理")],-1))])]),_:1}),o(T,{span:8},{default:c(()=>[r("div",Le,[r("div",Qe,[o(C,null,{default:c(()=>[o(n.Upload)]),_:1})]),t[23]||(t[23]=r("div",{class:"feature-content"},[r("h4",null,"提现管理"),r("p",null,"处理用户提现申请、审核提现资格、资金划转")],-1))])]),_:1}),o(T,{span:8},{default:c(()=>[r("div",Ge,[r("div",He,[o(C,null,{default:c(()=>[o(n.Download)]),_:1})]),t[24]||(t[24]=r("div",{class:"feature-content"},[r("h4",null,"报表导出"),r("p",null,"生成财务报表、数据导出、定期统计分析")],-1))])]),_:1})]),_:1})]),r("div",Ze,[t[26]||(t[26]=r("h3",null,"📊 财务指标说明",-1)),o(S,{data:n.financeMetrics,style:{width:"100%"}},{default:c(()=>[o(F,{prop:"metric",label:"指标名称",width:"120"},{default:c(({row:e})=>[o(P,{type:e.color},{default:c(()=>[f(q(e.metric),1)]),_:2},1032,["type"])]),_:1}),o(F,{prop:"description",label:"指标说明"}),o(F,{prop:"calculation",label:"计算方式"}),o(F,{prop:"significance",label:"业务意义"})]),_:1},8,["data"])]),r("div",Ke,[t[31]||(t[31]=r("h3",null,"💸 收入来源分析",-1)),r("div",Xe,[r("div",Ye,[r("div",Be,[o(C,null,{default:c(()=>[o(n.Comment)]),_:1})]),t[27]||(t[27]=r("div",{class:"source-content"},[r("h4",null,"群组收入"),r("p",null,"用户加入付费群组产生的收入，是平台主要收入来源"),r("div",{class:"source-details"},[r("span",null,"占比：约60-70%"),r("span",null,"特点：稳定性高，增长潜力大")])],-1))]),r("div",Ne,[r("div",$e,[o(C,null,{default:c(()=>[o(n.Share)]),_:1})]),t[28]||(t[28]=r("div",{class:"source-content"},[r("h4",null,"佣金收入"),r("p",null,"分销商推广产生的佣金收入，激励推广积极性"),r("div",{class:"source-details"},[r("span",null,"占比：约20-25%"),r("span",null,"特点：增长快速，波动较大")])],-1))]),r("div",Je,[r("div",ea,[o(C,null,{default:c(()=>[o(n.Shop)]),_:1})]),t[29]||(t[29]=r("div",{class:"source-content"},[r("h4",null,"分站收入"),r("p",null,"分站管理费用和服务费收入"),r("div",{class:"source-details"},[r("span",null,"占比：约10-15%"),r("span",null,"特点：稳定增长，利润率高")])],-1))]),r("div",aa,[r("div",la,[o(C,null,{default:c(()=>[o(n.Star)]),_:1})]),t[30]||(t[30]=r("div",{class:"source-content"},[r("h4",null,"其他收入"),r("p",null,"广告收入、增值服务等其他收入来源"),r("div",{class:"source-details"},[r("span",null,"占比：约5-10%"),r("span",null,"特点：多样化，补充性收入")])],-1))])])]),t[54]||(t[54]=r("div",{class:"help-section"},[r("h3",null,"🏦 提现管理流程"),r("div",{class:"withdrawal-process"},[r("div",{class:"process-step"},[r("div",{class:"step-number"},"1"),r("div",{class:"step-content"},[r("h4",null,"用户申请"),r("p",null,"用户在前台提交提现申请，填写提现金额和收款信息")])]),r("div",{class:"process-arrow"},"→"),r("div",{class:"process-step"},[r("div",{class:"step-number"},"2"),r("div",{class:"step-content"},[r("h4",null,"系统审核"),r("p",null,"系统自动检查用户余额、提现限额、实名认证等条件")])]),r("div",{class:"process-arrow"},"→"),r("div",{class:"process-step"},[r("div",{class:"step-number"},"3"),r("div",{class:"step-content"},[r("h4",null,"人工审核"),r("p",null,"管理员审核提现申请，核实用户身份和提现合规性")])]),r("div",{class:"process-arrow"},"→"),r("div",{class:"process-step"},[r("div",{class:"step-number"},"4"),r("div",{class:"step-content"},[r("h4",null,"资金处理"),r("p",null,"审核通过后，系统自动或手动处理资金转账")])]),r("div",{class:"process-arrow"},"→"),r("div",{class:"process-step"},[r("div",{class:"step-number"},"5"),r("div",{class:"step-content"},[r("h4",null,"完成提现"),r("p",null,"资金到账，更新提现状态，发送通知给用户")])])])],-1)),r("div",ta,[t[40]||(t[40]=r("h3",null,"📝 操作指南",-1)),o(M,{modelValue:n.activeGuides,"onUpdate:modelValue":t[1]||(t[1]=e=>n.activeGuides=e)},{default:c(()=>[o(A,{title:"如何查看财务数据？",name:"view-data"},{default:c(()=>[r("div",sa,[t[33]||(t[33]=r("ol",null,[r("li",null,"在工作台首页查看核心财务指标卡片"),r("li",null,'查看"收入趋势分析"图表了解收入变化'),r("li",null,'在"收入来源分布"图表中查看收入结构'),r("li",null,"在财务统计表格中查看详细的分时段数据"),r("li",null,"可以切换时间段查看不同期间的数据")],-1)),o(R,{type:"info",closable:!1},{default:c(()=>t[32]||(t[32]=[f(" 💡 提示：数据每小时更新一次，增长率基于同期对比计算 ",-1)])),_:1,__:[32]})])]),_:1}),o(A,{title:"如何处理提现申请？",name:"withdrawal-process"},{default:c(()=>[r("div",na,[t[35]||(t[35]=r("ol",null,[r("li",null,'点击"提现管理"进入提现管理页面'),r("li",null,"查看待审核的提现申请列表"),r("li",null,'点击"详情"查看申请人信息和提现详情'),r("li",null,"核实用户身份、余额、提现合规性"),r("li",null,'点击"通过"或"拒绝"处理申请'),r("li",null,"填写审核意见并确认操作")],-1)),o(R,{type:"warning",closable:!1},{default:c(()=>t[34]||(t[34]=[f(" ⚠️ 注意：提现审核要严格核实用户身份，防范资金风险 ",-1)])),_:1,__:[34]})])]),_:1}),o(A,{title:"如何导出财务报表？",name:"export-report"},{default:c(()=>[r("div",ia,[t[37]||(t[37]=r("ol",null,[r("li",null,'点击"导出报表"按钮'),r("li",null,"选择报表类型（收入报表、佣金报表、提现报表等）"),r("li",null,"设置导出时间范围"),r("li",null,"选择导出格式（Excel、PDF等）"),r("li",null,'点击"确认导出"生成报表'),r("li",null,"下载生成的报表文件")],-1)),o(R,{type:"success",closable:!1},{default:c(()=>t[36]||(t[36]=[f(" ✅ 说明：报表支持多种格式，可用于财务分析和对账 ",-1)])),_:1,__:[36]})])]),_:1}),o(A,{title:"如何分析收入趋势？",name:"revenue-analysis"},{default:c(()=>[r("div",ra,[t[39]||(t[39]=r("ol",null,[r("li",null,"查看收入趋势图表，观察收入变化规律"),r("li",null,"切换不同时间段（7天、30天、90天、1年）"),r("li",null,"对比不同时期的收入水平和增长率"),r("li",null,"分析收入来源分布，识别主要收入驱动因素"),r("li",null,"结合业务活动分析收入波动原因")],-1)),o(R,{type:"info",closable:!1},{default:c(()=>t[38]||(t[38]=[f(" 💡 建议：定期分析收入趋势，制定相应的运营策略 ",-1)])),_:1,__:[38]})])]),_:1})]),_:1},8,["modelValue"])]),r("div",oa,[t[47]||(t[47]=r("h3",null,"⚠️ 风险提示与注意事项",-1)),r("div",ca,[o(R,{type:"error",closable:!1,style:{"margin-bottom":"15px"}},{title:c(()=>t[41]||(t[41]=[r("strong",null,"资金安全风险",-1)])),default:c(()=>[t[42]||(t[42]=r("ul",{style:{margin:"10px 0","padding-left":"20px"}},[r("li",null,"严格审核提现申请，防范虚假提现和洗钱风险"),r("li",null,"定期核对账务，确保资金流水准确无误"),r("li",null,"建立资金监控机制，及时发现异常交易")],-1))]),_:1,__:[42]}),o(R,{type:"warning",closable:!1,style:{"margin-bottom":"15px"}},{title:c(()=>t[43]||(t[43]=[r("strong",null,"合规风险",-1)])),default:c(()=>[t[44]||(t[44]=r("ul",{style:{margin:"10px 0","padding-left":"20px"}},[r("li",null,"确保所有财务操作符合相关法律法规"),r("li",null,"完善财务记录，保留必要的凭证和文档"),r("li",null,"定期进行财务审计，确保合规经营")],-1))]),_:1,__:[44]}),o(R,{type:"info",closable:!1},{title:c(()=>t[45]||(t[45]=[r("strong",null,"操作建议",-1)])),default:c(()=>[t[46]||(t[46]=r("ul",{style:{margin:"10px 0","padding-left":"20px"}},[r("li",null,"建议每日查看财务数据，及时发现异常情况"),r("li",null,"定期备份财务数据，防止数据丢失"),r("li",null,"建立多人审核机制，降低操作风险")],-1))]),_:1,__:[46]})])]),r("div",da,[t[52]||(t[52]=r("h3",null,"❓ 常见问题",-1)),o(M,{modelValue:n.activeFAQ,"onUpdate:modelValue":t[2]||(t[2]=e=>n.activeFAQ=e)},{default:c(()=>[o(A,{title:"财务数据多久更新一次？",name:"faq1"},{default:c(()=>t[48]||(t[48]=[r("p",null,'财务数据每小时自动更新一次，重要指标如收入、佣金等实时更新。您也可以点击"刷新数据"按钮手动更新。',-1)])),_:1,__:[48]}),o(A,{title:"提现申请的处理时间是多久？",name:"faq2"},{default:c(()=>t[49]||(t[49]=[r("p",null,"一般情况下，提现申请在1-3个工作日内处理完成。具体时间取决于申请金额、用户等级和审核复杂度。",-1)])),_:1,__:[49]}),o(A,{title:"如何设置提现限额？",name:"faq3"},{default:c(()=>t[50]||(t[50]=[r("p",null,"可以在系统设置中配置不同用户等级的提现限额，包括单次提现限额、日提现限额和月提现限额。",-1)])),_:1,__:[50]}),o(A,{title:"财务报表可以定期自动生成吗？",name:"faq4"},{default:c(()=>t[51]||(t[51]=[r("p",null,"系统支持定期自动生成财务报表功能，可以设置每日、每周或每月自动生成并发送到指定邮箱。",-1)])),_:1,__:[51]})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-70683748"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/finance/FinanceDashboard.vue"]]);export{ua as default};
