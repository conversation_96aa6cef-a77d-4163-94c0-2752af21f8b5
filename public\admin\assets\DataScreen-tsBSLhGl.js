/* empty css             */import{l as e,q as a,ai as t,G as r,A as l,F as s,Y as o,ag as n,r as i,c as d,o as c,n as u,I as v,m}from"./vue-vendor-BcnDv-68.js";import{i as f,L as h,e as g}from"./echarts-D6CUuNS9.js";import{Z as b,s as p,ah as y,X as C,bl as w,u as S}from"./element-plus-C2UshkXo.js";import{l as x,f as k,a as L,g as M,b as D,d as R}from"./mapLoader-Bhh47pun.js";import{a as j}from"./index-D4AyIzGN.js";import{_ as z}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";const O=(e={})=>j.get("/admin/dashboard/full-stats",{params:e}),_=()=>j.get("/admin/dashboard/charts"),A=e=>j.get("/admin/dashboard/income-chart",{params:e}),P=e=>j.get("/admin/dashboard/user-growth-chart",{params:e}),T=e=>j.get("/admin/dashboard/order-chart",{params:e}),W=()=>j.get("/admin/dashboard/recent-orders"),F=()=>j.get("/admin/dashboard/realtime"),G={class:"data-screen"},N={class:"screen-header"},U={class:"header-left"},I={class:"header-center"},B={class:"time-display"},E={class:"current-time"},$={class:"current-date"},q={class:"screen-content"},H={class:"metrics-section"},V={class:"metric-header"},X={class:"metric-icon"},Y={class:"icon-emoji"},Z={class:"trend-icon"},J={class:"metric-content"},K={class:"metric-value"},Q={class:"value"},ee={class:"unit"},ae={class:"metric-label"},te={class:"metric-chart"},re={class:"charts-section"},le={class:"chart-container main-chart"},se={class:"chart-header"},oe={class:"chart-controls"},ne=["onClick"],ie={class:"chart-content",ref:"mainChartRef"},de={class:"chart-container"},ce={class:"chart-content",ref:"orderChartRef"},ue={class:"chart-container"},ve={class:"chart-content",ref:"revenueChartRef"},me={class:"details-section"},fe={class:"map-container"},he={class:"map-content",ref:"mapChartRef"},ge={class:"ranking-container"},be={class:"ranking-list"},pe={class:"ranking-info"},ye={class:"province-name"},Ce={class:"province-value"},we={class:"ranking-bar"},Se={class:"table-container"},xe={class:"table-header"},ke={class:"table-content"},Le={class:"data-table"},Me={class:"number"},De={class:"number"},Re={class:"number"},je={class:"number"};const ze=z({__name:"DataScreen",setup(e,{expose:a}){a();const t=n(),r=i(""),l=i(""),s=i("7d"),o=i(!1),m=i(!1),b=i(!0),p=i(!1),y=i(null),S=i(null),j=i(null),z=i(null),G=i({}),N=i({}),U=i([{label:"7天",value:"7d"},{label:"30天",value:"30d"},{label:"90天",value:"90d"}]),I=i([{id:"users",label:"总用户数",value:0,unit:"",icon:"👥",color:"#3b82f6",trend:{type:"up",value:0,icon:"↗️"}},{id:"orders",label:"总订单数",value:0,unit:"",icon:"📦",color:"#10b981",trend:{type:"up",value:0,icon:"↗️"}},{id:"revenue",label:"总收入",value:0,unit:"元",icon:"💰",color:"#f59e0b",trend:{type:"up",value:0,icon:"↗️"}},{id:"groups",label:"群组数量",value:0,unit:"",icon:"📊",color:"#ef4444",trend:{type:"up",value:0,icon:"↗️"}}]),B=i([{name:"广东",value:25680},{name:"江苏",value:18950},{name:"山东",value:16780},{name:"浙江",value:14320},{name:"北京",value:15420},{name:"河南",value:13560},{name:"四川",value:12890},{name:"上海",value:12680},{name:"湖北",value:11450},{name:"湖南",value:10980}]),E=d(()=>[...B.value].sort((e,a)=>a.value-e.value).slice(0,10)),$=i([]),q=i({userGrowth:[],orderSource:{labels:[],datasets:[]},income:[],orders:[]}),H=()=>{const e=new Date;r.value=e.toLocaleTimeString("zh-CN",{hour12:!1}),l.value=e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"})},V=async()=>{b.value=!0;try{const e=await O();if(e.success&&e.data){const a=e.data;a.users&&(I.value[0].value=a.users.total||0,I.value[0].trend.value=(a.users.today/a.users.total*100).toFixed(1)||0),a.orders&&(I.value[1].value=a.orders.total||0,I.value[1].trend.value=(a.orders.today/a.orders.total*100).toFixed(1)||0),a.revenue&&(I.value[2].value=a.revenue.total||0,I.value[2].trend.value=(a.revenue.today/a.revenue.total*100).toFixed(1)||0),a.groups&&(I.value[3].value=a.groups.total||0,I.value[3].trend.value=(a.groups.today/a.groups.total*100).toFixed(1)||0)}const a=await _();a.success&&a.data&&(q.value=a.data);const t=await F();if(t.success&&t.data){const e=new Date;$.value=[];for(let a=4;a>=0;a--){const t=new Date(e-5*a*60*1e3);$.value.push({time:t.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),newUsers:Math.floor(50*Math.random()+100),activeUsers:Math.floor(100*Math.random()+2e3),orders:Math.floor(30*Math.random()+70),revenue:Math.floor(5e3*Math.random()+1e4)})}}p.value=!0}catch(e){C.error("数据加载失败，请检查网络连接"),I.value[0].value=0,I.value[1].value=0,I.value[2].value=0,I.value[3].value=0}finally{b.value=!1}};c(async()=>{H(),setInterval(H,1e3),await V(),await u(),setTimeout(()=>{X(),window.addEventListener("resize",()=>{Object.values(N.value).forEach(e=>{e&&e.resize&&e.resize()})});const e=setInterval(async()=>{if(!o.value)try{await V(),Y(),Z(),J()}catch(e){}},3e5);v(()=>{clearInterval(e),Object.values(N.value).forEach(e=>{e&&e.dispose()})})},100)});const X=async()=>{try{Y(),Z(),J(),m.value=await x("local"),m.value&&await K(),Q()}catch(e){}},Y=()=>{if(!y.value)return;const e=f(y.value);N.value.main=e;const a={backgroundColor:"transparent",grid:{left:"10%",right:"10%",bottom:"15%",top:"15%",containLabel:!0},xAxis:{type:"category",data:q.value.userGrowth.length>0?q.value.userGrowth.map(e=>e.date):["暂无","数据","加载","中","...","请","稍候"],axisLine:{show:!0,lineStyle:{color:"rgba(255,255,255,0.3)"}},axisLabel:{color:"rgba(255,255,255,0.8)",fontSize:12}},yAxis:{type:"value",axisLine:{show:!0,lineStyle:{color:"rgba(255,255,255,0.3)"}},axisLabel:{color:"rgba(255,255,255,0.8)",fontSize:12},splitLine:{show:!0,lineStyle:{color:"rgba(255,255,255,0.1)"}}},series:[{name:"用户增长",data:q.value.userGrowth.length>0?q.value.userGrowth.map(e=>e.value):[0,0,0,0,0,0,0],type:"line",smooth:!0,lineStyle:{color:"#3b82f6",width:3},itemStyle:{color:"#3b82f6",borderWidth:2,borderColor:"#fff"},areaStyle:{color:new h(0,0,0,1,[{offset:0,color:"rgba(59, 130, 246, 0.3)"},{offset:1,color:"rgba(59, 130, 246, 0.05)"}])}}],tooltip:{trigger:"axis",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"rgba(255,255,255,0.2)",textStyle:{color:"#fff"}}};e.setOption(a)},Z=()=>{if(!S.value)return;const e=f(S.value);N.value.order=e;e.setOption({backgroundColor:"transparent",tooltip:{trigger:"item",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"rgba(255,255,255,0.2)",textStyle:{color:"#fff"}},series:[{type:"pie",radius:["40%","70%"],center:["50%","50%"],data:[{value:1048,name:"已完成",itemStyle:{color:"#10b981"}},{value:735,name:"进行中",itemStyle:{color:"#3b82f6"}},{value:580,name:"已取消",itemStyle:{color:"#ef4444"}},{value:484,name:"待付款",itemStyle:{color:"#f59e0b"}}],label:{color:"rgba(255,255,255,0.8)",fontSize:12},labelLine:{lineStyle:{color:"rgba(255,255,255,0.3)"}}}]})},J=()=>{if(!j.value)return;const e=f(j.value);N.value.revenue=e;const a={backgroundColor:"transparent",grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["1月","2月","3月","4月","5月","6月"],axisLine:{lineStyle:{color:"rgba(255,255,255,0.3)"}},axisLabel:{color:"rgba(255,255,255,0.8)"}},yAxis:{type:"value",axisLine:{lineStyle:{color:"rgba(255,255,255,0.3)"}},axisLabel:{color:"rgba(255,255,255,0.8)"},splitLine:{lineStyle:{color:"rgba(255,255,255,0.1)"}}},series:[{data:[2300,2800,3200,2900,3500,3800],type:"bar",itemStyle:{color:new h(0,0,0,1,[{offset:0,color:"#f59e0b"},{offset:1,color:"#fbbf24"}])},barWidth:"60%"}]};e.setOption(a)},K=async()=>{if(!z.value)return;if(!m.value)return;const e=f(z.value);N.value.map=e;const a=L(),t=k(a),r=Math.max(...Object.values(a)),l=Math.min(...Object.values(a)),s={backgroundColor:"transparent",grid:{left:10,right:10,top:50,bottom:80,containLabel:!0},title:{text:"全国用户分布",left:"center",top:10,textStyle:{color:"#fff",fontSize:18,fontWeight:"bold"}},tooltip:{trigger:"item",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"rgba(255,255,255,0.2)",textStyle:{color:"#fff"},formatter:function(e){try{return e&&e.name?e.data&&void 0!==e.data.value&&null!==e.data.value&&"number"==typeof e.data.value?`${e.name}<br/>用户数: ${e.data.value.toLocaleString()}`:`${e.name}<br/>暂无数据`:"数据加载中..."}catch(a){return"数据显示异常"}}},visualMap:M(l,r),series:[{name:"用户分布",type:"map",map:"china",roam:!1,zoom:1.8,center:[105,36],data:t,label:{show:!0,color:"#fff",fontSize:12,fontWeight:"bold",textBorderColor:"rgba(0,0,0,0.5)",textBorderWidth:1},itemStyle:{borderColor:"rgba(255,255,255,0.3)",borderWidth:1,areaColor:"#4a5568"},emphasis:{label:{show:!0,color:"#fff",fontSize:14,fontWeight:"bold",textBorderColor:"rgba(0,0,0,0.8)",textBorderWidth:2},itemStyle:{areaColor:"rgba(59, 130, 246, 0.8)",borderColor:"#60a5fa",borderWidth:3,shadowBlur:15,shadowColor:"rgba(59, 130, 246, 0.7)"}}}]};e.setOption(s)},Q=()=>{I.value.forEach(e=>{const a=G.value[e.id];if(a){const t=f(a);N.value[e.id]=t;const r={backgroundColor:"transparent",grid:{left:0,right:0,top:0,bottom:0},xAxis:{type:"category",show:!1,data:["1","2","3","4","5","6","7"]},yAxis:{type:"value",show:!1},series:[{data:[120,132,101,134,90,230,210],type:"line",smooth:!0,lineStyle:{color:e.color,width:2},itemStyle:{color:e.color},areaStyle:{color:new h(0,0,0,1,[{offset:0,color:e.color+"40"},{offset:1,color:e.color+"10"}])},symbol:"none"}]};t.setOption(r)}})},ee={router:t,currentTime:r,currentDate:l,selectedPeriod:s,isRefreshing:o,isMapLoaded:m,loading:b,dataLoaded:p,mainChartRef:y,orderChartRef:S,revenueChartRef:j,mapChartRef:z,chartRefs:G,charts:N,timePeriods:U,coreMetrics:I,provinceData:B,topProvinces:E,realtimeData:$,chartData:q,goToHome:()=>{t.push("/admin/dashboard")},formatNumber:e=>e>=1e4?(e/1e4).toFixed(1)+"w":e>=1e3?(e/1e3).toFixed(1)+"k":e.toString(),getRankClass:e=>0===e?"gold":1===e?"silver":2===e?"bronze":"normal",setChartRef:(e,a)=>{a&&(G.value[e]=a)},updateTime:H,refreshData:async()=>{o.value=!0;try{await V(),C.success("数据刷新成功")}catch(e){C.error("数据刷新失败")}finally{o.value=!1}},loadDashboardData:V,initCharts:X,initMainChart:Y,initOrderChart:Z,initRevenueChart:J,initMapChart:K,initMiniCharts:Q,ref:i,onMounted:c,onUnmounted:v,computed:d,nextTick:u,get useRouter(){return n},get echarts(){return g},get ArrowLeft(){return w},get loadChinaMap(){return x},get getProvinceDataMap(){return L},get formatMapData(){return k},get generateVisualMap(){return M},get downloadRealMapData(){return R},get getMapDataSources(){return D},get getDashboardStats(){return O},get getDashboardCharts(){return _},get getIncomeChart(){return A},get getOrderChart(){return T},get getUserGrowthChart(){return P},get getRealTimeData(){return F},get getRecentOrders(){return W},get ElMessage(){return C}};return Object.defineProperty(ee,"__isScriptSetup",{enumerable:!1,value:!0}),ee}},[["render",function(n,i,d,c,u,v){const f=y;return m(),e("div",G,[a("header",N,[a("div",U,[a("div",{class:"back-button",onClick:c.goToHome},[r(f,null,{default:l(()=>[r(c.ArrowLeft)]),_:1}),i[0]||(i[0]=a("span",null,"返回首页",-1))]),i[1]||(i[1]=a("div",{class:"logo-section"},[a("div",{class:"logo-icon"},"📊"),a("div",{class:"logo-text"},[a("h1",null,"晨鑫流量变现系统 数据中心"),a("p",null,"实时运营数据监控")])],-1))]),a("div",I,[a("div",B,[a("div",E,b(c.currentTime),1),a("div",$,b(c.currentDate),1)])]),i[2]||(i[2]=t('<div class="header-right" data-v-f9d75725><div class="status-indicators" data-v-f9d75725><div class="status-item" data-v-f9d75725><span class="status-dot online" data-v-f9d75725></span><span data-v-f9d75725>系统正常</span></div><div class="status-item" data-v-f9d75725><span class="status-dot" data-v-f9d75725></span><span data-v-f9d75725>实时更新</span></div></div></div>',1))]),a("main",q,[a("section",H,[(m(!0),e(s,null,o(c.coreMetrics,t=>(m(),e("div",{key:t.id,class:"metric-card",style:S({"--accent-color":t.color})},[a("div",V,[a("div",X,[a("span",Y,b(t.icon),1)]),a("div",{class:p(["metric-trend",t.trend.type])},[a("span",Z,b(t.trend.icon),1),a("span",null,b(t.trend.value)+"%",1)],2)]),a("div",J,[a("div",K,[a("span",Q,b(c.formatNumber(t.value)),1),a("span",ee,b(t.unit),1)]),a("div",ae,b(t.label),1)]),a("div",te,[a("div",{class:"mini-chart",ref_for:!0,ref:e=>c.setChartRef(t.id,e)},null,512)])],4))),128))]),a("section",re,[a("div",le,[a("div",se,[i[3]||(i[3]=a("h3",null,"用户增长趋势",-1)),a("div",oe,[(m(!0),e(s,null,o(c.timePeriods,a=>(m(),e("button",{key:a.value,class:p(["period-btn",{active:c.selectedPeriod===a.value}]),onClick:e=>c.selectedPeriod=a.value},b(a.label),11,ne))),128))])]),a("div",ie,null,512)]),a("div",de,[i[4]||(i[4]=a("div",{class:"chart-header"},[a("h3",null,"订单分析")],-1)),a("div",ce,null,512)]),a("div",ue,[i[5]||(i[5]=a("div",{class:"chart-header"},[a("h3",null,"收入分析")],-1)),a("div",ve,null,512)])]),a("section",me,[a("div",fe,[i[6]||(i[6]=t('<div class="map-header" data-v-f9d75725><h3 data-v-f9d75725>全国用户分布</h3><div class="map-legend" data-v-f9d75725><div class="legend-item" data-v-f9d75725><span class="legend-color" style="background:#4ade80;" data-v-f9d75725></span><span data-v-f9d75725>0-1000</span></div><div class="legend-item" data-v-f9d75725><span class="legend-color" style="background:#fbbf24;" data-v-f9d75725></span><span data-v-f9d75725>1000-5000</span></div><div class="legend-item" data-v-f9d75725><span class="legend-color" style="background:#f87171;" data-v-f9d75725></span><span data-v-f9d75725>5000-10000</span></div><div class="legend-item" data-v-f9d75725><span class="legend-color" style="background:#dc2626;" data-v-f9d75725></span><span data-v-f9d75725>10000+</span></div></div></div>',1)),a("div",he,null,512)]),a("div",ge,[i[7]||(i[7]=a("div",{class:"ranking-header"},[a("h3",null,"省份排行榜")],-1)),a("div",be,[(m(!0),e(s,null,o(c.topProvinces,(t,r)=>(m(),e("div",{key:t.name,class:p(["ranking-item",{"top-three":r<3}])},[a("div",{class:p(["ranking-number",c.getRankClass(r)])},b(r+1),3),a("div",pe,[a("div",ye,b(t.name),1),a("div",Ce,b(c.formatNumber(t.value)),1)]),a("div",we,[a("div",{class:"bar-fill",style:S({width:t.value/c.topProvinces[0].value*100+"%"})},null,4)])],2))),128))])]),a("div",Se,[a("div",xe,[i[8]||(i[8]=a("h3",null,"实时数据",-1)),a("div",{class:"refresh-btn",onClick:c.refreshData},[a("span",{class:p({spinning:c.isRefreshing})},"🔄",2)])]),a("div",ke,[a("table",Le,[i[9]||(i[9]=a("thead",null,[a("tr",null,[a("th",null,"时间"),a("th",null,"新增用户"),a("th",null,"活跃用户"),a("th",null,"订单数"),a("th",null,"收入")])],-1)),a("tbody",null,[(m(!0),e(s,null,o(c.realtimeData,t=>(m(),e("tr",{key:t.time},[a("td",null,b(t.time),1),a("td",Me,b(t.newUsers),1),a("td",De,b(t.activeUsers),1),a("td",Re,b(t.orders),1),a("td",je,"¥"+b(c.formatNumber(t.revenue)),1)]))),128))])])])])])])])}],["__scopeId","data-v-f9d75725"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/dashboard/DataScreen.vue"]]);export{ze as default};
