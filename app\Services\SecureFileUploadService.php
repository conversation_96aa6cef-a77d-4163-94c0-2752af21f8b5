<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use InvalidArgumentException;
use Exception;

/**
 * 安全文件上传服务
 * 提供安全的文件上传功能，包含恶意文件检测和安全验证
 */
class SecureFileUploadService
{
    /**
     * 允许的图片文件类型
     */
    private const ALLOWED_IMAGE_TYPES = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
    ];

    /**
     * 允许的文档文件类型
     */
    private const ALLOWED_DOCUMENT_TYPES = [
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls' => 'application/vnd.ms-excel',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'txt' => 'text/plain',
    ];

    /**
     * 危险文件扩展名黑名单
     */
    private const DANGEROUS_EXTENSIONS = [
        'php', 'phtml', 'php3', 'php4', 'php5', 'pht', 'phar',
        'js', 'vbs', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbe',
        'asp', 'aspx', 'cer', 'csr', 'jsp', 'jspx',
        'exe', 'msi', 'deb', 'rpm', 'dmg',
        'sh', 'bash', 'zsh', 'fish', 'csh', 'tcsh',
        'pl', 'py', 'rb', 'go', 'java', 'class',
        'htaccess', 'htpasswd', 'ini', 'conf', 'config',
    ];

    /**
     * 最大文件大小 (字节)
     */
    private const MAX_FILE_SIZES = [
        'image' => 5 * 1024 * 1024,    // 5MB
        'document' => 10 * 1024 * 1024, // 10MB
        'default' => 2 * 1024 * 1024,   // 2MB
    ];

    /**
     * 上传图片文件
     *
     * @param UploadedFile $file
     * @param string $directory
     * @return array
     * @throws InvalidArgumentException
     * @throws Exception
     */
    public function uploadImage(UploadedFile $file, string $directory = 'images'): array
    {
        return $this->uploadFile($file, $directory, 'image');
    }

    /**
     * 上传文档文件
     *
     * @param UploadedFile $file
     * @param string $directory
     * @return array
     * @throws InvalidArgumentException
     * @throws Exception
     */
    public function uploadDocument(UploadedFile $file, string $directory = 'documents'): array
    {
        return $this->uploadFile($file, $directory, 'document');
    }

    /**
     * 通用文件上传方法
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param string $type
     * @return array
     * @throws InvalidArgumentException
     * @throws Exception
     */
    public function uploadFile(UploadedFile $file, string $directory, string $type = 'default'): array
    {
        // 基础验证
        $this->validateFile($file, $type);

        // 安全检查
        $this->performSecurityChecks($file);

        // 生成安全的文件名
        $filename = $this->generateSecureFilename($file);

        // 构造完整路径
        $path = $this->buildFilePath($directory, $filename);

        try {
            // 存储文件
            $storedPath = Storage::disk('public')->putFileAs(
                $directory,
                $file,
                $filename
            );

            // 记录上传日志
            Log::info('文件上传成功', [
                'original_name' => $file->getClientOriginalName(),
                'stored_name' => $filename,
                'path' => $storedPath,
                'size' => $file->getSize(),
                'type' => $file->getMimeType(),
                'user_id' => auth()->id(),
                'ip' => request()->ip(),
            ]);

            return [
                'success' => true,
                'filename' => $filename,
                'path' => $storedPath,
                'url' => Storage::url($storedPath),
                'size' => $file->getSize(),
                'type' => $file->getMimeType(),
            ];
        } catch (Exception $e) {
            Log::error('文件上传失败', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName(),
                'user_id' => auth()->id(),
            ]);

            throw new Exception('文件上传失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证文件
     *
     * @param UploadedFile $file
     * @param string $type
     * @throws InvalidArgumentException
     */
    private function validateFile(UploadedFile $file, string $type): void
    {
        // 检查文件是否有效
        if (!$file->isValid()) {
            throw new InvalidArgumentException('无效的文件');
        }

        // 检查文件大小
        $maxSize = self::MAX_FILE_SIZES[$type] ?? self::MAX_FILE_SIZES['default'];
        if ($file->getSize() > $maxSize) {
            throw new InvalidArgumentException("文件大小超出限制，最大允许: " . $this->formatBytes($maxSize));
        }

        // 检查扩展名
        $extension = strtolower($file->getClientOriginalExtension());
        if (in_array($extension, self::DANGEROUS_EXTENSIONS)) {
            throw new InvalidArgumentException("不允许的文件类型: {$extension}");
        }

        // 根据类型验证
        $this->validateFileType($file, $type);
    }

    /**
     * 验证文件类型
     *
     * @param UploadedFile $file
     * @param string $type
     * @throws InvalidArgumentException
     */
    private function validateFileType(UploadedFile $file, string $type): void
    {
        $extension = strtolower($file->getClientOriginalExtension());
        $mimeType = $file->getMimeType();

        $allowedTypes = [];
        
        switch ($type) {
            case 'image':
                $allowedTypes = self::ALLOWED_IMAGE_TYPES;
                break;
            case 'document':
                $allowedTypes = self::ALLOWED_DOCUMENT_TYPES;
                break;
            default:
                // 对于默认类型，合并所有允许的类型
                $allowedTypes = array_merge(self::ALLOWED_IMAGE_TYPES, self::ALLOWED_DOCUMENT_TYPES);
                break;
        }

        // 检查扩展名是否在允许列表中
        if (!array_key_exists($extension, $allowedTypes)) {
            throw new InvalidArgumentException("不允许的文件扩展名: {$extension}");
        }

        // 检查MIME类型是否匹配
        if ($allowedTypes[$extension] !== $mimeType) {
            throw new InvalidArgumentException("文件类型不匹配: {$mimeType}");
        }
    }

    /**
     * 执行安全检查
     *
     * @param UploadedFile $file
     * @throws InvalidArgumentException
     */
    private function performSecurityChecks(UploadedFile $file): void
    {
        // 检查文件名中的危险字符
        $originalName = $file->getClientOriginalName();
        if (preg_match('/[<>:"\/\\\|\?\*\x00-\x1f]/', $originalName)) {
            throw new InvalidArgumentException('文件名包含非法字符');
        }

        // 检查文件内容（简单的恶意代码检测）
        $this->scanFileContent($file);

        // 对于图片文件，进行额外检查
        if ($this->isImageFile($file)) {
            $this->validateImageFile($file);
        }
    }

    /**
     * 扫描文件内容
     *
     * @param UploadedFile $file
     * @throws InvalidArgumentException
     */
    private function scanFileContent(UploadedFile $file): void
    {
        $content = file_get_contents($file->getPathname());
        
        // 检查常见的恶意代码模式
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onerror=/i',
            '/eval\(/i',
            '/exec\(/i',
            '/system\(/i',
            '/shell_exec/i',
            '/passthru/i',
            '/`.*`/i', // 反引号执行
        ];

        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                Log::warning('检测到恶意文件上传尝试', [
                    'file' => $file->getClientOriginalName(),
                    'pattern' => $pattern,
                    'user_id' => auth()->id(),
                    'ip' => request()->ip(),
                ]);

                throw new InvalidArgumentException('文件包含不安全的内容');
            }
        }
    }

    /**
     * 验证图片文件
     *
     * @param UploadedFile $file
     * @throws InvalidArgumentException
     */
    private function validateImageFile(UploadedFile $file): void
    {
        // 使用getimagesize验证图片
        $imageInfo = @getimagesize($file->getPathname());
        if ($imageInfo === false) {
            throw new InvalidArgumentException('无效的图片文件');
        }

        // 检查图片尺寸限制
        [$width, $height] = $imageInfo;
        
        if ($width > 4096 || $height > 4096) {
            throw new InvalidArgumentException('图片尺寸过大，最大允许: 4096x4096');
        }

        if ($width < 1 || $height < 1) {
            throw new InvalidArgumentException('无效的图片尺寸');
        }
    }

    /**
     * 生成安全的文件名
     *
     * @param UploadedFile $file
     * @return string
     */
    private function generateSecureFilename(UploadedFile $file): string
    {
        $extension = strtolower($file->getClientOriginalExtension());
        $timestamp = time();
        $random = Str::random(16);
        $userId = auth()->id() ?? 'anonymous';

        return "{$timestamp}_{$userId}_{$random}.{$extension}";
    }

    /**
     * 构造文件路径
     *
     * @param string $directory
     * @param string $filename
     * @return string
     */
    private function buildFilePath(string $directory, string $filename): string
    {
        // 清理目录名
        $directory = trim($directory, '/\\');
        $directory = preg_replace('/[^a-zA-Z0-9\-_\/]/', '', $directory);

        // 按日期组织目录结构
        $dateDir = date('Y/m/d');

        return "{$directory}/{$dateDir}/{$filename}";
    }

    /**
     * 检查是否为图片文件
     *
     * @param UploadedFile $file
     * @return bool
     */
    private function isImageFile(UploadedFile $file): bool
    {
        return strpos($file->getMimeType(), 'image/') === 0;
    }

    /**
     * 格式化文件大小
     *
     * @param int $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen($bytes) - 1) / 3);

        return sprintf('%.2f %s', $bytes / (1024 ** $factor), $units[$factor]);
    }

    /**
     * 删除文件
     *
     * @param string $path
     * @return bool
     */
    public function deleteFile(string $path): bool
    {
        try {
            return Storage::disk('public')->delete($path);
        } catch (Exception $e) {
            Log::error('文件删除失败', [
                'path' => $path,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}