/* empty css             *//* empty css               *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                *//* empty css                  *//* empty css                 */import{l as e,G as s,A as t,r as a,M as r,c as l,m as c,z as u,C as i,q as o,E as n}from"./vue-vendor-BcnDv-68.js";import{p,b as d,c as m,d as v}from"./export-C8s0bFWZ.js";import{b as y}from"./order-DI2E_fWj.js";import{_ as g}from"./index-eUTsTR3J.js";import{V as f,bk as h,X as w,aZ as _,U as x,Y as b,a1 as E,ak as T,Z as D,a5 as j,a6 as k}from"./element-plus-C2UshkXo.js";import"./index-D4AyIzGN.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const P={class:"function-test-page"},S={class:"test-content"},z={class:"test-buttons"},C={key:0,class:"test-result"},U={class:"test-content"},A={class:"test-buttons"},V={key:0,class:"test-result"},M={class:"test-content"},R={class:"test-buttons"},q={key:0,class:"test-result"},B={class:"test-content"},F={class:"test-buttons"},G={key:0,class:"test-result"},O={class:"test-summary"},Z={class:"summary-item"},H={class:"summary-number"},I={class:"summary-item success"},N={class:"summary-number"},Q={class:"summary-item error"},X={class:"summary-number"},Y={class:"summary-item"},J={class:"summary-number"};const K=g({__name:"FunctionTest",setup(e,{expose:s}){s();const t=a(""),c=r({userExport:!1,transactionExport:!1,distributorExport:!1,preview:!1,batch:!1,alert:!1}),u=r({export:null,preview:null,batch:null,alert:null}),i=a(!1),o=a([]),n=l(()=>o.value.length),g=l(()=>o.value.filter(e=>e.success).length),f=l(()=>o.value.filter(e=>!e.success).length),_=l(()=>n.value>0?g.value/n.value*100:0),x={previewType:t,testStatus:c,results:u,showSummary:i,testHistory:o,totalTests:n,successTests:g,failedTests:f,successRate:_,testUserExport:async()=>{c.userExport=!0;try{await new Promise(e=>setTimeout(e,1e3)),u.export={success:!0,message:"用户导出功能测试成功"},o.value.push({type:"export",success:!0,time:new Date}),w.success("用户导出功能测试成功")}catch(e){u.export={success:!1,message:"用户导出功能测试失败"},o.value.push({type:"export",success:!1,time:new Date}),w.error("用户导出功能测试失败")}finally{c.userExport=!1,i.value=!0}},testTransactionExport:async()=>{c.transactionExport=!0;try{await new Promise(e=>setTimeout(e,1e3)),u.export={success:!0,message:"交易导出功能测试成功"},o.value.push({type:"export",success:!0,time:new Date}),w.success("交易导出功能测试成功")}catch(e){u.export={success:!1,message:"交易导出功能测试失败"},o.value.push({type:"export",success:!1,time:new Date}),w.error("交易导出功能测试失败")}finally{c.transactionExport=!1,i.value=!0}},testDistributorExport:async()=>{c.distributorExport=!0;try{await new Promise(e=>setTimeout(e,1e3)),u.export={success:!0,message:"分销商导出功能测试成功"},o.value.push({type:"export",success:!0,time:new Date}),w.success("分销商导出功能测试成功")}catch(e){u.export={success:!1,message:"分销商导出功能测试失败"},o.value.push({type:"export",success:!1,time:new Date}),w.error("分销商导出功能测试失败")}finally{c.distributorExport=!1,i.value=!0}},testDataPreview:async()=>{c.preview=!0;try{t.value;await new Promise(e=>setTimeout(e,800)),u.preview={success:!0,message:"数据预览功能测试成功"},o.value.push({type:"preview",success:!0,time:new Date}),h({title:"测试成功",message:"数据预览功能工作正常",type:"success"})}catch(e){u.preview={success:!1,message:"数据预览功能测试失败"},o.value.push({type:"preview",success:!1,time:new Date}),h({title:"测试失败",message:"数据预览功能异常",type:"error"})}finally{c.preview=!1,i.value=!0}},testBatchProcess:async()=>{c.batch=!0;try{await new Promise(e=>setTimeout(e,1200)),u.batch={success:!0,message:"批量处理功能测试成功"},o.value.push({type:"batch",success:!0,time:new Date}),w.success("批量处理功能测试成功")}catch(e){u.batch={success:!1,message:"批量处理功能测试失败"},o.value.push({type:"batch",success:!1,time:new Date}),w.error("批量处理功能测试失败")}finally{c.batch=!1,i.value=!0}},testAlertSettings:async()=>{c.alert=!0;try{await new Promise(e=>setTimeout(e,1e3)),u.alert={success:!0,message:"告警设置功能测试成功"},o.value.push({type:"alert",success:!0,time:new Date}),w.success("告警设置功能测试成功")}catch(e){u.alert={success:!1,message:"告警设置功能测试失败"},o.value.push({type:"alert",success:!1,time:new Date}),w.error("告警设置功能测试失败")}finally{c.alert=!1,i.value=!0}},ref:a,reactive:r,computed:l,get ElMessage(){return w},get ElNotification(){return h},get exportUsers(){return v},get exportTransactions(){return m},get exportDistributors(){return d},get previewExportData(){return p},get batchProcessOrders(){return y}};return Object.defineProperty(x,"__isScriptSetup",{enumerable:!1,value:!0}),x}},[["render",function(a,r,l,p,d,m){const v=_,y=E,g=T,h=f,w=b,K=k,L=j,W=x;return c(),e("div",P,[s(h,null,{header:t(()=>r[1]||(r[1]=[o("div",{class:"card-header"},[o("span",null,"🧪 功能测试页面"),o("small",{style:{color:"#666","font-size":"12px","margin-left":"10px"}}," 验证所有已完善的后台功能 ")],-1)])),default:t(()=>[s(v,{title:"测试说明",type:"info",description:"此页面用于测试管理后台的各项功能是否正常工作。点击下方按钮即可测试对应功能。",style:{"margin-bottom":"20px"}}),s(W,{gutter:20},{default:t(()=>[s(w,{span:12},{default:t(()=>[s(h,{class:"test-card"},{header:t(()=>r[2]||(r[2]=[o("div",{class:"test-header"},[o("i",{class:"el-icon-download"}),o("span",null,"导出功能测试")],-1)])),default:t(()=>[o("div",S,[r[6]||(r[6]=o("p",{class:"test-desc"},"测试用户列表、财务记录、分销商等数据的导出功能",-1)),o("div",z,[s(y,{type:"primary",size:"small",onClick:p.testUserExport,loading:p.testStatus.userExport},{default:t(()=>r[3]||(r[3]=[n(" 测试用户导出 ",-1)])),_:1,__:[3]},8,["loading"]),s(y,{type:"success",size:"small",onClick:p.testTransactionExport,loading:p.testStatus.transactionExport},{default:t(()=>r[4]||(r[4]=[n(" 测试交易导出 ",-1)])),_:1,__:[4]},8,["loading"]),s(y,{type:"warning",size:"small",onClick:p.testDistributorExport,loading:p.testStatus.distributorExport},{default:t(()=>r[5]||(r[5]=[n(" 测试分销商导出 ",-1)])),_:1,__:[5]},8,["loading"])]),p.results.export?(c(),e("div",C,[s(g,{type:p.results.export.success?"success":"danger"},{default:t(()=>[n(D(p.results.export.message),1)]),_:1},8,["type"])])):i("",!0)])]),_:1})]),_:1}),s(w,{span:12},{default:t(()=>[s(h,{class:"test-card"},{header:t(()=>r[7]||(r[7]=[o("div",{class:"test-header"},[o("i",{class:"el-icon-view"}),o("span",null,"数据预览功能测试")],-1)])),default:t(()=>[o("div",U,[r[9]||(r[9]=o("p",{class:"test-desc"},"测试数据导出前的预览功能",-1)),o("div",A,[s(L,{modelValue:p.previewType,"onUpdate:modelValue":r[0]||(r[0]=e=>p.previewType=e),placeholder:"选择数据类型",size:"small"},{default:t(()=>[s(K,{label:"用户数据",value:"users"}),s(K,{label:"订单数据",value:"orders"}),s(K,{label:"财务数据",value:"finance"})]),_:1},8,["modelValue"]),s(y,{type:"primary",size:"small",onClick:p.testDataPreview,loading:p.testStatus.preview,disabled:!p.previewType},{default:t(()=>r[8]||(r[8]=[n(" 测试预览功能 ",-1)])),_:1,__:[8]},8,["loading","disabled"])]),p.results.preview?(c(),e("div",V,[s(g,{type:p.results.preview.success?"success":"danger"},{default:t(()=>[n(D(p.results.preview.message),1)]),_:1},8,["type"])])):i("",!0)])]),_:1})]),_:1}),s(w,{span:12},{default:t(()=>[s(h,{class:"test-card"},{header:t(()=>r[10]||(r[10]=[o("div",{class:"test-header"},[o("i",{class:"el-icon-s-operation"}),o("span",null,"批量处理功能测试")],-1)])),default:t(()=>[o("div",M,[r[12]||(r[12]=o("p",{class:"test-desc"},"测试订单等数据的批量处理功能",-1)),o("div",R,[s(y,{type:"primary",size:"small",onClick:p.testBatchProcess,loading:p.testStatus.batch},{default:t(()=>r[11]||(r[11]=[n(" 测试批量处理 ",-1)])),_:1,__:[11]},8,["loading"])]),p.results.batch?(c(),e("div",q,[s(g,{type:p.results.batch.success?"success":"danger"},{default:t(()=>[n(D(p.results.batch.message),1)]),_:1},8,["type"])])):i("",!0)])]),_:1})]),_:1}),s(w,{span:12},{default:t(()=>[s(h,{class:"test-card"},{header:t(()=>r[13]||(r[13]=[o("div",{class:"test-header"},[o("i",{class:"el-icon-warning"}),o("span",null,"告警设置功能测试")],-1)])),default:t(()=>[o("div",B,[r[15]||(r[15]=o("p",{class:"test-desc"},"测试防红系统的告警设置功能",-1)),o("div",F,[s(y,{type:"primary",size:"small",onClick:p.testAlertSettings,loading:p.testStatus.alert},{default:t(()=>r[14]||(r[14]=[n(" 测试告警设置 ",-1)])),_:1,__:[14]},8,["loading"])]),p.results.alert?(c(),e("div",G,[s(g,{type:p.results.alert.success?"success":"danger"},{default:t(()=>[n(D(p.results.alert.message),1)]),_:1},8,["type"])])):i("",!0)])]),_:1})]),_:1})]),_:1}),p.showSummary?(c(),u(h,{key:0,style:{"margin-top":"20px"}},{header:t(()=>r[16]||(r[16]=[o("div",{class:"card-header"},[o("span",null,"📊 测试结果汇总")],-1)])),default:t(()=>[o("div",O,[s(W,{gutter:20},{default:t(()=>[s(w,{span:6},{default:t(()=>[o("div",Z,[o("div",H,D(p.totalTests),1),r[17]||(r[17]=o("div",{class:"summary-label"},"总测试数",-1))])]),_:1}),s(w,{span:6},{default:t(()=>[o("div",I,[o("div",N,D(p.successTests),1),r[18]||(r[18]=o("div",{class:"summary-label"},"成功测试",-1))])]),_:1}),s(w,{span:6},{default:t(()=>[o("div",Q,[o("div",X,D(p.failedTests),1),r[19]||(r[19]=o("div",{class:"summary-label"},"失败测试",-1))])]),_:1}),s(w,{span:6},{default:t(()=>[o("div",Y,[o("div",J,D(Math.round(p.successRate))+"%",1),r[20]||(r[20]=o("div",{class:"summary-label"},"成功率",-1))])]),_:1})]),_:1})])]),_:1})):i("",!0)]),_:1})])}],["__scopeId","data-v-704fe3d8"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/FunctionTest.vue"]]);export{K as default};
