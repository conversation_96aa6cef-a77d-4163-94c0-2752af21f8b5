/**
 * ECharts 管理器 - 防止重复注册组件
 * 
 * 这个管理器确保ECharts组件只被注册一次，避免 "axisPointer CartesianAxisPointer exists" 错误
 */

let isInitialized = false
let initPromise = null

/**
 * 初始化ECharts组件 - 单例模式
 * @returns {Promise<void>}
 */
export async function initializeECharts() {
  // 如果已经初始化，直接返回
  if (isInitialized) {
    return Promise.resolve()
  }
  
  // 如果正在初始化，返回现有的Promise
  if (initPromise) {
    return initPromise
  }
  
  // 开始初始化
  initPromise = performInitialization()
  return initPromise
}

async function performInitialization() {
  try {
    console.log('🎯 开始初始化ECharts组件管理器...')
    
    // 动态导入ECharts组件，避免重复注册
    const { use } = await import('echarts/core')
    const { CanvasRenderer } = await import('echarts/renderers')
    const { 
      LineChart, 
      BarChart, 
      PieChart, 
      MapChart,
      HeatmapChart,
      FunnelChart,
      ScatterChart,
      RadarChart,
      GaugeChart
    } = await import('echarts/charts')
    
    const {
      TitleComponent,
      TooltipComponent,
      LegendComponent,
      GridComponent,
      CalendarComponent,
      VisualMapComponent,
      DataZoomComponent,
      MarkLineComponent,
      MarkPointComponent,
      DatasetComponent,
      TransformComponent,
      BrushComponent,
      TimelineComponent,
      ToolboxComponent
    } = await import('echarts/components')
    
    // 检查是否已经注册过，防止重复注册
    try {
      use([
        CanvasRenderer,
        LineChart,
        BarChart,
        PieChart,
        MapChart,
        HeatmapChart,
        FunnelChart,
        ScatterChart,
        RadarChart,
        GaugeChart,
        TitleComponent,
        TooltipComponent,
        LegendComponent,
        GridComponent,
        CalendarComponent,
        VisualMapComponent,
        DataZoomComponent,
        MarkLineComponent,
        MarkPointComponent,
        DatasetComponent,
        TransformComponent,
        BrushComponent,
        TimelineComponent,
        ToolboxComponent
      ])
    } catch (error) {
      // 如果组件已经注册，忽略错误
      if (error.message && error.message.includes('exists')) {
        console.warn('⚠️ ECharts组件已存在，跳过重复注册:', error.message)
      } else {
        throw error
      }
    }
    
    isInitialized = true
    console.log('✅ ECharts组件管理器初始化完成')
    
  } catch (error) {
    console.error('❌ ECharts组件管理器初始化失败:', error)
    // 重置状态以允许重试
    isInitialized = false
    initPromise = null
    throw error
  }
}

/**
 * 检查ECharts是否已初始化
 * @returns {boolean}
 */
export function isEChartsInitialized() {
  return isInitialized
}

/**
 * 重置ECharts管理器状态（仅用于测试或特殊情况）
 */
export function resetEChartsManager() {
  isInitialized = false
  initPromise = null
  console.log('🔄 ECharts管理器状态已重置')
}

/**
 * 安全获取ECharts实例
 * @param {HTMLElement} container DOM容器
 * @param {Object} theme 主题配置
 * @param {Object} opts 其他选项
 * @returns {Promise<echarts.ECharts>}
 */
export async function createEChartsInstance(container, theme = null, opts = {}) {
  // 确保ECharts已初始化
  await initializeECharts()
  
  // 直接导入echarts，因为组件已经在初始化时注册
  const echarts = await import('echarts')
  
  try {
    // 如果容器已经有图表实例，先销毁
    const existingInstance = echarts.getInstanceByDom(container)
    if (existingInstance) {
      existingInstance.dispose()
    }
    
    // 创建新实例 - 使用default导出
    return echarts.default ? echarts.default.init(container, theme, opts) : echarts.init(container, theme, opts)
  } catch (error) {
    console.error('❌ 创建ECharts实例失败:', error)
    throw error
  }
}

/**
 * 销毁ECharts实例的安全方法
 * @param {echarts.ECharts|HTMLElement} chartOrContainer 图表实例或容器
 */
export async function disposeEChartsInstance(chartOrContainer) {
  try {
    if (!chartOrContainer) return
    
    // 如果传入的是DOM元素，先获取实例
    if (chartOrContainer.nodeType === 1) {
      const echarts = await import('echarts')
      const echartsApi = echarts.default || echarts
      const instance = echartsApi.getInstanceByDom(chartOrContainer)
      if (instance) {
        instance.dispose()
      }
    } else if (chartOrContainer.dispose) {
      // 如果传入的是图表实例
      chartOrContainer.dispose()
    }
  } catch (error) {
    console.error('⚠️ 销毁ECharts实例时出错:', error)
  }
}

export default {
  initializeECharts,
  isEChartsInitialized,
  resetEChartsManager,
  createEChartsInstance,
  disposeEChartsInstance
}