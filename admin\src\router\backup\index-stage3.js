// 阶段三：财务和支付模块迁移
// 在阶段二基础上，迁移财务管理和支付管理模块
import { createRouter, createWebHashHistory } from 'vue-router'
import { dataScreenRoutes } from './dataScreen.js'

// === 完整的迁移路由配置 (阶段一 + 阶段二 + 阶段三) ===
const migratedRoutes = [
  // 登录页面
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      hideInMenu: true
    }
  },
  
  // 主布局 - 包含核心功能 + 业务管理 + 财务管理
  {
    path: '/admin',
    component: () => import('@/components/layout/ModernLayout.vue'),
    redirect: '/admin/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      // === 核心仪表板 (阶段一) ===
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/ModernDashboard.vue'),
        meta: {
          title: '仪表板',
          icon: 'TrendCharts',
          requiresAuth: true,
          group: '核心功能'
        }
      },

      // === 用户管理模块 (阶段一) ===
      {
        path: 'users',
        name: 'UserManagement',
        redirect: '/admin/users/list',
        meta: {
          title: '用户管理',
          icon: 'User',
          requiresAuth: true,
          group: '用户管理'
        },
        children: [
          {
            path: 'list',
            name: 'UserList',
            component: () => import('@/views/user/UserList.vue'),
            meta: {
              title: '用户列表',
              icon: 'List',
              requiresAuth: true
            }
          },
          {
            path: 'analytics',
            name: 'UserAnalytics',
            component: () => import('@/views/user/UserAnalytics.vue'),
            meta: {
              title: '用户分析',
              icon: 'TrendCharts',
              requiresAuth: true
            }
          },
          {
            path: 'profile',
            name: 'UserProfile',
            component: () => import('@/views/user/Profile.vue'),
            meta: {
              title: '个人资料',
              icon: 'Avatar',
              requiresAuth: true
            }
          }
        ]
      },

      // === 社群管理模块 (阶段二) ===
      {
        path: 'community',
        name: 'CommunityManagement',
        redirect: '/admin/community/groups',
        meta: {
          title: '社群管理',
          icon: 'ChatDotRound',
          requiresAuth: true,
          group: '社群功能'
        },
        children: [
          {
            path: 'groups',
            name: 'GroupManagement',
            component: () => import('@/views/community/GroupList.vue'),
            meta: {
              title: '群组管理',
              icon: 'ChatDotRound',
              requiresAuth: true
            }
          },
          {
            path: 'templates',
            name: 'TemplateManagement',
            component: () => import('@/views/community/TemplateManagement.vue'),
            meta: {
              title: '模板管理',
              icon: 'Document',
              requiresAuth: true
            }
          }
        ]
      },

      // === 代理商管理模块 (阶段二) ===
      {
        path: 'agents',
        name: 'AgentManagement',
        redirect: '/admin/agents/list',
        meta: {
          title: '代理商管理',
          icon: 'Avatar',
          requiresAuth: true,
          roles: ['admin'],
          group: '业务管理'
        },
        children: [
          {
            path: 'list',
            name: 'AgentList',
            component: () => import('@/views/agent/AgentList.vue'),
            meta: {
              title: '代理商列表',
              icon: 'List',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'hierarchy',
            name: 'AgentHierarchy',
            component: () => import('@/views/agent/AgentHierarchy.vue'),
            meta: {
              title: '层级结构',
              icon: 'Grid',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'commission',
            name: 'AgentCommission',
            component: () => import('@/views/agent/AgentCommission.vue'),
            meta: {
              title: '佣金管理',
              icon: 'Money',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'performance',
            name: 'AgentPerformance',
            component: () => import('@/views/agent/AgentPerformance.vue'),
            meta: {
              title: '业绩统计',
              icon: 'TrendCharts',
              requiresAuth: true,
              roles: ['admin']
            }
          }
        ]
      },

      // === 订单管理模块 (阶段二) ===
      {
        path: 'orders',
        name: 'OrderManagement',
        component: () => import('@/views/orders/OrderList.vue'),
        meta: {
          title: '订单管理',
          icon: 'ShoppingCart',
          requiresAuth: true,
          group: '业务管理'
        }
      },

      // === 财务管理模块 (新迁移 - 阶段三) ===
      {
        path: 'finance',
        name: 'FinanceManagement',
        redirect: '/admin/finance/dashboard',
        meta: {
          title: '财务管理',
          icon: 'Money',
          requiresAuth: true,
          roles: ['admin'],
          group: '财务管理'
        },
        children: [
          {
            path: 'dashboard',
            name: 'FinanceDashboard',
            component: () => import('@/views/finance/FinanceDashboard.vue'),
            meta: {
              title: '财务概览',
              icon: 'DataBoard',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'transactions',
            name: 'TransactionList',
            component: () => import('@/views/finance/TransactionList.vue'),
            meta: {
              title: '交易记录',
              icon: 'CreditCard',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'commission',
            name: 'CommissionLog',
            component: () => import('@/views/finance/CommissionLog.vue'),
            meta: {
              title: '佣金日志',
              icon: 'Coin',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'withdraw',
            name: 'WithdrawManage',
            component: () => import('@/views/finance/WithdrawManage.vue'),
            meta: {
              title: '提现管理',
              icon: 'Upload',
              requiresAuth: true,
              roles: ['admin']
            }
          }
        ]
      },

      // === 支付管理模块 (新迁移 - 阶段三) ===
      {
        path: 'payment',
        name: 'PaymentManagement',
        redirect: '/admin/payment/settings',
        meta: {
          title: '支付管理',
          icon: 'CreditCard',
          requiresAuth: true,
          roles: ['admin'],
          group: '支付系统'
        },
        children: [
          {
            path: 'settings',
            name: 'PaymentSettings',
            component: () => import('@/views/payment/PaymentSettings.vue'),
            meta: {
              title: '支付设置',
              icon: 'Setting',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'channels',
            name: 'PaymentChannelManagement',
            component: () => import('@/views/payment/PaymentChannelManagement.vue'),
            meta: {
              title: '支付渠道',
              icon: 'Connection',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'orders',
            name: 'PaymentOrders',
            component: () => import('@/views/payment/PaymentOrders.vue'),
            meta: {
              title: '支付订单',
              icon: 'Tickets',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'logs',
            name: 'PaymentLogs',
            component: () => import('@/views/payment/PaymentLogs.vue'),
            meta: {
              title: '支付日志',
              icon: 'Document',
              requiresAuth: true,
              roles: ['admin']
            }
          }
        ]
      },

      // === 分销推广模块 (新迁移 - 阶段三) ===
      {
        path: 'promotion',
        name: 'PromotionManagement',
        redirect: '/admin/promotion/distributors',
        meta: {
          title: '分销推广',
          icon: 'Share',
          requiresAuth: true,
          roles: ['admin'],
          group: '推广营销'
        },
        children: [
          {
            path: 'distributors',
            name: 'DistributorManagement',
            component: () => import('@/views/distribution/DistributorList.vue'),
            meta: {
              title: '分销商管理',
              icon: 'User',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'links',
            name: 'PromotionLinks',
            component: () => import('@/views/promotion/LinkManagement.vue'),
            meta: {
              title: '推广链接',
              icon: 'Link',
              requiresAuth: true,
              roles: ['admin']
            }
          }
        ]
      }
    ]
  }
]

// === 兼容性重定向路由 ===
const compatibilityRoutes = [
  {
    path: '/',
    redirect: '/admin/dashboard'
  },
  {
    path: '/dashboard',
    redirect: '/admin/dashboard'
  },
  
  // 用户管理重定向
  {
    path: '/users',
    redirect: '/admin/users/list'
  },
  {
    path: '/users/analytics',
    redirect: '/admin/users/analytics'
  },
  
  // 社群管理重定向
  {
    path: '/community/groups',
    redirect: '/admin/community/groups'
  },
  {
    path: '/community/templates',
    redirect: '/admin/community/templates'
  },
  
  // 代理商管理重定向
  {
    path: '/agent/list',
    redirect: '/admin/agents/list'
  },
  {
    path: '/agent/hierarchy',
    redirect: '/admin/agents/hierarchy'
  },
  {
    path: '/agent/commission',
    redirect: '/admin/agents/commission'
  },
  {
    path: '/agent/performance',
    redirect: '/admin/agents/performance'
  },
  
  // 订单管理重定向
  {
    path: '/orders',
    redirect: '/admin/orders'
  },
  
  // 财务管理重定向 (新增)
  {
    path: '/finance',
    redirect: '/admin/finance/dashboard'
  },
  {
    path: '/finance/transactions',
    redirect: '/admin/finance/transactions'
  },
  {
    path: '/finance/commission',
    redirect: '/admin/finance/commission'
  },
  {
    path: '/finance/withdraw',
    redirect: '/admin/finance/withdraw'
  },
  
  // 支付管理重定向 (新增)
  {
    path: '/payment/settings',
    redirect: '/admin/payment/settings'
  },
  {
    path: '/payment/channels',
    redirect: '/admin/payment/channels'
  },
  {
    path: '/payment/orders',
    redirect: '/admin/payment/orders'
  },
  {
    path: '/payment/logs',
    redirect: '/admin/payment/logs'
  },
  
  // 分销推广重定向 (新增)
  {
    path: '/distribution/distributors',
    redirect: '/admin/promotion/distributors'
  },
  {
    path: '/promotion/links',
    redirect: '/admin/promotion/links'
  }
]

// === 暂时保留的旧路由 (未迁移模块) ===
const legacyRoutes = [
  // 防红系统（保持旧结构，待阶段四迁移）
  {
    path: '/anti-block/dashboard',
    name: 'AntiBlockDashboard',
    component: () => import('@/views/anti-block/Dashboard.vue'),
    meta: {
      title: '防红系统',
      icon: 'Lock',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/anti-block/domains',
    name: 'DomainManagement',
    component: () => import('@/views/anti-block/DomainList.vue'),
    meta: {
      title: '域名管理',
      icon: 'Connection',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/anti-block/short-links',
    name: 'ShortLinkManagement',
    component: () => import('@/views/anti-block/ShortLinkList.vue'),
    meta: {
      title: '短链管理',
      icon: 'Link',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/anti-block/analytics',
    name: 'AntiBlockAnalytics',
    component: () => import('@/views/anti-block/Analytics.vue'),
    meta: {
      title: '防红分析',
      icon: 'TrendCharts',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 权限管理（保持旧结构，待阶段四迁移）
  {
    path: '/permission/roles',
    name: 'RoleManagement',
    component: () => import('@/views/permission/RoleManagement.vue'),
    meta: {
      title: '角色管理',
      icon: 'Avatar',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/permission/permissions',
    name: 'PermissionManagement',
    component: () => import('@/views/permission/PermissionManagement.vue'),
    meta: {
      title: '权限管理',
      icon: 'Lock',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 系统管理（保持旧结构，待阶段四迁移）
  {
    path: '/system/settings',
    name: 'SystemSettings',
    component: () => import('@/views/system/Settings.vue'),
    meta: {
      title: '系统设置',
      icon: 'Setting',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/monitor',
    name: 'SystemMonitor',
    component: () => import('@/views/system/Monitor.vue'),
    meta: {
      title: '系统监控',
      icon: 'Monitor',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/operation-logs',
    name: 'OperationLogs',
    component: () => import('@/views/system/OperationLogs.vue'),
    meta: {
      title: '操作日志',
      icon: 'Document',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/notifications',
    name: 'NotificationManagement',
    component: () => import('@/views/system/Notifications.vue'),
    meta: {
      title: '通知管理',
      icon: 'Bell',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/data-export',
    name: 'DataExport',
    component: () => import('@/views/system/DataExport.vue'),
    meta: {
      title: '数据导出',
      icon: 'Document',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/file-management',
    name: 'FileManagement',
    component: () => import('@/views/system/FileManagement.vue'),
    meta: {
      title: '文件管理',
      icon: 'Folder',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 测试路由
  {
    path: '/test',
    name: 'TestRoute',
    component: () => import('@/views/TestRoute.vue'),
    meta: {
      title: '路由测试',
      hideInMenu: true
    }
  },
  {
    path: '/navigation-test',
    name: 'NavigationTest',
    component: () => import('@/views/NavigationTest.vue'),
    meta: {
      title: '导航测试',
      hideInMenu: true
    }
  },

  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/ErrorPage.vue'),
    meta: {
      title: '页面未找到',
      hideInMenu: true
    }
  }
]

// 合并所有路由
const allRoutes = [...migratedRoutes, ...compatibilityRoutes, ...legacyRoutes, ...dataScreenRoutes]

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes: allRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  console.log(`🛣️ [阶段三] 路由导航: ${from.path} → ${to.path}`)

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 晨鑫流量变现系统 管理系统`
  }

  console.log('✅ 阶段三路由导航完成')
  next()
})

// 导出迁移状态信息
export const migrationStatus = {
  stage: 3,
  description: '财务和支付模块迁移',
  migratedRoutes: [
    '/admin/dashboard',
    '/admin/users/*',
    '/admin/community/*',
    '/admin/agents/*',
    '/admin/orders',
    '/admin/finance/*',
    '/admin/payment/*',
    '/admin/promotion/*'
  ],
  pendingRoutes: ['防红系统', '权限管理', '系统管理'],
  riskLevel: 'medium',
  rollbackReady: true,
  newFeatures: ['财务模块嵌套', '支付管理整合', '分销推广统一'],
  breaking_changes: ['财务相关URL变更', '支付路径调整', '分销推广路由重构'],
  completedModules: [
    '核心功能模块',
    '用户管理模块',
    '社群管理模块',
    '代理商管理模块',
    '订单管理模块',
    '财务管理模块',
    '支付管理模块',
    '分销推广模块'
  ]
}

export default router