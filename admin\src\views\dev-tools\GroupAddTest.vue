<template>
  <div class="group-add-test">
    <div class="test-header">
      <h1>🧪 创建群组测试页面</h1>
      <p>这是一个简化的测试页面，用于验证组件是否能正常加载</p>
    </div>

    <div class="test-content">
      <div class="test-section">
        <h2>📝 基础表单测试</h2>
        <el-form :model="form" label-width="120px">
          <el-form-item label="群组名称">
            <el-input v-model="form.title" placeholder="请输入群组名称" />
          </el-form-item>
          
          <el-form-item label="群组价格">
            <el-input-number v-model="form.price" :min="0" />
          </el-form-item>
          
          <el-form-item label="群组描述">
            <el-input 
              v-model="form.description" 
              type="textarea" 
              :rows="3"
              placeholder="请输入群组描述"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSubmit">
              创建群组
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="test-section">
        <h2>👁️ 实时预览测试</h2>
        <div class="preview-box">
          <h3>{{ form.title || '群组名称预览' }}</h3>
          <p>价格: {{ form.price === 0 ? '免费' : `¥${form.price}` }}</p>
          <p>{{ form.description || '群组描述预览' }}</p>
          <p class="update-time">更新时间: {{ updateTime }}</p>
        </div>
      </div>

      <div class="test-section">
        <h2>🔧 功能测试</h2>
        <div class="test-buttons">
          <el-button @click="testMessage" type="success">
            测试消息提示
          </el-button>
          <el-button @click="testNavigation" type="info">
            测试页面跳转
          </el-button>
          <el-button @click="testValidation" type="warning">
            测试表单验证
          </el-button>
        </div>
      </div>

      <div class="test-section">
        <h2>📊 诊断信息</h2>
        <div class="diagnostic-info">
          <p><strong>当前路由:</strong> {{ $route.path }}</p>
          <p><strong>组件名称:</strong> GroupAddTest.vue</p>
          <p><strong>Vue版本:</strong> {{ vueVersion }}</p>
          <p><strong>Element Plus:</strong> {{ elementPlusLoaded ? '✅ 已加载' : '❌ 未加载' }}</p>
          <p><strong>页面加载时间:</strong> {{ loadTime }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 响应式数据
const form = reactive({
  title: '',
  price: 0,
  description: ''
})

const updateTime = ref(new Date().toLocaleString())
const loadTime = ref('')
const vueVersion = ref('3.x')
const elementPlusLoaded = ref(true)

// 监听表单变化
watch(form, () => {
  updateTime.value = new Date().toLocaleString()
}, { deep: true })

// 页面加载时记录时间
onMounted(() => {
  loadTime.value = new Date().toLocaleString()
  console.log('🧪 GroupAddTest 组件已加载')
  console.log('路由信息:', route)
})

// 方法
const handleSubmit = () => {
  if (!form.title) {
    ElMessage.error('请输入群组名称')
    return
  }
  
  ElMessage.success('测试提交成功！')
  console.log('提交的数据:', form)
}

const handleReset = () => {
  form.title = ''
  form.price = 0
  form.description = ''
  ElMessage.info('表单已重置')
}

const testMessage = () => {
  ElMessage({
    message: '消息提示功能正常工作！',
    type: 'success',
    duration: 3000
  })
}

const testNavigation = () => {
  ElMessage.info('准备跳转到群组列表页面')
  setTimeout(() => {
    router.push('/community/groups')
  }, 1000)
}

const testValidation = () => {
  if (form.title && form.description) {
    ElMessage.success('表单验证通过！')
  } else {
    ElMessage.warning('请填写群组名称和描述')
  }
}
</script>

<style scoped>
.group-add-test {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-header h1 {
  color: #409eff;
  margin-bottom: 10px;
}

.test-header p {
  color: #606266;
}

.test-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.test-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 18px;
}

.preview-box {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.preview-box h3 {
  color: #409eff;
  margin-bottom: 10px;
}

.preview-box p {
  margin: 5px 0;
  color: #606266;
}

.update-time {
  font-size: 12px;
  color: #909399;
  border-top: 1px solid #ebeef5;
  padding-top: 10px;
  margin-top: 10px;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.diagnostic-info {
  background: #f0f9ff;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #b3d8ff;
}

.diagnostic-info p {
  margin: 5px 0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

@media (max-width: 768px) {
  .test-content {
    grid-template-columns: 1fr;
  }
  
  .test-buttons {
    flex-direction: column;
  }
}
</style>
