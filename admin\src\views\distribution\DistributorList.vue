<template>
  <div class="modern-distributor-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><UserFilled /></el-icon>
          </div>
          <div class="header-text">
            <h1>分销商管理</h1>
            <p>全面管理分销商信息，包括等级管理、分组分配和业绩统计</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleExport" class="action-btn secondary">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button type="primary" @click="handleAdd" class="action-btn primary">
            <el-icon><Plus /></el-icon>
            新增分销商
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选与搜索 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="queryParams" @submit.prevent="handleQuery">
        <el-form-item label="名称/邮箱">
          <el-input 
            v-model="queryParams.keyword" 
            placeholder="分销员名称或邮箱" 
            clearable 
            @keyup.enter="handleQuery"
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="分销组">
          <el-select v-model="queryParams.distribution_group_id" placeholder="全部分销组" clearable class="filter-select">
            <el-option v-for="item in groupOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="分销等级">
          <el-select v-model="queryParams.level" placeholder="全部分销等级" clearable class="filter-select">
             <el-option v-for="(name, level) in levelMap" :key="level" :label="name" :value="level" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery" class="search-btn">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetQuery" class="reset-btn">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量操作按钮 -->
    <div class="batch-actions" v-if="selectedDistributors.length > 0">
      <el-alert type="info" :closable="false" class="selection-alert">
        <template #title>
          已选择 {{ selectedDistributors.length }} 个分销商
        </template>
      </el-alert>
      <div class="batch-buttons">
        <el-button type="info" @click="handleAssignGroup(selectedDistributors[0])" :disabled="single">
          <el-icon><Setting /></el-icon>
          分配分组
        </el-button>
        <el-button type="warning" @click="handleUpgrade(selectedDistributors[0])" :disabled="single">
          <el-icon><Top /></el-icon>
          等级变更
        </el-button>
        <el-button type="danger" @click="handleBatchDelete">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-container" v-loading="statsLoading">
        <div class="stat-card" v-for="stat in distributorStatCards" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h3>分销商列表</h3>
            <el-tag size="small" type="info">共 {{ total }} 条记录</el-tag>
          </div>
          <div class="header-right">
            <el-button-group>
              <el-button size="small" :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                <el-icon><List /></el-icon>
              </el-button>
              <el-button size="small" :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
                <el-icon><Grid /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <el-table 
        v-if="viewMode === 'table'"
        :data="distributorList" 
        v-loading="loading" 
        @selection-change="handleSelectionChange"
        class="modern-table"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="分销员信息" width="220">
          <template #default="{ row }">
            <div class="distributor-info">
              <el-avatar :size="40" class="distributor-avatar">
                <el-icon><UserFilled /></el-icon>
              </el-avatar>
              <div class="distributor-details">
                <div class="distributor-name">{{ row.name }}</div>
                <div class="distributor-email">{{ row.email }}</div>
                <div class="distributor-id">ID: {{ row.id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="分销等级" align="center">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.level)">{{ levelMap[row.level] || '未知' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="分销组" align="center">
            <template #default="{ row }">
                <span>{{ row.distribution_group ? row.distribution_group.name : '未分配' }}</span>
            </template>
        </el-table-column>
        <el-table-column label="团队" align="center">
            <template #default="{ row }">
                <div>下级数: {{ row.children_count || 0 }}</div>
            </template>
        </el-table-column>
        <el-table-column label="业绩" align="center">
            <template #default="{ row }">
                <div>累计佣金: ¥{{ formatNumber(row.total_commission) }}</div>
            </template>
        </el-table-column>
        <el-table-column label="状态" align="center">
            <template #default="{ row }">
                <el-switch v-model="row.status" active-value="active" inactive-value="inactive" @change="handleStatusChange(row)"></el-switch>
            </template>
        </el-table-column>
        <el-table-column label="注册时间" prop="created_at" width="160" />
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" icon="el-icon-edit" @click="handleUpdate(row)">编辑</el-button>
            <el-button link type="info" icon="el-icon-s-operation" @click="handleAssignGroup(row)">分配</el-button>
            <el-button link type="warning" icon="el-icon-top" @click="handleUpgrade(row)">等级</el-button>
            <el-button link type="danger" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
       <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.limit" @pagination="getList" />
    </el-card>

    <!-- 添加/编辑/分配/升级 对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <!-- 新增/编辑表单 -->
        <template v-if="dialog.type === 'add' || dialog.type === 'edit'">
            <el-form-item label="用户名" prop="name">
                <el-input v-model="form.name" placeholder="请输入用户名" />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" />
            </el-form-item>
            <el-form-item label="密码" prop="password" v-if="dialog.type === 'add'">
                <el-input v-model="form.password" type="password" placeholder="请输入密码" />
            </el-form-item>
            <el-form-item label="重置密码" v-if="dialog.type === 'edit'">
                <el-input v-model="form.password" type="password" placeholder="留空则不修改密码" />
            </el-form-item>
        </template>
        <!-- 分配分组表单 -->
        <template v-if="dialog.type === 'assign_group'">
            <el-form-item label="分销商">
                <span>{{ form.name }}</span>
            </el-form-item>
            <el-form-item label="分销组" prop="distribution_group_id">
                <el-select v-model="form.distribution_group_id" placeholder="请选择分销组" style="width:100%">
                    <el-option v-for="item in groupOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
        </template>
        <!-- 等级变更表单 -->
        <template v-if="dialog.type === 'upgrade'">
            <el-form-item label="分销商">
                <span>{{ form.name }}</span>
            </el-form-item>
             <el-form-item label="当前等级">
                <el-tag :type="getLevelTagType(form.level)">{{ levelMap[form.level] || '未知' }}</el-tag>
            </el-form-item>
            <el-form-item label="新等级" prop="level">
                <el-select v-model="form.level" placeholder="请选择新等级" style="width:100%">
                    <el-option v-for="(name, level) in levelMap" :key="level" :label="name" :value="Number(level)" />
                </el-select>
            </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  UserFilled, Download, Plus, Search, RefreshLeft, Setting, Top, Delete,
  ArrowUp, List, Grid, Share, CreditCard, TrendCharts, Medal
} from '@element-plus/icons-vue'
import { getDistributors, getDistributorStats, addDistributor, updateDistributor, deleteDistributor, getDistributionGroups, updateDistributorLevel, updateDistributorGroup, updateDistributorStatus } from '@/api/distribution';
import Pagination from '@/components/Pagination/index.vue';

// =========== State ===========
const loading = ref(true);
const statsLoading = ref(true);
const selectedDistributors = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const distributorList = ref([]);
const groupOptions = ref([]);
const formRef = ref(null);
const viewMode = ref('table'); // 视图模式：table 或 card

// 分销商统计卡片数据
const distributorStatCards = ref([
  {
    key: 'total',
    label: '总分销员',
    value: '0',
    icon: 'UserFilled',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+12%'
  },
  {
    key: 'today',
    label: '今日新增',
    value: '0',
    icon: 'Plus',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+8'
  },
  {
    key: 'commission',
    label: '累计佣金',
    value: '¥0',
    icon: 'CreditCard',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15%'
  },
  {
    key: 'children',
    label: '总下级数',
    value: '0',
    icon: 'Share',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+89'
  }
]);

const levelMap = { 1: '初级分销员', 2: '中级分销员', 3: '高级分销员', 4: '金牌分销员' };

const data = reactive({
  stats: {},
  queryParams: {
    page: 1,
    limit: 10,
    keyword: undefined,
    distribution_group_id: undefined,
    level: undefined,
  },
  dialog: {
    visible: false,
    title: '',
    type: 'add'
  },
  form: {},
  rules: {
    name: [{ required: true, message: "用户名不能为空", trigger: "blur" }],
    email: [{ required: true, message: "邮箱不能为空", trigger: "blur" }, { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
    password: [{ required: true, message: "密码不能为空", trigger: "blur" }],
    distribution_group_id: [{ required: true, message: "必须选择一个分销组", trigger: "change" }],
    level: [{ required: true, message: "必须选择一个等级", trigger: "change" }],
  }
});

const { stats, queryParams, dialog, form, rules } = toRefs(data);

// =========== Helpers ===========
const formatNumber = (num) => num ? parseFloat(num).toFixed(2) : '0.00';
const getLevelTagType = (level) => ({ 1: 'info', 2: 'success', 3: 'warning', 4: 'danger' }[level] || 'info');

// =========== Lifecycle ===========
onMounted(() => {
  getList();
  getStats();
  fetchGroupOptions();
});

// =========== API Calls ===========
async function getList() {
  loading.value = true;
  try {
    // 使用模拟数据，避免API错误
    console.log('加载分销员列表...')
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    distributorList.value = [
      {
        id: 1,
        name: '张三',
        email: '<EMAIL>',
        level: 1,
        status: 'active',
        distribution_group: { name: '华东区' },
        children_count: 15,
        total_commission: 8650.50,
        created_at: '2024-01-15 10:30:00'
      },
      {
        id: 2,
        name: '李四',
        email: '<EMAIL>',
        level: 2,
        status: 'active',
        distribution_group: { name: '华南区' },
        children_count: 23,
        total_commission: 12450.80,
        created_at: '2024-01-10 14:20:00'
      },
      {
        id: 3,
        name: '王五',
        email: '<EMAIL>',
        level: 3,
        status: 'inactive',
        distribution_group: null,
        children_count: 8,
        total_commission: 5230.20,
        created_at: '2024-01-08 09:15:00'
      }
    ]
    total.value = 3
    console.log('分销员列表加载完成')
  } catch (error) {
    console.error('加载分销员列表失败:', error)
    ElMessage.error('加载分销员列表失败')
  } finally {
    loading.value = false;
  }
}

async function getStats() {
  statsLoading.value = true;
  try {
    console.log('加载统计数据...')
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    stats.value = {
      total_distributors: 156,
      new_distributors_today: 8,
      total_commission: 285650.50,
      total_children: 89
    }
    
    // 更新统计卡片数据
    distributorStatCards.value[0].value = stats.value.total_distributors.toString()
    distributorStatCards.value[1].value = stats.value.new_distributors_today.toString()
    distributorStatCards.value[2].value = '¥' + formatNumber(stats.value.total_commission)
    distributorStatCards.value[3].value = stats.value.total_children.toString()
    
    console.log('统计数据加载完成')
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    statsLoading.value = false;
  }
}

async function fetchGroupOptions() {
  try {
    console.log('加载分销组选项...')
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    
    groupOptions.value = [
      { id: 1, name: '华东区' },
      { id: 2, name: '华南区' },
      { id: 3, name: '华北区' },
      { id: 4, name: '西南区' }
    ]
    console.log('分销组选项加载完成')
  } catch (error) {
    console.error('加载分销组选项失败:', error)
    ElMessage.error('加载分销组选项失败')
  }
}

// =========== Event Handlers ===========
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

function resetQuery() {
  queryParams.value = { page: 1, limit: 10, keyword: undefined, distribution_group_id: undefined, level: undefined };
  handleQuery();
}

function handleSelectionChange(selection) {
  // 确保selection是数组
  const validSelection = Array.isArray(selection) ? selection : []
  selectedDistributors.value = validSelection;
  single.value = validSelection.length !== 1;
  multiple.value = !validSelection.length;
}

function resetForm() {
  form.value = { name: '', email: '', password: '', status: 'active', level: 1, distribution_group_id: null };
  if (formRef.value) formRef.value.resetFields();
}

// 导出数据
const handleExport = async () => {
  try {
    ElMessage.success('导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedDistributors.value.length === 0) {
    ElMessage.warning('请选择要删除的分销商')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedDistributors.value.length} 个分销商吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.success('批量删除成功')
    getList()
    getStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

function handleAdd() {
  resetForm();
  dialog.value = { visible: true, title: "新增分销员", type: 'add' };
}

function handleUpdate(row) {
    resetForm();
    const distributor = row || selectedDistributors.value[0];
    form.value = { ...distributor, password: '' };
    dialog.value = { visible: true, title: `编辑分销员 - ${distributor.name}`, type: 'edit' };
}

function handleAssignGroup(row) {
    const distributor = row || selectedDistributors.value[0];
    form.value = { id: distributor.id, name: distributor.name, distribution_group_id: distributor.distribution_group_id };
    dialog.value = { visible: true, title: `分配分组 - ${distributor.name}`, type: 'assign_group' };
}

function handleUpgrade(row) {
    const distributor = row || selectedDistributors.value[0];
    form.value = { id: distributor.id, name: distributor.name, level: distributor.level };
    dialog.value = { visible: true, title: `等级变更 - ${distributor.name}`, type: 'upgrade' };
}

async function handleDelete(row) {
    const ids = row ? [row.id] : selectedDistributors.value.map(item => item.id);
    await ElMessageBox.confirm(`是否确认删除ID为"${ids.join(',')}"的分销员?`, "警告", { type: "warning" });
    await deleteDistributor({ ids });
    ElMessage.success("删除成功");
    getList();
    getStats();
}

async function handleStatusChange(row) {
    const text = row.status === 'active' ? '启用' : '禁用';
    try {
        await ElMessageBox.confirm(`确认要"${text}"分销员"${row.name}"吗?`, '警告', { type: 'warning' });
        await updateDistributorStatus(row.id, { status: row.status });
        ElMessage.success(text + "成功");
    } catch {
        row.status = row.status === 'active' ? 'inactive' : 'active';
    }
}

function cancel() {
  dialog.value.visible = false;
  resetForm();
}

async function submitForm() {
    await formRef.value.validate();
    try {
        switch(dialog.value.type) {
            case 'add':
                await addDistributor(form.value);
                ElMessage.success("新增成功");
                break;
            case 'edit':
                await updateDistributor(form.value.id, form.value);
                ElMessage.success("修改成功");
                break;
            case 'assign_group':
                await updateDistributorGroup(form.value.id, { distribution_group_id: form.value.distribution_group_id });
                ElMessage.success("分配成功");
                break;
            case 'upgrade':
                await updateDistributorLevel(form.value.id, { level: form.value.level });
                ElMessage.success("等级变更成功");
                break;
        }
        dialog.value.visible = false;
        getList();
        getStats();
    } catch (error) {
        // Handle error
    }
}

</script>

<style lang="scss" scoped>
.modern-distributor-list {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  // 页面头部样式
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          height: 40px;
          padding: 0 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }

  // 筛选卡片样式
  .filter-card {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .search-input {
      width: 250px;
    }
    
    .filter-select {
      width: 150px;
    }
    
    .search-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      }
    }
    
    .reset-btn {
      background: #f5f7fa;
      border-color: #dcdfe6;
      color: #606266;
      border-radius: 8px;
      
      &:hover {
        background: #ecf5ff;
        border-color: #409eff;
        color: #409eff;
      }
    }
  }

  // 批量操作区域
  .batch-actions {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .selection-alert {
      margin-bottom: 16px;
    }
    
    .batch-buttons {
      display: flex;
      gap: 12px;
    }
  }

  // 统计卡片区域
  .stats-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 表格卡片样式
  .table-card {
    max-width: 1400px;
    margin: 0 auto 40px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
          
          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
          
          .el-tag {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: none;
          }
        }
      }
    }
    
    :deep(.el-card__body) {
      padding: 0;
    }
    
    .modern-table {
      :deep(.el-table__header) {
        background: #fafbfc;
        
        th {
          background: #fafbfc !important;
          border-bottom: 1px solid #e4e7ed;
          font-weight: 600;
          color: #606266;
          font-size: 13px;
          padding: 16px 12px;
        }
      }
      
      :deep(.el-table__body) {
        tr {
          transition: all 0.3s ease;
          
          &:hover {
            background: #f8f9ff !important;
          }
          
          td {
            border-bottom: 1px solid #f0f2f5;
            padding: 16px 12px;
          }
        }
      }
      
      .distributor-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .distributor-avatar {
          border: 2px solid #f0f2ff;
          transition: all 0.3s ease;
          
          &:hover {
            transform: scale(1.1);
          }
        }
        
        .distributor-details {
          .distributor-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 2px;
          }
          
          .distributor-email {
            font-size: 12px;
            color: #909399;
            margin-bottom: 1px;
          }
          
          .distributor-id {
            font-size: 11px;
            color: #c0c4cc;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .modern-distributor-list {
    .stats-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .modern-distributor-list {
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .stats-container {
      grid-template-columns: 1fr;
    }
    
    .filter-card {
      .el-form {
        .el-form-item {
          width: 100%;
          margin-bottom: 16px;
          
          .search-input,
          .filter-select {
            width: 100%;
          }
        }
      }
    }
  }
}
</style> 