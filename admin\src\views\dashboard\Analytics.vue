<template>
  <div class="analytics-dashboard">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">📊 数据分析</h1>
        <p class="page-description">深入了解系统运行数据和用户行为分析</p>
      </div>
      
      <!-- 时间范围选择器 -->
      <div class="time-selector">
        <el-radio-group v-model="selectedTimeRange" @change="handleTimeRangeChange">
          <el-radio-button label="7d">最近7天</el-radio-button>
          <el-radio-button label="30d">最近30天</el-radio-button>
          <el-radio-button label="90d">最近90天</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <div class="analytics-content">
      <!-- 关键指标卡片 -->
      <div class="metrics-grid">
        <div 
          v-for="metric in metricsData" 
          :key="metric.key"
          class="metric-card"
          :class="{ loading: isLoading }"
        >
          <div class="metric-icon" :style="{ 
            background: metric.gradient, 
            color: 'white',
            boxShadow: `0 8px 25px ${metric.shadowColor}`
          }">
            <i :class="metric.icon"></i>
          </div>
          <div class="metric-info">
            <h3>{{ metric.title }}</h3>
            <p class="metric-value">
              <span v-if="!isLoading">{{ formatMetricValue(metric.value, metric.unit) }}</span>
              <el-skeleton-item v-else variant="text" style="width: 80px" />
            </p>
            <span 
              v-if="!isLoading"
              class="metric-change" 
              :class="metric.change >= 0 ? 'positive' : 'negative'"
            >
              <i :class="metric.change >= 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
              {{ Math.abs(metric.change) }}%
            </span>
            <el-skeleton-item v-else variant="text" style="width: 50px" />
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <!-- 访问趋势图表 -->
        <div class="chart-card trend-chart">
          <div class="chart-header">
            <h3>访问趋势</h3>
            <div class="chart-controls">
              <el-select v-model="trendChartType" size="small" style="width: 120px">
                <el-option label="访问量" value="visits"></el-option>
                <el-option label="用户数" value="users"></el-option>
                <el-option label="页面浏览" value="pageviews"></el-option>
              </el-select>
            </div>
          </div>
          <div class="chart-content" ref="trendChartRef" v-loading="isLoading"></div>
        </div>
        
        <!-- 用户地理分布地图 -->
        <div class="chart-card map-chart">
          <div class="chart-header">
            <h3>用户地理分布</h3>
            <div class="chart-info">
              <span class="total-users">总用户: {{ formatNumber(totalMapUsers) }}</span>
            </div>
          </div>
          <div class="chart-content" ref="mapChartRef" v-loading="isLoading"></div>
        </div>
      </div>

      <!-- 详细分析区域 -->
      <div class="detailed-section">
        <!-- 用户行为分析 -->
        <div class="chart-card behavior-chart">
          <h3>用户行为分析</h3>
          <div class="chart-content" ref="behaviorChartRef" v-loading="isLoading"></div>
        </div>
        
        <!-- 设备分析 -->
        <div class="chart-card device-chart">
          <h3>设备分析</h3>
          <div class="chart-content" ref="deviceChartRef" v-loading="isLoading"></div>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="data-table-section">
        <div class="table-header">
          <h3>详细数据</h3>
          <div class="table-controls">
            <el-button size="small" @click="exportData">
              <i class="el-icon-download"></i>
              导出数据
            </el-button>
            <el-button size="small" @click="refreshData">
              <i class="el-icon-refresh" :class="{ spinning: isRefreshing }"></i>
              刷新
            </el-button>
          </div>
        </div>
        <div class="table-container">
          <el-table 
            :data="tableData" 
            v-loading="isLoading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="date" label="日期" width="120" />
            <el-table-column prop="visits" label="访问量" width="100" />
            <el-table-column prop="users" label="用户数" width="100" />
            <el-table-column prop="pageviews" label="页面浏览" width="120" />
            <el-table-column prop="conversion" label="转化率" width="100">
              <template #default="scope">
                {{ scope.row.conversion }}%
              </template>
            </el-table-column>
            <el-table-column prop="revenue" label="收入" width="120">
              <template #default="scope">
                ¥{{ formatNumber(scope.row.revenue) }}
              </template>
            </el-table-column>
            <el-table-column prop="bounce" label="跳出率" width="100">
              <template #default="scope">
                {{ scope.row.bounce }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { createEChartsInstance, disposeEChartsInstance } from '@/utils/echartsManager'
import { loadChinaMap, getProvinceDataMap, formatMapData, generateVisualMap } from '@/utils/mapLoader'

// 响应式数据
const isLoading = ref(true)
const isRefreshing = ref(false)
const selectedTimeRange = ref('30d')
const trendChartType = ref('visits')

// 图表引用
const trendChartRef = ref(null)
const mapChartRef = ref(null)
const behaviorChartRef = ref(null)
const deviceChartRef = ref(null)

// 图表实例
const charts = ref({})

// 指标数据
const metricsData = ref([
  {
    key: 'visits',
    title: '总访问量',
    value: 125847,
    unit: '',
    change: 12.5,
    icon: 'el-icon-view',
    color: '#667eea',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    shadowColor: 'rgba(102, 126, 234, 0.3)'
  },
  {
    key: 'users',
    title: '活跃用户',
    value: 8432,
    unit: '',
    change: 8.2,
    icon: 'el-icon-user',
    color: '#f093fb',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    shadowColor: 'rgba(240, 147, 251, 0.3)'
  },
  {
    key: 'revenue',
    title: '收入',
    value: 45678,
    unit: '¥',
    change: -2.1,
    icon: 'el-icon-money',
    color: '#4facfe',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    shadowColor: 'rgba(79, 172, 254, 0.3)'
  },
  {
    key: 'conversion',
    title: '转化率',
    value: 3.24,
    unit: '%',
    change: 0.5,
    icon: 'el-icon-pie-chart',
    color: '#43e97b',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    shadowColor: 'rgba(67, 233, 123, 0.3)'
  }
])

// 表格数据
const tableData = ref([])

// 计算属性
const totalMapUsers = computed(() => {
  return metricsData.value.find(m => m.key === 'users')?.value || 0
})

// 方法
const formatMetricValue = (value, unit) => {
  if (unit === '¥') return `¥${formatNumber(value)}`
  if (unit === '%') return `${value}%`
  return formatNumber(value)
}

const formatNumber = (num) => {
  if (num >= 10000) return (num / 10000).toFixed(1) + 'w'
  if (num >= 1000) return (num / 1000).toFixed(1) + 'k'
  return num.toString()
}

const handleTimeRangeChange = (value) => {
  console.log('时间范围变更:', value)
  loadAnalyticsData()
}

const exportData = () => {
  ElMessage.success('数据导出功能开发中...')
}

const refreshData = async () => {
  isRefreshing.value = true
  await loadAnalyticsData()
  isRefreshing.value = false
  ElMessage.success('数据已刷新')
}

// 加载分析数据
const loadAnalyticsData = async () => {
  isLoading.value = true
  
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 生成模拟数据
    generateMockData()
    
    // 初始化图表
    await nextTick()
    initCharts()
    
  } catch (error) {
    console.error('数据加载失败:', error)
    ElMessage.error('数据加载失败，请重试')
  } finally {
    isLoading.value = false
  }
}

// 生成模拟数据
const generateMockData = () => {
  // 生成表格数据
  tableData.value = []
  const today = new Date()
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    
    tableData.value.push({
      date: date.toISOString().split('T')[0],
      visits: Math.floor(Math.random() * 2000) + 1000,
      users: Math.floor(Math.random() * 500) + 200,
      pageviews: Math.floor(Math.random() * 5000) + 2000,
      conversion: (Math.random() * 5 + 1).toFixed(2),
      revenue: Math.floor(Math.random() * 10000) + 5000,
      bounce: (Math.random() * 30 + 20).toFixed(1)
    })
  }
  
  // 更新指标数据
  const totalVisits = tableData.value.reduce((sum, item) => sum + item.visits, 0)
  const totalUsers = tableData.value.reduce((sum, item) => sum + item.users, 0)
  const totalRevenue = tableData.value.reduce((sum, item) => sum + item.revenue, 0)
  const avgConversion = (tableData.value.reduce((sum, item) => sum + parseFloat(item.conversion), 0) / tableData.value.length).toFixed(2)
  
  metricsData.value[0].value = totalVisits
  metricsData.value[1].value = totalUsers
  metricsData.value[2].value = totalRevenue
  metricsData.value[3].value = parseFloat(avgConversion)
}

// 初始化所有图表
const initCharts = async () => {
  try {
    console.log('📊 开始初始化图表...')

    await initTrendChart()
    await initMapChart()
    await initBehaviorChart()
    await initDeviceChart()

    console.log('✅ 所有图表初始化完成')
  } catch (error) {
    console.error('❌ 图表初始化失败:', error)
  }
}

// 初始化趋势图表
const initTrendChart = async () => {
  if (!trendChartRef.value) return

  try {
    const chart = await createEChartsInstance(trendChartRef.value)
    charts.value.trend = chart

    const dates = tableData.value.map(item => item.date.split('-').slice(1).join('/'))
    const visitsData = tableData.value.map(item => item.visits)
    const usersData = tableData.value.map(item => item.users)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: 'rgba(255,255,255,0.2)',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['访问量', '用户数'],
      textStyle: { color: '#666' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280' }
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280' },
      splitLine: { lineStyle: { color: '#f3f4f6' } }
    },
    series: [
      {
        name: '访问量',
        type: 'line',
        data: visitsData,
        smooth: true,
        lineStyle: { color: '#3b82f6', width: 3 },
        itemStyle: { color: '#3b82f6' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
            { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
          ])
        }
      },
      {
        name: '用户数',
        type: 'line',
        data: usersData,
        smooth: true,
        lineStyle: { color: '#10b981', width: 3 },
        itemStyle: { color: '#10b981' }
      }
    ]
  }

    chart.setOption(option)
  } catch (error) {
    console.error('❌ 趋势图表初始化失败:', error)
  }
}

// 智能标签显示策略
const getSmartLabelFormatter = (provinceDataMap, maxValue) => {
  return function(params) {
    try {
      // 确保参数和省份名称存在
      if (!params || !params.name) {
        return ''
      }

      // 重要省份列表（经济发达或人口众多）
      const importantProvinces = [
        '北京', '上海', '广东', '江苏', '浙江',
        '山东', '河南', '四川', '湖北', '湖南',
        '河北', '福建', '安徽', '辽宁', '陕西'
      ]

      // 获取当前省份的数据值，确保安全访问
      const currentValue = provinceDataMap && provinceDataMap[params.name] ? provinceDataMap[params.name] : 0
      const safeMaxValue = maxValue && maxValue > 0 ? maxValue : 1
      const valueRatio = currentValue / safeMaxValue

      // 显示策略：
      // 1. 重要省份始终显示
      // 2. 数据值较高的省份显示（前30%）
      // 3. 其他省份不显示标签，避免拥挤
      if (importantProvinces.includes(params.name) || valueRatio > 0.3) {
        return params.name
      }

      return ''
    } catch (error) {
      console.warn('智能标签格式化错误:', error, params)
      return params && params.name ? params.name : ''
    }
  }
}

// 初始化地图图表
const initMapChart = async () => {
  if (!mapChartRef.value) return

  try {
    // 加载地图数据
    const mapLoaded = await loadChinaMap('local')
    if (!mapLoaded) {
      console.warn('地图数据加载失败')
      return
    }

    const chart = await createEChartsInstance(mapChartRef.value)
    charts.value.map = chart

    const provinceDataMap = getProvinceDataMap()
    const mapData = formatMapData(provinceDataMap)
    
    // 安全地计算最大值和最小值
    const values = Object.values(provinceDataMap).filter(v => typeof v === 'number' && !isNaN(v))
    const maxValue = values.length > 0 ? Math.max(...values) : 1000
    const minValue = values.length > 0 ? Math.min(...values) : 0

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: 'rgba(255,255,255,0.2)',
      textStyle: { color: '#fff' },
      formatter: function (params) {
        try {
          if (params && params.name) {
            // 安全地获取数据值
            let value = 0
            if (params.data && params.data.value !== undefined && params.data.value !== null) {
              value = params.data.value
            } else if (provinceDataMap && provinceDataMap[params.name]) {
              value = provinceDataMap[params.name]
            }
            
            // 确保value是数字类型
            const numValue = typeof value === 'number' && !isNaN(value) ? value : 0
            const formattedValue = numValue.toLocaleString()
            
            return `${params.name}<br/>用户数: ${formattedValue}`
          }
          return '数据加载中...'
        } catch (error) {
          console.warn('⚠️ Tooltip格式化错误:', error, params)
          return params && params.name ? `${params.name}<br/>数据显示异常` : '数据显示异常'
        }
      }
    },
    visualMap: {
      min: minValue,
      max: maxValue,
      left: 20,
      bottom: 20,
      text: ['用户多', '用户少'],
      textStyle: { 
        color: '#1f2937', 
        fontSize: 13, 
        fontWeight: '700',
        textShadowBlur: 1,
        textShadowColor: 'rgba(255,255,255,0.8)'
      },
      inRange: {
        color: [
          '#e0f2fe',  // 最浅 - 极少用户 (sky-50)
          '#bae6fd',  // 很少用户 (sky-200)
          '#7dd3fc',  // 少量用户 (sky-300)
          '#38bdf8',  // 中等用户 (sky-400)
          '#0ea5e9',  // 较多用户 (sky-500)
          '#0284c7',  // 很多用户 (sky-600)
          '#0369a1',  // 大量用户 (sky-700)
          '#075985'   // 最深 - 用户最多 (sky-800)
        ]
      },
      calculable: true,
      orient: 'horizontal',
      itemWidth: 24,
      itemHeight: 140,
      borderColor: '#cbd5e1',
      borderWidth: 2,
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      padding: [12, 16],
      borderRadius: 8,
      shadowBlur: 8,
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffsetY: 2
    },
    series: [{
      name: '用户分布',
      type: 'map',
      map: 'china',
      roam: false,
      zoom: 1.4,
      center: [104, 35.5],
      data: mapData,
      label: {
        show: true,
        color: '#1f2937',
        fontSize: 12,
        fontWeight: 'bold',
        textBorderColor: 'rgba(255,255,255,0.9)',
        textBorderWidth: 2,
        textShadowBlur: 2,
        textShadowColor: 'rgba(255,255,255,0.7)',
        // 应用智能标签显示策略
        formatter: getSmartLabelFormatter(provinceDataMap, maxValue)
      },
      itemStyle: {
        borderColor: '#cbd5e1',
        borderWidth: 2,
        areaColor: '#f1f5f9',
        shadowBlur: 5,
        shadowColor: 'rgba(0, 0, 0, 0.15)',
        shadowOffsetY: 2
      },
      emphasis: {
        label: {
          show: true,
          color: '#ffffff',
          fontSize: 14,
          fontWeight: 'bold',
          textBorderColor: 'rgba(0,0,0,0.9)',
          textBorderWidth: 3,
          textShadowBlur: 4,
          textShadowColor: 'rgba(0,0,0,0.8)',
          // 悬停时始终显示省份名称和数据
          formatter: function(params) {
            try {
              if (params && params.name) {
                const value = params.data && params.data.value !== undefined ? params.data.value : 0
                const formattedValue = typeof value === 'number' ? value.toLocaleString() : '0'
                return `${params.name}\n用户: ${formattedValue}`
              }
              return params ? params.name : ''
            } catch (error) {
              console.warn('悬停标签格式化错误:', error)
              return params ? params.name : ''
            }
          }
        },
        itemStyle: {
          areaColor: '#2563eb',
          borderColor: '#1e40af',
          borderWidth: 3,
          shadowBlur: 15,
          shadowColor: 'rgba(37, 99, 235, 0.6)',
          shadowOffsetX: 0,
          shadowOffsetY: 3
        }
      }
    }]
  }

    chart.setOption(option)
  } catch (error) {
    console.error('❌ 地图图表初始化失败:', error)
  }
}

// 初始化用户行为图表
const initBehaviorChart = async () => {
  if (!behaviorChartRef.value) return

  try {
    const chart = await createEChartsInstance(behaviorChartRef.value)
    charts.value.behavior = chart

    const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: 'rgba(255,255,255,0.2)',
      textStyle: { color: '#fff' }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: { color: '#666' }
    },
    series: [{
      name: '用户行为',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      data: [
        { value: 1048, name: '页面浏览', itemStyle: { color: '#3b82f6' } },
        { value: 735, name: '搜索', itemStyle: { color: '#10b981' } },
        { value: 580, name: '下载', itemStyle: { color: '#f59e0b' } },
        { value: 484, name: '分享', itemStyle: { color: '#ef4444' } },
        { value: 300, name: '其他', itemStyle: { color: '#8b5cf6' } }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

    chart.setOption(option)
  } catch (error) {
    console.error('❌ 用户行为图表初始化失败:', error)
  }
}

// 初始化设备分析图表
const initDeviceChart = async () => {
  if (!deviceChartRef.value) return

  try {
    const chart = await createEChartsInstance(deviceChartRef.value)
    charts.value.device = chart

    const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: 'rgba(255,255,255,0.2)',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['桌面端', '移动端', '平板'],
      textStyle: { color: '#666' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280' }
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280' },
      splitLine: { lineStyle: { color: '#f3f4f6' } }
    },
    series: [
      {
        name: '桌面端',
        type: 'bar',
        data: [320, 332, 301, 334, 390, 330, 320],
        itemStyle: { color: '#3b82f6' }
      },
      {
        name: '移动端',
        type: 'bar',
        data: [220, 182, 191, 234, 290, 330, 310],
        itemStyle: { color: '#10b981' }
      },
      {
        name: '平板',
        type: 'bar',
        data: [150, 232, 201, 154, 190, 330, 410],
        itemStyle: { color: '#f59e0b' }
      }
    ]
  }

    chart.setOption(option)
  } catch (error) {
    console.error('❌ 设备分析图表初始化失败:', error)
  }
}

// 生命周期
onMounted(async () => {
  console.log('📊 数据分析页面开始加载...')
  await loadAnalyticsData()

  // 窗口大小调整时重新调整图表大小
  window.addEventListener('resize', () => {
    Object.values(charts.value).forEach(chart => {
      if (chart && chart.resize) {
        chart.resize()
      }
    })
  })
})

onUnmounted(() => {
  // 销毁图表实例
  Object.values(charts.value).forEach(async (chart) => {
    if (chart) await disposeEChartsInstance(chart)
  })
})
</script>

<style scoped>
.analytics-dashboard {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.analytics-content {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.time-selector {
  flex-shrink: 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.metric-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.metric-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.metric-card:hover .metric-icon {
  transform: scale(1.1) rotate(5deg);
}

.metric-card.loading {
  opacity: 0.7;
}

.metric-icon {
  font-size: 24px;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.metric-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-card:hover .metric-icon::after {
  opacity: 1;
}

.metric-info h3 {
  margin: 0 0 8px 0;
  color: #6b7280;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.metric-value {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 800;
  color: #1f2937;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.metric-change {
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 8px;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.metric-change.positive {
  color: #059669;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.metric-change.negative {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* 为卡片添加微妙的动画效果 */
@keyframes cardGlow {
  0%, 100% { 
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); 
  }
  50% { 
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12); 
  }
}

.metric-card:hover {
  animation: cardGlow 2s ease-in-out infinite;
}

.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.detailed-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.chart-card h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-header h3 {
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart-info {
  font-size: 14px;
  color: #6b7280;
}

.total-users {
  font-weight: 600;
}

.chart-content {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.trend-chart .chart-content {
  min-height: 350px;
}

.map-chart .chart-content {
  min-height: 450px;
}

.map-chart {
  position: relative;
}

.map-chart .chart-header .chart-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.map-chart .total-users {
  color: #3b82f6;
  font-weight: 600;
  font-size: 14px;
}

.data-table-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.table-controls {
  display: flex;
  gap: 12px;
}

.table-container {
  overflow-x: auto;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 1200px) {
  .charts-section,
  .detailed-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .analytics-dashboard {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    padding: 20px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .chart-content {
    min-height: 250px;
  }

  .trend-chart .chart-content {
    min-height: 300px;
  }

  .map-chart .chart-content {
    min-height: 350px;
  }

  /* 移动端地图标签优化 */
  .map-chart .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .map-chart .chart-info {
    font-size: 12px;
  }
}
</style>
