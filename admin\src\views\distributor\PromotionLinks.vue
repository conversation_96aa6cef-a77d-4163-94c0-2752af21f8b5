<template>
  <div class="promotion-links">
    <div class="page-header">
      <div class="header-left">
        <h2>推广链接管理</h2>
        <p class="page-description">管理您的推广链接，跟踪推广效果，优化推广策略</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建链接
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 反检测系统状态概览 -->
    <el-card class="anti-detection-overview" style="margin-bottom: 20px;">
      <template #header>
        <div class="card-header">
          <span>🛡️ 防红系统状态</span>
          <el-button type="text" @click="$router.push('/admin/anti-block/dashboard')">
            查看详情 →
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="15">
        <el-col :span="6">
          <div class="overview-item">
            <div class="overview-icon" style="background-color: #67C23A20; color: #67C23A;">
              <el-icon :size="20"><Lock /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-value">{{ stats.protected_links }}/{{ stats.total_links }}</div>
              <div class="overview-label">防红保护</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="overview-icon" style="background-color: #409EFF20; color: #409EFF;">
              <el-icon :size="20"><Link /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-value">{{ stats.avg_domain_health }}%</div>
              <div class="overview-label">平均域名健康度</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="overview-icon" style="background-color: #E6A23C20; color: #E6A23C;">
              <el-icon :size="20"><Refresh /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-value">{{ stats.domain_switches_today }}</div>
              <div class="overview-label">今日域名切换</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="overview-icon" style="background-color: #F56C6C20; color: #F56C6C;">
              <el-icon :size="20"><SuccessFilled /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-value">{{ stats.protection_success_rate }}%</div>
              <div class="overview-label">保护成功率</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #409EFF20; color: #409EFF;">
            <el-icon :size="24"><Link /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total_links }}</div>
            <div class="stat-title">推广链接</div>
            <div class="stat-trend positive">
              <el-icon><ArrowUp /></el-icon>
              8.5%
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #67C23A20; color: #67C23A;">
            <el-icon :size="24"><View /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total_clicks }}</div>
            <div class="stat-title">总点击量</div>
            <div class="stat-trend positive">
              <el-icon><ArrowUp /></el-icon>
              15.2%
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #E6A23C20; color: #E6A23C;">
            <el-icon :size="24"><UserFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total_conversions }}</div>
            <div class="stat-title">转化数量</div>
            <div class="stat-trend positive">
              <el-icon><ArrowUp /></el-icon>
              12.3%
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #F56C6C20; color: #F56C6C;">
            <el-icon :size="24"><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.conversion_rate }}%</div>
            <div class="stat-title">转化率</div>
            <div class="stat-trend negative">
              <el-icon><ArrowDown /></el-icon>
              1.2%
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <!-- 快捷操作 -->
    <el-card class="quick-actions-card">
      <template #header>
        <span>快捷操作</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="action-item" @click="generateQRCode">
            <div class="action-icon" style="color: #409EFF;">
              <el-icon :size="32"><Grid /></el-icon>
            </div>
            <span class="action-text">生成二维码</span>
            <span class="action-desc">为推广链接生成二维码</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-item" @click="batchExport">
            <div class="action-icon" style="color: #67C23A;">
              <el-icon :size="32"><Download /></el-icon>
            </div>
            <span class="action-text">批量导出</span>
            <span class="action-desc">导出推广链接数据</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-item" @click="viewAnalytics">
            <div class="action-icon" style="color: #E6A23C;">
              <el-icon :size="32"><TrendCharts /></el-icon>
            </div>
            <span class="action-text">数据分析</span>
            <span class="action-desc">查看推广效果分析</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-item" @click="viewHelp">
            <div class="action-icon" style="color: #606266;">
              <el-icon :size="32"><QuestionFilled /></el-icon>
            </div>
            <span class="action-text">使用帮助</span>
            <span class="action-desc">查看使用说明文档</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 推广链接列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>推广链接列表</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索链接名称或URL"
              style="width: 200px; margin-right: 10px;"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="statusFilter" placeholder="链接状态" style="width: 120px;" @change="loadLinks">
              <el-option label="全部" value="" />
              <el-option label="活跃" value="active" />
              <el-option label="暂停" value="paused" />
              <el-option label="过期" value="expired" />
            </el-select>
          </div>
        </div>
      </template>

      <el-table :data="links" v-loading="loading" stripe>
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="链接名称" width="200">
          <template #default="{ row }">
            <div class="link-info">
              <div class="link-name">{{ row.name }}</div>
              <div class="link-desc">{{ row.description }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="推广链接" width="350">
          <template #default="{ row }">
            <div class="link-url-with-status">
              <el-input 
                :value="row.short_url" 
                readonly 
                size="small"
              >
                <template #append>
                  <el-button @click="copyLink(row.short_url)" size="small">
                    <el-icon><DocumentCopy /></el-icon>
                  </el-button>
                </template>
              </el-input>
              <div class="link-status-info">
                <el-tag :type="getDomainHealthColor(row.domain_health)" size="mini">
                  域名健康: {{ row.domain_health }}%
                </el-tag>
                <el-tag v-if="row.anti_detection_enabled" type="success" size="mini">
                  防红保护
                </el-tag>
                <el-tag v-else type="warning" size="mini">
                  未保护
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="click_count" label="点击量" width="100" />
        <el-table-column prop="conversion_count" label="转化数" width="100" />
        <el-table-column label="转化率" width="100">
          <template #default="{ row }">
            <span :class="getConversionRateClass(row.conversion_rate)">
              {{ row.conversion_rate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column label="域名信息" width="150">
          <template #default="{ row }">
            <div class="domain-info">
              <div class="domain-name">{{ row.domain_name }}</div>
              <div class="domain-stats">
                <span class="success-rate">成功率: {{ row.access_success_rate }}%</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="过期时间" width="120">
          <template #default="{ row }">
            <span v-if="row.expires_at" :class="getExpiryClass(row.expires_at)">
              {{ formatDate(row.expires_at) }}
            </span>
            <span v-else class="no-expiry">永久有效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewStats(row)">
              统计
            </el-button>
            <el-button size="small" @click="editLink(row)">
              编辑
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="switchDomain(row)"
              v-if="row.domain_health < 80">
              切换域名
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, row)">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="qrcode">生成二维码</el-dropdown-item>
                  <el-dropdown-item command="switch-domain">切换域名</el-dropdown-item>
                  <el-dropdown-item command="toggle-protection">
                    {{ row.anti_detection_enabled ? '关闭防红' : '开启防红' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="pause" v-if="row.status === 'active'">暂停</el-dropdown-item>
                  <el-dropdown-item command="resume" v-if="row.status === 'paused'">恢复</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadLinks"
          @current-change="loadLinks"
        />
      </div>
    </el-card>

    <!-- 创建链接对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建推广链接" width="600px">
      <el-form :model="linkForm" :rules="linkRules" ref="linkFormRef" label-width="100px">
        <el-form-item label="链接名称" prop="name">
          <el-input v-model="linkForm.name" placeholder="请输入链接名称" />
        </el-form-item>
        <el-form-item label="链接描述" prop="description">
          <el-input 
            v-model="linkForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入链接描述"
          />
        </el-form-item>
        <el-form-item label="目标URL" prop="target_url">
          <el-input v-model="linkForm.target_url" placeholder="请输入目标URL" />
        </el-form-item>
        <el-form-item label="链接类型" prop="type">
          <el-select v-model="linkForm.type" placeholder="选择类型">
            <el-option label="产品推广" value="product" />
            <el-option label="活动推广" value="activity" />
            <el-option label="注册邀请" value="register" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="过期时间" prop="expires_at">
          <el-date-picker
            v-model="linkForm.expires_at"
            type="datetime"
            placeholder="选择过期时间（可选）"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="自定义后缀">
          <el-input v-model="linkForm.custom_suffix" placeholder="自定义短链接后缀（可选）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createLink" :loading="createLoading">
          创建
        </el-button>
      </template>
    </el-dialog>

    <!-- 二维码生成对话框 -->
    <QRCodeGenerator
      v-model:visible="showQRCodeDialog"
      :link-data="selectedLinkForQR || {}"
      @close="handleQRCodeClose"
    />

    <!-- 功能开发提示 -->
    <!-- 功能开发提示 -->
    <el-dialog v-model="showDevDialog" title="功能开发中" width="400px" center>
      <div class="dev-notice">
        <el-icon :size="60" color="#409EFF"><Tools /></el-icon>
        <h3>功能开发中</h3>
        <p>该功能正在紧急开发中，敬请期待！</p>
        <p>预计上线时间：2024年1月</p>
      </div>
      <template #footer>
        <el-button type="primary" @click="showDevDialog = false">知道了</el-button>
      </template>
    </el-dialog>

    <!-- 使用说明对话框 -->
    <!-- 数据分析对话框 -->
    <el-dialog v-model="showAnalyticsDialog" title="推广链接数据分析" width="90%" top="3vh">
      <div class="analytics-content">
        <el-scrollbar height="75vh">
          <!-- 总体概览 -->
          <el-row :gutter="20" class="analytics-overview">
            <el-col :span="6">
              <div class="analytics-card">
                <div class="card-header">
                  <span class="card-title">总链接数</span>
                  <el-icon color="#409EFF"><Link /></el-icon>
                </div>
                <div class="card-value">{{ stats.total_links }}</div>
                <div class="card-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  较上月 +8.5%
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="analytics-card">
                <div class="card-header">
                  <span class="card-title">总点击量</span>
                  <el-icon color="#67C23A"><View /></el-icon>
                </div>
                <div class="card-value">{{ stats.total_clicks.toLocaleString() }}</div>
                <div class="card-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  较上月 +15.2%
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="analytics-card">
                <div class="card-header">
                  <span class="card-title">总转化数</span>
                  <el-icon color="#E6A23C"><UserFilled /></el-icon>
                </div>
                <div class="card-value">{{ stats.total_conversions.toLocaleString() }}</div>
                <div class="card-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  较上月 +12.3%
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="analytics-card">
                <div class="card-header">
                  <span class="card-title">平均转化率</span>
                  <el-icon color="#F56C6C"><TrendCharts /></el-icon>
                </div>
                <div class="card-value">{{ stats.conversion_rate }}%</div>
                <div class="card-trend negative">
                  <el-icon><ArrowDown /></el-icon>
                  较上月 -1.2%
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 链接类型分析 -->
          <el-card class="analytics-section">
            <template #header>
              <span>📊 链接类型分析</span>
            </template>
            <el-table :data="getLinkTypeAnalytics()" stripe>
              <el-table-column prop="type" label="链接类型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getTypeColor(row.type)">{{ row.typeName }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="count" label="链接数量" width="100" />
              <el-table-column prop="clicks" label="总点击量" width="120">
                <template #default="{ row }">
                  {{ row.clicks.toLocaleString() }}
                </template>
              </el-table-column>
              <el-table-column prop="conversions" label="总转化数" width="120">
                <template #default="{ row }">
                  {{ row.conversions.toLocaleString() }}
                </template>
              </el-table-column>
              <el-table-column prop="conversionRate" label="转化率" width="100">
                <template #default="{ row }">
                  <span :class="getConversionRateClass(row.conversionRate)">
                    {{ row.conversionRate }}%
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="avgClicks" label="平均点击量" width="120">
                <template #default="{ row }">
                  {{ Math.round(row.clicks / row.count).toLocaleString() }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>

          <!-- 域名健康分析 -->
          <el-card class="analytics-section">
            <template #header>
              <span>🛡️ 域名健康分析</span>
            </template>
            <el-table :data="getDomainHealthAnalytics()" stripe>
              <el-table-column prop="domain" label="域名" width="180">
                <template #default="{ row }">
                  <span class="domain-name">{{ row.domain }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="health" label="健康度" width="120">
                <template #default="{ row }">
                  <el-tag :type="getDomainHealthColor(row.health)" size="small">
                    {{ row.health }}%
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="linkCount" label="使用链接数" width="120" />
              <el-table-column prop="totalClicks" label="总点击量" width="120">
                <template #default="{ row }">
                  {{ row.totalClicks.toLocaleString() }}
                </template>
              </el-table-column>
              <el-table-column prop="successRate" label="访问成功率" width="120">
                <template #default="{ row }">
                  <span :style="{ color: row.successRate >= 95 ? '#67C23A' : row.successRate >= 90 ? '#E6A23C' : '#F56C6C' }">
                    {{ row.successRate }}%
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="lastSwitch" label="最后切换" width="120">
                <template #default="{ row }">
                  {{ row.lastSwitch || '无' }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>

          <!-- 防红保护效果分析 -->
          <el-card class="analytics-section">
            <template #header>
              <span>🔒 防红保护效果分析</span>
            </template>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="protection-stats">
                  <h4>保护状态统计</h4>
                  <div class="stat-item">
                    <span class="stat-label">已保护链接:</span>
                    <span class="stat-value success">{{ stats.protected_links }} 个</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">未保护链接:</span>
                    <span class="stat-value warning">{{ stats.total_links - stats.protected_links }} 个</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">保护覆盖率:</span>
                    <span class="stat-value primary">{{ Math.round(stats.protected_links / stats.total_links * 100) }}%</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">保护成功率:</span>
                    <span class="stat-value success">{{ stats.protection_success_rate }}%</span>
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="protection-comparison">
                  <h4>保护效果对比</h4>
                  <div class="comparison-item">
                    <div class="comparison-label">已保护链接平均存活率</div>
                    <div class="comparison-bar">
                      <div class="bar-fill success" style="width: 95%"></div>
                      <span class="bar-text">95%</span>
                    </div>
                  </div>
                  <div class="comparison-item">
                    <div class="comparison-label">未保护链接平均存活率</div>
                    <div class="comparison-bar">
                      <div class="bar-fill danger" style="width: 60%"></div>
                      <span class="bar-text">60%</span>
                    </div>
                  </div>
                  <div class="comparison-item">
                    <div class="comparison-label">已保护链接平均点击率</div>
                    <div class="comparison-bar">
                      <div class="bar-fill success" style="width: 88%"></div>
                      <span class="bar-text">88%</span>
                    </div>
                  </div>
                  <div class="comparison-item">
                    <div class="comparison-label">未保护链接平均点击率</div>
                    <div class="comparison-bar">
                      <div class="bar-fill warning" style="width: 65%"></div>
                      <span class="bar-text">65%</span>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-card>

          <!-- 推广建议 -->
          <el-card class="analytics-section">
            <template #header>
              <span>💡 推广建议</span>
            </template>
            <div class="suggestions">
              <div class="suggestion-item">
                <el-icon color="#67C23A"><SuccessFilled /></el-icon>
                <div class="suggestion-content">
                  <h4>优化建议</h4>
                  <p>产品推广类链接转化率最高(8.5%)，建议增加此类链接的投放比例。</p>
                </div>
              </div>
              <div class="suggestion-item">
                <el-icon color="#E6A23C"><QuestionFilled /></el-icon>
                <div class="suggestion-content">
                  <h4>注意事项</h4>
                  <p>域名 d3.linkhub.pro 健康度较低(72%)，建议及时切换到更健康的域名。</p>
                </div>
              </div>
              <div class="suggestion-item">
                <el-icon color="#409EFF"><TrendCharts /></el-icon>
                <div class="suggestion-content">
                  <h4>趋势分析</h4>
                  <p>整体点击量呈上升趋势，但转化率略有下降，建议优化目标页面内容。</p>
                </div>
              </div>
              <div class="suggestion-item">
                <el-icon color="#F56C6C"><Lock /></el-icon>
                <div class="suggestion-content">
                  <h4>安全建议</h4>
                  <p>{{ stats.total_links - stats.protected_links }} 个链接未开启防红保护，建议全部开启以提高存活率。</p>
                </div>
              </div>
            </div>
          </el-card>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="analytics-footer">
          <el-button @click="exportAnalytics">导出分析报告</el-button>
          <el-button type="primary" @click="showAnalyticsDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 使用说明对话框 -->
    <el-dialog v-model="showHelpDialog" title="推广链接反检测系统使用说明" width="80%" top="5vh">
      <div class="help-content">
        <el-scrollbar height="70vh">
          <div class="help-section">
            <h2>📖 系统概述</h2>
            <p>推广链接反检测系统是一个智能化的链接管理和保护系统，通过域名轮换、健康监控、自动切换等技术手段，确保推广链接的高存活率和防封禁能力。</p>
            
            <h2>🚀 快速开始</h2>
            <h3>1. 查看防红系统状态</h3>
            <p>页面顶部显示防红系统状态概览：</p>
            <ul>
              <li><strong>防红保护</strong>: 显示已保护链接数量/总链接数量</li>
              <li><strong>平均域名健康度</strong>: 所有域名的平均健康分数</li>
              <li><strong>今日域名切换</strong>: 当天自动切换域名的次数</li>
              <li><strong>保护成功率</strong>: 防红保护的成功率百分比</li>
            </ul>

            <h3>2. 创建智能推广链接</h3>
            <ol>
              <li>点击"创建链接"按钮</li>
              <li>填写链接信息（名称、描述、目标URL等）</li>
              <li>系统会自动选择健康度最高的域名</li>
              <li>创建成功后显示"已自动选择最佳域名"提示</li>
            </ol>

            <h2>🔧 核心功能详解</h2>
            
            <h3>1. 智能域名选择</h3>
            <div class="feature-box">
              <p><strong>功能说明</strong>: 创建推广链接时，系统会自动选择健康度最高的域名，确保链接的最佳可用性。</p>
              <p><strong>选择规则</strong>:</p>
              <ul>
                <li>优先选择健康度≥90%的域名</li>
                <li>考虑域名的访问成功率</li>
                <li>负载均衡，避免单一域名过载</li>
              </ul>
            </div>

            <h3>2. 域名健康监控</h3>
            <div class="feature-box">
              <p><strong>健康度指标</strong>:</p>
              <ul>
                <li><el-tag type="success" size="small">90%以上</el-tag> 域名状态优秀，访问稳定</li>
                <li><el-tag type="primary" size="small">80-89%</el-tag> 域名状态良好，可正常使用</li>
                <li><el-tag type="warning" size="small">70-79%</el-tag> 域名状态一般，建议关注</li>
                <li><el-tag type="danger" size="small">70%以下</el-tag> 域名状态较差，建议立即切换</li>
              </ul>
            </div>

            <h3>3. 智能域名切换</h3>
            <div class="feature-box">
              <p><strong>自动切换触发条件</strong>:</p>
              <ul>
                <li>域名健康度低于70%</li>
                <li>访问成功率连续下降</li>
                <li>域名被检测到异常</li>
              </ul>
              <p><strong>手动切换</strong>: 在链接列表中点击"切换域名"按钮，系统会自动选择最佳域名。</p>
            </div>

            <h3>4. 防红保护控制</h3>
            <div class="feature-box">
              <p><strong>功能说明</strong>: 防红保护是一套综合性的反检测机制，包括域名轮换、参数混淆、流量分发等技术。</p>
              <p><strong>操作方式</strong>: 在链接操作菜单中选择"开启防红"或"关闭防红"。</p>
            </div>

            <h3>5. 二维码生成</h3>
            <div class="feature-box">
              <p><strong>功能特性</strong>:</p>
              <ul>
                <li>可调整二维码大小、颜色、容错级别</li>
                <li>支持下载、复制、打印功能</li>
                <li>包含完整的链接信息</li>
              </ul>
            </div>

            <h2>🚨 注意事项与最佳实践</h2>
            
            <h3>域名使用建议</h3>
            <div class="tips-box">
              <ul>
                <li><strong>定期检查</strong>: 每天查看域名健康状态</li>
                <li><strong>及时切换</strong>: 健康度低于80%时及时切换</li>
                <li><strong>分散使用</strong>: 避免所有链接使用同一域名</li>
                <li><strong>备份准备</strong>: 保持多个备用域名可用</li>
              </ul>
            </div>

            <h3>防红保护建议</h3>
            <div class="tips-box">
              <ul>
                <li><strong>重要链接</strong>: 对重要推广链接务必开启防红保护</li>
                <li><strong>定期监控</strong>: 关注防红保护的效果统计</li>
                <li><strong>及时调整</strong>: 根据效果调整保护策略</li>
              </ul>
            </div>

            <h2>🔍 常见问题</h2>
            
            <div class="faq-item">
              <h4>Q: 创建链接时提示"无可用域名"？</h4>
              <p>A: 检查域名池状态，确保有健康的域名可用。联系管理员添加新域名。</p>
            </div>

            <div class="faq-item">
              <h4>Q: 域名切换失败？</h4>
              <p>A: 可能是网络问题或域名服务异常。稍后重试或联系技术支持。</p>
            </div>

            <div class="faq-item">
              <h4>Q: 防红保护无效？</h4>
              <p>A: 确认域名健康度，检查防红保护配置，必要时切换域名。</p>
            </div>

            <h2>📞 技术支持</h2>
            <div class="contact-info">
              <p><strong>技术支持邮箱</strong>: <EMAIL></p>
              <p><strong>客服热线</strong>: 400-xxx-xxxx</p>
              <p><strong>在线客服</strong>: 工作日 9:00-18:00</p>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="help-footer">
          <el-button @click="openFullGuide">查看完整文档</el-button>
          <el-button type="primary" @click="showHelpDialog = false">知道了</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { 
  Plus, Refresh, Link, View, UserFilled, TrendCharts, Setting,
  Download, Share, QuestionFilled, Search, ArrowDown, ArrowUp,
  DocumentCopy, Tools, Lock, SuccessFilled, Grid
} from '@element-plus/icons-vue'
import QRCodeGenerator from '@/components/QRCodeGenerator.vue'
import { 
  createShortLink, 
  getShortLinks, 
  switchShortLinkDomain,
  getDomainList 
} from '@/api/anti-block'

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const showCreateDialog = ref(false)
const showDevDialog = ref(false)
  const showQRCodeDialog = ref(false)
  const showHelpDialog = ref(false)
  const showAnalyticsDialog = ref(false)
const selectedLinkForQR = ref(null)
const searchKeyword = ref('')
const statusFilter = ref('')

const stats = reactive({
  total_links: 28,
  total_clicks: 15680,
  total_conversions: 1256,
  conversion_rate: 8.0,
  // 反检测系统统计
  protected_links: 25,
  avg_domain_health: 88.5,
  domain_switches_today: 3,
  protection_success_rate: 98.2
})

const links = ref([])
const pagination = reactive({
  current_page: 1,
  per_page: 20,
  total: 0
})

// 反检测系统集成数据
const availableDomains = ref([])
const domainHealthStats = reactive({
  total: 0,
  healthy: 0,
  warning: 0,
  critical: 0
})

const linkForm = reactive({
  name: '',
  description: '',
  target_url: '',
  type: '',
  expires_at: '',
  custom_suffix: ''
})

const linkRules = {
  name: [
    { required: true, message: '请输入链接名称', trigger: 'blur' }
  ],
  target_url: [
    { required: true, message: '请输入目标URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择链接类型', trigger: 'change' }
  ]
}

const linkFormRef = ref()

// 模拟数据 - 集成反检测系统
const mockLinks = [
  {
    id: 1,
    name: 'VIP产品推广',
    description: '高端VIP产品推广链接',
    short_url: 'https://d1.linkhub.pro/vip2024',
    target_url: 'https://example.com/vip-product',
    type: 'product',
    status: 'active',
    click_count: 1580,
    conversion_count: 126,
    conversion_rate: 8.0,
    created_at: new Date(Date.now() - 86400000 * 15),
    expires_at: new Date(Date.now() + 86400000 * 30),
    // 反检测系统字段
    domain_id: 1,
    domain_name: 'd1.linkhub.pro',
    domain_health: 95,
    anti_detection_enabled: true,
    last_domain_switch: new Date(Date.now() - 86400000 * 5),
    access_success_rate: 98.5
  },
  {
    id: 2,
    name: '新年活动推广',
    description: '2024新年特惠活动',
    short_url: 'https://d2.linkhub.pro/ny2024',
    target_url: 'https://example.com/new-year-sale',
    type: 'activity',
    status: 'active',
    click_count: 2340,
    conversion_count: 187,
    conversion_rate: 8.0,
    created_at: new Date(Date.now() - 86400000 * 10),
    expires_at: new Date(Date.now() + 86400000 * 15),
    // 反检测系统字段
    domain_id: 2,
    domain_name: 'd2.linkhub.pro',
    domain_health: 88,
    anti_detection_enabled: true,
    last_domain_switch: new Date(Date.now() - 86400000 * 2),
    access_success_rate: 96.8
  },
  {
    id: 3,
    name: '用户注册邀请',
    description: '邀请新用户注册',
    short_url: 'https://d3.linkhub.pro/reg2024',
    target_url: 'https://example.com/register',
    type: 'register',
    status: 'paused',
    click_count: 890,
    conversion_count: 67,
    conversion_rate: 7.5,
    created_at: new Date(Date.now() - 86400000 * 25),
    expires_at: null,
    // 反检测系统字段
    domain_id: 3,
    domain_name: 'd3.linkhub.pro',
    domain_health: 72,
    anti_detection_enabled: false,
    last_domain_switch: new Date(Date.now() - 86400000 * 10),
    access_success_rate: 89.2
  }
]

// 方法
const loadLinks = async () => {
  try {
    loading.value = true
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    let filteredLinks = [...mockLinks]
    
    // 搜索过滤
    if (searchKeyword.value) {
      filteredLinks = filteredLinks.filter(link => 
        link.name.includes(searchKeyword.value) ||
        link.description.includes(searchKeyword.value) ||
        link.short_url.includes(searchKeyword.value)
      )
    }
    
    // 状态过滤
    if (statusFilter.value) {
      filteredLinks = filteredLinks.filter(link => link.status === statusFilter.value)
    }
    
    links.value = filteredLinks
    pagination.total = filteredLinks.length
    
  } catch (error) {
    ElMessage.error('加载推广链接失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadLinks()
  ElMessage.success('数据已刷新')
}

const handleSearch = debounce(() => {
  pagination.current_page = 1
  loadLinks()
}, 500)

const copyLink = async (url) => {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(url)
      ElNotification.success({
        title: '复制成功',
        message: '推广链接已复制到剪贴板'
      })
    } else {
      // 降级处理
      const textArea = document.createElement('textarea')
      textArea.value = url
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('链接已复制')
    }
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const viewStats = (link) => {
  ElMessage.info(`查看"${link.name}"的详细统计`)
  showDevDialog.value = true
}

const editLink = (link) => {
  ElMessage.info(`编辑链接"${link.name}"`)
  showDevDialog.value = true
}

const handleCommand = (command, link) => {
  switch (command) {
    case 'qrcode':
      generateQRCodeForLink(link)
      break
    case 'switch-domain':
      switchDomain(link)
      break
    case 'toggle-protection':
      toggleAntiDetection(link)
      break
    case 'pause':
      pauseLink(link)
      break
    case 'resume':
      resumeLink(link)
      break
    case 'delete':
      deleteLink(link)
      break
  }
}

const generateQRCodeForLink = (link) => {
  selectedLinkForQR.value = link
  showQRCodeDialog.value = true
}

const pauseLink = async (link) => {
  try {
    await ElMessageBox.confirm(`确定要暂停链接"${link.name}"吗？`, '确认暂停', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    link.status = 'paused'
    ElMessage.success('链接已暂停')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('暂停失败')
    }
  }
}

const resumeLink = async (link) => {
  try {
    link.status = 'active'
    ElMessage.success('链接已恢复')
  } catch (error) {
    ElMessage.error('恢复失败')
  }
}

const deleteLink = async (link) => {
  try {
    await ElMessageBox.confirm(`确定要删除链接"${link.name}"吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const index = links.value.findIndex(l => l.id === link.id)
    if (index > -1) {
      links.value.splice(index, 1)
      stats.total_links--
    }
    
    ElMessage.success('链接删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const createLink = async () => {
  try {
    await linkFormRef.value.validate()
    createLoading.value = true
    
    // 使用反检测系统API创建短链接
    const shortLinkData = {
      original_url: linkForm.target_url,
      link_type: 'recruit',
      custom_code: linkForm.custom_suffix,
      expires_at: linkForm.expires_at,
      remarks: `推广链接: ${linkForm.name}`
    }
    
    // 模拟API调用（实际应该调用 createShortLink）
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟选择最佳域名
    const bestDomain = availableDomains.value.length > 0 
      ? availableDomains.value.reduce((best, current) => 
          current.health_score > best.health_score ? current : best
        )
      : { id: 1, domain: 'd1.linkhub.pro', health_score: 95 }
    
    const newLink = {
      id: Date.now(),
      ...linkForm,
      short_url: `https://${bestDomain.domain}/${linkForm.custom_suffix || Math.random().toString(36).substr(2, 8)}`,
      status: 'active',
      click_count: 0,
      conversion_count: 0,
      conversion_rate: 0,
      created_at: new Date(),
      // 反检测系统字段
      domain_id: bestDomain.id,
      domain_name: bestDomain.domain,
      domain_health: bestDomain.health_score,
      anti_detection_enabled: true,
      last_domain_switch: new Date(),
      access_success_rate: 100
    }
    
    links.value.unshift(newLink)
    stats.total_links++
    
    // 重置表单
    Object.keys(linkForm).forEach(key => {
      linkForm[key] = ''
    })
    
    showCreateDialog.value = false
    ElMessage.success(`推广链接创建成功！已自动选择最佳域名: ${bestDomain.domain}`)
    
  } catch (error) {
    console.error('创建链接失败:', error)
    ElMessage.error('创建链接失败')
  } finally {
    createLoading.value = false
  }
}

// 快捷操作方法
const generateQRCode = () => {
  if (links.value.length === 0) {
    ElMessage.warning('暂无推广链接，请先创建链接')
    return
  }
  
  // 为第一个链接生成二维码作为示例
  const firstLink = links.value[0]
  selectedLinkForQR.value = firstLink
  showQRCodeDialog.value = true
  ElMessage.success(`正在为"${firstLink.name}"生成二维码`)
}

const batchExport = async () => {
  try {
    ElMessage.info('正在导出推广链接数据...')
    
    // 准备导出数据
    const exportData = links.value.map(link => ({
      '链接名称': link.name,
      '链接描述': link.description,
      '推广链接': link.short_url,
      '目标URL': link.target_url,
      '链接类型': getTypeText(link.type),
      '状态': getStatusText(link.status),
      '点击量': link.click_count,
      '转化数': link.conversion_count,
      '转化率': `${link.conversion_rate}%`,
      '域名': link.domain_name,
      '域名健康度': `${link.domain_health}%`,
      '防红保护': link.anti_detection_enabled ? '已开启' : '未开启',
      '访问成功率': `${link.access_success_rate}%`,
      '创建时间': formatDateTime(link.created_at),
      '过期时间': link.expires_at ? formatDateTime(link.expires_at) : '永久有效'
    }))
    
    // 转换为CSV格式
    const headers = Object.keys(exportData[0])
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => 
        headers.map(header => `"${row[header]}"`).join(',')
      )
    ].join('\n')
    
    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `推广链接数据_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success(`成功导出 ${exportData.length} 条推广链接数据`)
    
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const viewAnalytics = () => {
  showAnalyticsDialog.value = true
}

const shareLinks = () => {
  ElMessage.info('批量分享功能开发中...')
  showDevDialog.value = true
}

const linkSettings = () => {
  ElMessage.info('链接设置功能开发中...')
  showDevDialog.value = true
}

const viewHelp = () => {
  // 打开使用说明对话框
  showHelpDialog.value = true
}

const handleQRCodeClose = () => {
  selectedLinkForQR.value = null
  showQRCodeDialog.value = false
}

const openFullGuide = () => {
  // 在新窗口中打开完整的使用说明文档
  const guideUrl = '/docs/promotion-links-anti-detection-guide.md'
  window.open(guideUrl, '_blank')
  ElMessage.success('完整使用说明已在新窗口中打开')
}

// 反检测系统集成方法
const switchDomain = async (link) => {
  try {
    await ElMessageBox.confirm(
      `当前域名 ${link.domain_name} 健康度为 ${link.domain_health}%，是否切换到更健康的域名？`,
      '域名切换建议',
      {
        confirmButtonText: '立即切换',
        cancelButtonText: '暂不切换',
        type: 'warning'
      }
    )
    
    // 模拟选择最佳域名
    const bestDomain = availableDomains.value.find(d => 
      d.id !== link.domain_id && d.health_score > link.domain_health
    ) || { id: 99, domain: 'backup.linkhub.pro', health_score: 98 }
    
    // 更新链接信息
    const linkIndex = links.value.findIndex(l => l.id === link.id)
    if (linkIndex > -1) {
      links.value[linkIndex] = {
        ...links.value[linkIndex],
        domain_id: bestDomain.id,
        domain_name: bestDomain.domain,
        domain_health: bestDomain.health_score,
        short_url: links.value[linkIndex].short_url.replace(link.domain_name, bestDomain.domain),
        last_domain_switch: new Date(),
        access_success_rate: 100
      }
    }
    
    ElMessage.success(`域名已切换到: ${bestDomain.domain}，健康度: ${bestDomain.health_score}%`)
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('域名切换失败')
    }
  }
}

const toggleAntiDetection = async (link) => {
  try {
    const action = link.anti_detection_enabled ? '关闭' : '开启'
    await ElMessageBox.confirm(
      `确定要${action}链接"${link.name}"的防红保护吗？`,
      `${action}防红保护`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: link.anti_detection_enabled ? 'warning' : 'info'
      }
    )
    
    // 更新链接状态
    const linkIndex = links.value.findIndex(l => l.id === link.id)
    if (linkIndex > -1) {
      links.value[linkIndex].anti_detection_enabled = !link.anti_detection_enabled
    }
    
    ElMessage.success(`防红保护已${action}`)
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}防红保护失败`)
    }
  }
}

const loadAvailableDomains = async () => {
  try {
    // 模拟加载可用域名
    availableDomains.value = [
      { id: 1, domain: 'd1.linkhub.pro', health_score: 95, status: 'active' },
      { id: 2, domain: 'd2.linkhub.pro', health_score: 88, status: 'active' },
      { id: 3, domain: 'd3.linkhub.pro', health_score: 72, status: 'warning' },
      { id: 4, domain: 'backup.linkhub.pro', health_score: 98, status: 'active' }
    ]
    
    // 更新域名健康统计
    domainHealthStats.total = availableDomains.value.length
    domainHealthStats.healthy = availableDomains.value.filter(d => d.health_score >= 90).length
    domainHealthStats.warning = availableDomains.value.filter(d => d.health_score >= 70 && d.health_score < 90).length
    domainHealthStats.critical = availableDomains.value.filter(d => d.health_score < 70).length
    
  } catch (error) {
    console.error('加载域名列表失败:', error)
  }
}

// 工具方法
// 数据分析相关方法
const getLinkTypeAnalytics = () => {
  const typeStats = {}
  
  links.value.forEach(link => {
    if (!typeStats[link.type]) {
      typeStats[link.type] = {
        type: link.type,
        typeName: getTypeText(link.type),
        count: 0,
        clicks: 0,
        conversions: 0
      }
    }
    
    typeStats[link.type].count++
    typeStats[link.type].clicks += link.click_count
    typeStats[link.type].conversions += link.conversion_count
  })
  
  return Object.values(typeStats).map(stat => ({
    ...stat,
    conversionRate: stat.clicks > 0 ? Math.round(stat.conversions / stat.clicks * 100 * 100) / 100 : 0
  }))
}

const getDomainHealthAnalytics = () => {
  const domainStats = {}
  
  links.value.forEach(link => {
    if (!domainStats[link.domain_name]) {
      domainStats[link.domain_name] = {
        domain: link.domain_name,
        health: link.domain_health,
        linkCount: 0,
        totalClicks: 0,
        successRate: link.access_success_rate,
        lastSwitch: null
      }
    }
    
    domainStats[link.domain_name].linkCount++
    domainStats[link.domain_name].totalClicks += link.click_count
    
    if (link.last_domain_switch) {
      const switchDate = formatDate(link.last_domain_switch)
      if (!domainStats[link.domain_name].lastSwitch || switchDate > domainStats[link.domain_name].lastSwitch) {
        domainStats[link.domain_name].lastSwitch = switchDate
      }
    }
  })
  
  return Object.values(domainStats)
}

const exportAnalytics = async () => {
  try {
    ElMessage.info('正在生成分析报告...')
    
    const reportData = {
      '生成时间': new Date().toLocaleString('zh-CN'),
      '总体统计': {
        '总链接数': stats.total_links,
        '总点击量': stats.total_clicks,
        '总转化数': stats.total_conversions,
        '平均转化率': `${stats.conversion_rate}%`,
        '防红保护覆盖率': `${Math.round(stats.protected_links / stats.total_links * 100)}%`,
        '保护成功率': `${stats.protection_success_rate}%`
      },
      '链接类型分析': getLinkTypeAnalytics(),
      '域名健康分析': getDomainHealthAnalytics()
    }
    
    const jsonContent = JSON.stringify(reportData, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `推广链接分析报告_${new Date().toISOString().slice(0, 10)}.json`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('分析报告导出成功')
    
  } catch (error) {
    console.error('导出分析报告失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 工具方法
const getStatusColor = (status) => {
  const colors = {
    'active': 'success',
    'paused': 'warning',
    'expired': 'danger'
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'active': '活跃',
    'paused': '暂停',
    'expired': '过期'
  }
  return texts[status] || '未知'
}

const getConversionRateClass = (rate) => {
  if (rate >= 10) return 'high-rate'
  if (rate >= 5) return 'medium-rate'
  return 'low-rate'
}

const getExpiryClass = (expiryDate) => {
  const now = new Date()
  const expiry = new Date(expiryDate)
  const diffDays = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'expired'
  if (diffDays <= 7) return 'expiring-soon'
  return 'normal-expiry'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getTypeText = (type) => {
  const types = {
    'product': '产品推广',
    'activity': '活动推广', 
    'register': '注册邀请',
    'other': '其他'
  }
  return types[type] || '未知'
}

const getTypeColor = (type) => {
  const colors = {
    'product': 'success',
    'activity': 'primary',
    'register': 'warning',
    'other': 'info'
  }
  return colors[type] || 'info'
}

// 反检测系统辅助方法
const getDomainHealthColor = (health) => {
  if (health >= 90) return 'success'
  if (health >= 80) return 'primary'  
  if (health >= 70) return 'warning'
  return 'danger'
}

const getDomainHealthText = (health) => {
  if (health >= 90) return '优秀'
  if (health >= 80) return '良好'
  if (health >= 70) return '一般'
  return '需要关注'
}

// 防抖函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 生命周期
onMounted(() => {
  loadLinks()
  loadAvailableDomains()
})
</script>

<style scoped>
.promotion-links {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.page-description {
  color: #909399;
  font-size: 14px;
  margin: 5px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-title {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  gap: 4px;
}

.stat-trend.positive {
  color: #67C23A;
}

.stat-trend.negative {
  color: #F56C6C;
}

.quick-actions-card {
  margin-bottom: 20px;
}
  
  .action-item {
    display: flex;
    flex-direction: column;
  align-items: center;
  padding: 25px 20px;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
  height: 140px;
  justify-content: center;
}

.action-item:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
  transform: translateY(-3px);
}

.action-icon {
  margin-bottom: 12px;
}

.action-text {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.action-desc {
  font-size: 12px;
  color: #909399;
  text-align: center;
  line-height: 1.4;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.link-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.link-name {
  font-weight: bold;
  color: #303133;
}

.link-desc {
  font-size: 12px;
  color: #909399;
}

.link-url {
  width: 100%;
}

.high-rate {
  color: #67C23A;
  font-weight: bold;
}

.medium-rate {
  color: #E6A23C;
  font-weight: bold;
}

.low-rate {
  color: #F56C6C;
}

.expired {
  color: #F56C6C;
}

.expiring-soon {
  color: #E6A23C;
}

.normal-expiry {
  color: #606266;
}

.no-expiry {
  color: #67C23A;
  font-size: 12px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dev-notice {
  text-align: center;
  padding: 20px;
}

.dev-notice h3 {
  margin: 20px 0 10px 0;
  color: #303133;
}

.dev-notice p {
  color: #606266;
  margin: 10px 0;
}

/* 反检测系统样式 */
.link-url-with-status {
  .link-status-info {
    display: flex;
    gap: 5px;
    margin-top: 5px;
    flex-wrap: wrap;
  }
}

.domain-info {
  .domain-name {
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 12px;
    color: #303133;
    font-weight: 500;
    margin-bottom: 2px;
  }
  
  .domain-stats {
    font-size: 11px;
    color: #909399;
    
    .success-rate {
      color: #67C23A;
      font-weight: 500;
    }
  }
}

.anti-detection-overview {
  .overview-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409EFF;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }
  }
  
  .overview-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
  }
  
  .overview-content {
    flex: 1;
    
    .overview-value {
      font-size: 20px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .overview-label {
      font-size: 12px;
      color: #909399;
    }
  }
}

/* 使用说明对话框样式 */
/* 数据分析对话框样式 */
.analytics-content {
  .analytics-overview {
    margin-bottom: 20px;
    
    .analytics-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        .card-title {
          font-size: 14px;
          color: #909399;
        }
      }
      
      .card-value {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .card-trend {
        display: flex;
        align-items: center;
        font-size: 12px;
        gap: 4px;
        
        &.positive {
          color: #67C23A;
        }
        
        &.negative {
          color: #F56C6C;
        }
      }
    }
  }
  
  .analytics-section {
    margin-bottom: 20px;
  }
  
  .protection-stats {
    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .stat-label {
        color: #606266;
        font-size: 14px;
      }
      
      .stat-value {
        font-weight: bold;
        
        &.success {
          color: #67C23A;
        }
        
        &.warning {
          color: #E6A23C;
        }
        
        &.primary {
          color: #409EFF;
        }
      }
    }
  }
  
  .protection-comparison {
    .comparison-item {
      margin-bottom: 15px;
      
      .comparison-label {
        font-size: 12px;
        color: #909399;
        margin-bottom: 5px;
      }
      
      .comparison-bar {
        position: relative;
        height: 20px;
        background: #f5f7fa;
        border-radius: 10px;
        overflow: hidden;
        
        .bar-fill {
          height: 100%;
          border-radius: 10px;
          transition: width 0.3s;
          
          &.success {
            background: linear-gradient(90deg, #67C23A, #85ce61);
          }
          
          &.warning {
            background: linear-gradient(90deg, #E6A23C, #ebb563);
          }
          
          &.danger {
            background: linear-gradient(90deg, #F56C6C, #f78989);
          }
        }
        
        .bar-text {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 12px;
          color: #303133;
          font-weight: bold;
        }
      }
    }
  }
  
  .suggestions {
    .suggestion-item {
      display: flex;
      align-items: flex-start;
      padding: 15px;
      margin-bottom: 10px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #409EFF;
      
      .el-icon {
        margin-right: 12px;
        margin-top: 2px;
      }
      
      .suggestion-content {
        flex: 1;
        
        h4 {
          margin: 0 0 5px 0;
          color: #303133;
          font-size: 14px;
        }
        
        p {
          margin: 0;
          color: #606266;
          font-size: 13px;
          line-height: 1.5;
        }
      }
    }
  }
}

.analytics-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 使用说明对话框样式 */
.help-content {
  .help-section {
    h2 {
      color: #303133;
      font-size: 18px;
      margin: 20px 0 10px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #409EFF;
    }
    
    h3 {
      color: #606266;
      font-size: 16px;
      margin: 15px 0 8px 0;
    }
    
    h4 {
      color: #909399;
      font-size: 14px;
      margin: 10px 0 5px 0;
    }
    
    p {
      color: #606266;
      line-height: 1.6;
      margin: 8px 0;
    }
    
    ul, ol {
      color: #606266;
      line-height: 1.6;
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
      }
    }
  }
  
  .feature-box {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin: 10px 0;
    
    p {
      margin: 8px 0;
    }
    
    ul {
      margin: 8px 0;
    }
  }
  
  .tips-box {
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 6px;
    padding: 15px;
    margin: 10px 0;
    
    ul {
      margin: 8px 0;
    }
  }
  
  .faq-item {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 6px;
    padding: 15px;
    margin: 10px 0;
    
    h4 {
      color: #52c41a;
      margin: 0 0 8px 0;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
  
  .contact-info {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 6px;
    padding: 15px;
    margin: 10px 0;
    
    p {
      margin: 5px 0;
      color: #1890ff;
    }
  }
}

.help-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .promotion-links {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .stats-row .el-col {
    margin-bottom: 15px;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .quick-actions-card .el-row {
    flex-direction: column;
  }
  
  .action-item {
    margin-bottom: 10px;
  }
}
</style>
