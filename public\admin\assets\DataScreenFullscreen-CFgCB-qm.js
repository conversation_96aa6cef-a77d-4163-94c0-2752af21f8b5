/* empty css             */import{l as e,m as a,q as t,ai as l,E as s,G as n,A as i,F as r,Y as c,C as o,ag as u,r as d,c as v,o as g,I as m}from"./vue-vendor-BcnDv-68.js";import{Z as p,a1 as h,u as f,s as y,Y as b,a5 as k,a6 as x,U as w,X as C,ah as D,bl as _}from"./element-plus-C2UshkXo.js";/* empty css               *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                  */import{_ as L}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const R={class:"simple-data-screen"},M={class:"screen-header"},S={class:"header-right"},N={class:"current-time"},j={class:"header-actions"},I={class:"metrics-section"},z={class:"metrics-grid"},A={class:"metric-content"},P={class:"metric-label"},B={class:"metric-value"},F={class:"charts-section"},G={class:"chart-card"},$={class:"chart-header"},U={class:"chart-controls"},V={class:"chart-content"},E={class:"revenue-chart"},T={class:"chart-data"},O={class:"data-label"},q={class:"data-value"},Y={class:"chart-card"},Z={class:"chart-content"},J={class:"user-distribution"},Q={class:"distribution-info"},X={class:"distribution-label"},H={class:"distribution-value"},K={class:"distribution-percentage"},W={class:"chart-card"},ee={class:"chart-header"},ae={class:"chart-controls"},te={class:"chart-content"},le={class:"activity-ranking"},se={class:"ranking-info"},ne={class:"ranking-name"},ie={class:"ranking-stats"},re={class:"stat-item"},ce={class:"stat-value"},oe={class:"stat-item"},ue={class:"stat-value active"},de={class:"ranking-rate"},ve={class:"rate-inner"},ge={class:"chart-card"},me={class:"chart-header"},pe={class:"chart-controls"},he={class:"conversion-summary"},fe={class:"chart-content"},ye={class:"funnel-chart"},be={class:"funnel-stage"},ke={class:"funnel-content"},xe={class:"funnel-label"},we={class:"funnel-value"},Ce={class:"funnel-meta"},De={class:"funnel-percentage"},_e={key:0,class:"funnel-conversion"},Le={class:"chart-card map-card"},Re={class:"chart-content"},Me={class:"china-map"},Se={class:"map-container"},Ne={viewBox:"0 0 800 600",class:"map-svg"},je={class:"city-markers"},Ie=["fill"],ze=["fill"],Ae=["fill"],Pe=["fill"],Be=["fill"],Fe=["fill"],Ge={class:"region-details"},$e={class:"region-stats"},Ue={class:"stats-header"},Ve={class:"stats-grid"},Ee={class:"stat-info"},Te={class:"stat-name"},Oe={class:"stat-count"},qe=["data-negative"],Ye={class:"realtime-section"},Ze={class:"realtime-content"},Je={class:"realtime-time"},Qe={class:"realtime-event"},Xe={class:"realtime-value"};const He=L({__name:"SimpleDataScreen",setup(e,{expose:a}){a();const t=u(),l=d(""),s=d("7d"),n=d("today"),i=d(""),r=d(!1),c=d([{key:"revenue",label:"今日收入",value:"¥25,680",change:12.5,icon:"💰",color:"var(--gradient-primary)"},{key:"users",label:"活跃用户",value:"1,234",change:8.3,icon:"👥",color:"var(--gradient-success)"},{key:"groups",label:"活跃社群",value:"56",change:-2.1,icon:"🏘️",color:"var(--gradient-warning)"},{key:"conversion",label:"转化率",value:"12.8%",change:5.7,icon:"📈",color:"var(--primary-500)"}]),o=d([{time:"00:00",label:"00",value:"2.1k",percentage:30},{time:"04:00",label:"04",value:"1.8k",percentage:25},{time:"08:00",label:"08",value:"3.2k",percentage:45},{time:"12:00",label:"12",value:"4.5k",percentage:65},{time:"16:00",label:"16",value:"5.8k",percentage:85},{time:"20:00",label:"20",value:"3.9k",percentage:55},{time:"24:00",label:"24",value:"2.7k",percentage:40}]),p=d([{type:"vip",label:"VIP用户",count:156,percentage:12.6,icon:"👑",color:"var(--gradient-primary)"},{type:"premium",label:"付费用户",count:423,percentage:34.3,icon:"💎",color:"var(--gradient-success)"},{type:"active",label:"活跃用户",count:567,percentage:46,icon:"🔥",color:"var(--gradient-warning)"},{type:"new",label:"新用户",count:88,percentage:7.1,icon:"🌟",color:"var(--primary-500)"}]),h=d([{name:"前端开发群",members:1234,active:89,rate:72},{name:"产品经理群",members:567,active:45,rate:79},{name:"设计师联盟",members:890,active:67,rate:75},{name:"创业者俱乐部",members:345,active:23,rate:67},{name:"AI技术群",members:678,active:56,rate:83}]),f=d([{stage:"访问",count:"10,000",percentage:100},{stage:"注册",count:"3,200",percentage:80},{stage:"激活",count:"2,400",percentage:60},{stage:"付费",count:"1,600",percentage:40},{stage:"复购",count:"800",percentage:20}]),y=d([{name:"北京",count:456,percentage:85,growth:12.5,icon:"🏛️",level:"heavy"},{name:"上海",count:389,percentage:72,growth:8.3,icon:"🏙️",level:"heavy"},{name:"广州",count:234,percentage:43,growth:-2.1,icon:"🌸",level:"medium"},{name:"深圳",count:198,percentage:37,growth:15.7,icon:"🏢",level:"medium"},{name:"杭州",count:167,percentage:31,growth:6.8,icon:"🌊",level:"medium"},{name:"成都",count:134,percentage:25,growth:4.2,icon:"🐼",level:"light"}]),b=v(()=>{const e=parseInt(f.value[0].count.replace(",","")),a=parseInt(f.value[f.value.length-1].count.replace(",",""));return Math.round(a/e*100)}),k=d([{id:1,time:"16:58:23",event:"新用户注册",value:"张三",type:"user"},{id:2,time:"16:58:15",event:"订单支付",value:"¥299",type:"payment"},{id:3,time:"16:58:08",event:"社群加入",value:"前端交流群",type:"group"},{id:4,time:"16:57:55",event:"佣金结算",value:"¥156",type:"commission"},{id:5,time:"16:57:42",event:"新用户注册",value:"李四",type:"user"}]),x=()=>{const e=new Date;l.value=e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},w=e=>y.value.find(a=>a.name===e)||{count:0},D=()=>{const e=["新用户注册","订单支付","社群加入","佣金结算","推广点击"],a=["张三","李四","王五","¥299","¥156","¥88","前端交流群","产品经理群"],t={id:Date.now(),time:(new Date).toLocaleTimeString("zh-CN"),event:e[Math.floor(Math.random()*e.length)],value:a[Math.floor(Math.random()*a.length)]};k.value.unshift(t),k.value.length>10&&k.value.pop()};let _=null,L=null;g(()=>{x(),_=setInterval(x,1e3),L=setInterval(D,3e3)}),m(()=>{_&&clearInterval(_),L&&clearInterval(L)});const R={router:t,currentTime:l,selectedPeriod:s,selectedActivityPeriod:n,selectedRegion:i,isRefreshing:r,coreMetrics:c,revenueData:o,userDistribution:p,groupActivity:h,conversionFunnel:f,regionData:y,finalConversionRate:b,realtimeData:k,updateTime:x,openFullscreen:()=>{t.push("/fullscreen-data-screen")},refreshData:async()=>{r.value=!0;try{await new Promise(e=>setTimeout(e,1500)),c.value.forEach(e=>{const a=20*(Math.random()-.5);if(e.change=Math.round(10*a)/10,"revenue"===e.key){const t=25680,l=Math.round(t*(1+a/100));e.value=`¥${l.toLocaleString()}`}else if("users"===e.key){const t=1234,l=Math.round(t*(1+a/100));e.value=l.toLocaleString()}else if("groups"===e.key){const t=56,l=Math.round(t*(1+a/100));e.value=l.toString()}else if("conversion"===e.key){const t=12.8,l=Math.round(t*(1+a/100)*10)/10;e.value=`${l}%`}}),o.value.forEach(e=>{e.percentage=Math.max(10,Math.min(90,e.percentage+20*(Math.random()-.5)))}),h.value.forEach(e=>{e.rate=Math.max(50,Math.min(95,e.rate+10*(Math.random()-.5))),e.active=Math.round(e.members*e.rate/100)}),C.success("数据刷新成功")}catch(e){C.error("数据刷新失败")}finally{r.value=!1}},exportData:()=>{try{const e={exportTime:(new Date).toLocaleString("zh-CN"),coreMetrics:c.value,revenueData:o.value,userDistribution:p.value,groupActivity:h.value,conversionFunnel:f.value,regionData:y.value,realtimeData:k.value},a=JSON.stringify(e,null,2),t=new Blob([a],{type:"application/json"}),l=document.createElement("a");l.href=URL.createObjectURL(t),l.download=`数据大屏导出_${(new Date).toISOString().slice(0,10)}.json`,l.click(),C.success("数据导出成功")}catch(e){C.error("数据导出失败")}},getRegionData:w,getRegionColor:e=>({heavy:"#3b82f6",medium:"#06b6d4",light:"#8b5cf6"}[w(e).level]||"#64748b"),selectRegion:e=>{i.value=i.value===e?"":e,C.info(`选择了${e}地区`)},updateRealtimeData:D,get timeInterval(){return _},set timeInterval(e){_=e},get dataInterval(){return L},set dataInterval(e){L=e},ref:d,onMounted:g,onUnmounted:m,get useRouter(){return u},get ElMessage(){return C}};return Object.defineProperty(R,"__isScriptSetup",{enumerable:!1,value:!0}),R}},[["render",function(u,d,v,g,m,C){const D=h,_=x,L=k,He=b,Ke=w;return a(),e("div",R,[t("div",M,[d[12]||(d[12]=l('<div class="header-left" data-v-87eef6f3><div class="logo" data-v-87eef6f3><div class="logo-icon" data-v-87eef6f3>📊</div><span class="logo-text" data-v-87eef6f3>数据中心</span></div></div><div class="header-center" data-v-87eef6f3><h1 class="screen-title" data-v-87eef6f3>实时运营数据大屏</h1></div>',2)),t("div",S,[t("div",N,p(g.currentTime),1),d[11]||(d[11]=t("div",{class:"refresh-info"},[t("span",{class:"refresh-dot"}),s(" 实时更新 ")],-1)),t("div",j,[n(D,{onClick:g.exportData,size:"small"},{default:i(()=>d[8]||(d[8]=[s(" 导出数据 ",-1)])),_:1,__:[8]}),n(D,{onClick:g.refreshData,size:"small",loading:g.isRefreshing},{default:i(()=>d[9]||(d[9]=[s(" 刷新数据 ",-1)])),_:1,__:[9]},8,["loading"]),n(D,{onClick:g.openFullscreen,type:"primary",size:"small"},{default:i(()=>d[10]||(d[10]=[s(" 全屏显示 ",-1)])),_:1,__:[10]})])])]),t("div",I,[t("div",z,[(a(!0),e(r,null,c(g.coreMetrics,l=>(a(),e("div",{class:"metric-card",key:l.key},[t("div",{class:"metric-icon",style:f({background:l.color})},p(l.icon),5),t("div",A,[t("div",P,p(l.label),1),t("div",B,p(l.value),1),t("div",{class:y(["metric-change",{positive:l.change>0,negative:l.change<0}])},p(l.change>0?"+":"")+p(l.change)+"% ",3)])]))),128))])]),t("div",F,[n(Ke,{gutter:24},{default:i(()=>[n(He,{span:12},{default:i(()=>[t("div",G,[t("div",$,[d[13]||(d[13]=t("h3",null,"收入趋势",-1)),t("div",U,[n(L,{modelValue:g.selectedPeriod,"onUpdate:modelValue":d[0]||(d[0]=e=>g.selectedPeriod=e),size:"small"},{default:i(()=>[n(_,{label:"今日",value:"1d"}),n(_,{label:"7天",value:"7d"}),n(_,{label:"30天",value:"30d"})]),_:1},8,["modelValue"])])]),t("div",V,[t("div",E,[t("div",T,[(a(!0),e(r,null,c(g.revenueData,l=>(a(),e("div",{class:"data-point",key:l.time},[t("div",{class:"data-bar",style:f({height:l.percentage+"%"})},null,4),t("div",O,p(l.label),1),t("div",q,"¥"+p(l.value),1)]))),128))])])])])]),_:1}),n(He,{span:12},{default:i(()=>[t("div",Y,[d[14]||(d[14]=t("div",{class:"chart-header"},[t("h3",null,"用户分布")],-1)),t("div",Z,[t("div",J,[(a(!0),e(r,null,c(g.userDistribution,l=>(a(),e("div",{class:"distribution-item",key:l.type},[t("div",{class:"distribution-icon",style:f({background:l.color})},p(l.icon),5),t("div",Q,[t("div",X,p(l.label),1),t("div",H,p(l.count)+"人",1),t("div",K,p(l.percentage)+"%",1)])]))),128))])])])]),_:1})]),_:1}),n(Ke,{gutter:24,style:{"margin-top":"24px"}},{default:i(()=>[n(He,{span:12},{default:i(()=>[t("div",W,[t("div",ee,[d[15]||(d[15]=t("h3",null,"社群活跃度排行",-1)),t("div",ae,[n(L,{modelValue:g.selectedActivityPeriod,"onUpdate:modelValue":d[1]||(d[1]=e=>g.selectedActivityPeriod=e),size:"small"},{default:i(()=>[n(_,{label:"今日",value:"today"}),n(_,{label:"本周",value:"week"}),n(_,{label:"本月",value:"month"})]),_:1},8,["modelValue"])])]),t("div",te,[t("div",le,[(a(!0),e(r,null,c(g.groupActivity,(l,s)=>(a(),e("div",{class:"ranking-item",key:l.name},[t("div",{class:y(["ranking-number",{"top-1":0===s,"top-2":1===s,"top-3":2===s}])},p(s+1),3),t("div",se,[t("div",ne,p(l.name),1),t("div",ie,[t("span",re,[d[16]||(d[16]=t("span",{class:"stat-label"},"成员",-1)),t("span",ce,p(l.members),1)]),t("span",oe,[d[17]||(d[17]=t("span",{class:"stat-label"},"活跃",-1)),t("span",ue,p(l.active),1)])])]),t("div",de,[t("div",{class:"rate-circle",style:f({background:`conic-gradient(var(--primary-500) ${3.6*l.rate}deg, var(--gray-200) 0deg)`})},[t("div",ve,p(l.rate)+"%",1)],4)])]))),128))])])])]),_:1}),n(He,{span:12},{default:i(()=>[t("div",ge,[t("div",me,[d[18]||(d[18]=t("h3",null,"转化漏斗分析",-1)),t("div",pe,[t("span",he,"总转化率: "+p(g.finalConversionRate)+"%",1)])]),t("div",fe,[t("div",ye,[(a(!0),e(r,null,c(g.conversionFunnel,(l,s)=>(a(),e("div",{class:"funnel-item",key:l.stage},[t("div",be,[t("div",{class:"funnel-bar",style:f({width:l.percentage+"%",background:`linear-gradient(135deg,\n                        hsl(${220-15*s}, 70%, ${60+5*s}%),\n                        hsl(${220-15*s}, 70%, ${50+5*s}%))`})},[t("div",ke,[t("span",xe,p(l.stage),1),t("span",we,p(l.count),1)])],4),t("div",Ce,[t("span",De,p(l.percentage)+"%",1),s>0?(a(),e("span",_e," 转化率: "+p(Math.round(l.count.replace(",","")/g.conversionFunnel[s-1].count.replace(",","")*100))+"% ",1)):o("",!0)])])]))),128))])])])]),_:1})]),_:1}),n(Ke,{gutter:24,style:{"margin-top":"24px"}},{default:i(()=>[n(He,{span:24},{default:i(()=>[t("div",Le,[d[36]||(d[36]=t("div",{class:"chart-header"},[t("h3",null,"用户地域分布"),t("div",{class:"chart-controls"},[t("div",{class:"map-legend"},[t("span",{class:"legend-item"},[t("span",{class:"legend-color light"}),t("span",{class:"legend-text"},"1-100人")]),t("span",{class:"legend-item"},[t("span",{class:"legend-color medium"}),t("span",{class:"legend-text"},"101-300人")]),t("span",{class:"legend-item"},[t("span",{class:"legend-color heavy"}),t("span",{class:"legend-text"},"300+人")])])])],-1)),t("div",Re,[t("div",Me,[t("div",Se,[(a(),e("svg",Ne,[d[31]||(d[31]=t("defs",null,[t("radialGradient",{id:"mapBg",cx:"50%",cy:"50%",r:"50%"},[t("stop",{offset:"0%",style:{"stop-color":"rgba(15,23,42,0.8)","stop-opacity":"1"}}),t("stop",{offset:"100%",style:{"stop-color":"rgba(30,41,59,0.9)","stop-opacity":"1"}})]),t("filter",{id:"glow"},[t("feGaussianBlur",{stdDeviation:"3",result:"coloredBlur"}),t("feMerge",null,[t("feMergeNode",{in:"coloredBlur"}),t("feMergeNode",{in:"SourceGraphic"})])]),t("linearGradient",{id:"heatGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%"},[t("stop",{offset:"0%",style:{"stop-color":"#3b82f6","stop-opacity":"0.3"}}),t("stop",{offset:"50%",style:{"stop-color":"#06b6d4","stop-opacity":"0.5"}}),t("stop",{offset:"100%",style:{"stop-color":"#8b5cf6","stop-opacity":"0.7"}})])],-1)),d[32]||(d[32]=t("rect",{width:"800",height:"600",fill:"url(#mapBg)"},null,-1)),d[33]||(d[33]=t("g",{class:"china-outline"},[t("path",{d:"M200,150 L250,120 L300,130 L350,140 L400,135 L450,145 L500,160 L550,180 L580,220 L590,260 L580,300 L570,340 L550,380 L520,420 L480,450 L440,470 L400,480 L360,475 L320,465 L280,450 L240,430 L200,400 L170,360 L160,320 L155,280 L160,240 L170,200 L180,170 Z",fill:"rgba(59, 130, 246, 0.1)",stroke:"rgba(59, 130, 246, 0.5)","stroke-width":"2",class:"china-border"})],-1)),d[34]||(d[34]=t("g",{class:"heat-zones"},[t("circle",{cx:"400",cy:"180",r:"40",fill:"url(#heatGradient)",opacity:"0.6",class:"heat-zone"},[t("animate",{attributeName:"r",values:"35;45;35",dur:"3s",repeatCount:"indefinite"}),t("animate",{attributeName:"opacity",values:"0.4;0.8;0.4",dur:"3s",repeatCount:"indefinite"})]),t("circle",{cx:"480",cy:"280",r:"35",fill:"url(#heatGradient)",opacity:"0.5",class:"heat-zone"},[t("animate",{attributeName:"r",values:"30;40;30",dur:"2.5s",repeatCount:"indefinite"}),t("animate",{attributeName:"opacity",values:"0.3;0.7;0.3",dur:"2.5s",repeatCount:"indefinite"})]),t("ellipse",{cx:"430",cy:"390",rx:"25",ry:"20",fill:"url(#heatGradient)",opacity:"0.4",class:"heat-zone"},[t("animate",{attributeName:"rx",values:"20;30;20",dur:"2s",repeatCount:"indefinite"}),t("animate",{attributeName:"opacity",values:"0.2;0.6;0.2",dur:"2s",repeatCount:"indefinite"})])],-1)),t("g",je,[t("circle",{cx:"400",cy:"180",r:"8",fill:g.getRegionColor("北京"),class:"city-marker",onClick:d[2]||(d[2]=e=>g.selectRegion("北京")),filter:"url(#glow)"},[t("title",null,"北京: "+p(g.getRegionData("北京").count)+"人",1),d[19]||(d[19]=t("animate",{attributeName:"r",values:"6;10;6",dur:"2s",repeatCount:"indefinite"},null,-1))],8,Ie),d[25]||(d[25]=t("text",{x:"400",y:"165","text-anchor":"middle",class:"city-label"},"北京",-1)),t("circle",{cx:"480",cy:"280",r:"7",fill:g.getRegionColor("上海"),class:"city-marker",onClick:d[3]||(d[3]=e=>g.selectRegion("上海")),filter:"url(#glow)"},[t("title",null,"上海: "+p(g.getRegionData("上海").count)+"人",1),d[20]||(d[20]=t("animate",{attributeName:"r",values:"5;9;5",dur:"1.8s",repeatCount:"indefinite"},null,-1))],8,ze),d[26]||(d[26]=t("text",{x:"480",y:"265","text-anchor":"middle",class:"city-label"},"上海",-1)),t("circle",{cx:"420",cy:"380",r:"6",fill:g.getRegionColor("广州"),class:"city-marker",onClick:d[4]||(d[4]=e=>g.selectRegion("广州")),filter:"url(#glow)"},[t("title",null,"广州: "+p(g.getRegionData("广州").count)+"人",1),d[21]||(d[21]=t("animate",{attributeName:"r",values:"4;8;4",dur:"1.5s",repeatCount:"indefinite"},null,-1))],8,Ae),d[27]||(d[27]=t("text",{x:"420",y:"365","text-anchor":"middle",class:"city-label"},"广州",-1)),t("circle",{cx:"440",cy:"400",r:"6",fill:g.getRegionColor("深圳"),class:"city-marker",onClick:d[5]||(d[5]=e=>g.selectRegion("深圳")),filter:"url(#glow)"},[t("title",null,"深圳: "+p(g.getRegionData("深圳").count)+"人",1),d[22]||(d[22]=t("animate",{attributeName:"r",values:"4;8;4",dur:"1.3s",repeatCount:"indefinite"},null,-1))],8,Pe),d[28]||(d[28]=t("text",{x:"440",y:"415","text-anchor":"middle",class:"city-label"},"深圳",-1)),t("circle",{cx:"460",cy:"260",r:"5",fill:g.getRegionColor("杭州"),class:"city-marker",onClick:d[6]||(d[6]=e=>g.selectRegion("杭州")),filter:"url(#glow)"},[t("title",null,"杭州: "+p(g.getRegionData("杭州").count)+"人",1),d[23]||(d[23]=t("animate",{attributeName:"r",values:"3;7;3",dur:"1.2s",repeatCount:"indefinite"},null,-1))],8,Be),d[29]||(d[29]=t("text",{x:"460",y:"245","text-anchor":"middle",class:"city-label"},"杭州",-1)),t("circle",{cx:"320",cy:"300",r:"5",fill:g.getRegionColor("成都"),class:"city-marker",onClick:d[7]||(d[7]=e=>g.selectRegion("成都")),filter:"url(#glow)"},[t("title",null,"成都: "+p(g.getRegionData("成都").count)+"人",1),d[24]||(d[24]=t("animate",{attributeName:"r",values:"3;7;3",dur:"1.1s",repeatCount:"indefinite"},null,-1))],8,Fe),d[30]||(d[30]=t("text",{x:"320",y:"285","text-anchor":"middle",class:"city-label"},"成都",-1))]),d[35]||(d[35]=t("g",{class:"connection-lines"},[t("line",{x1:"400",y1:"180",x2:"480",y2:"280",stroke:"rgba(59, 130, 246, 0.3)","stroke-width":"1",class:"connection-line"},[t("animate",{attributeName:"stroke-opacity",values:"0.1;0.5;0.1",dur:"3s",repeatCount:"indefinite"})]),t("line",{x1:"480",y1:"280",x2:"430",y2:"390",stroke:"rgba(59, 130, 246, 0.3)","stroke-width":"1",class:"connection-line"},[t("animate",{attributeName:"stroke-opacity",values:"0.1;0.5;0.1",dur:"2.5s",repeatCount:"indefinite"})])],-1))]))]),t("div",Ge,[t("div",$e,[t("div",Ue,[t("h4",null,p(g.selectedRegion||"全国")+"用户统计",1)]),t("div",Ve,[(a(!0),e(r,null,c(g.regionData,l=>(a(),e("div",{class:"stat-card",key:l.name},[t("div",{class:"stat-icon",style:f({background:g.getRegionColor(l.name)})},p(l.icon),5),t("div",Ee,[t("div",Te,p(l.name),1),t("div",Oe,p(l.count)+"人",1),t("div",{class:"stat-growth","data-negative":l.growth<0},p(l.growth>0?"+":"")+p(l.growth)+"% ",9,qe)])]))),128))])])])])])])]),_:1})]),_:1})]),t("div",Ye,[d[37]||(d[37]=t("div",{class:"realtime-header"},[t("h3",null,"实时数据流")],-1)),t("div",Ze,[(a(!0),e(r,null,c(g.realtimeData,l=>(a(),e("div",{class:"realtime-item",key:l.id},[t("div",Je,p(l.time),1),t("div",Qe,p(l.event),1),t("div",Xe,p(l.value),1)]))),128))])])])}],["__scopeId","data-v-87eef6f3"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/dashboard/SimpleDataScreen.vue"]]),Ke={class:"fullscreen-data-screen"};const We=L({__name:"DataScreenFullscreen",setup(e,{expose:a}){a();const t=u(),l={router:t,goBack:()=>{t.go(-1)},get useRouter(){return u},get ArrowLeft(){return _},DataScreen:He};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}},[["render",function(l,s,r,c,o,u){const d=D;return a(),e("div",Ke,[t("div",{class:"back-button",onClick:c.goBack},[n(d,null,{default:i(()=>[n(c.ArrowLeft)]),_:1}),s[0]||(s[0]=t("span",null,"返回",-1))]),n(c.DataScreen)])}],["__scopeId","data-v-4f36bafa"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/dashboard/DataScreenFullscreen.vue"]]);export{We as default};
