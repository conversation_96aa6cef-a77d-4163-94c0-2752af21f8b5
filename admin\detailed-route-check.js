/**
 * 详细路由检查脚本 - 精确分析路由问题
 */

import fs from 'fs';
import path from 'path';

// 旧路由模式 -> 新路由模式的精确映射
const exactRouteMapping = {
  '/users': '/admin/users/list',
  '/users/list': '/admin/users/list', 
  '/users/analytics': '/admin/users/analytics',
  '/community': '/admin/community/groups',
  '/community/groups': '/admin/community/groups',
  '/community/templates': '/admin/community/templates',
  '/agents': '/admin/agents/list',
  '/agents/list': '/admin/agents/list',
  '/finance': '/admin/finance/dashboard',
  '/finance/dashboard': '/admin/finance/dashboard',
  '/finance/transactions': '/admin/finance/transactions',
  '/finance/commission': '/admin/finance/commission',
  '/finance/withdraw': '/admin/finance/withdraw',
  '/payment': '/admin/payment/settings',
  '/payment/settings': '/admin/payment/settings',
  '/payment/channels': '/admin/payment/channels',
  '/orders': '/admin/orders',
  '/distribution': '/admin/promotion/distributors',
  '/promotion': '/admin/promotion/links',
  '/promotion/links': '/admin/promotion/links',
  '/anti-block': '/admin/anti-block/dashboard',
  '/anti-block/dashboard': '/admin/anti-block/dashboard',
  '/anti-block/domains': '/admin/anti-block/domains',
  '/anti-block/links': '/admin/anti-block/links',
  '/anti-block/analytics': '/admin/anti-block/analytics',
  '/permissions': '/admin/permissions/roles',
  '/permissions/roles': '/admin/permissions/roles',
  '/system': '/admin/system/settings',
  '/system/settings': '/admin/system/settings',
  '/system/monitor': '/admin/system/monitor',
  '/system/logs': '/admin/system/logs',
  '/system/notifications': '/admin/system/notifications',
  '/dashboard': '/admin/dashboard',
  '/analytics': '/admin/analytics'
};

function findVueFiles(dir) {
  const result = [];
  
  function traverse(currentDir) {
    try {
      const files = fs.readdirSync(currentDir);
      
      for (const file of files) {
        const filePath = path.join(currentDir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
          traverse(filePath);
        } else if (file.endsWith('.vue')) {
          result.push(filePath);
        }
      }
    } catch (error) {
      // 忽略权限错误
    }
  }
  
  traverse(dir);
  return result;
}

function analyzeRouteIssues(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 更精确的路由匹配模式
    const routePatterns = [
      /router\.push\(['"`]([^'"`]+)['"`]\)/g,
      /\$router\.push\(['"`]([^'"`]+)['"`]\)/g,
      /@click=\"[^\"]*router\.push\(['"`]([^'"`]+)['"`]\)/g,
      /@click=\"[^\"]*\$router\.push\(['"`]([^'"`]+)['"`]\)/g
    ];
    
    routePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const fullMatch = match[0];
        const route = match[1];
        
        // 跳过已经正确的路由
        if (route.startsWith('/admin/') || route.startsWith('/data-screen') || route === '/') {
          continue;
        }
        
        // 查找需要修复的路由
        let suggestion = '';
        if (exactRouteMapping[route]) {
          suggestion = exactRouteMapping[route];
        } else {
          // 模糊匹配
          const keys = Object.keys(exactRouteMapping);
          const matchedKey = keys.find(key => route.includes(key.replace('/', '')));
          if (matchedKey) {
            suggestion = exactRouteMapping[matchedKey];
          }
        }
        
        if (suggestion || !route.startsWith('/admin/')) {
          issues.push({
            fullMatch,
            route,
            suggestion: suggestion || '需要检查路由格式',
            lineNumber: getLineNumber(content, match.index)
          });
        }
      }
    });
    
    return issues;
  } catch (error) {
    return [];
  }
}

function getLineNumber(content, index) {
  return content.substring(0, index).split('\n').length;
}

async function main() {
  console.log('🔍 详细分析路由问题...\n');
  
  try {
    const vueFiles = findVueFiles('src');
    let totalIssues = 0;
    const priorityFiles = [];
    
    for (const file of vueFiles) {
      const issues = analyzeRouteIssues(file);
      if (issues.length > 0) {
        const relativeFile = path.relative(process.cwd(), file);
        console.log(`\n📁 ${relativeFile}:`);
        
        issues.forEach(issue => {
          console.log(`  ❌ 第${issue.lineNumber}行: ${issue.fullMatch}`);
          console.log(`     🔄 当前路由: ${issue.route}`);
          if (issue.suggestion !== '需要检查路由格式') {
            console.log(`     ✅ 建议修改为: ${issue.suggestion}`);
          } else {
            console.log(`     ⚠️  ${issue.suggestion}`);
          }
        });
        
        totalIssues += issues.length;
        
        // 标记重要文件
        if (file.includes('ModernDashboard') || file.includes('Dashboard') || file.includes('Layout')) {
          priorityFiles.push({ file: relativeFile, issueCount: issues.length });
        }
      }
    }
    
    console.log(`\n\n📊 统计结果:`);
    console.log(`发现 ${totalIssues} 个路由问题，涉及 ${vueFiles.filter(f => analyzeRouteIssues(f).length > 0).length} 个文件`);
    
    if (priorityFiles.length > 0) {
      console.log(`\n🔥 优先修复文件:`);
      priorityFiles.forEach(item => {
        console.log(`  • ${item.file} (${item.issueCount} 个问题)`);
      });
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  }
}

// 直接运行
main();