<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center">
          <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-lg">L</span>
          </div>
        </div>
        <h2 class="mt-6 text-center text-3xl font-bold text-gray-900">
          创建您的账户
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          已有账户？
          <NuxtLink to="/login" class="font-medium text-primary-600 hover:text-primary-500">
            立即登录
          </NuxtLink>
        </p>
      </div>

      <!-- 注册表单 -->
      <form class="mt-8 space-y-6" @submit.prevent="handleRegister">
        <div class="space-y-4">
          <!-- 用户名 -->
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700">
              用户名 <span class="text-red-500">*</span>
            </label>
            <input
              id="username"
              v-model="form.username"
              type="text"
              required
              class="input mt-1"
              :class="{ 'input-error': errors.username }"
              placeholder="请输入用户名"
            />
            <p v-if="errors.username" class="mt-1 text-sm text-error-600">
              {{ errors.username }}
            </p>
          </div>

          <!-- 邮箱 -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
              邮箱地址 <span class="text-red-500">*</span>
            </label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              required
              class="input mt-1"
              :class="{ 'input-error': errors.email }"
              placeholder="请输入邮箱地址"
            />
            <p v-if="errors.email" class="mt-1 text-sm text-error-600">
              {{ errors.email }}
            </p>
          </div>

          <!-- 密码 -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              密码 <span class="text-red-500">*</span>
            </label>
            <div class="relative mt-1">
              <input
                id="password"
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                required
                class="input pr-10"
                :class="{ 'input-error': errors.password }"
                placeholder="请输入密码"
              />
              <button
                type="button"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                @click="showPassword = !showPassword"
              >
                <svg
                  v-if="showPassword"
                  class="h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <svg
                  v-else
                  class="h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                </svg>
              </button>
            </div>
            <p v-if="errors.password" class="mt-1 text-sm text-error-600">
              {{ errors.password }}
            </p>
            <p class="mt-1 text-sm text-gray-500">
              密码至少6位，包含数字和字母
            </p>
          </div>

          <!-- 确认密码 -->
          <div>
            <label for="password_confirmation" class="block text-sm font-medium text-gray-700">
              确认密码 <span class="text-red-500">*</span>
            </label>
            <div class="relative mt-1">
              <input
                id="password_confirmation"
                v-model="form.password_confirmation"
                :type="showConfirmPassword ? 'text' : 'password'"
                required
                class="input pr-10"
                :class="{ 'input-error': errors.password_confirmation }"
                placeholder="请再次输入密码"
              />
              <button
                type="button"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                @click="showConfirmPassword = !showConfirmPassword"
              >
                <svg
                  v-if="showConfirmPassword"
                  class="h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <svg
                  v-else
                  class="h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                </svg>
              </button>
            </div>
            <p v-if="errors.password_confirmation" class="mt-1 text-sm text-error-600">
              {{ errors.password_confirmation }}
            </p>
          </div>

          <!-- 手机号 -->
          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700">
              手机号码
            </label>
            <input
              id="phone"
              v-model="form.phone"
              type="tel"
              class="input mt-1"
              :class="{ 'input-error': errors.phone }"
              placeholder="请输入手机号码（可选）"
            />
            <p v-if="errors.phone" class="mt-1 text-sm text-error-600">
              {{ errors.phone }}
            </p>
          </div>

          <!-- 邀请码 -->
          <div>
            <label for="invite_code" class="block text-sm font-medium text-gray-700">
              邀请码
            </label>
            <input
              id="invite_code"
              v-model="form.invite_code"
              type="text"
              class="input mt-1"
              :class="{ 'input-error': errors.invite_code }"
              placeholder="请输入邀请码（可选）"
            />
            <p v-if="errors.invite_code" class="mt-1 text-sm text-error-600">
              {{ errors.invite_code }}
            </p>
          </div>
        </div>

        <!-- 用户协议 -->
        <div class="flex items-center">
          <input
            id="agree"
            v-model="form.agree"
            type="checkbox"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label for="agree" class="ml-2 block text-sm text-gray-900">
            我已阅读并同意
            <a href="#" class="text-primary-600 hover:text-primary-500">服务条款</a>
            和
            <a href="#" class="text-primary-600 hover:text-primary-500">隐私政策</a>
          </label>
        </div>
        <p v-if="errors.agree" class="mt-1 text-sm text-error-600">
          {{ errors.agree }}
        </p>

        <!-- 提交按钮 -->
        <div>
          <button
            type="submit"
            :disabled="loading"
            class="btn btn-primary w-full py-3"
          >
            <span v-if="loading" class="spinner mr-2"></span>
            {{ loading ? '注册中...' : '注册账户' }}
          </button>
        </div>

        <!-- 错误提示 -->
        <div v-if="globalError" class="text-center">
          <p class="text-sm text-error-600">{{ globalError }}</p>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '~/stores/auth'

// 页面元数据
definePageMeta({
  layout: 'default',
  middleware: 'guest'
})

// 页面头部
useHead({
  title: '注册账户 - 晨鑫流量变现系统',
  meta: [
    { name: 'description', content: '注册晨鑫流量变现系统账户，开始您的智能社群营销之旅' }
  ]
})

// 状态管理
const authStore = useAuthStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const globalError = ref('')

// 表单数据
const form = reactive({
  username: '',
  email: '',
  password: '',
  password_confirmation: '',
  phone: '',
  invite_code: '',
  agree: false
})

// 错误信息
const errors = reactive({
  username: '',
  email: '',
  password: '',
  password_confirmation: '',
  phone: '',
  invite_code: '',
  agree: ''
})

// 清除错误
const clearErrors = () => {
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })
  globalError.value = ''
}

// 验证表单
const validateForm = () => {
  clearErrors()
  let isValid = true

  // 用户名验证
  if (!form.username.trim()) {
    errors.username = '请输入用户名'
    isValid = false
  } else if (form.username.length < 3) {
    errors.username = '用户名长度至少3位'
    isValid = false
  } else if (!/^[a-zA-Z0-9_]+$/.test(form.username)) {
    errors.username = '用户名只能包含字母、数字和下划线'
    isValid = false
  }

  // 邮箱验证
  if (!form.email.trim()) {
    errors.email = '请输入邮箱地址'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = '请输入正确的邮箱地址'
    isValid = false
  }

  // 密码验证
  if (!form.password) {
    errors.password = '请输入密码'
    isValid = false
  } else if (form.password.length < 6) {
    errors.password = '密码长度至少6位'
    isValid = false
  } else if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,}$/.test(form.password)) {
    errors.password = '密码必须包含字母和数字'
    isValid = false
  }

  // 确认密码验证
  if (!form.password_confirmation) {
    errors.password_confirmation = '请确认密码'
    isValid = false
  } else if (form.password !== form.password_confirmation) {
    errors.password_confirmation = '两次输入的密码不一致'
    isValid = false
  }

  // 手机号验证（可选）
  if (form.phone && !/^1[3-9]\d{9}$/.test(form.phone)) {
    errors.phone = '请输入正确的手机号码'
    isValid = false
  }

  // 用户协议验证
  if (!form.agree) {
    errors.agree = '请同意服务条款和隐私政策'
    isValid = false
  }

  return isValid
}

// 处理注册
const handleRegister = async () => {
  if (!validateForm()) return

  loading.value = true
  clearErrors()

  try {
    const result = await authStore.register({
      username: form.username.trim(),
      email: form.email.trim(),
      password: form.password,
      password_confirmation: form.password_confirmation,
      phone: form.phone.trim() || undefined,
      invite_code: form.invite_code.trim() || undefined
    })

    if (result.success) {
      // 注册成功，跳转到仪表盘
      await navigateTo('/dashboard')
    } else {
      // 处理服务器返回的错误
      if (result.errors) {
        Object.keys(result.errors).forEach(key => {
          if (errors.hasOwnProperty(key)) {
            errors[key] = result.errors[key][0]
          }
        })
      } else {
        globalError.value = result.message || '注册失败，请重试'
      }
    }
  } catch (error) {
    console.error('注册错误:', error)
    globalError.value = '注册失败，请检查网络连接'
  } finally {
    loading.value = false
  }
}

// 监听表单变化，清除错误
watch(form, () => {
  if (globalError.value) {
    globalError.value = ''
  }
  // 清除对应字段的错误
  Object.keys(form).forEach(key => {
    if (errors.hasOwnProperty(key) && errors[key] && form[key]) {
      errors[key] = ''
    }
  })
})
</script>

<style scoped>
/* 注册页面特定样式 */
.btn {
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-error {
  border-color: #ef4444;
}

.input-error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 密码强度指示器 */
.password-strength {
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 8px;
}

.password-strength-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.password-strength-weak {
  background-color: #ef4444;
  width: 25%;
}

.password-strength-medium {
  background-color: #f59e0b;
  width: 50%;
}

.password-strength-strong {
  background-color: #10b981;
  width: 75%;
}

.password-strength-very-strong {
  background-color: #059669;
  width: 100%;
}
</style> 