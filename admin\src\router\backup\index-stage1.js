// 阶段一：核心功能模块迁移
// 优先迁移低风险的核心功能：仪表板、基础用户管理
import { createRouter, createWebHashHistory } from 'vue-router'
import { dataScreenRoutes } from './dataScreen.js'

// === 核心路由配置 (新架构) ===
const coreRoutes = [
  // 登录页面
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      hideInMenu: true
    }
  },
  
  // 主布局 - 仅包含核心功能
  {
    path: '/admin',
    component: () => import('@/components/layout/ModernLayout.vue'),
    redirect: '/admin/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      // === 核心仪表板 ===
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/ModernDashboard.vue'),
        meta: {
          title: '仪表板',
          icon: 'TrendCharts',
          requiresAuth: true,
          group: '核心功能'
        }
      },

      // === 用户管理模块 ===
      {
        path: 'users',
        name: 'UserManagement',
        redirect: '/admin/users/list',
        meta: {
          title: '用户管理',
          icon: 'User',
          requiresAuth: true,
          group: '用户管理'
        },
        children: [
          {
            path: 'list',
            name: 'UserList',
            component: () => import('@/views/user/UserList.vue'),
            meta: {
              title: '用户列表',
              icon: 'List',
              requiresAuth: true
            }
          },
          {
            path: 'profile',
            name: 'UserProfile',
            component: () => import('@/views/user/Profile.vue'),
            meta: {
              title: '个人资料',
              icon: 'Avatar',
              requiresAuth: true
            }
          }
        ]
      }
    ]
  }
]

// === 保留的旧路由 (暂时保持不变) ===
const legacyRoutes = [
  // 兼容性重定向
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/dashboard',
    redirect: '/admin/dashboard'
  },
  
  // === 暂时保留的旧路由结构 ===
  // 社群管理（暂时保持旧结构）
  {
    path: '/community/groups',
    name: 'CommunityGroups',
    component: () => import('@/views/community/GroupList.vue'),
    meta: {
      title: '群组管理',
      icon: 'ChatDotRound',
      requiresAuth: true
    }
  },
  {
    path: '/community/templates',
    name: 'CommunityTemplates',
    component: () => import('@/views/community/TemplateManagement.vue'),
    meta: {
      title: '模板管理',
      icon: 'Document',
      requiresAuth: true
    }
  },

  // 代理商管理（保持旧结构）
  {
    path: '/agent/list',
    name: 'AgentList',
    component: () => import('@/views/agent/AgentList.vue'),
    meta: {
      title: '代理商列表',
      icon: 'Avatar',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/agent/hierarchy',
    name: 'AgentHierarchy',
    component: () => import('@/views/agent/AgentHierarchy.vue'),
    meta: {
      title: '代理商层级',
      icon: 'Grid',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/agent/commission',
    name: 'AgentCommission',
    component: () => import('@/views/agent/AgentCommission.vue'),
    meta: {
      title: '佣金管理',
      icon: 'Money',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/agent/performance',
    name: 'AgentPerformance',
    component: () => import('@/views/agent/AgentPerformance.vue'),
    meta: {
      title: '业绩统计',
      icon: 'TrendCharts',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 财务管理（保持旧结构）
  {
    path: '/finance',
    name: 'FinanceDashboard',
    component: () => import('@/views/finance/FinanceDashboard.vue'),
    meta: {
      title: '财务管理',
      icon: 'Money',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/finance/transactions',
    name: 'TransactionList',
    component: () => import('@/views/finance/TransactionList.vue'),
    meta: {
      title: '交易记录',
      icon: 'CreditCard',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/finance/commission',
    name: 'CommissionLog',
    component: () => import('@/views/finance/CommissionLog.vue'),
    meta: {
      title: '佣金日志',
      icon: 'Money',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/finance/withdraw',
    name: 'WithdrawManage',
    component: () => import('@/views/finance/WithdrawManage.vue'),
    meta: {
      title: '提现管理',
      icon: 'CreditCard',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 支付管理（保持旧结构）
  {
    path: '/payment/settings',
    name: 'PaymentSettings',
    component: () => import('@/views/payment/PaymentSettings.vue'),
    meta: {
      title: '支付设置',
      icon: 'Setting',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/payment/channels',
    name: 'PaymentChannelManagement',
    component: () => import('@/views/payment/PaymentChannelManagement.vue'),
    meta: {
      title: '支付渠道',
      icon: 'CreditCard',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/payment/orders',
    name: 'PaymentOrders',
    component: () => import('@/views/payment/PaymentOrders.vue'),
    meta: {
      title: '支付订单',
      icon: 'Tickets',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/payment/logs',
    name: 'PaymentLogs',
    component: () => import('@/views/payment/PaymentLogs.vue'),
    meta: {
      title: '支付日志',
      icon: 'Document',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 订单管理
  {
    path: '/orders',
    name: 'OrderManagement',
    component: () => import('@/views/orders/OrderList.vue'),
    meta: {
      title: '订单管理',
      icon: 'ShoppingCart',
      requiresAuth: true
    }
  },

  // 分销管理
  {
    path: '/distribution/distributors',
    name: 'DistributorManagement',
    component: () => import('@/views/distribution/DistributorList.vue'),
    meta: {
      title: '分销管理',
      icon: 'Share',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 推广管理
  {
    path: '/promotion/links',
    name: 'PromotionManagement',
    component: () => import('@/views/promotion/LinkManagement.vue'),
    meta: {
      title: '推广管理',
      icon: 'Share',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 防红系统
  {
    path: '/anti-block/dashboard',
    name: 'AntiBlockDashboard',
    component: () => import('@/views/anti-block/Dashboard.vue'),
    meta: {
      title: '防红系统',
      icon: 'Lock',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/anti-block/domains',
    name: 'DomainManagement',
    component: () => import('@/views/anti-block/DomainList.vue'),
    meta: {
      title: '域名管理',
      icon: 'Connection',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/anti-block/short-links',
    name: 'ShortLinkManagement',
    component: () => import('@/views/anti-block/ShortLinkList.vue'),
    meta: {
      title: '短链管理',
      icon: 'Link',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/anti-block/analytics',
    name: 'AntiBlockAnalytics',
    component: () => import('@/views/anti-block/Analytics.vue'),
    meta: {
      title: '防红分析',
      icon: 'TrendCharts',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 权限管理
  {
    path: '/permission/roles',
    name: 'RoleManagement',
    component: () => import('@/views/permission/RoleManagement.vue'),
    meta: {
      title: '角色管理',
      icon: 'Avatar',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/permission/permissions',
    name: 'PermissionManagement',
    component: () => import('@/views/permission/PermissionManagement.vue'),
    meta: {
      title: '权限管理',
      icon: 'Lock',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 系统管理
  {
    path: '/system/settings',
    name: 'SystemSettings',
    component: () => import('@/views/system/Settings.vue'),
    meta: {
      title: '系统设置',
      icon: 'Setting',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/monitor',
    name: 'SystemMonitor',
    component: () => import('@/views/system/Monitor.vue'),
    meta: {
      title: '系统监控',
      icon: 'Monitor',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/operation-logs',
    name: 'OperationLogs',
    component: () => import('@/views/system/OperationLogs.vue'),
    meta: {
      title: '操作日志',
      icon: 'Document',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/notifications',
    name: 'NotificationManagement',
    component: () => import('@/views/system/Notifications.vue'),
    meta: {
      title: '通知管理',
      icon: 'Bell',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/data-export',
    name: 'DataExport',
    component: () => import('@/views/system/DataExport.vue'),
    meta: {
      title: '数据导出',
      icon: 'Document',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/file-management',
    name: 'FileManagement',
    component: () => import('@/views/system/FileManagement.vue'),
    meta: {
      title: '文件管理',
      icon: 'Folder',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 其他页面
  {
    path: '/user/analytics',
    name: 'UserAnalytics',
    component: () => import('@/views/user/UserAnalytics.vue'),
    meta: {
      title: '用户分析',
      icon: 'TrendCharts',
      requiresAuth: true
    }
  },

  // 测试路由
  {
    path: '/test',
    name: 'TestRoute',
    component: () => import('@/views/TestRoute.vue'),
    meta: {
      title: '路由测试',
      hideInMenu: true
    }
  },
  {
    path: '/navigation-test',
    name: 'NavigationTest',
    component: () => import('@/views/NavigationTest.vue'),
    meta: {
      title: '导航测试',
      hideInMenu: true
    }
  },

  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/ErrorPage.vue'),
    meta: {
      title: '页面未找到',
      hideInMenu: true
    }
  }
]

// 合并所有路由
const allRoutes = [...coreRoutes, ...legacyRoutes, ...dataScreenRoutes]

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes: allRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  console.log(`🛣️ [阶段一] 路由导航: ${from.path} → ${to.path}`)

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 晨鑫流量变现系统 管理系统`
  }

  // 简化的认证检查
  console.log('✅ 阶段一路由导航完成')
  next()
})

// 导出迁移状态信息
export const migrationStatus = {
  stage: 1,
  description: '核心功能模块迁移',
  migratedRoutes: ['/admin/dashboard', '/admin/users'],
  pendingRoutes: ['社群管理', '代理商管理', '财务管理', '支付管理', '防红系统', '权限管理', '系统管理'],
  riskLevel: 'low',
  rollbackReady: true
}

export default router