<template>
  <div class="test-route">
    <h1>🎯 路由测试页面</h1>
    <p>如果您能看到这个页面，说明路由系统工作正常！</p>
    
    <div class="test-info">
      <h3>当前路由信息：</h3>
      <p><strong>路径:</strong> {{ $route.path }}</p>
      <p><strong>名称:</strong> {{ $route.name }}</p>
      <p><strong>参数:</strong> {{ JSON.stringify($route.params) }}</p>
      <p><strong>查询:</strong> {{ JSON.stringify($route.query) }}</p>
    </div>
    
    <div class="test-actions">
      <el-button @click="goToDashboard" type="primary">
        跳转到 Dashboard
      </el-button>
      <el-button @click="goToLogin" type="default">
        返回登录页
      </el-button>
    </div>
    
    <div class="token-info">
      <h3>认证信息：</h3>
      <p><strong>Token:</strong> {{ token || '无' }}</p>
      <p><strong>用户信息:</strong> {{ userInfo ? JSON.stringify(userInfo) : '无' }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'

const router = useRouter()
const userStore = useUserStore()

const token = ref('')
const userInfo = ref(null)

onMounted(() => {
  token.value = getToken()
  userInfo.value = userStore.userInfo
  console.log('🧪 测试页面加载完成')
  console.log('Token:', token.value)
  console.log('用户信息:', userInfo.value)
})

const goToDashboard = () => {
  console.log('🚀 手动跳转到 dashboard')
  router.push('/dashboard')
}

const goToLogin = () => {
  console.log('🔙 返回登录页')
  router.push('/login')
}
</script>

<style scoped>
.test-route {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.test-info, .token-info {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.test-actions {
  margin: 20px 0;
}

.test-actions .el-button {
  margin-right: 10px;
}

h1 {
  color: #409eff;
  text-align: center;
}

h3 {
  color: #303133;
  margin-bottom: 10px;
}

p {
  margin: 8px 0;
  color: #606266;
}
</style>
