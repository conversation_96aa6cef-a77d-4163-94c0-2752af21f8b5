/* empty css             */import{l as e,m as a,q as t,C as s,G as r,A as o,c as l,F as i,Y as n,z as c,E as d,D as u,r as p}from"./vue-vendor-BcnDv-68.js";import{ah as v,Z as m,u as g,ad as y,ak as f,a1 as b,af as h,a9 as w,aE as _,at as D,b2 as C,s as k,W as I,bW as x,bl as P,cl as j,aQ as A,by as E}from"./element-plus-C2UshkXo.js";import{_ as S}from"./index-eUTsTR3J.js";/* empty css                  *//* empty css               *//* empty css                   */import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const B={__name:"BannerPreview",props:{section:{type:Object,default:()=>({})},groupData:{type:Object,default:()=>({})},preview:{type:Boolean,default:!1}},setup(e,{expose:a}){a();const t=e,s=l(()=>t.groupData.banner_image||""),r=l(()=>!1!==t.section.config?.showOverlay),o=l(()=>({height:`${(t.section.config||{}).height||200}px`,position:"relative"})),i={props:t,bannerImage:s,showOverlay:r,bannerStyle:o,formatPrice:e=>0===e||"0"===e?"免费":`¥${e}`,computed:l,get Picture(){return y}};return Object.defineProperty(i,"__isScriptSetup",{enumerable:!1,value:!0}),i}},$={class:"banner-preview"},F=["src","alt"],M={key:1,class:"banner-placeholder"},O={key:2,class:"banner-overlay"},L={class:"overlay-content"},U={class:"banner-title"},T={key:0,class:"banner-description"},V={key:1,class:"banner-price"},z={class:"price-value"};const q=S(B,[["render",function(l,i,n,c,d,u){const p=v;return a(),e("div",$,[t("div",{class:"banner-container",style:g(c.bannerStyle)},[c.bannerImage?(a(),e("img",{key:0,src:c.bannerImage,alt:n.groupData.title,class:"banner-image"},null,8,F)):(a(),e("div",M,[r(p,{class:"placeholder-icon"},{default:o(()=>[r(c.Picture)]),_:1}),i[0]||(i[0]=t("div",{class:"placeholder-text"},"顶部横幅图片",-1))])),c.showOverlay?(a(),e("div",O,[t("div",L,[t("h1",U,m(n.groupData.title||"群组标题"),1),n.groupData.description?(a(),e("p",T,m(n.groupData.description),1)):s("",!0),void 0!==n.groupData.price?(a(),e("div",V,[i[1]||(i[1]=t("span",{class:"price-label"},"入群费用：",-1)),t("span",z,m(c.formatPrice(n.groupData.price)),1)])):s("",!0)])])):s("",!0)],4)])}],["__scopeId","data-v-2b7ac8e2"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/preview/BannerPreview.vue"]]),R={__name:"InfoPreview",props:{section:{type:Object,default:()=>({})},groupData:{type:Object,default:()=>({})},preview:{type:Boolean,default:!1}},setup(e,{expose:a}){a();const t=e,s=l(()=>!1!==t.section.config?.showPrice),r=l(()=>!1!==t.section.config?.showDescription),o=l(()=>t.groupData.member_count||"500+"),i=l(()=>t.groupData.join_button_text||"立即加入群组"),n=l(()=>t.groupData.activity_level||"高"),c=l(()=>t.groupData.group_type||"学习交流"),d=l(()=>t.groupData.tags||["技术交流","学习成长","资源分享"]),u={props:t,showPrice:s,showDescription:r,memberCount:o,joinButtonText:i,activityLevel:n,groupType:c,groupTags:d,formatPrice:e=>0===e||"0"===e?"免费入群":`¥${e} 入群`,computed:l,get User(){return w},get Plus(){return h}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}},H={class:"info-preview"},G={class:"info-container"},Z={class:"group-header"},W={class:"group-avatar"},Y=["src","alt"],N={key:1,class:"avatar-placeholder"},Q={class:"group-info"},J={class:"group-title"},K={key:0,class:"group-price"},X={class:"price-text"},ee={key:1,class:"group-description"},ae={class:"group-stats"},te={class:"stat-item"},se={class:"stat-value"},re={class:"stat-item"},oe={class:"stat-value"},le={class:"stat-item"},ie={class:"stat-value"},ne={key:0,class:"group-tags"},ce={class:"join-actions"};const de=S(R,[["render",function(l,u,p,g,y,h){const w=v,_=f,D=b;return a(),e("div",H,[t("div",G,[t("div",Z,[t("div",W,[p.groupData.avatar?(a(),e("img",{key:0,src:p.groupData.avatar,alt:p.groupData.title,class:"avatar-image"},null,8,Y)):(a(),e("div",N,[r(w,{class:"placeholder-icon"},{default:o(()=>[r(g.User)]),_:1})]))]),t("div",Q,[t("h2",J,m(p.groupData.title||"群组标题"),1),g.showPrice&&void 0!==p.groupData.price?(a(),e("div",K,[t("span",X,m(g.formatPrice(p.groupData.price)),1)])):s("",!0),g.showDescription&&p.groupData.description?(a(),e("div",ee,m(p.groupData.description),1)):s("",!0)])]),t("div",ae,[t("div",te,[t("div",se,m(g.memberCount),1),u[0]||(u[0]=t("div",{class:"stat-label"},"群成员",-1))]),t("div",re,[t("div",oe,m(g.activityLevel),1),u[1]||(u[1]=t("div",{class:"stat-label"},"活跃度",-1))]),t("div",le,[t("div",ie,m(g.groupType),1),u[2]||(u[2]=t("div",{class:"stat-label"},"群类型",-1))])]),g.groupTags.length>0?(a(),e("div",ne,[(a(!0),e(i,null,n(g.groupTags,e=>(a(),c(_,{key:e,size:"small",type:"primary",effect:"plain"},{default:o(()=>[d(m(e),1)]),_:2},1024))),128))])):s("",!0),t("div",ce,[r(D,{type:"primary",size:"large",class:"join-button"},{default:o(()=>[r(w,null,{default:o(()=>[r(g.Plus)]),_:1}),d(" "+m(g.joinButtonText),1)]),_:1}),u[3]||(u[3]=t("div",{class:"join-hint"},"点击按钮扫码入群",-1))])])])}],["__scopeId","data-v-6d1c077b"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/preview/InfoPreview.vue"]]),ue={__name:"ContentPreview",props:{section:{type:Object,default:()=>({})},groupData:{type:Object,default:()=>({})},preview:{type:Boolean,default:!1}},setup(e,{expose:a}){a();const t=e,s=p(!1),r=l(()=>t.groupData.rich_content||""),o=l(()=>t.section.config?.maxHeight||300),i=l(()=>r.value&&r.value.length>500),n=l(()=>{const e={lineHeight:"1.6",color:"#606266"};return!s.value&&i.value&&(e.maxHeight=`${o.value}px`,e.overflow="hidden",e.position="relative"),e}),c={props:t,isExpanded:s,richContent:r,maxHeight:o,showExpandButton:i,contentStyle:n,toggleExpanded:()=>{s.value=!s.value},ref:p,computed:l,get Document(){return C},get ArrowUp(){return D},get ArrowDown(){return _}};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}},pe={class:"content-preview"},ve={class:"content-container"},me={class:"content-body"},ge=["innerHTML"],ye={key:1,class:"content-placeholder"},fe={key:0,class:"content-actions"};const be=S(ue,[["render",function(l,i,n,p,y,f){const h=v,w=b;return a(),e("div",pe,[t("div",ve,[i[2]||(i[2]=t("div",{class:"content-header"},[t("h3",{class:"content-title"},"群组介绍")],-1)),t("div",me,[p.richContent?(a(),e("div",{key:0,class:"rich-content",style:g(p.contentStyle),innerHTML:p.richContent},null,12,ge)):(a(),e("div",ye,[r(h,{class:"placeholder-icon"},{default:o(()=>[r(p.Document)]),_:1}),i[0]||(i[0]=t("div",{class:"placeholder-text"},"暂无详细介绍内容",-1)),i[1]||(i[1]=t("div",{class:"placeholder-hint"},"请在编辑器中添加群组介绍",-1))]))]),p.showExpandButton?(a(),e("div",fe,[r(w,{onClick:p.toggleExpanded,type:"primary",text:"",size:"small"},{default:o(()=>[d(m(p.isExpanded?"收起":"展开全部")+" ",1),r(h,null,{default:o(()=>[(a(),c(u(p.isExpanded?"ArrowUp":"ArrowDown")))]),_:1})]),_:1})])):s("",!0)])])}],["__scopeId","data-v-04799622"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/preview/ContentPreview.vue"]]),he={__name:"GalleryPreview",props:{section:{type:Object,default:()=>({})},groupData:{type:Object,default:()=>({})},preview:{type:Boolean,default:!1}},setup(e,{expose:a}){a();const t=e,s=p(!1),r=p(0),o=l(()=>t.groupData.gallery_images||[]),i=l(()=>t.section.config?.columns||3),n=l(()=>!1!==t.section.config?.showThumbnails),c=l(()=>2*i.value),d=l(()=>o.value.slice(0,c.value)),u=l(()=>o.value.length>c.value),v=l(()=>o.value.length-c.value),m=l(()=>o.value[r.value]||""),g=e=>{r.value=e,s.value=!0},f={props:t,previewVisible:s,currentIndex:r,galleryImages:o,columns:i,showThumbnails:n,maxDisplay:c,displayImages:d,hasMoreImages:u,remainingCount:v,currentPreviewImage:m,previewImage:g,setCurrentImage:e=>{r.value=e},prevImage:()=>{r.value>0&&r.value--},nextImage:()=>{r.value<o.value.length-1&&r.value++},showAllImages:()=>{g(c.value)},ref:p,computed:l,get Picture(){return y},get ZoomIn(){return j},get ArrowLeft(){return P},get ArrowRight(){return x}};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}},we={class:"gallery-preview"},_e={class:"gallery-container"},De={key:0,class:"gallery-content"},Ce=["onClick"],ke=["src","alt"],Ie={class:"image-overlay"},xe={class:"more-overlay"},Pe={class:"more-text"},je={key:0,class:"thumbnail-nav"},Ae=["onClick"],Ee=["src","alt"],Se={key:1,class:"gallery-placeholder"},Be={class:"preview-content"},$e=["src","alt"],Fe={key:0,class:"preview-nav"},Me={class:"preview-info"},Oe={class:"image-counter"};const Le=S(he,[["render",function(l,c,d,u,p,g){const y=v,f=b,h=I;return a(),e("div",we,[t("div",_e,[c[4]||(c[4]=t("div",{class:"gallery-header"},[t("h3",{class:"gallery-title"},"精彩瞬间")],-1)),u.galleryImages.length>0?(a(),e("div",De,[t("div",{class:k(["image-grid",`grid-${u.columns}`])},[(a(!0),e(i,null,n(u.displayImages,(s,l)=>(a(),e("div",{key:l,class:"image-item",onClick:e=>u.previewImage(l)},[t("img",{src:s,alt:`图片${l+1}`},null,8,ke),t("div",Ie,[r(y,{class:"overlay-icon"},{default:o(()=>[r(u.ZoomIn)]),_:1})])],8,Ce))),128)),u.hasMoreImages?(a(),e("div",{key:0,class:"more-images",onClick:u.showAllImages},[t("div",xe,[t("div",Pe,"+"+m(u.remainingCount),1),c[1]||(c[1]=t("div",{class:"more-hint"},"更多图片",-1))])])):s("",!0)],2),u.showThumbnails&&u.galleryImages.length>1?(a(),e("div",je,[(a(!0),e(i,null,n(u.galleryImages,(s,r)=>(a(),e("div",{key:r,class:k(["thumbnail-item",{active:u.currentIndex===r}]),onClick:e=>u.setCurrentImage(r)},[t("img",{src:s,alt:`缩略图${r+1}`},null,8,Ee)],10,Ae))),128))])):s("",!0)])):(a(),e("div",Se,[r(y,{class:"placeholder-icon"},{default:o(()=>[r(u.Picture)]),_:1}),c[2]||(c[2]=t("div",{class:"placeholder-text"},"暂无展示图片",-1)),c[3]||(c[3]=t("div",{class:"placeholder-hint"},"请上传群组相关图片",-1))]))]),r(h,{modelValue:u.previewVisible,"onUpdate:modelValue":c[0]||(c[0]=e=>u.previewVisible=e),title:"图片预览",width:"80%","append-to-body":"",class:"image-preview-dialog"},{default:o(()=>[t("div",Be,[t("img",{src:u.currentPreviewImage,alt:`预览图片${u.currentIndex+1}`,class:"preview-image"},null,8,$e),u.galleryImages.length>1?(a(),e("div",Fe,[r(f,{onClick:u.prevImage,disabled:0===u.currentIndex,circle:"",size:"large",class:"nav-button prev-button"},{default:o(()=>[r(y,null,{default:o(()=>[r(u.ArrowLeft)]),_:1})]),_:1},8,["disabled"]),r(f,{onClick:u.nextImage,disabled:u.currentIndex===u.galleryImages.length-1,circle:"",size:"large",class:"nav-button next-button"},{default:o(()=>[r(y,null,{default:o(()=>[r(u.ArrowRight)]),_:1})]),_:1},8,["disabled"])])):s("",!0),t("div",Me,[t("span",Oe,m(u.currentIndex+1)+" / "+m(u.galleryImages.length),1)])])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-cc5234be"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/preview/GalleryPreview.vue"]]),Ue={__name:"VideoPreview",props:{section:{type:Object,default:()=>({})},groupData:{type:Object,default:()=>({})},preview:{type:Boolean,default:!1}},setup(e,{expose:a}){a();const t=e,s=p(0),r=l(()=>{const e=t.groupData.intro_video;return"string"==typeof e?{url:e}:e||null}),o=l(()=>{if(!r.value)return!1;const e=r.value.url;return!(e.includes("qq.com")||e.includes("youku.com")||e.includes("bilibili.com")||e.includes("youtube.com"))}),i=l(()=>!1!==t.section.config?.controls),n=l(()=>!0===t.section.config?.autoplay),c=l(()=>!0===t.section.config?.loop),d=l(()=>!0===t.section.config?.muted),u=l(()=>{if(!r.value)return"";const e=r.value.url;return e.includes("qq.com")?"腾讯视频":e.includes("youku.com")?"优酷":e.includes("iqiyi.com")?"爱奇艺":e.includes("bilibili.com")?"B站":e.includes("youtube.com")?"YouTube":"本地视频"}),v={props:t,videoDuration:s,videoData:r,isLocalVideo:o,showControls:i,autoplay:n,loop:c,muted:d,videoSource:u,getEmbedUrl:e=>{if(e.includes("bilibili.com")){const a=e.match(/BV[a-zA-Z0-9]+/);if(a)return`//player.bilibili.com/player.html?bvid=${a[0]}&page=1`}if(e.includes("youtube.com")){const a=e.match(/v=([^&]+)/);if(a)return`//www.youtube.com/embed/${a[1]}`}return e},handleVideoLoaded:e=>{s.value=e.target.duration},formatDuration:e=>{if(!e)return"00:00";const a=Math.floor(e/3600),t=Math.floor(e%3600/60),s=Math.floor(e%60);return a>0?`${a}:${t.toString().padStart(2,"0")}:${s.toString().padStart(2,"0")}`:`${t}:${s.toString().padStart(2,"0")}`},ref:p,computed:l,get VideoPlay(){return E},get Clock(){return A}};return Object.defineProperty(v,"__isScriptSetup",{enumerable:!1,value:!0}),v}},Te={class:"video-preview"},Ve={class:"video-container"},ze={key:0,class:"video-content"},qe={class:"video-player"},Re=["src","poster","controls","autoplay","loop","muted"],He={key:1,class:"video-embed"},Ge=["src"],Ze={class:"video-info"},We={key:0,class:"video-title-text"},Ye={class:"video-meta"},Ne={key:0,class:"meta-item"},Qe={class:"meta-item"},Je={key:1,class:"video-placeholder"};const Ke=S(Ue,[["render",function(l,i,n,c,u,p){const g=v;return a(),e("div",Te,[t("div",Ve,[i[2]||(i[2]=t("div",{class:"video-header"},[t("h3",{class:"video-title"},"介绍视频")],-1)),c.videoData?(a(),e("div",ze,[t("div",qe,[c.isLocalVideo?(a(),e("video",{key:0,src:c.videoData.url,poster:c.videoData.poster,controls:c.showControls,autoplay:c.autoplay,loop:c.loop,muted:c.muted,class:"video-element",onLoadedmetadata:c.handleVideoLoaded}," 您的浏览器不支持视频播放 ",40,Re)):(a(),e("div",He,[t("iframe",{src:c.getEmbedUrl(c.videoData.url),frameborder:"0",allowfullscreen:"",class:"embed-iframe"},null,8,Ge)]))]),t("div",Ze,[c.videoData.title?(a(),e("div",We,m(c.videoData.title),1)):s("",!0),t("div",Ye,[c.videoDuration?(a(),e("span",Ne,[r(g,null,{default:o(()=>[r(c.Clock)]),_:1}),d(" "+m(c.formatDuration(c.videoDuration)),1)])):s("",!0),t("span",Qe,[r(g,null,{default:o(()=>[r(c.VideoPlay)]),_:1}),d(" "+m(c.videoSource),1)])])])])):(a(),e("div",Je,[r(g,{class:"placeholder-icon"},{default:o(()=>[r(c.VideoPlay)]),_:1}),i[0]||(i[0]=t("div",{class:"placeholder-text"},"暂无介绍视频",-1)),i[1]||(i[1]=t("div",{class:"placeholder-hint"},"请上传或添加视频链接",-1))]))])])}],["__scopeId","data-v-637c6453"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/preview/VideoPreview.vue"]]),Xe={__name:"MembersPreview",props:{section:{type:Object,default:()=>({})},groupData:{type:Object,default:()=>({})},preview:{type:Boolean,default:!1}},setup(e,{expose:a}){a();const t=e,s=l(()=>t.groupData.avatar_library||"default"),r=l(()=>t.section.config?.maxCount||12),o=l(()=>!1!==t.section.config?.showCount),i=l(()=>{const e=[],a=["张三","李四","王五","赵六","钱七","孙八","周九","吴十","郑一","王二","李三","张四","刘五","陈六","杨七","黄八","赵九","周十","徐一","朱二","秦三","尤四","许五","何六","吕七","施八","张九","孔十","曹一","严二","华三","金四","魏五","陶六","姜七","戚八","谢九","邹十","喻一","柏二","水三","窦四","章五","云六","苏七","潘八","葛九","奚十"],t=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9","#F8C471","#82E0AA","#F1948A","#85C1E9","#D7BDE2","#A3E4D7","#F9E79F","#D5A6BD","#AED6F1","#A9DFBF"];for(let s=0;s<50;s++){const r=a[s]||`用户${s+1}`,o=t[s%t.length].replace("#",""),l=`https://ui-avatars.com/api/?name=${encodeURIComponent(r)}&size=64&background=${o}&color=fff&font-size=0.33`;e.push(l)}return e}),n=l(()=>i.value.slice(0,r.value)),c=l(()=>i.value.length>r.value),d=l(()=>Math.max(0,i.value.length-r.value)),u=l(()=>t.groupData.member_count||i.value.length),p=l(()=>Math.floor(.3*u.value)),v=l(()=>Math.floor(.15*u.value)),m=l(()=>Math.floor(10*Math.random())+1),g=l(()=>{const e=["刚刚","5分钟前","10分钟前","半小时前","1小时前"],a=["FF6B6B","4ECDC4","45B7D1","96CEB4","FFEAA7"];return["小明","小红","小李","小王","小张"].slice(0,3).map((t,s)=>({name:t,avatar:`https://ui-avatars.com/api/?name=${encodeURIComponent(t)}&size=32&background=${a[s]}&color=fff&font-size=0.33`,joinTime:e[s]}))}),y={props:t,avatarLibrary:s,maxCount:r,showCount:o,allAvatars:i,displayAvatars:n,hasMoreMembers:c,remainingCount:d,totalMembers:u,onlineCount:p,activeToday:v,newMembers:m,recentMembers:g,handleImageError:(e,a)=>{const t=["FF6B6B","4ECDC4","45B7D1","96CEB4","FFEAA7"],s=t[a%t.length];e.target.src=`https://ui-avatars.com/api/?name=用户${a+1}&size=64&background=${s}&color=fff&font-size=0.33`},handleRecentImageError:(e,a)=>{const t=["FF6B6B","4ECDC4","45B7D1","96CEB4","FFEAA7"],s=t[a%t.length],r=["小明","小红","小李","小王","小张"][a]||`用户${a+1}`;e.target.src=`https://ui-avatars.com/api/?name=${encodeURIComponent(r)}&size=32&background=${s}&color=fff&font-size=0.33`},handleImageLoad:e=>{e.target.style.opacity="1"},getAvatarStyle:e=>({animationDelay:.1*e+"s"}),computed:l,get User(){return w}};return Object.defineProperty(y,"__isScriptSetup",{enumerable:!1,value:!0}),y}},ea={class:"members-preview"},aa={class:"members-container"},ta={class:"members-header"},sa={key:0,class:"member-count"},ra={key:0,class:"members-content"},oa={class:"avatar-grid"},la=["src","alt","onError"],ia={key:0,class:"more-members"},na={class:"more-overlay"},ca={class:"more-text"},da={class:"member-stats"},ua={class:"stat-item"},pa={class:"stat-value"},va={class:"stat-item"},ma={class:"stat-value"},ga={class:"stat-item"},ya={class:"stat-value"},fa={class:"recent-joins"},ba={class:"recent-list"},ha=["src","alt","onError"],wa={class:"recent-info"},_a={class:"recent-name"},Da={class:"recent-time"},Ca={key:1,class:"members-placeholder"};const ka=S(Xe,[["render",function(l,c,d,u,p,y){const f=v;return a(),e("div",ea,[t("div",aa,[t("div",ta,[c[2]||(c[2]=t("h3",{class:"members-title"},"群成员",-1)),u.showCount?(a(),e("div",sa,m(u.totalMembers)+"人 ",1)):s("",!0)]),u.displayAvatars.length>0?(a(),e("div",ra,[t("div",oa,[(a(!0),e(i,null,n(u.displayAvatars,(s,r)=>(a(),e("div",{key:r,class:"avatar-item",style:g(u.getAvatarStyle(r))},[t("img",{src:s,alt:`成员${r+1}`,onError:e=>u.handleImageError(e,r),onLoad:c[0]||(c[0]=e=>u.handleImageLoad(e))},null,40,la)],4))),128)),u.hasMoreMembers?(a(),e("div",ia,[t("div",na,[t("span",ca,"+"+m(u.remainingCount),1)])])):s("",!0)]),t("div",da,[t("div",ua,[c[3]||(c[3]=t("div",{class:"stat-label"},"在线成员",-1)),t("div",pa,m(u.onlineCount),1)]),t("div",va,[c[4]||(c[4]=t("div",{class:"stat-label"},"今日活跃",-1)),t("div",ma,m(u.activeToday),1)]),t("div",ga,[c[5]||(c[5]=t("div",{class:"stat-label"},"新增成员",-1)),t("div",ya,m(u.newMembers),1)])]),t("div",fa,[c[6]||(c[6]=t("div",{class:"recent-title"},"最新加入",-1)),t("div",ba,[(a(!0),e(i,null,n(u.recentMembers,(s,r)=>(a(),e("div",{key:r,class:"recent-item"},[t("img",{src:s.avatar,alt:s.name,class:"recent-avatar",onError:e=>u.handleRecentImageError(e,r),onLoad:c[1]||(c[1]=e=>u.handleImageLoad(e))},null,40,ha),t("div",wa,[t("div",_a,m(s.name),1),t("div",Da,m(s.joinTime),1)])]))),128))])])])):(a(),e("div",Ca,[r(f,{class:"placeholder-icon"},{default:o(()=>[r(u.User)]),_:1}),c[7]||(c[7]=t("div",{class:"placeholder-text"},"暂无成员头像",-1)),c[8]||(c[8]=t("div",{class:"placeholder-hint"},"请设置虚拟成员头像库",-1))]))])])}],["__scopeId","data-v-ef64ae68"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/preview/MembersPreview.vue"]]),Ia={class:"landing-page-preview"},xa={key:0,class:"empty-preview"};const Pa=S({__name:"LandingPagePreview",props:{groupData:{type:Object,default:()=>({})},layoutConfig:{type:Object,default:()=>({sections:[]})},viewMode:{type:String,default:"mobile"}},setup(e,{expose:a}){a();const t=e,s=l(()=>t.layoutConfig.sections?t.layoutConfig.sections.filter(e=>e.visible).sort((e,a)=>(e.order||0)-(a.order||0)):[]),r={props:t,visibleSections:s,getSectionComponent:e=>({banner:q,info:de,content:be,gallery:Le,video:Ke,members:ka}[e]||de),getSectionStyle:e=>{const a=e.style||{};return{backgroundColor:a.backgroundColor||"#ffffff",padding:`${a.padding||20}px`,margin:`${a.margin||0}px 0`,...a}},computed:l,get Document(){return C},BannerPreview:q,InfoPreview:de,ContentPreview:be,GalleryPreview:Le,VideoPreview:Ke,MembersPreview:ka};return Object.defineProperty(r,"__isScriptSetup",{enumerable:!1,value:!0}),r}},[["render",function(l,d,p,m,y,f){const b=v;return a(),e("div",Ia,[t("div",{class:k(["preview-container",[`view-${p.viewMode}`,`layout-${p.groupData.layout_style||"card"}`]])},[(a(!0),e(i,null,n(m.visibleSections,t=>(a(),e("div",{key:t.id,class:k(["preview-section",[`section-${t.type}`,`layout-${p.groupData.layout_style||"card"}`]]),style:g(m.getSectionStyle(t))},[(a(),c(u(m.getSectionComponent(t.type)),{section:t,"group-data":p.groupData,"layout-style":p.groupData.layout_style||"card",preview:!0},null,8,["section","group-data","layout-style"]))],6))),128)),0===m.visibleSections.length?(a(),e("div",xa,[r(b,{class:"empty-icon"},{default:o(()=>[r(m.Document)]),_:1}),d[0]||(d[0]=t("div",{class:"empty-text"},"暂无内容",-1)),d[1]||(d[1]=t("div",{class:"empty-hint"},"请在左侧添加内容模块",-1))])):s("",!0)],2)])}],["__scopeId","data-v-6f2d4848"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/LandingPagePreview.vue"]]);export{Pa as default};
