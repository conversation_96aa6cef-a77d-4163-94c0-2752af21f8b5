#!/usr/bin/env node

/**
 * 测试修复效果的脚本
 * 检查项目是否正确处理了ECharts和路由导航的问题
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🧪 开始测试修复效果...\n')

const checks = []

// 检查1: ECharts管理器是否存在
const echartsManagerPath = path.join(__dirname, 'src/utils/echartsManager.js')
if (fs.existsSync(echartsManagerPath)) {
  checks.push({ name: 'ECharts管理器', status: '✅ 已创建', details: '防重复注册管理器已就位' })
} else {
  checks.push({ name: 'ECharts管理器', status: '❌ 未找到', details: '管理器文件缺失' })
}

// 检查2: 全局ECharts配置是否使用管理器
const echartsConfigPath = path.join(__dirname, 'src/utils/echarts.js')
if (fs.existsSync(echartsConfigPath)) {
  const content = fs.readFileSync(echartsConfigPath, 'utf8')
  if (content.includes('echartsManager') && content.includes('initializeECharts')) {
    checks.push({ name: '全局ECharts配置', status: '✅ 已更新', details: '使用管理器初始化' })
  } else {
    checks.push({ name: '全局ECharts配置', status: '⚠️ 可能有问题', details: '未检测到管理器引用' })
  }
} else {
  checks.push({ name: '全局ECharts配置', status: '❌ 未找到', details: '配置文件缺失' })
}

// 检查3: Analytics.vue是否使用新的ECharts API
const analyticsPath = path.join(__dirname, 'src/views/dashboard/Analytics.vue')
if (fs.existsSync(analyticsPath)) {
  const content = fs.readFileSync(analyticsPath, 'utf8')
  if (content.includes('createEChartsInstance') && content.includes('disposeEChartsInstance') && content.includes('import * as echarts')) {
    checks.push({ name: 'Analytics组件', status: '✅ 已更新', details: '使用安全的ECharts实例管理，包含必需的echarts导入' })
  } else {
    checks.push({ name: 'Analytics组件', status: '⚠️ 可能有问题', details: '新API使用不完整或缺少echarts导入' })
  }
} else {
  checks.push({ name: 'Analytics组件', status: '❌ 未找到', details: '组件文件缺失' })
}

// 检查4: DashboardCharts是否移除重复注册
const dashboardChartsPath = path.join(__dirname, 'src/components/dashboard/DashboardCharts.vue')
if (fs.existsSync(dashboardChartsPath)) {
  const content = fs.readFileSync(dashboardChartsPath, 'utf8')
  if (!content.includes('use([') && !content.includes('CanvasRenderer')) {
    checks.push({ name: 'DashboardCharts组件', status: '✅ 已修复', details: '移除重复ECharts注册' })
  } else {
    checks.push({ name: 'DashboardCharts组件', status: '⚠️ 可能有问题', details: '仍包含重复注册代码' })
  }
} else {
  checks.push({ name: 'DashboardCharts组件', status: '❌ 未找到', details: '组件文件缺失' })
}

// 检查5: 路由守卫是否有错误处理
const routerPath = path.join(__dirname, 'src/router/index.js')
if (fs.existsSync(routerPath)) {
  const content = fs.readFileSync(routerPath, 'utf8')
  if (content.includes('router.onError') && content.includes('try {') && content.includes('catch')) {
    checks.push({ name: '路由守卫', status: '✅ 已增强', details: '添加错误处理和恢复机制' })
  } else {
    checks.push({ name: '路由守卫', status: '⚠️ 可能有问题', details: '错误处理不完整' })
  }
} else {
  checks.push({ name: '路由守卫', status: '❌ 未找到', details: '路由文件缺失' })
}

// 检查6: init.js是否过滤ECharts错误
const initPath = path.join(__dirname, 'src/utils/init.js')
if (fs.existsSync(initPath)) {
  const content = fs.readFileSync(initPath, 'utf8')
  if (content.includes('echartsErrors') && content.includes('axisPointer')) {
    checks.push({ name: '错误过滤机制', status: '✅ 已更新', details: 'ECharts错误已添加到忽略列表' })
  } else {
    checks.push({ name: '错误过滤机制', status: '⚠️ 可能有问题', details: 'ECharts错误过滤未完全配置' })
  }
} else {
  checks.push({ name: '错误过滤机制', status: '❌ 未找到', details: '初始化文件缺失' })
}

// 输出检查结果
console.log('📋 修复效果检查报告\n')
console.log(''.padEnd(60, '='))

let passCount = 0
let warnCount = 0
let failCount = 0

checks.forEach((check, index) => {
  console.log(`${index + 1}. ${check.name.padEnd(20)} ${check.status}`)
  console.log(`   ${check.details}\n`)
  
  if (check.status.includes('✅')) passCount++
  else if (check.status.includes('⚠️')) warnCount++
  else failCount++
})

console.log(''.padEnd(60, '='))
console.log('📊 总结:')
console.log(`   ✅ 通过: ${passCount} 项`)
console.log(`   ⚠️ 警告: ${warnCount} 项`)
console.log(`   ❌ 失败: ${failCount} 项`)

if (failCount === 0 && warnCount === 0) {
  console.log('\n🎉 所有检查通过！修复应该已经生效。')
} else if (failCount === 0) {
  console.log('\n⚠️ 基本修复完成，但有一些小问题需要注意。')
} else {
  console.log('\n❌ 发现关键问题，需要进一步修复。')
}

console.log('\n🚀 建议下一步操作:')
console.log('1. 启动开发服务器: npm run dev')
console.log('2. 访问管理后台: http://localhost:5173/admin/analytics')
console.log('3. 检查浏览器控制台是否还有相关错误')
console.log('4. 测试侧边栏菜单的数据分析链接是否正常工作')

process.exit(failCount > 0 ? 1 : 0)