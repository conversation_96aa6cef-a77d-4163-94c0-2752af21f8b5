/* empty css             *//* empty css                  *//* empty css                         */import{l as e,q as r,C as o,G as t,A as a,F as i,Y as s,ag as l,ah as n,c,o as d,m as u,z as p,D as y,E as f}from"./vue-vendor-BcnDv-68.js";import{Z as m,ae as b,bl as g,bm as k,bn as h,aO as C,b3 as v,ah as _,aX as j,aY as w,a1 as R}from"./element-plus-C2UshkXo.js";import{_ as A}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const D={class:"error-page"},q={class:"error-container"},E={class:"error-content"},x={class:"error-icon"},F={class:"error-title"},O={class:"error-subtitle"},P={class:"error-description"},$={key:0,class:"error-details"},z={class:"error-actions"};const H=A({__name:"ErrorPage",setup(e,{expose:r}){r();const o=l(),t=n(),a={403:{code:"403",title:"访问被拒绝",description:"抱歉，您没有权限访问此页面。请联系管理员获取相应权限。",icon:v,iconColor:"#f56565",actions:[{key:"back",label:"返回上页",type:"primary",icon:g},{key:"home",label:"回到首页",type:"default",icon:k}]},404:{code:"404",title:"页面未找到",description:"抱歉，您访问的页面不存在或已被删除。",icon:C,iconColor:"#909399",actions:[{key:"home",label:"返回首页",type:"primary",icon:k},{key:"back",label:"返回上页",type:"default",icon:g}]},500:{code:"500",title:"服务器错误",description:"抱歉，服务器出现了一些问题。请稍后再试或联系技术支持。",icon:h,iconColor:"#f56565",actions:[{key:"retry",label:"重新加载",type:"primary",icon:b},{key:"back",label:"返回上页",type:"default",icon:g},{key:"home",label:"回到首页",type:"default",icon:k}]},load:{code:"ERROR",title:"组件加载失败",description:"抱歉，请求的页面组件无法正常加载。这可能是由于网络问题或组件文件缺失导致的。",icon:h,iconColor:"#f56565",actions:[{key:"retry",label:"重新加载",type:"primary",icon:b},{key:"back",label:"返回上页",type:"default",icon:g},{key:"home",label:"回到首页",type:"default",icon:k}]}},i=c(()=>t.params.type||t.query.type||"404"),s=c(()=>t.query.details||null),u=c(()=>a[i.value]||a[404]),p=()=>{window.history.length>1?o.go(-1):y()},y=()=>{o.push("/")},f=()=>{window.location.reload()};d(()=>{document.title=`${u.value.code} - ${u.value.title}`});const m={router:o,route:t,errorConfigs:a,errorType:i,errorDetails:s,errorConfig:u,handleAction:e=>{switch(e){case"back":p();break;case"home":y();break;case"retry":f()}},goBack:p,goHome:y,handleRetry:f,computed:c,onMounted:d,get useRouter(){return l},get useRoute(){return n},get Lock(){return v},get Warning(){return h},get QuestionFilled(){return C},get ArrowLeft(){return g},get HomeFilled(){return k},get Refresh(){return b}};return Object.defineProperty(m,"__isScriptSetup",{enumerable:!1,value:!0}),m}},[["render",function(l,n,c,d,b,g){const k=_,h=w,C=j,v=R;return u(),e("div",D,[r("div",q,[r("div",E,[r("div",x,[t(k,{size:120,color:d.errorConfig.iconColor},{default:a(()=>[(u(),p(y(d.errorConfig.icon)))]),_:1},8,["color"])]),r("h1",F,m(d.errorConfig.code),1),r("h2",O,m(d.errorConfig.title),1),r("p",P,m(d.errorConfig.description),1),d.errorDetails?(u(),e("div",$,[t(C,null,{default:a(()=>[t(h,{title:"错误详情",name:"details"},{default:a(()=>[r("pre",null,m(d.errorDetails),1)]),_:1})]),_:1})])):o("",!0),r("div",z,[(u(!0),e(i,null,s(d.errorConfig.actions,e=>(u(),p(v,{key:e.key,type:e.type,onClick:r=>d.handleAction(e.key)},{default:a(()=>[t(k,null,{default:a(()=>[(u(),p(y(e.icon)))]),_:2},1024),f(" "+m(e.label),1)]),_:2},1032,["type","onClick"]))),128))])])])])}],["__scopeId","data-v-233f9da5"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/ErrorPage.vue"]]);export{H as default};
