/* empty css             *//* empty css                   *//* empty css                    *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                  *//* empty css               *//* empty css                    *//* empty css                 *//* empty css                  *//* empty css                *//* empty css                      *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 *//* empty css                *//* empty css                *//* empty css                  *//* empty css                        *//* empty css                         *//* empty css                  */import{l as e,q as a,G as t,A as l,B as n,F as s,Y as o,r as d,M as i,c as r,o as c,m as u,E as m,z as g,D as _,C as p}from"./vue-vendor-BcnDv-68.js";import{a7 as v,ah as h,a1 as f,V as b,W as w,X as y,at as A,aE as C,bW as k,aw as D,bz as j,bN as z,bM as x,bX as V,bY as T,ab as S,ac as F,aO as P,ax as L,aT as U,b1 as M,aN as E,aD as H,af as R,ae as I,ag as N,u as O,Z as q,s as $,bZ as B,aG as G,ak as Q,aH as Y,aI as W,aJ as X,ai as Z,aj as J,bV as K,an as ee,aK as ae,a2 as te,a3 as le,a4 as ne,a5 as se,a6 as oe,b_ as de,aU as ie,aV as re}from"./element-plus-C2UshkXo.js";import{_ as ce}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const ue={class:"modern-agent-hierarchy"},me={class:"page-header"},ge={class:"header-content"},_e={class:"header-left"},pe={class:"header-icon"},ve={class:"header-actions"},he={class:"stats-section"},fe={class:"stats-container"},be={class:"stat-content"},we={class:"stat-value"},ye={class:"stat-label"},Ae={class:"card-header"},Ce={class:"header-left"},ke={class:"header-right"},De={key:0,class:"tree-view"},je={class:"tree-node"},ze={class:"node-info"},xe={class:"node-details"},Ve={class:"node-name"},Te={class:"node-meta"},Se={class:"node-code"},Fe={class:"node-stats"},Pe={class:"stat-item"},Le={class:"stat-value"},Ue={class:"stat-item"},Me={class:"stat-value"},Ee={class:"node-actions"},He={key:1,class:"table-view"},Re={class:"agent-info"},Ie={class:"agent-details"},Ne={class:"agent-name"},Oe={class:"agent-code"},qe={class:"level-path"},$e={class:"commission-amount"},Be={class:"pagination"},Ge={class:"dialog-footer"},Qe={key:0,class:"agent-detail"},Ye={class:"detail-header"},We={class:"agent-info"},Xe={class:"agent-code"},Ze={class:"info-grid"},Je={class:"info-item"},Ke={class:"info-item"},ea={class:"info-item"},aa={class:"info-item"},ta={class:"info-item"},la={class:"info-item"},na={class:"performance-stats"},sa={class:"stat-row"},oa={class:"stat-item"},da={class:"stat-value"},ia={class:"stat-item"},ra={class:"stat-value"},ca={class:"stat-item"},ua={class:"stat-value"},ma={class:"stat-item"},ga={class:"stat-value"};const _a=ce({__name:"AgentHierarchy",setup(e,{expose:a}){a();const t=d(!1),l=d(!0),n=d("tree"),s=d(!1),o=d(!1),u=d(!1),m=d(!1),g=d(!1),_=d(!1),p=d(!1),v=d(!1),h=d(null),f=d("basic"),b=d(1),w=d(20),O=d(["add-agent"]),q=d([]),$=i({totalAgents:0,directChildren:0,maxLevel:0,totalCommission:0}),B=d([{key:"totalAgents",label:"团队总人数",value:"0",icon:"Avatar",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"ArrowUp",change:"+8人"},{key:"directChildren",label:"直属下级",value:"0",icon:"Connection",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"ArrowUp",change:"+3人"},{key:"maxLevel",label:"最大层级",value:"0",icon:"TrendCharts",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"ArrowUp",change:"5级"},{key:"totalCommission",label:"团队总佣金",value:"¥0",icon:"Money",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"ArrowUp",change:"+12.8%"}]),G=i({agent_name:"",phone:"",email:"",agent_level:"",parent_id:null,remark:""}),Q=d({agent_name:"",phone:"",email:"",agent_level:"",remark:""}),Y=d({agent_id:null,new_parent_id:null,reason:""}),W=d(),X=r(()=>{const e=(a,t=1,l=[])=>{let n=[];return a.forEach(a=>{const s=[...l,a.agent_name],o={...a,level:t,level_path:s};n.push(o),a.children&&a.children.length>0&&(n=n.concat(e(a.children,t+1,s)))}),n};return e(q.value)}),Z=r(()=>q.value),J=async()=>{try{l.value=!0,await new Promise(e=>setTimeout(e,300)),B.value[0].value=$.totalAgents.toString(),B.value[1].value=$.directChildren.toString(),B.value[2].value=$.maxLevel.toString(),B.value[3].value="¥"+($.totalCommission||0).toLocaleString()}catch(e){y.error("加载统计数据失败")}finally{l.value=!1}},K=()=>{Object.assign(G,{agent_name:"",phone:"",email:"",agent_level:"",parent_id:null,remark:""}),W.value?.clearValidate()},ee=e=>{h.value=e,Q.value={agent_name:e.agent_name,phone:e.phone,email:e.email,agent_level:e.agent_level,remark:e.remark||""},u.value=!0},ae=e=>{h.value=e,m.value=!0,loadAgentCommissionData(e.id)},te=e=>{h.value=e,g.value=!0,loadAgentPerformanceData(e.id)},le=e=>{h.value=e,Y.value={agent_id:e.id,new_parent_id:null,reason:""},_.value=!0},ne=async(e,a)=>{try{const t="active"===a?"启用":"停用";await N.confirm(`确定要${t}代理商 "${e.agent_name}" 吗？`,`确认${t}`,{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await new Promise(e=>setTimeout(e,500)),y.success(`${t}成功`),await se()}catch{y.info("已取消操作")}},se=async()=>{t.value=!0;try{await new Promise(e=>setTimeout(e,500)),q.value=[{id:1,agent_name:"张三",agent_code:"A001",agent_level:"gold",phone:"13800138001",email:"<EMAIL>",avatar:"",status:"active",children_count:2,total_commission:15e3,month_commission:3e3,total_orders:150,month_orders:25,team_count:8,created_at:"2024-01-01",children:[{id:2,agent_name:"李四",agent_code:"A002",agent_level:"senior",phone:"13800138002",email:"<EMAIL>",avatar:"",status:"active",children_count:1,total_commission:8e3,month_commission:1500,total_orders:80,month_orders:12,team_count:3,created_at:"2024-01-15",children:[{id:3,agent_name:"王五",agent_code:"A003",agent_level:"intermediate",phone:"13800138003",email:"<EMAIL>",avatar:"",status:"active",children_count:0,total_commission:3e3,month_commission:800,total_orders:30,month_orders:8,team_count:1,created_at:"2024-02-01",children:[]}]},{id:4,agent_name:"赵六",agent_code:"A004",agent_level:"junior",phone:"13800138004",email:"<EMAIL>",avatar:"",status:"inactive",children_count:0,total_commission:1200,month_commission:200,total_orders:15,month_orders:3,team_count:1,created_at:"2024-02-15",children:[]}]}];const e=(e=>{let a=0,t=0,l=0,n=0;const s=(e,o=1)=>{e.forEach(e=>{a++,n+=e.total_commission||0,l=Math.max(l,o),1===o&&(t+=e.children_count||0),e.children&&e.children.length>0&&s(e.children,o+1)})};return s(e),{totalAgents:a,directChildren:t,maxLevel:l,totalCommission:n}})(q.value);Object.assign($,e)}catch(e){y.error("加载数据失败")}finally{t.value=!1}};c(()=>{se(),J()});const oe={loading:t,statsLoading:l,viewMode:n,showAddDialog:s,showDetailDialog:o,showEditDialog:u,showCommissionDialog:m,showPerformanceDialog:g,showTransferDialog:_,showHelpDialog:p,addLoading:v,selectedAgent:h,activeTab:f,currentPage:b,pageSize:w,activeGuides:O,hierarchyData:q,hierarchyStats:$,hierarchyStatCards:B,treeProps:{children:"children",label:"agent_name"},cascaderProps:{value:"id",label:"agent_name",children:"children"},addForm:G,editForm:Q,transferForm:Y,addRules:{agent_name:[{required:!0,message:"请输入代理商姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],agent_level:[{required:!0,message:"请选择代理等级",trigger:"change"}]},addFormRef:W,flatHierarchyData:X,agentOptions:Z,refreshHierarchy:async()=>{await Promise.all([se(),J()]),y.success("数据刷新成功")},handleExport:async()=>{try{y.success("团队数据导出成功")}catch(e){y.error("导出失败")}},handleSizeChange:e=>{w.value=e,se()},handleCurrentChange:e=>{b.value=e,se()},loadStats:J,showAddAgentDialog:()=>{K(),s.value=!0},resetAddForm:K,handleAddAgent:async()=>{try{await W.value.validate(),v.value=!0,await new Promise(e=>setTimeout(e,1e3)),y.success("添加代理商成功"),s.value=!1,await se()}catch(e){!1!==e&&y.error("添加代理商失败")}finally{v.value=!1}},viewAgentDetail:e=>{h.value=e,f.value="basic",o.value=!0},addSubAgent:e=>{K(),G.parent_id=[e.id],s.value=!0},showEditAgentDialog:ee,showAgentCommissionDialog:ae,showAgentPerformanceDialog:te,showTransferAgentDialog:le,handleNodeCommand:async({action:e,data:a})=>{switch(e){case"edit":ee(a);break;case"commission":ae(a);break;case"performance":te(a);break;case"transfer":le(a);break;case"disable":await ne(a,"inactive");break;case"enable":await ne(a,"active")}},handleToggleStatus:ne,getLevelTagType:e=>({junior:"info",intermediate:"warning",senior:"success",gold:"danger"}[e]||"info"),getLevelText:e=>({junior:"初级代理",intermediate:"中级代理",senior:"高级代理",gold:"金牌代理"}[e]||"未知等级"),formatDate:e=>e?new Date(e).toLocaleDateString("zh-CN"):"-",loadHierarchyData:se,ref:d,reactive:i,computed:r,onMounted:c,get ElMessage(){return y},get ElMessageBox(){return N},get Refresh(){return I},get Plus(){return R},get Avatar(){return H},get Connection(){return E},get TrendCharts(){return M},get Money(){return U},get Download(){return L},get QuestionFilled(){return P},get View(){return F},get Edit(){return S},get More(){return T},get Switch(){return V},get CircleClose(){return x},get CircleCheck(){return z},get Share(){return j},get List(){return D},get ArrowRight(){return k},get ArrowDown(){return C},get ArrowUp(){return A}};return Object.defineProperty(oe,"__isScriptSetup",{enumerable:!1,value:!0}),oe}},[["render",function(d,i,r,c,y,A){const C=h,k=f,D=Q,j=ae,z=G,x=X,V=W,T=Y,S=B,F=J,P=K,L=Z,U=ee,M=b,E=ne,H=le,R=oe,I=se,N=de,ce=te,_a=w,pa=re,va=ie,ha=v;return u(),e("div",ue,[a("div",me,[a("div",ge,[a("div",_e,[a("div",pe,[t(C,{size:"24"},{default:l(()=>[t(c.Connection)]),_:1})]),i[15]||(i[15]=a("div",{class:"header-text"},[a("h1",null,"团队层级管理"),a("p",null,"管理代理商团队结构和层级关系，优化团队组织架构")],-1))]),a("div",ve,[t(k,{onClick:c.handleExport,class:"action-btn secondary"},{default:l(()=>[t(C,null,{default:l(()=>[t(c.Download)]),_:1}),i[16]||(i[16]=m(" 导出数据 ",-1))]),_:1,__:[16]}),t(k,{onClick:i[0]||(i[0]=e=>c.showHelpDialog=!0),class:"action-btn secondary"},{default:l(()=>[t(C,null,{default:l(()=>[t(c.QuestionFilled)]),_:1}),i[17]||(i[17]=m(" 功能说明 ",-1))]),_:1,__:[17]}),t(k,{onClick:c.showAddAgentDialog,class:"action-btn secondary"},{default:l(()=>[t(C,null,{default:l(()=>[t(c.Plus)]),_:1}),i[18]||(i[18]=m(" 添加下级代理 ",-1))]),_:1,__:[18]}),t(k,{type:"primary",onClick:c.refreshHierarchy,class:"action-btn primary",loading:c.loading},{default:l(()=>[t(C,null,{default:l(()=>[t(c.Refresh)]),_:1}),i[19]||(i[19]=m(" 刷新数据 ",-1))]),_:1,__:[19]},8,["loading"])])])]),a("div",he,[n((u(),e("div",fe,[(u(!0),e(s,null,o(c.hierarchyStatCards,n=>(u(),e("div",{class:"stat-card",key:n.key},[a("div",{class:"stat-icon",style:O({background:n.color})},[t(C,{size:"20"},{default:l(()=>[(u(),g(_(n.icon)))]),_:2},1024)],4),a("div",be,[a("div",we,q(n.value),1),a("div",ye,q(n.label),1)]),a("div",{class:$(["stat-trend",n.trend])},[t(C,{size:"14"},{default:l(()=>[(u(),g(_(n.trendIcon)))]),_:2},1024),a("span",null,q(n.change),1)],2)]))),128))])),[[ha,c.statsLoading]])]),t(M,{class:"table-card"},{header:l(()=>[a("div",Ae,[a("div",Ce,[i[20]||(i[20]=a("h3",null,"团队层级结构",-1)),t(D,{size:"small",type:"info"},{default:l(()=>[m("共 "+q(c.hierarchyStats.totalAgents||0)+" 人",1)]),_:1})]),a("div",ke,[t(j,null,{default:l(()=>[t(k,{size:"small",type:"tree"===c.viewMode?"primary":"",onClick:i[1]||(i[1]=e=>c.viewMode="tree")},{default:l(()=>[t(C,null,{default:l(()=>[t(c.Share)]),_:1})]),_:1},8,["type"]),t(k,{size:"small",type:"table"===c.viewMode?"primary":"",onClick:i[2]||(i[2]=e=>c.viewMode="table")},{default:l(()=>[t(C,null,{default:l(()=>[t(c.List)]),_:1})]),_:1},8,["type"])]),_:1})])])]),default:l(()=>["tree"===c.viewMode?(u(),e("div",De,[n((u(),g(S,{ref:"hierarchyTree",data:c.hierarchyData,props:c.treeProps,"expand-on-click-node":!1,"default-expand-all":!1,"node-key":"id",class:"modern-tree"},{default:l(({node:e,data:n})=>[a("div",je,[a("div",ze,[t(z,{size:32,src:n.avatar,class:"node-avatar"},{default:l(()=>[m(q(n.agent_name&&n.agent_name.charAt(0)),1)]),_:2},1032,["src"]),a("div",xe,[a("div",Ve,q(n.agent_name),1),a("div",Te,[t(D,{type:c.getLevelTagType(n.agent_level),size:"small"},{default:l(()=>[m(q(c.getLevelText(n.agent_level)),1)]),_:2},1032,["type"]),a("span",Se,q(n.agent_code),1)])])]),a("div",Fe,[a("div",Pe,[i[21]||(i[21]=a("span",{class:"stat-label"},"下级:",-1)),a("span",Le,q(n.children_count||0),1)]),a("div",Ue,[i[22]||(i[22]=a("span",{class:"stat-label"},"佣金:",-1)),a("span",Me,"¥"+q(n.total_commission||0),1)])]),a("div",Ee,[t(k,{size:"small",type:"primary",link:"",onClick:e=>c.viewAgentDetail(n)},{default:l(()=>[t(C,null,{default:l(()=>[t(c.View)]),_:1}),i[23]||(i[23]=m(" 详情 ",-1))]),_:2,__:[23]},1032,["onClick"]),t(k,{size:"small",type:"success",link:"",onClick:e=>c.addSubAgent(n)},{default:l(()=>[t(C,null,{default:l(()=>[t(c.Plus)]),_:1}),i[24]||(i[24]=m(" 添加下级 ",-1))]),_:2,__:[24]},1032,["onClick"]),t(T,{onCommand:c.handleNodeCommand,trigger:"click"},{dropdown:l(()=>[t(V,null,{default:l(()=>[t(x,{command:{action:"edit",data:n}},{default:l(()=>[t(C,null,{default:l(()=>[t(c.Edit)]),_:1}),i[26]||(i[26]=m(" 编辑信息 ",-1))]),_:2,__:[26]},1032,["command"]),t(x,{command:{action:"commission",data:n}},{default:l(()=>[t(C,null,{default:l(()=>[t(c.Money)]),_:1}),i[27]||(i[27]=m(" 佣金管理 ",-1))]),_:2,__:[27]},1032,["command"]),t(x,{command:{action:"performance",data:n}},{default:l(()=>[t(C,null,{default:l(()=>[t(c.TrendCharts)]),_:1}),i[28]||(i[28]=m(" 业绩分析 ",-1))]),_:2,__:[28]},1032,["command"]),t(x,{command:{action:"transfer",data:n},divided:""},{default:l(()=>[t(C,null,{default:l(()=>[t(c.Switch)]),_:1}),i[29]||(i[29]=m(" 转移代理 ",-1))]),_:2,__:[29]},1032,["command"]),"active"===n.status?(u(),g(x,{key:0,command:{action:"disable",data:n}},{default:l(()=>[t(C,null,{default:l(()=>[t(c.CircleClose)]),_:1}),i[30]||(i[30]=m(" 停用代理 ",-1))]),_:2,__:[30]},1032,["command"])):(u(),g(x,{key:1,command:{action:"enable",data:n}},{default:l(()=>[t(C,null,{default:l(()=>[t(c.CircleCheck)]),_:1}),i[31]||(i[31]=m(" 启用代理 ",-1))]),_:2,__:[31]},1032,["command"]))]),_:2},1024)]),default:l(()=>[t(k,{size:"small",type:"info",link:""},{default:l(()=>[t(C,null,{default:l(()=>[t(c.More)]),_:1}),i[25]||(i[25]=m(" 更多 ",-1))]),_:1,__:[25]})]),_:2},1024)])])]),_:1},8,["data"])),[[ha,c.loading]])])):(u(),e("div",He,[n((u(),g(L,{data:c.flatHierarchyData,class:"modern-table"},{default:l(()=>[t(F,{prop:"agent_name",label:"代理商","min-width":"200"},{default:l(({row:e})=>[a("div",Re,[t(z,{size:32,src:e.avatar},{default:l(()=>[m(q(e.agent_name&&e.agent_name.charAt(0)),1)]),_:2},1032,["src"]),a("div",Ie,[a("div",Ne,q(e.agent_name),1),a("div",Oe,q(e.agent_code),1)])])]),_:1}),t(F,{prop:"level_path",label:"层级路径","min-width":"150"},{default:l(({row:n})=>[a("div",qe,[(u(!0),e(s,null,o(n.level_path,(a,s)=>(u(),e("span",{key:s,class:"level-item"},[m(q(a)+" ",1),s<n.level_path.length-1?(u(),g(C,{key:0},{default:l(()=>[t(c.ArrowRight)]),_:1})):p("",!0)]))),128))])]),_:1}),t(F,{prop:"agent_level",label:"代理等级",width:"120"},{default:l(({row:e})=>[t(D,{type:c.getLevelTagType(e.agent_level),size:"small"},{default:l(()=>[m(q(c.getLevelText(e.agent_level)),1)]),_:2},1032,["type"])]),_:1}),t(F,{prop:"children_count",label:"下级数量",width:"100",align:"center"},{default:l(({row:e})=>[t(P,{value:e.children_count||0,max:99,class:"children-badge"},{default:l(()=>[t(C,null,{default:l(()=>[t(c.Avatar)]),_:1})]),_:2},1032,["value"])]),_:1}),t(F,{prop:"total_commission",label:"累计佣金",width:"120",align:"right"},{default:l(({row:e})=>[a("span",$e,"¥"+q(e.total_commission||0),1)]),_:1}),t(F,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(({row:e})=>[t(D,{type:"active"===e.status?"success":"danger",size:"small"},{default:l(()=>[m(q("active"===e.status?"正常":"停用"),1)]),_:2},1032,["type"])]),_:1}),t(F,{prop:"created_at",label:"加入时间",width:"120"},{default:l(({row:e})=>[m(q(c.formatDate(e.created_at)),1)]),_:1}),t(F,{label:"操作",width:"200",fixed:"right"},{default:l(({row:e})=>[t(k,{size:"small",type:"primary",link:"",onClick:a=>c.viewAgentDetail(e)},{default:l(()=>i[32]||(i[32]=[m(" 详情 ",-1)])),_:2,__:[32]},1032,["onClick"]),t(k,{size:"small",type:"success",link:"",onClick:a=>c.addSubAgent(e)},{default:l(()=>i[33]||(i[33]=[m(" 添加下级 ",-1)])),_:2,__:[33]},1032,["onClick"]),t(T,{onCommand:c.handleNodeCommand,trigger:"click"},{dropdown:l(()=>[t(V,null,{default:l(()=>[t(x,{command:{action:"edit",data:e}},{default:l(()=>i[35]||(i[35]=[m("编辑",-1)])),_:2,__:[35]},1032,["command"]),t(x,{command:{action:"commission",data:e}},{default:l(()=>i[36]||(i[36]=[m("佣金",-1)])),_:2,__:[36]},1032,["command"]),t(x,{command:{action:"performance",data:e}},{default:l(()=>i[37]||(i[37]=[m("业绩",-1)])),_:2,__:[37]},1032,["command"])]),_:2},1024)]),default:l(()=>[t(k,{size:"small",type:"info",link:""},{default:l(()=>[i[34]||(i[34]=m(" 更多",-1)),t(C,null,{default:l(()=>[t(c.ArrowDown)]),_:1})]),_:1,__:[34]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[ha,c.loading]]),a("div",Be,[t(U,{"current-page":c.currentPage,"onUpdate:currentPage":i[3]||(i[3]=e=>c.currentPage=e),"page-size":c.pageSize,"onUpdate:pageSize":i[4]||(i[4]=e=>c.pageSize=e),total:c.hierarchyStats.totalAgents,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:c.handleSizeChange,onCurrentChange:c.handleCurrentChange},null,8,["current-page","page-size","total"])])]))]),_:1}),t(_a,{modelValue:c.showAddDialog,"onUpdate:modelValue":i[12]||(i[12]=e=>c.showAddDialog=e),title:"添加下级代理",width:"600px",class:"add-agent-dialog"},{footer:l(()=>[a("div",Ge,[t(k,{onClick:i[11]||(i[11]=e=>c.showAddDialog=!1)},{default:l(()=>i[38]||(i[38]=[m("取消",-1)])),_:1,__:[38]}),t(k,{type:"primary",onClick:c.handleAddAgent,loading:c.addLoading},{default:l(()=>i[39]||(i[39]=[m(" 确定添加 ",-1)])),_:1,__:[39]},8,["loading"])])]),default:l(()=>[t(ce,{model:c.addForm,rules:c.addRules,ref:"addFormRef","label-width":"100px"},{default:l(()=>[t(H,{label:"代理商姓名",prop:"agent_name"},{default:l(()=>[t(E,{modelValue:c.addForm.agent_name,"onUpdate:modelValue":i[5]||(i[5]=e=>c.addForm.agent_name=e),placeholder:"请输入代理商姓名"},null,8,["modelValue"])]),_:1}),t(H,{label:"手机号码",prop:"phone"},{default:l(()=>[t(E,{modelValue:c.addForm.phone,"onUpdate:modelValue":i[6]||(i[6]=e=>c.addForm.phone=e),placeholder:"请输入手机号码"},null,8,["modelValue"])]),_:1}),t(H,{label:"邮箱地址",prop:"email"},{default:l(()=>[t(E,{modelValue:c.addForm.email,"onUpdate:modelValue":i[7]||(i[7]=e=>c.addForm.email=e),placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1}),t(H,{label:"代理等级",prop:"agent_level"},{default:l(()=>[t(I,{modelValue:c.addForm.agent_level,"onUpdate:modelValue":i[8]||(i[8]=e=>c.addForm.agent_level=e),placeholder:"请选择代理等级"},{default:l(()=>[t(R,{label:"初级代理",value:"junior"}),t(R,{label:"中级代理",value:"intermediate"}),t(R,{label:"高级代理",value:"senior"}),t(R,{label:"金牌代理",value:"gold"})]),_:1},8,["modelValue"])]),_:1}),t(H,{label:"上级代理",prop:"parent_id"},{default:l(()=>[t(N,{modelValue:c.addForm.parent_id,"onUpdate:modelValue":i[9]||(i[9]=e=>c.addForm.parent_id=e),options:c.agentOptions,props:c.cascaderProps,placeholder:"请选择上级代理",clearable:""},null,8,["modelValue","options"])]),_:1}),t(H,{label:"备注信息"},{default:l(()=>[t(E,{modelValue:c.addForm.remark,"onUpdate:modelValue":i[10]||(i[10]=e=>c.addForm.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(_a,{modelValue:c.showDetailDialog,"onUpdate:modelValue":i[14]||(i[14]=e=>c.showDetailDialog=e),title:"代理商详情",width:"800px",class:"detail-dialog"},{default:l(()=>[c.selectedAgent?(u(),e("div",Qe,[a("div",Ye,[t(z,{size:80,src:c.selectedAgent.avatar},{default:l(()=>[m(q(c.selectedAgent.agent_name&&c.selectedAgent.agent_name.charAt(0)),1)]),_:1},8,["src"]),a("div",We,[a("h3",null,q(c.selectedAgent.agent_name),1),a("p",Xe,"代理编号: "+q(c.selectedAgent.agent_code),1),t(D,{type:c.getLevelTagType(c.selectedAgent.agent_level)},{default:l(()=>[m(q(c.getLevelText(c.selectedAgent.agent_level)),1)]),_:1},8,["type"])])]),t(va,{modelValue:c.activeTab,"onUpdate:modelValue":i[13]||(i[13]=e=>c.activeTab=e),class:"detail-tabs"},{default:l(()=>[t(pa,{label:"基本信息",name:"basic"},{default:l(()=>[a("div",Ze,[a("div",Je,[i[40]||(i[40]=a("label",null,"手机号码:",-1)),a("span",null,q(c.selectedAgent.phone||"-"),1)]),a("div",Ke,[i[41]||(i[41]=a("label",null,"邮箱地址:",-1)),a("span",null,q(c.selectedAgent.email||"-"),1)]),a("div",ea,[i[42]||(i[42]=a("label",null,"加入时间:",-1)),a("span",null,q(c.formatDate(c.selectedAgent.created_at)),1)]),a("div",aa,[i[43]||(i[43]=a("label",null,"状态:",-1)),t(D,{type:"active"===c.selectedAgent.status?"success":"danger",size:"small"},{default:l(()=>[m(q("active"===c.selectedAgent.status?"正常":"停用"),1)]),_:1},8,["type"])]),a("div",ta,[i[44]||(i[44]=a("label",null,"直属下级:",-1)),a("span",null,q(c.selectedAgent.children_count||0)+"人",1)]),a("div",la,[i[45]||(i[45]=a("label",null,"团队总人数:",-1)),a("span",null,q(c.selectedAgent.team_count||0)+"人",1)])])]),_:1}),t(pa,{label:"业绩统计",name:"performance"},{default:l(()=>[a("div",na,[a("div",sa,[a("div",oa,[a("div",da,"¥"+q(c.selectedAgent.total_commission||0),1),i[46]||(i[46]=a("div",{class:"stat-label"},"累计佣金",-1))]),a("div",ia,[a("div",ra,"¥"+q(c.selectedAgent.month_commission||0),1),i[47]||(i[47]=a("div",{class:"stat-label"},"本月佣金",-1))]),a("div",ca,[a("div",ua,q(c.selectedAgent.total_orders||0),1),i[48]||(i[48]=a("div",{class:"stat-label"},"累计订单",-1))]),a("div",ma,[a("div",ga,q(c.selectedAgent.month_orders||0),1),i[49]||(i[49]=a("div",{class:"stat-label"},"本月订单",-1))])])])]),_:1}),t(pa,{label:"团队结构",name:"team"},{default:l(()=>i[50]||(i[50]=[a("div",{class:"team-structure"},[a("p",null,"团队层级结构图表将在这里显示")],-1)])),_:1,__:[50]})]),_:1},8,["modelValue"])])):p("",!0)]),_:1},8,["modelValue"])])}],["__scopeId","data-v-58d66a77"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/agent/AgentHierarchy.vue"]]);export{_a as default};
