import{a}from"./index-D4AyIzGN.js";const s=()=>a.get("/admin/anti-block/stats"),i=s=>a.get("/admin/anti-block/domains",{params:s}),t=s=>a.get("/admin/anti-block/domains",{params:s}),n=s=>a.post("/admin/anti-block/domains",s),o=(s,i)=>a.put(`/admin/anti-block/domains/${s}`,i),m=s=>a.delete(`/admin/anti-block/domains/${s}`),d=s=>a.post("/admin/anti-block/domains/batch-check",s),c=s=>a.post("/admin/anti-block/domains/batch-delete",s),l=s=>a.get("/admin/anti-block/short-links",{params:s}),k=s=>a.post("/admin/anti-block/short-links",s),e=s=>a.delete(`/admin/anti-block/short-links/${s}`),p=s=>a.post("/admin/anti-block/short-links/batch-delete",s),b=s=>a.get("/admin/anti-block/short-links/export",{params:s}),r=s=>a.get("/admin/anti-block/access-stats",{params:s}),h=s=>a.get("/admin/anti-block/access-logs",{params:s}),g=s=>a.get("/admin/anti-block/click-trends",{params:s}),$=s=>a.post("/admin/anti-block/qrcode",s),u=(s,i)=>a.put(`/admin/anti-block/short-links/${s}`,i),x=(s,i)=>a.post(`/admin/anti-block/short-links/${s}/switch-domain`,i),f=(s,i)=>a.post(`/api/admin/groups/${s}/promotion-link`,i);export{n as a,c as b,d as c,m as d,l as e,t as f,i as g,b as h,p as i,$ as j,h as k,e as l,u as m,k as n,g as o,r as p,s as q,f as r,x as s,o as u};
