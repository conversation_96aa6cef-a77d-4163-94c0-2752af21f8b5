import{l as e,m as t,q as o,C as n,K as a,ai as l,E as i,F as s,Y as r,r as u,c,w as d,o as p,I as m,n as v}from"./vue-vendor-BcnDv-68.js";import{_ as g}from"./index-eUTsTR3J.js";import{s as f,u as b,Z as h,ag as y,X as C}from"./element-plus-C2UshkXo.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const x={class:"editor-toolbar"},w={class:"toolbar-group"},k={class:"toolbar-group"},M={class:"toolbar-group"},S={class:"toolbar-group"},L={class:"toolbar-group"},T={class:"toolbar-group"},j=["data-placeholder"],E={class:"editor-footer"},F={class:"editor-stats"},R={class:"word-count"},A={class:"char-count"},B={key:0,class:"limit-info"},I={key:0,class:"over-limit"},D={class:"emoji-header"},P={class:"emoji-grid"},H=["onClick"];const U=g({__name:"ModernRichTextEditor",props:{modelValue:{type:String,default:""},height:{type:Number,default:200},placeholder:{type:String,default:"请输入内容..."},maxLength:{type:Number,default:0}},emits:["update:modelValue","change","focus","blur"],setup(e,{expose:t,emit:o}){t();const n=e,a=o,l=u(null),i=u(""),s=u(!1),r=u(!1),g=u(!1),f=u(!1),b=u(null),h=u({bold:!1,italic:!1,underline:!1}),x=c(()=>(l.value?.innerText||"").length),w=c(()=>(i.value||"").length),k=()=>{const e=window.getSelection();e.rangeCount>0&&(b.value=e.getRangeAt(0).cloneRange())},M=()=>{if(b.value){const e=window.getSelection();e.removeAllRanges(),e.addRange(b.value)}},S=()=>{if(s.value)return;const e=l.value?.innerHTML||"";i.value=e,a("update:modelValue",e),a("change",e),n.maxLength>0&&x.value>n.maxLength&&C.warning(`内容长度不能超过 ${n.maxLength} 字`)},L=()=>{if(l.value&&!s.value)try{h.value.bold=document.queryCommandState("bold"),h.value.italic=document.queryCommandState("italic"),h.value.underline=document.queryCommandState("underline")}catch(e){}},T=e=>{if(l.value){l.value.focus();try{document.execCommand(e,!1,null),L(),S()}catch(t){}}},j=e=>{if(l.value){l.value.focus();try{document.execCommand("insertText",!1,e),S()}catch(t){const o=window.getSelection();if(o.rangeCount>0){const t=o.getRangeAt(0);t.deleteContents(),t.insertNode(document.createTextNode(e)),t.collapse(!1),S()}}}},E=()=>{const e=document.createElement("input");e.type="file",e.accept="image/*",e.onchange=e=>{const t=e.target.files[0];if(t){if(!t.type.startsWith("image/"))return void C.error("请选择图片文件");if(t.size>5242880)return void C.error("图片大小不能超过5MB");const e=C({message:"正在处理图片...",type:"info",duration:0}),o=new FileReader;o.onload=t=>{e.close();const o=t.target.result;R(o)},o.onerror=()=>{e.close(),C.error("图片读取失败")},o.readAsDataURL(t)}},e.click()},F=async()=>{try{const{value:e}=await y.prompt("请输入图片地址","插入图片",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i,inputErrorMessage:"请输入有效的图片地址"});e&&R(e)}catch(e){}},R=e=>{if(l.value)try{l.value.focus();const t=`<img src="${e}" style="max-width: 100%; height: auto; display: block; margin: 10px 0;" alt="插入的图片" />`;if(document.queryCommandSupported("insertHTML"))document.execCommand("insertHTML",!1,t);else{const e=l.value.innerHTML;l.value.innerHTML=e+t}S(),C.success("图片插入成功")}catch(t){C.error("图片插入失败")}},A=()=>{if(l.value){l.value.focus();try{document.execCommand("undo",!1,null),S()}catch(e){}}},B=()=>{if(l.value){l.value.focus();try{document.execCommand("redo",!1,null),S()}catch(e){}}};d(()=>n.modelValue,e=>{e===i.value||s.value||(i.value=e||"",l.value&&(l.value.innerHTML=i.value))},{immediate:!0}),p(()=>{l.value&&(l.value.innerHTML=n.modelValue||"",i.value=n.modelValue||"",I())});const I=()=>{if(!l.value)return;!l.value.innerText.trim()&&!r.value?l.value.classList.add("empty"):l.value.classList.remove("empty")};d([r,i],()=>{I()});const D=e=>{f.value&&!e.target.closest(".emoji-picker")&&(f.value=!1)};p(()=>{document.addEventListener("click",D)}),m(()=>{document.removeEventListener("click",D)});const P={props:n,emit:a,editorRef:l,content:i,isComposing:s,isFocused:r,isFullscreen:g,showEmojiPicker:f,savedRange:b,formatStates:h,commonEmojis:["😊","😂","🤣","😍","🥰","😘","😗","😙","😚","😋","😛","😝","😜","🤪","🤨","🧐","🤓","😎","🤩","🥳","😏","😒","😞","😔","😟","😕","🙁","☹️","😣","😖","👍","👎","👌","🤝","👏","🙌","👐","🤲","🙏","✍️","❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔"],wordCount:x,charCount:w,saveSelection:k,restoreSelection:M,getCurrentRange:()=>{const e=window.getSelection();return e.rangeCount>0?e.getRangeAt(0):null},setCursorToEnd:()=>{if(l.value){const e=document.createRange(),t=window.getSelection();e.selectNodeContents(l.value),e.collapse(!1),t.removeAllRanges(),t.addRange(e)}},handleCompositionStart:()=>{s.value=!0,k()},handleCompositionUpdate:()=>{},handleCompositionEnd:()=>{s.value=!1,v(()=>{S()})},handleInput:S,handleFocus:()=>{r.value=!0,a("focus")},handleBlur:()=>{r.value=!1,k(),a("blur")},handlePaste:e=>{e.preventDefault();const t=e.clipboardData.getData("text/plain");j(t)},handleKeydown:e=>{if(e.ctrlKey||e.metaKey)switch(e.key){case"b":e.preventDefault(),T("bold");break;case"i":e.preventDefault(),T("italic");break;case"u":e.preventDefault(),T("underline");break;case"z":e.shiftKey?(e.preventDefault(),B()):(e.preventDefault(),A());break;case"y":e.preventDefault(),B()}setTimeout(L,10)},updateFormatStates:L,toggleFormat:T,setAlignment:e=>{if(!l.value)return;l.value.focus();const t={left:"justifyLeft",center:"justifyCenter",right:"justifyRight",justify:"justifyFull"};try{document.execCommand(t[e],!1,null),S()}catch(o){}},toggleList:e=>{if(!l.value)return;l.value.focus();const t="ul"===e?"insertUnorderedList":"insertOrderedList";try{document.execCommand(t,!1,null),S()}catch(o){}},changeFontSize:e=>{const t=e.target.value;if(t&&l.value){l.value.focus();try{document.execCommand("fontSize",!1,"7");l.value.querySelectorAll('font[size="7"]').forEach(e=>{e.removeAttribute("size"),e.style.fontSize=t}),S()}catch(o){}e.target.value=""}},changeTextColor:e=>{const t=e.target.value;if(l.value){l.value.focus();try{document.execCommand("foreColor",!1,t),S()}catch(o){}}},changeBackgroundColor:e=>{const t=e.target.value;if(l.value){l.value.focus();try{document.execCommand("backColor",!1,t),S()}catch(o){}}},insertText:j,insertLink:async()=>{try{const{value:t}=await y.prompt("请输入链接地址","插入链接",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^https?:\/\/.+/,inputErrorMessage:"请输入有效的链接地址"});if(t&&l.value){l.value.focus(),M();try{document.execCommand("createLink",!1,t),S()}catch(e){}}}catch(e){}},showImageOptions:async()=>{try{const{value:e}=await y.confirm("请选择插入图片的方式","插入图片",{confirmButtonText:"上传图片",cancelButtonText:"图片链接",distinguishCancelAndClose:!0,type:"info"});E()}catch(e){"cancel"===e&&F()}},uploadImage:E,insertImageByUrl:F,insertImageToEditor:R,insertVideo:async()=>{try{const{value:t}=await y.prompt("请输入视频地址或嵌入代码","插入视频",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"textarea",inputPlaceholder:"支持视频URL或iframe嵌入代码"});if(t&&l.value){l.value.focus(),M();try{let e;if(t.includes("<iframe")||t.includes("iframe")){const e=document.createElement("div");e.innerHTML=t,e.style.margin="10px 0";const o=window.getSelection();if(o.rangeCount>0){const t=o.getRangeAt(0);t.deleteContents(),t.insertNode(e),t.collapse(!1)}}else{e=document.createElement("video"),e.src=t,e.controls=!0,e.style.maxWidth="100%",e.style.height="auto",e.style.display="block",e.style.margin="10px 0";const o=window.getSelection();if(o.rangeCount>0){const t=o.getRangeAt(0);t.deleteContents(),t.insertNode(e),t.collapse(!1)}}S()}catch(e){C.error("视频插入失败")}}}catch(e){}},insertEmoji:()=>{k(),f.value=!f.value},insertEmojiChar:e=>{f.value=!1,l.value&&(l.value.focus(),M(),j(e))},clearFormat:()=>{if(l.value){l.value.focus();try{document.execCommand("removeFormat",!1,null),S()}catch(e){}}},undo:A,redo:B,toggleFullscreen:()=>{g.value=!g.value},clearAll:()=>{l.value&&(l.value.innerHTML="",i.value="",a("update:modelValue",""),a("change",""),l.value.focus())},updatePlaceholder:I,handleClickOutside:D,ref:u,computed:c,watch:d,nextTick:v,onMounted:p,onUnmounted:m,get ElMessage(){return C},get ElMessageBox(){return y}};return Object.defineProperty(P,"__isScriptSetup",{enumerable:!1,value:!0}),P}},[["render",function(u,c,d,p,m,v){return t(),e("div",{class:f(["modern-rich-editor",{fullscreen:p.isFullscreen}])},[o("div",x,[o("div",w,[o("button",{type:"button",class:f(["toolbar-btn",{active:p.formatStates.bold}]),onMousedown:c[0]||(c[0]=a(()=>{},["prevent"])),onClick:c[1]||(c[1]=e=>p.toggleFormat("bold")),title:"粗体 (Ctrl+B)"},c[29]||(c[29]=[o("strong",null,"B",-1)]),34),o("button",{type:"button",class:f(["toolbar-btn",{active:p.formatStates.italic}]),onMousedown:c[2]||(c[2]=a(()=>{},["prevent"])),onClick:c[3]||(c[3]=e=>p.toggleFormat("italic")),title:"斜体 (Ctrl+I)"},c[30]||(c[30]=[o("em",null,"I",-1)]),34),o("button",{type:"button",class:f(["toolbar-btn",{active:p.formatStates.underline}]),onMousedown:c[4]||(c[4]=a(()=>{},["prevent"])),onClick:c[5]||(c[5]=e=>p.toggleFormat("underline")),title:"下划线 (Ctrl+U)"},c[31]||(c[31]=[o("u",null,"U",-1)]),34),o("button",{type:"button",class:"toolbar-btn",onMousedown:c[6]||(c[6]=a(()=>{},["prevent"])),onClick:c[7]||(c[7]=e=>p.toggleFormat("strikethrough")),title:"删除线"},c[32]||(c[32]=[o("s",null,"S",-1)]),32)]),c[34]||(c[34]=o("div",{class:"toolbar-divider"},null,-1)),o("div",k,[o("select",{class:"font-size-select",onChange:c[8]||(c[8]=e=>p.changeFontSize(e)),title:"字体大小"},c[33]||(c[33]=[l('<option value="" data-v-afb1cbf8>字号</option><option value="12px" data-v-afb1cbf8>12px</option><option value="14px" data-v-afb1cbf8>14px</option><option value="16px" data-v-afb1cbf8>16px</option><option value="18px" data-v-afb1cbf8>18px</option><option value="20px" data-v-afb1cbf8>20px</option><option value="24px" data-v-afb1cbf8>24px</option><option value="28px" data-v-afb1cbf8>28px</option>',8)]),32),o("input",{type:"color",class:"color-picker",onChange:c[9]||(c[9]=e=>p.changeTextColor(e)),title:"文字颜色",value:"#000000"},null,32),o("input",{type:"color",class:"color-picker",onChange:c[10]||(c[10]=e=>p.changeBackgroundColor(e)),title:"背景颜色",value:"#ffffff"},null,32)]),c[35]||(c[35]=o("div",{class:"toolbar-divider"},null,-1)),o("div",M,[o("button",{type:"button",class:"toolbar-btn",onMousedown:c[11]||(c[11]=a(()=>{},["prevent"])),onClick:c[12]||(c[12]=e=>p.setAlignment("left")),title:"左对齐"}," ⬅ ",32),o("button",{type:"button",class:"toolbar-btn",onMousedown:c[13]||(c[13]=a(()=>{},["prevent"])),onClick:c[14]||(c[14]=e=>p.setAlignment("center")),title:"居中对齐"}," ↔ ",32),o("button",{type:"button",class:"toolbar-btn",onMousedown:c[15]||(c[15]=a(()=>{},["prevent"])),onClick:c[16]||(c[16]=e=>p.setAlignment("right")),title:"右对齐"}," ➡ ",32)]),c[36]||(c[36]=o("div",{class:"toolbar-divider"},null,-1)),o("div",S,[o("button",{type:"button",class:"toolbar-btn",onMousedown:c[17]||(c[17]=a(()=>{},["prevent"])),onClick:c[18]||(c[18]=e=>p.toggleList("ul")),title:"无序列表"}," • ",32),o("button",{type:"button",class:"toolbar-btn",onMousedown:c[19]||(c[19]=a(()=>{},["prevent"])),onClick:c[20]||(c[20]=e=>p.toggleList("ol")),title:"有序列表"}," 1. ",32)]),c[37]||(c[37]=o("div",{class:"toolbar-divider"},null,-1)),o("div",L,[o("button",{type:"button",class:"toolbar-btn",onMousedown:c[21]||(c[21]=a(()=>{},["prevent"])),onClick:p.insertLink,title:"插入链接"}," 🔗 ",32),o("button",{type:"button",class:"toolbar-btn",onMousedown:c[22]||(c[22]=a(()=>{},["prevent"])),onClick:p.showImageOptions,title:"插入图片"}," 🖼️ ",32),o("button",{type:"button",class:"toolbar-btn",onMousedown:c[23]||(c[23]=a(()=>{},["prevent"])),onClick:p.insertVideo,title:"插入视频"}," 🎬 ",32),o("button",{type:"button",class:"toolbar-btn",onMousedown:c[24]||(c[24]=a(()=>{},["prevent"])),onClick:p.insertEmoji,title:"插入表情"}," 😊 ",32)]),c[38]||(c[38]=o("div",{class:"toolbar-divider"},null,-1)),o("div",T,[o("button",{type:"button",class:"toolbar-btn",onMousedown:c[25]||(c[25]=a(()=>{},["prevent"])),onClick:p.clearFormat,title:"清除格式"}," 🧹 ",32),o("button",{type:"button",class:"toolbar-btn",onMousedown:c[26]||(c[26]=a(()=>{},["prevent"])),onClick:p.toggleFullscreen,title:"全屏编辑"}," ⛶ ",32)])]),o("div",{ref:"editorRef",class:"editor-content",style:b({minHeight:d.height+"px"}),contenteditable:"true",onInput:p.handleInput,onCompositionstart:p.handleCompositionStart,onCompositionupdate:p.handleCompositionUpdate,onCompositionend:p.handleCompositionEnd,onFocus:p.handleFocus,onBlur:p.handleBlur,onPaste:p.handlePaste,onKeydown:p.handleKeydown,onMouseup:p.updateFormatStates,onKeyup:p.updateFormatStates,"data-placeholder":d.placeholder},null,44,j),o("div",E,[o("div",F,[o("span",R,"字数: "+h(p.wordCount),1),o("span",A,"字符: "+h(p.charCount),1),d.maxLength>0?(t(),e("span",B,[i(" / "+h(d.maxLength)+" ",1),p.wordCount>d.maxLength?(t(),e("span",I,"超出限制")):n("",!0)])):n("",!0)]),o("div",{class:"editor-actions"},[o("button",{type:"button",class:"action-btn",onClick:p.clearAll,title:"清空内容"}," 清空 ")])]),p.showEmojiPicker?(t(),e("div",{key:0,class:"emoji-picker",onClick:c[28]||(c[28]=a(()=>{},["stop"]))},[o("div",D,[c[39]||(c[39]=o("span",null,"选择表情",-1)),o("button",{onClick:c[27]||(c[27]=e=>p.showEmojiPicker=!1)},"×")]),o("div",P,[(t(),e(s,null,r(p.commonEmojis,e=>o("span",{key:e,class:"emoji-item",onClick:t=>p.insertEmojiChar(e)},h(e),9,H)),64))])])):n("",!0)],2)}],["__scopeId","data-v-afb1cbf8"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/ModernRichTextEditor.vue"]]);export{U as default};
