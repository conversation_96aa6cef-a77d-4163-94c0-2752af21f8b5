<template>
  <div class="unified-analytics">
    <div class="analytics-header">
      <div class="header-content">
        <h2 class="page-title">
          <el-icon><TrendCharts /></el-icon>
          数据分析中心
        </h2>
        <p class="page-subtitle">全方位数据洞察，助力业务决策</p>
      </div>
      
      <div class="header-controls">
        <el-select
          v-model="selectedTimeRange"
          placeholder="选择时间范围"
          size="large"
          @change="handleTimeRangeChange"
        >
          <el-option label="今天" value="today" />
          <el-option label="最近7天" value="7days" />
          <el-option label="最近30天" value="30days" />
          <el-option label="最近3个月" value="3months" />
          <el-option label="自定义" value="custom" />
        </el-select>
        
        <el-button-group class="view-controls">
          <el-button
            v-for="tab in analyticsTabs"
            :key="tab.key"
            :type="activeTab === tab.key ? 'primary' : 'default'"
            @click="switchTab(tab.key)"
          >
            <el-icon><component :is="tab.icon" /></el-icon>
            {{ tab.label }}
          </el-button>
        </el-button-group>
        
        <el-button type="primary" :icon="Refresh" @click="refreshData">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 快速概览卡片 -->
    <div class="quick-overview">
      <el-row :gutter="24">
        <el-col
          v-for="card in overviewCards"
          :key="card.key"
          :xs="24"
          :sm="12"
          :md="6"
        >
          <div class="overview-card" :class="`card-${card.type}`">
            <div class="card-icon">
              <el-icon><component :is="card.icon" /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ formatNumber(card.value) }}</div>
              <div class="card-title">{{ card.title }}</div>
              <div class="card-trend" :class="card.trend > 0 ? 'up' : 'down'">
                <el-icon>
                  <component :is="card.trend > 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
                {{ Math.abs(card.trend) }}%
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 分析内容区域 -->
    <div class="analytics-content">
      <!-- 用户分析 -->
      <div v-show="activeTab === 'users'" class="analytics-section">
        <el-row :gutter="24">
          <el-col :span="16">
            <el-card title="用户增长趋势" class="chart-card">
              <div class="chart-container">
                <LineChart
                  :data="userGrowthData"
                  :options="chartOptions.userGrowth"
                  height="300"
                />
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card title="用户类型分布" class="chart-card">
              <div class="chart-container">
                <PieChart
                  :data="userTypeData"
                  :options="chartOptions.userType"
                  height="300"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="24" style="margin-top: 24px;">
          <el-col :span="12">
            <el-card title="活跃用户统计" class="data-card">
              <div class="data-list">
                <div class="data-item" v-for="item in activeUserStats" :key="item.label">
                  <span class="label">{{ item.label }}</span>
                  <span class="value">{{ formatNumber(item.value) }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card title="用户留存率" class="data-card">
              <div class="retention-chart">
                <BarChart
                  :data="retentionData"
                  :options="chartOptions.retention"
                  height="200"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 业务分析 -->
      <div v-show="activeTab === 'business'" class="analytics-section">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-card title="收入趋势" class="chart-card">
              <div class="chart-container">
                <LineChart
                  :data="revenueData"
                  :options="chartOptions.revenue"
                  height="300"
                />
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card title="订单统计" class="chart-card">
              <div class="chart-container">
                <BarChart
                  :data="orderData"
                  :options="chartOptions.orders"
                  height="300"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="24" style="margin-top: 24px;">
          <el-col :span="8">
            <el-card title="热门群组" class="ranking-card">
              <div class="ranking-list">
                <div
                  v-for="(group, index) in topGroups"
                  :key="group.id"
                  class="ranking-item"
                >
                  <div class="rank">{{ index + 1 }}</div>
                  <div class="info">
                    <div class="name">{{ group.name }}</div>
                    <div class="value">{{ group.members }} 成员</div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card title="业务指标对比" class="chart-card">
              <div class="chart-container">
                <MixedChart
                  :data="businessMetricsData"
                  :options="chartOptions.businessMetrics"
                  height="280"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 防红分析 -->
      <div v-show="activeTab === 'security'" class="analytics-section">
        <el-row :gutter="24">
          <el-col :span="16">
            <el-card title="防红效果监控" class="chart-card">
              <div class="chart-container">
                <LineChart
                  :data="securityData"
                  :options="chartOptions.security"
                  height="300"
                />
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card title="域名健康度" class="gauge-card">
              <div class="gauge-container">
                <GaugeChart
                  :data="domainHealthData"
                  :options="chartOptions.domainHealth"
                  height="300"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="24" style="margin-top: 24px;">
          <el-col :span="24">
            <el-card title="风险事件统计" class="table-card">
              <el-table :data="riskEvents" style="width: 100%">
                <el-table-column prop="time" label="时间" width="180" />
                <el-table-column prop="type" label="事件类型">
                  <template #default="{ row }">
                    <el-tag :type="getRiskTypeColor(row.type)">
                      {{ row.type }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="domain" label="涉及域名" />
                <el-table-column prop="impact" label="影响级别">
                  <template #default="{ row }">
                    <el-rate
                      :model-value="row.impact"
                      disabled
                      show-score
                      text-color="#ff9900"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="处理状态">
                  <template #default="{ row }">
                    <el-tag :type="getStatusColor(row.status)">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 系统分析 -->
      <div v-show="activeTab === 'system'" class="analytics-section">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-card title="系统性能监控" class="chart-card">
              <div class="chart-container">
                <LineChart
                  :data="performanceData"
                  :options="chartOptions.performance"
                  height="300"
                />
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card title="资源使用率" class="chart-card">
              <div class="chart-container">
                <RadarChart
                  :data="resourceUsageData"
                  :options="chartOptions.resourceUsage"
                  height="300"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import {
  TrendCharts,
  User,
  Money,
  Lock,
  Monitor,
  Refresh,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'

// 图表组件
import LineChart from '@/components/Charts/LineChart.vue'
import BarChart from '@/components/Charts/BarChart.vue'
import PieChart from '@/components/Charts/PieChart.vue'
import MixedChart from '@/components/Charts/MixedChart.vue'
import GaugeChart from '@/components/Charts/GaugeChart.vue'
import RadarChart from '@/components/Charts/RadarChart.vue'

// 响应式数据
const selectedTimeRange = ref('30days')
const activeTab = ref('users')
const loading = ref(false)

// 分析标签配置
const analyticsTabs = [
  { key: 'users', label: '用户分析', icon: 'User' },
  { key: 'business', label: '业务分析', icon: 'Money' },
  { key: 'security', label: '防红分析', icon: 'Lock' },
  { key: 'system', label: '系统分析', icon: 'Monitor' }
]

// 概览卡片数据
const overviewCards = reactive([
  {
    key: 'total_users',
    title: '总用户数',
    value: 12580,
    trend: 12.5,
    icon: 'User',
    type: 'primary'
  },
  {
    key: 'active_groups',
    title: '活跃群组',
    value: 1258,
    trend: 8.2,
    icon: 'ChatDotRound',
    type: 'success'
  },
  {
    key: 'total_revenue',
    title: '总收入',
    value: 458960,
    trend: 15.8,
    icon: 'Money',
    type: 'warning'
  },
  {
    key: 'security_score',
    title: '安全评分',
    value: 95.8,
    trend: 2.1,
    icon: 'Lock',
    type: 'danger'
  }
])

// 模拟数据
const userGrowthData = reactive({
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [{
    label: '新增用户',
    data: [120, 190, 300, 500, 200, 300],
    borderColor: '#3b82f6',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    tension: 0.4
  }]
})

const userTypeData = reactive({
  labels: ['普通用户', 'VIP用户', '代理商', '管理员'],
  datasets: [{
    data: [65, 25, 8, 2],
    backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444']
  }]
})

const activeUserStats = reactive([
  { label: '日活跃用户', value: 2580 },
  { label: '周活跃用户', value: 8960 },
  { label: '月活跃用户', value: 25800 },
  { label: '平均在线时长', value: '45分钟' }
])

const retentionData = reactive({
  labels: ['1天', '3天', '7天', '14天', '30天'],
  datasets: [{
    label: '留存率(%)',
    data: [88, 75, 62, 48, 35],
    backgroundColor: '#10b981'
  }]
})

const revenueData = reactive({
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [{
    label: '月收入',
    data: [45000, 52000, 48000, 61000, 55000, 67000],
    borderColor: '#10b981',
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    tension: 0.4
  }]
})

const orderData = reactive({
  labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
  datasets: [{
    label: '订单量',
    data: [120, 150, 180, 160, 200, 220, 180],
    backgroundColor: '#3b82f6'
  }]
})

const topGroups = reactive([
  { id: 1, name: '超级VIP群', members: 2580 },
  { id: 2, name: '精品资源群', members: 1890 },
  { id: 3, name: '会员专享群', members: 1456 },
  { id: 4, name: '新手交流群', members: 1234 },
  { id: 5, name: '高级会员群', members: 987 }
])

const businessMetricsData = reactive({
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [
    {
      type: 'line',
      label: '转化率(%)',
      data: [3.2, 3.8, 4.1, 3.9, 4.5, 4.8],
      borderColor: '#ef4444',
      yAxisID: 'y1'
    },
    {
      type: 'bar',
      label: '新增群组',
      data: [45, 52, 48, 61, 55, 67],
      backgroundColor: '#3b82f6',
      yAxisID: 'y'
    }
  ]
})

const securityData = reactive({
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [
    {
      label: '拦截次数',
      data: [120, 95, 150, 80, 100, 75],
      borderColor: '#ef4444',
      backgroundColor: 'rgba(239, 68, 68, 0.1)'
    },
    {
      label: '成功率(%)',
      data: [95.5, 97.2, 94.8, 98.1, 96.5, 98.8],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)'
    }
  ]
})

const domainHealthData = reactive({
  value: 95.8,
  max: 100,
  title: '域名健康度'
})

const riskEvents = reactive([
  {
    time: '2024-01-15 14:30',
    type: '域名异常',
    domain: 'example1.com',
    impact: 3,
    status: '已处理'
  },
  {
    time: '2024-01-14 09:15',
    type: '访问拦截',
    domain: 'example2.com',
    impact: 2,
    status: '处理中'
  },
  {
    time: '2024-01-13 16:45',
    type: '链接失效',
    domain: 'example3.com',
    impact: 4,
    status: '已修复'
  }
])

const performanceData = reactive({
  labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
  datasets: [
    {
      label: 'CPU使用率(%)',
      data: [25, 15, 45, 65, 55, 35],
      borderColor: '#3b82f6'
    },
    {
      label: '内存使用率(%)',
      data: [60, 55, 70, 80, 75, 65],
      borderColor: '#10b981'
    }
  ]
})

const resourceUsageData = reactive({
  labels: ['CPU', '内存', '磁盘', '网络', '数据库'],
  datasets: [{
    label: '使用率(%)',
    data: [45, 65, 35, 55, 40],
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
    borderColor: '#3b82f6'
  }]
})

// 图表配置
const chartOptions = {
  userGrowth: {
    responsive: true,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  },
  userType: {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom'
      }
    }
  },
  retention: {
    responsive: true,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100
      }
    }
  },
  revenue: {
    responsive: true,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  },
  orders: {
    responsive: true,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  },
  businessMetrics: {
    responsive: true,
    interaction: {
      intersect: false,
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        },
      },
    }
  },
  security: {
    responsive: true,
    scales: {
      y: {
        beginAtZero: true
      }
    }
  },
  domainHealth: {
    responsive: true,
    plugins: {
      legend: {
        display: false
      }
    }
  },
  performance: {
    responsive: true,
    scales: {
      y: {
        beginAtZero: true,
        max: 100
      }
    }
  },
  resourceUsage: {
    responsive: true,
    scales: {
      r: {
        beginAtZero: true,
        max: 100
      }
    }
  }
}

// 方法
const switchTab = (tab) => {
  activeTab.value = tab
}

const handleTimeRangeChange = (range) => {
  console.log('Time range changed:', range)
  // TODO: 根据时间范围重新获取数据
  refreshData()
}

const refreshData = async () => {
  loading.value = true
  try {
    // TODO: 调用 API 获取最新数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const formatNumber = (num) => {
  if (typeof num === 'string') return num
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toLocaleString()
}

const getRiskTypeColor = (type) => {
  const colorMap = {
    '域名异常': 'danger',
    '访问拦截': 'warning',
    '链接失效': 'info'
  }
  return colorMap[type] || 'default'
}

const getStatusColor = (status) => {
  const colorMap = {
    '已处理': 'success',
    '处理中': 'warning',
    '已修复': 'info'
  }
  return colorMap[status] || 'default'
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.unified-analytics {
  padding: 24px;
  min-height: 100vh;
  background: #f5f7fa;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 28px;
      font-weight: 600;
      color: #1a1a1a;
      margin: 0 0 8px 0;
      
      .el-icon {
        color: #3b82f6;
      }
    }
    
    .page-subtitle {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
  }
  
  .header-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .view-controls {
      .el-button {
        border-radius: 6px;
        
        &.el-button--primary {
          background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
          border: none;
        }
      }
    }
  }
}

.quick-overview {
  margin-bottom: 32px;
  
  .overview-card {
    display: flex;
    align-items: center;
    padding: 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
    
    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      
      .el-icon {
        font-size: 24px;
        color: white;
      }
    }
    
    .card-content {
      flex: 1;
      
      .card-value {
        font-size: 28px;
        font-weight: 700;
        color: #1a1a1a;
        line-height: 1.2;
      }
      
      .card-title {
        font-size: 14px;
        color: #666;
        margin: 4px 0;
      }
      
      .card-trend {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        font-weight: 600;
        
        &.up {
          color: #10b981;
        }
        
        &.down {
          color: #ef4444;
        }
        
        .el-icon {
          font-size: 14px;
        }
      }
    }
    
    &.card-primary .card-icon {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }
    
    &.card-success .card-icon {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    
    &.card-warning .card-icon {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
    
    &.card-danger .card-icon {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
  }
}

.analytics-content {
  .analytics-section {
    .chart-card,
    .data-card,
    .gauge-card,
    .ranking-card,
    .table-card {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: none;
      
      :deep(.el-card__header) {
        border-bottom: 1px solid #f0f0f0;
        padding: 20px 24px;
        
        .card-header {
          font-size: 16px;
          font-weight: 600;
          color: #1a1a1a;
        }
      }
      
      :deep(.el-card__body) {
        padding: 24px;
      }
    }
    
    .chart-container,
    .gauge-container,
    .retention-chart {
      position: relative;
      width: 100%;
    }
    
    .data-list {
      .data-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .label {
          color: #666;
          font-size: 14px;
        }
        
        .value {
          font-weight: 600;
          color: #1a1a1a;
          font-size: 16px;
        }
      }
    }
    
    .ranking-list {
      .ranking-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .rank {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 14px;
          margin-right: 16px;
        }
        
        .info {
          flex: 1;
          
          .name {
            font-weight: 500;
            color: #1a1a1a;
            font-size: 14px;
          }
          
          .value {
            color: #666;
            font-size: 12px;
            margin-top: 2px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .analytics-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    
    .header-controls {
      flex-wrap: wrap;
      gap: 12px;
      
      .view-controls {
        order: 2;
        width: 100%;
        
        .el-button {
          flex: 1;
          font-size: 12px;
        }
      }
    }
  }
  
  .overview-card {
    .card-icon {
      width: 48px;
      height: 48px;
      margin-right: 12px;
      
      .el-icon {
        font-size: 20px;
      }
    }
    
    .card-value {
      font-size: 24px !important;
    }
  }
}
</style>