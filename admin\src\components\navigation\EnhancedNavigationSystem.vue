<!-- 增强导航系统 - 现代化UI优化版本 -->
<template>
  <div class="enhanced-navigation-system" :class="systemClasses">
    <!-- 顶部导航栏 -->
    <header class="enhanced-navigation-header">
      <div class="header-container">
        <!-- Logo品牌区域 -->
        <div class="header-brand" @click="navigateHome">
          <div class="brand-logo">
            <el-icon><DataLine /></el-icon>
          </div>
          <div class="brand-text" v-if="!isMobile">
            <h1 class="brand-title">晨鑫流量变现系统</h1>
            <p class="brand-subtitle">智能导航管理系统</p>
          </div>
        </div>

        <!-- 全局搜索 -->
        <div class="header-search" v-if="!isMobile">
          <div class="search-container">
            <input
              ref="searchInput"
              v-model="searchQuery"
              type="text"
              class="search-input"
              placeholder="搜索功能、页面、用户..."
              @focus="handleSearchFocus"
              @blur="handleSearchBlur"
              @input="handleSearch"
              @keydown.enter="handleSearchEnter"
            />
            <el-icon class="search-icon"><Search /></el-icon>
            <div class="search-shortcuts">
              <span class="shortcut-key">⌘</span>
              <span class="shortcut-key">K</span>
            </div>
          </div>

          <!-- 搜索建议下拉 -->
          <transition name="fade-in-up">
            <div v-if="showSearchDropdown" class="search-dropdown">
              <div
                v-for="suggestion in searchSuggestions"
                :key="suggestion.id"
                class="suggestion-item"
                @click="selectSuggestion(suggestion)"
              >
                <el-icon class="suggestion-icon">
                  <component :is="suggestion.icon" />
                </el-icon>
                <div class="suggestion-content">
                  <div class="suggestion-title">{{ suggestion.title }}</div>
                  <div class="suggestion-desc">{{ suggestion.description }}</div>
                </div>
                <span class="suggestion-type">{{ suggestion.type }}</span>
              </div>
            </div>
          </transition>
        </div>

        <!-- 右侧操作区域 -->
        <div class="header-actions">
          <!-- 主题切换 -->
          <button class="action-button" @click="toggleTheme" title="切换主题">
            <el-icon>
              <component :is="isDark ? 'Sunny' : 'Moon'" />
            </el-icon>
          </button>

          <!-- 全屏切换 -->
          <button class="action-button" @click="toggleFullscreen" title="全屏显示">
            <el-icon>
              <component :is="isFullscreen ? 'Fold' : 'FullScreen'" />
            </el-icon>
          </button>

          <!-- 通知中心 -->
          <button class="action-button" @click="showNotifications = true" title="通知中心">
            <el-icon><Bell /></el-icon>
            <span v-if="unreadCount > 0" class="action-badge">{{ unreadCount }}</span>
          </button>

          <!-- 用户菜单 -->
          <div class="user-menu" :class="{ open: showUserMenu }" @click="toggleUserMenu">
            <div class="user-avatar">
              {{ userInfo.name?.charAt(0) || 'U' }}
            </div>
            <div class="user-info" v-if="!isMobile">
              <div class="user-name">{{ userInfo.name || '用户' }}</div>
              <div class="user-role">{{ getRoleText(userInfo.role) }}</div>
            </div>
            <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
          </div>

          <!-- 移动端菜单按钮 -->
          <button v-if="isMobile" class="action-button" @click="toggleMobileMenu">
            <el-icon>
              <component :is="showMobileMenu ? 'Close' : 'Menu'" />
            </el-icon>
          </button>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="navigation-main">
      <!-- 侧边栏 -->
      <aside class="enhanced-navigation-sidebar" :class="{ collapsed: sidebarCollapsed }">
        <!-- 侧边栏头部 -->
        <div class="sidebar-header">
          <div class="sidebar-brand" @click="navigateHome">
            <div class="brand-icon">
              <el-icon><DataLine /></el-icon>
            </div>
            <div class="sidebar-brand-text">
              <h2 class="brand-title">晨鑫流量变现系统</h2>
              <span class="brand-version">v2.0.1</span>
            </div>
          </div>
        </div>

        <!-- 导航内容 -->
        <div class="sidebar-content">
          <el-scrollbar>
            <!-- 收藏夹 -->
            <div v-if="favoriteItems.length" class="nav-section">
              <div class="nav-section-header">
                <el-icon class="section-icon"><StarFilled /></el-icon>
                <span class="nav-section-title">收藏夹</span>
                <span class="section-badge">{{ favoriteItems.length }}</span>
              </div>
              <ul class="nav-items">
                <li v-for="item in favoriteItems" :key="item.key" class="nav-item">
                  <a class="nav-link" @click="navigateToItem(item)">
                    <el-icon class="nav-icon">
                      <component :is="item.icon" />
                    </el-icon>
                    <span class="nav-item-text">{{ item.title }}</span>
                  </a>
                </li>
              </ul>
            </div>

            <!-- 主导航分组 -->
            <div
              v-for="section in navigationSections"
              :key="section.key"
              class="nav-section"
            >
              <div class="nav-section-header">
                <el-icon class="section-icon" :style="{ color: section.color }">
                  <component :is="section.icon" />
                </el-icon>
                <span class="nav-section-title">{{ section.title }}</span>
                <span v-if="getSectionBadgeCount(section)" class="section-badge">
                  {{ getSectionBadgeCount(section) }}
                </span>
              </div>
              <ul class="nav-items">
                <li
                  v-for="item in section.items"
                  :key="item.key"
                  class="nav-item"
                >
                  <a
                    class="nav-link"
                    :class="{
                      active: isActiveItem(item),
                      'has-submenu': item.children?.length,
                      expanded: expandedItems.includes(item.key)
                    }"
                    @click="handleNavItemClick(item)"
                  >
                    <el-icon class="nav-icon">
                      <component :is="item.icon" />
                    </el-icon>
                    <span class="nav-item-text">{{ item.title }}</span>
                    <span v-if="item.badge" class="nav-badge">{{ item.badge }}</span>
                    <el-icon v-if="item.children?.length" class="nav-arrow">
                      <ArrowRight />
                    </el-icon>
                  </a>

                  <!-- 子菜单 -->
                  <transition name="submenu-expand">
                    <ul
                      v-if="item.children?.length && expandedItems.includes(item.key)"
                      class="nav-submenu expanded"
                    >
                      <li
                        v-for="child in item.children"
                        :key="child.key"
                        class="nav-subitem"
                      >
                        <a
                          class="nav-link"
                          :class="{ active: isActiveItem(child) }"
                          @click="navigateToItem(child)"
                        >
                          <el-icon class="nav-icon">
                            <component :is="child.icon || 'Document'" />
                          </el-icon>
                          <span class="nav-item-text">{{ child.title }}</span>
                        </a>
                      </li>
                    </ul>
                  </transition>
                </li>
              </ul>
            </div>
          </el-scrollbar>
        </div>

        <!-- 侧边栏底部 -->
        <div class="sidebar-footer">
          <button class="collapse-toggle" @click="toggleSidebar">
            <el-icon>
              <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
            </el-icon>
          </button>
        </div>
      </aside>

      <!-- 内容区域 -->
      <main class="content-area">
        <slot />
      </main>
    </div>

    <!-- 移动端导航 -->
    <div v-if="isMobile" class="enhanced-mobile-navigation">
      <!-- 移动端抽屉菜单 -->
      <transition name="slide-fade">
        <div v-if="showMobileMenu" class="mobile-drawer" @click="closeMobileMenu">
          <div class="drawer-content" @click.stop>
            <!-- 移动端导航内容 -->
            <div class="mobile-nav-header">
              <div class="mobile-brand">
                <div class="brand-icon">
                  <el-icon><DataLine /></el-icon>
                </div>
                <span class="brand-text">晨鑫流量变现系统</span>
              </div>
              <button class="mobile-action-btn" @click="closeMobileMenu">
                <el-icon><Close /></el-icon>
              </button>
            </div>

            <div class="mobile-nav-content">
              <!-- 移动端导航项目 -->
              <div
                v-for="section in navigationSections"
                :key="section.key"
                class="mobile-nav-section"
              >
                <div class="mobile-section-title">{{ section.title }}</div>
                <div
                  v-for="item in section.items"
                  :key="item.key"
                  class="mobile-nav-item"
                  @click="navigateToItem(item)"
                >
                  <el-icon><component :is="item.icon" /></el-icon>
                  <span>{{ item.title }}</span>
                  <span v-if="item.badge" class="mobile-badge">{{ item.badge }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>

      <!-- 移动端底部导航 -->
      <div class="mobile-bottom-nav">
        <div
          v-for="tab in bottomNavTabs"
          :key="tab.key"
          class="nav-tab"
          :class="{ active: activeTab === tab.key }"
          @click="handleTabClick(tab)"
        >
          <el-icon class="tab-icon">
            <component :is="tab.icon" />
          </el-icon>
          <span class="tab-label">{{ tab.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  DataLine, Search, Bell, Moon, Sunny, FullScreen, Fold, Expand,
  Menu, Close, ArrowDown, ArrowRight, StarFilled, Document,
  User, Setting, TrendCharts, Comment, ShoppingCart, CreditCard,
  Monitor, Lightning, Star, Grid, Wallet, Plus, Avatar,
  Share, Link, Lock, Folder
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['sidebar-toggle', 'theme-change'])

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const isMobile = ref(false)
const sidebarCollapsed = ref(props.collapsed)
const showSearchDropdown = ref(false)
const showUserMenu = ref(false)
const showMobileMenu = ref(false)
const showNotifications = ref(false)
const searchQuery = ref('')
const searchSuggestions = ref([])
const expandedItems = ref([])
const favoriteItems = ref([])
const isDark = ref(false)
const isFullscreen = ref(false)
const activeTab = ref('dashboard')

// 用户信息
const userInfo = computed(() => userStore.userInfo || {})
const unreadCount = ref(3)

// 导航数据
const navigationSections = ref([
  {
    key: 'dashboard',
    title: '仪表板',
    icon: 'TrendCharts',
    color: '#3B82F6',
    items: [
      {
        key: 'overview',
        title: '概览',
        icon: 'TrendCharts',
        path: '/dashboard',
        badge: null
      },
      {
        key: 'analytics',
        title: '数据分析',
        icon: 'DataLine',
        path: '/dashboard/analytics',
        badge: 2
      }
    ]
  },
  {
    key: 'data-screen',
    title: '数据大屏',
    icon: 'Monitor',
    color: '#6366F1',
    items: [
      {
        key: 'data-screen-demo',
        title: '大屏演示',
        icon: 'Monitor',
        path: '/data-screen',
        badge: null
      },
      {
        key: 'data-screen-ultra',
        title: 'Ultra版本',
        icon: 'Lightning',
        path: '/data-screen/ultra',
        badge: null
      },
      {
        key: 'data-screen-enhanced',
        title: '增强版本',
        icon: 'Star',
        path: '/data-screen/enhanced',
        badge: null
      },
      {
        key: 'data-screen-classic',
        title: '经典版本',
        icon: 'Grid',
        path: '/data-screen/classic',
        badge: null
      }
    ]
  },
  {
    key: 'community',
    title: '社群管理',
    icon: 'Comment',
    color: '#10B981',
    items: [
      {
        key: 'groups',
        title: '群组列表',
        icon: 'Comment',
        path: '/community/groups',
        badge: 5
      },
      {
        key: 'templates',
        title: '模板管理',
        icon: 'Document',
        path: '/community/templates'
      },
      {
        key: 'group-add',
        title: '创建群组',
        icon: 'Plus',
        path: '/community/add-enhanced'
      },
      {
        key: 'content-moderation',
        title: '内容审核',
        icon: 'Lock',
        path: '/community/content-moderation'
      }
    ]
  },
  {
    key: 'users',
    title: '用户管理',
    icon: 'User',
    color: '#F59E0B',
    items: [
      {
        key: 'user-list',
        title: '用户列表',
        icon: 'User',
        path: '/users',
        badge: 12
      },
      {
        key: 'user-analytics',
        title: '用户分析',
        icon: 'TrendCharts',
        path: '/users/analytics'
      },
      {
        key: 'user-add',
        title: '添加用户',
        icon: 'Plus',
        path: '/user/add'
      }
    ]
  },
  {
    key: 'agent',
    title: '代理商管理',
    icon: 'Avatar',
    color: '#EC4899',
    items: [
      {
        key: 'agent-list',
        title: '代理商列表',
        icon: 'Avatar',
        path: '/agent/list',
        badge: 3
      },
      {
        key: 'agent-hierarchy',
        title: '代理商层级',
        icon: 'Grid',
        path: '/agent/hierarchy'
      },
      {
        key: 'agent-commission',
        title: '佣金管理',
        icon: 'CreditCard',
        path: '/agent/commission'
      },
      {
        key: 'agent-performance',
        title: '业绩统计',
        icon: 'TrendCharts',
        path: '/agent/performance'
      }
    ]
  },
  {
    key: 'distribution',
    title: '分销管理',
    icon: 'Share',
    color: '#8B5CF6',
    items: [
      {
        key: 'distributor-list',
        title: '分销商列表',
        icon: 'User',
        path: '/distribution/distributors'
      },
      {
        key: 'distributor-detail',
        title: '分销商详情',
        icon: 'Document',
        path: '/distribution/detail'
      }
    ]
  },
  {
    key: 'promotion',
    title: '推广管理',
    icon: 'Share',
    color: '#F59E0B',
    items: [
      {
        key: 'promotion-links',
        title: '推广链接',
        icon: 'Link',
        path: '/promotion/links'
      },
      {
        key: 'landing-pages',
        title: '落地页管理',
        icon: 'Document',
        path: '/promotion/landing-pages'
      },
      {
        key: 'promotion-analytics',
        title: '推广分析',
        icon: 'TrendCharts',
        path: '/promotion/analytics'
      }
    ]
  },
  {
    key: 'anti-block',
    title: '防红系统',
    icon: 'Lock',
    color: '#EF4444',
    items: [
      {
        key: 'anti-block-dashboard',
        title: '防红控制台',
        icon: 'Monitor',
        path: '/anti-block/dashboard'
      },
      {
        key: 'domain-list',
        title: '域名管理',
        icon: 'Link',
        path: '/anti-block/domains'
      },
      {
        key: 'short-links',
        title: '短链管理',
        icon: 'Link',
        path: '/anti-block/short-links'
      }
    ]
  },
  {
    key: 'finance',
    title: '财务管理',
    icon: 'CreditCard',
    color: '#8B5CF6',
    items: [
      {
        key: 'orders',
        title: '订单管理',
        icon: 'ShoppingCart',
        path: '/orders',
        badge: 8
      },
      {
        key: 'finance',
        title: '财务中心',
        icon: 'CreditCard',
        path: '/finance'
      },
      {
        key: 'commission-logs',
        title: '佣金记录',
        icon: 'CreditCard',
        path: '/finance/commission'
      },
      {
        key: 'withdraw-manage',
        title: '提现管理',
        icon: 'Wallet',
        path: '/finance/withdraw'
      }
    ]
  },
  {
    key: 'payment',
    title: '支付设置',
    icon: 'Wallet',
    color: '#F97316',
    items: [
      {
        key: 'payment-settings',
        title: '支付配置',
        icon: 'CreditCard',
        path: '/admin/payment-settings',
        badge: null
      },
      {
        key: 'payment-config',
        title: '通道管理',
        icon: 'Setting',
        path: '/admin/payment-channels',
        badge: null
      },
      {
        key: 'payment-logs',
        title: '支付日志',
        icon: 'Document',
        path: '/admin/payment-logs',
        badge: null
      },
      {
        key: 'payment-orders',
        title: '支付订单',
        icon: 'ShoppingCart',
        path: '/admin/payment-orders'
      }
    ]
  },
  {
    key: 'permission',
    title: '权限管理',
    icon: 'Lock',
    color: '#6B7280',
    items: [
      {
        key: 'role-management',
        title: '角色管理',
        icon: 'Avatar',
        path: '/permission/roles'
      },
      {
        key: 'permission-management',
        title: '权限管理',
        icon: 'Lock',
        path: '/permission/permissions'
      }
    ]
  },
  {
    key: 'system',
    title: '系统设置',
    icon: 'Setting',
    color: '#EF4444',
    items: [
      {
        key: 'settings',
        title: '基础设置',
        icon: 'Setting',
        path: '/system/settings'
      },
      {
        key: 'monitor',
        title: '系统监控',
        icon: 'TrendCharts',
        path: '/system/monitor',
        badge: 1
      },
      {
        key: 'operation-logs',
        title: '操作日志',
        icon: 'Document',
        path: '/system/operation-logs'
      },
      {
        key: 'notifications',
        title: '通知管理',
        icon: 'Bell',
        path: '/system/notifications'
      },
      {
        key: 'file-management',
        title: '文件管理',
        icon: 'Folder',
        path: '/system/files'
      }
    ]
  }
])

const bottomNavTabs = ref([
  { key: 'dashboard', label: '首页', icon: 'TrendCharts' },
  { key: 'community', label: '社群', icon: 'Comment' },
  { key: 'users', label: '用户', icon: 'User' },
  { key: 'finance', label: '财务', icon: 'CreditCard' },
  { key: 'profile', label: '我的', icon: 'User' }
])

// 计算属性
const systemClasses = computed(() => ({
  'mobile': isMobile.value,
  'sidebar-collapsed': sidebarCollapsed.value,
  'dark-theme': isDark.value
}))

// 方法
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
}

const navigateHome = () => {
  router.push('/dashboard')
}

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  emit('sidebar-toggle', sidebarCollapsed.value)
}

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.setAttribute('data-theme', isDark.value ? 'dark' : 'light')
  emit('theme-change', isDark.value ? 'dark' : 'light')
}

const toggleFullscreen = () => {
  if (!isFullscreen.value) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

// 搜索相关
const handleSearchFocus = () => {
  showSearchDropdown.value = true
}

const handleSearchBlur = () => {
  setTimeout(() => {
    showSearchDropdown.value = false
  }, 200)
}

const handleSearch = (query) => {
  if (!query) {
    searchSuggestions.value = []
    return
  }

  // 模拟搜索建议
  searchSuggestions.value = [
    {
      id: 1,
      title: '群组管理',
      description: '管理微信群组',
      type: '功能',
      icon: 'Comment',
      path: '/community/groups'
    },
    {
      id: 2,
      title: '用户分析',
      description: '查看用户数据',
      type: '页面',
      icon: 'TrendCharts',
      path: '/analytics/users'
    }
  ].filter(item => 
    item.title.toLowerCase().includes(query.toLowerCase())
  )
}

const handleSearchEnter = () => {
  if (searchSuggestions.value.length > 0) {
    selectSuggestion(searchSuggestions.value[0])
  }
}

const selectSuggestion = (suggestion) => {
  router.push(suggestion.path)
  searchQuery.value = ''
  showSearchDropdown.value = false
}

// 导航相关
const handleNavItemClick = (item) => {
  if (item.children?.length) {
    toggleItemExpansion(item.key)
  } else {
    navigateToItem(item)
  }
}

const toggleItemExpansion = (itemKey) => {
  const index = expandedItems.value.indexOf(itemKey)
  if (index > -1) {
    expandedItems.value.splice(index, 1)
  } else {
    expandedItems.value.push(itemKey)
  }
}

const navigateToItem = (item) => {
  if (item.path) {
    router.push(item.path)
    closeMobileMenu()
  }
}

const isActiveItem = (item) => {
  return route.path === item.path
}

const getSectionBadgeCount = (section) => {
  return section.items.reduce((count, item) => count + (item.badge || 0), 0)
}

const handleTabClick = (tab) => {
  activeTab.value = tab.key
  // 根据tab导航到对应页面
}

const getRoleText = (role) => {
  const roles = {
    admin: '系统管理员',
    manager: '管理员',
    user: '普通用户',
    distributor: '分销商'
  }
  return roles[role] || '用户'
}

// 全屏事件监听
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 键盘快捷键
const handleKeydown = (e) => {
  if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
    e.preventDefault()
    document.querySelector('.search-input')?.focus()
  }
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('keydown', handleKeydown)
  
  // 初始化主题
  const savedTheme = localStorage.getItem('theme') || 'light'
  isDark.value = savedTheme === 'dark'
  document.documentElement.setAttribute('data-theme', savedTheme)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('keydown', handleKeydown)
})

// 监听props变化
watch(() => props.collapsed, (newVal) => {
  sidebarCollapsed.value = newVal
})
</script>

<style lang="scss" scoped>
@use '@/styles/navigation.scss';

.enhanced-navigation-system {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  
  .navigation-main {
    display: flex;
    flex: 1;
    
    .content-area {
      flex: 1;
      padding: var(--nav-spacing-lg);
      background: var(--nav-bg-secondary);
      min-height: calc(100vh - var(--nav-header-height));
      overflow-y: auto;
    }
  }
  
  &.mobile {
    .navigation-main {
      .enhanced-navigation-sidebar {
        display: none;
      }
      
      .content-area {
        padding-bottom: 80px; // 为底部导航留空间
      }
    }
  }
}

// 移动端特定样式
.mobile-nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--nav-spacing-lg);
  border-bottom: 1px solid var(--nav-bg-tertiary);
  
  .mobile-brand {
    display: flex;
    align-items: center;
    gap: var(--nav-spacing-sm);
    
    .brand-icon {
      width: 32px;
      height: 32px;
      background: var(--nav-primary-gradient);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
    }
    
    .brand-text {
      font-size: 18px;
      font-weight: 700;
      background: var(--nav-primary-gradient);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}

.mobile-nav-content {
  padding: var(--nav-spacing-lg);
  
  .mobile-nav-section {
    margin-bottom: var(--nav-spacing-xl);
    
    .mobile-section-title {
      font-size: 12px;
      font-weight: 700;
      color: var(--nav-text-muted);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: var(--nav-spacing-md);
    }
    
    .mobile-nav-item {
      display: flex;
      align-items: center;
      gap: var(--nav-spacing-md);
      padding: var(--nav-spacing-md);
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-bottom: var(--nav-spacing-xs);
      
      &:hover {
        background: var(--nav-bg-secondary);
        transform: translateX(4px);
      }
      
      .mobile-badge {
        margin-left: auto;
        min-width: 18px;
        height: 18px;
        background: var(--nav-error-color);
        color: white;
        border-radius: 9px;
        font-size: 10px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

// 过渡动画
.fade-in-up-enter-active,
.fade-in-up-leave-active {
  transition: all 0.3s ease;
}

.fade-in-up-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.fade-in-up-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.submenu-expand-enter-active,
.submenu-expand-leave-active {
  transition: all 0.3s ease;
}

.submenu-expand-enter-from,
.submenu-expand-leave-to {
  max-height: 0;
  opacity: 0;
}
</style>