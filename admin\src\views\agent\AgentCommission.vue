<template>
  <div class="modern-agent-commission">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><Medal /></el-icon>
          </div>
          <div class="header-text">
            <h1>代理商佣金管理</h1>
            <p>全面管理代理商佣金计算、结算和提现，实时跟踪收益状况</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleExportReport" class="action-btn secondary">
            <el-icon><Download /></el-icon>
            导出报表
          </el-button>
          <el-button @click="showHelpDialog = true" class="action-btn secondary">
            <el-icon><QuestionFilled /></el-icon>
            功能说明
          </el-button>
          <el-button @click="showBatchSettleDialog" class="action-btn secondary">
            <el-icon><Money /></el-icon>
            批量结算
          </el-button>
          <el-button type="primary" @click="refreshData" class="action-btn primary" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-container" v-loading="statsLoading">
        <div class="stat-card" v-for="stat in commissionStatCards" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="recordFilters" inline @submit.prevent="searchRecords">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="recordFilters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            class="date-picker"
          />
        </el-form-item>
        <el-form-item label="佣金类型">
          <el-select v-model="recordFilters.type" placeholder="全部类型" clearable class="filter-select">
            <el-option label="直推佣金" value="direct" />
            <el-option label="团队佣金" value="team" />
            <el-option label="管理奖励" value="management" />
            <el-option label="特殊奖励" value="special" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="recordFilters.status" placeholder="全部状态" clearable class="filter-select">
            <el-option label="待结算" value="pending" />
            <el-option label="已结算" value="settled" />
            <el-option label="已提现" value="withdrawn" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchRecords" class="search-btn">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetRecordFilters" class="reset-btn">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 功能标签页 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h3>佣金管理</h3>
            <el-tag size="small" type="info">共 {{ recordPagination.total }} 条记录</el-tag>
          </div>
          <div class="header-right">
            <el-button @click="showWithdrawDialog" type="primary" size="small">
              <el-icon><CreditCard /></el-icon>
              申请提现
            </el-button>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeTab" class="commission-tabs">
        <!-- 佣金记录 -->
        <el-tab-pane label="佣金记录" name="records">
          <el-table :data="commissionRecords" v-loading="loading" class="modern-table">
            <el-table-column prop="order_no" label="订单号" width="140">
              <template #default="{ row }">
                <el-link type="primary" @click="viewOrderDetail(row.order_no)">
                  {{ row.order_no }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="commission_type" label="佣金类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getCommissionTypeTag(row.commission_type)" size="small">
                  {{ getCommissionTypeText(row.commission_type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="佣金金额" width="120" align="right">
              <template #default="{ row }">
                <span class="commission-amount">¥{{ formatNumber(row.amount) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="rate" label="佣金比例" width="100" align="center">
              <template #default="{ row }">
                <span class="commission-rate">{{ row.rate }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="source_agent" label="来源代理" width="150">
              <template #default="{ row }">
                <div class="agent-info">
                  <el-avatar :size="32" :src="row.source_agent?.avatar">
                    {{ row.source_agent?.name?.charAt(0) }}
                  </el-avatar>
                  <div class="agent-details">
                    <div class="agent-name">{{ row.source_agent?.name }}</div>
                    <div class="agent-code">{{ row.source_agent?.code || 'N/A' }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusTag(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="产生时间" width="140">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="settled_at" label="结算时间" width="140">
              <template #default="{ row }">
                {{ row.settled_at ? formatDateTime(row.settled_at) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button size="small" type="primary" link @click="viewCommissionDetail(row)">
                  详情
                </el-button>
                <el-button 
                  v-if="row.status === 'pending'" 
                  size="small" 
                  type="success" 
                  link 
                  @click="settleCommission(row)"
                >
                  结算
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination">
            <el-pagination
              v-model:current-page="recordPagination.current"
              v-model:page-size="recordPagination.size"
              :total="recordPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleRecordSizeChange"
              @current-change="handleRecordCurrentChange"
            />
          </div>
        </el-tab-pane>

        <!-- 提现记录 -->
        <el-tab-pane label="提现记录" name="withdrawals">
          <el-table :data="withdrawalRecords" v-loading="loading" class="modern-table">
            <el-table-column prop="withdraw_no" label="提现单号" width="140" />
            <el-table-column prop="amount" label="提现金额" width="120" align="right">
              <template #default="{ row }">
                <span class="withdraw-amount">¥{{ formatNumber(row.amount) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="bank_info" label="收款账户" min-width="200">
              <template #default="{ row }">
                <div class="bank-info">
                  <div class="bank-name">{{ row.bank_info?.bank_name }}</div>
                  <div class="account-no">{{ row.bank_info?.account_no }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getWithdrawStatusTag(row.status)" size="small">
                  {{ getWithdrawStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="申请时间" width="140">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="processed_at" label="处理时间" width="140">
              <template #default="{ row }">
                {{ row.processed_at ? formatDateTime(row.processed_at) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ row }">
                <el-button size="small" type="primary" link @click="viewWithdrawDetail(row)">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 佣金规则 -->
        <el-tab-pane label="佣金规则" name="rules">
          <div class="rules-content">
            <div class="rule-section">
              <h3>💰 佣金计算规则</h3>
              <div class="rule-grid">
                <div class="rule-item">
                  <div class="rule-icon direct">
                    <el-icon><User /></el-icon>
                  </div>
                  <div class="rule-content">
                    <h4>直推佣金</h4>
                    <p>直接推广用户产生订单的佣金</p>
                    <div class="rule-rate">佣金比例：10-15%</div>
                  </div>
                </div>
                <div class="rule-item">
                  <div class="rule-icon team">
                    <el-icon><Connection /></el-icon>
                  </div>
                  <div class="rule-content">
                    <h4>团队佣金</h4>
                    <p>下级代理商推广产生的团队佣金</p>
                    <div class="rule-rate">佣金比例：3-5%</div>
                  </div>
                </div>
                <div class="rule-item">
                  <div class="rule-icon management">
                    <el-icon><Trophy /></el-icon>
                  </div>
                  <div class="rule-content">
                    <h4>管理奖励</h4>
                    <p>团队管理和业绩达标的额外奖励</p>
                    <div class="rule-rate">奖励比例：1-3%</div>
                  </div>
                </div>
                <div class="rule-item">
                  <div class="rule-icon special">
                    <el-icon><Star /></el-icon>
                  </div>
                  <div class="rule-content">
                    <h4>特殊奖励</h4>
                    <p>平台活动和特殊任务的奖励佣金</p>
                    <div class="rule-rate">奖励金额：不定</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="rule-section">
              <h3>📋 结算规则</h3>
              <ul class="rule-list">
                <li>佣金每日统计，T+1结算到账</li>
                <li>最低提现金额：100元</li>
                <li>提现手续费：2%（平台承担）</li>
                <li>工作日提现当日处理，非工作日顺延</li>
                <li>单次提现限额：50,000元</li>
              </ul>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 功能说明对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="代理商佣金管理功能说明"
      width="1000px"
      class="help-dialog"
    >
      <div class="help-content">
        <!-- 功能概述 -->
        <div class="help-section">
          <h3>💰 功能概述</h3>
          <p>代理商佣金管理是平台分销体系的核心功能，提供全面的佣金计算、结算、提现管理，帮助代理商实时掌握收益状况，高效管理资金流转。</p>
        </div>

        <!-- 核心功能 -->
        <div class="help-section">
          <h3>🚀 核心功能模块</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Medal /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>佣金统计</h4>
                  <p>实时统计累计佣金、可提现金额、已提现金额等关键指标</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><List /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>佣金记录</h4>
                  <p>详细记录每笔佣金的来源、类型、金额和结算状态</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><CreditCard /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>提现管理</h4>
                  <p>在线申请提现，查看提现记录和处理状态</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 操作指南 -->
        <div class="help-section">
          <h3>📝 操作指南</h3>
          <el-collapse v-model="activeGuides">
            <el-collapse-item title="如何查看佣金记录？" name="view-commission">
              <div class="guide-content">
                <ol>
                  <li>在"佣金记录"标签页查看所有佣金明细</li>
                  <li>使用筛选条件按时间、类型、状态查询</li>
                  <li>点击"详情"查看具体的佣金计算信息</li>
                  <li>关注佣金状态：待结算、已结算、已提现</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 提示：佣金记录按产生时间倒序排列，最新的记录在前
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何申请提现？" name="withdraw-process">
              <div class="guide-content">
                <ol>
                  <li>确保有足够的可提现佣金（≥100元）</li>
                  <li>点击"申请提现"按钮</li>
                  <li>填写提现金额和收款账户信息</li>
                  <li>提交申请后等待平台审核</li>
                  <li>审核通过后资金将转入指定账户</li>
                </ol>
                <el-alert type="warning" :closable="false">
                  ⚠️ 注意：请确保收款账户信息准确，避免提现失败
                </el-alert>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Money, CreditCard, Wallet, Clock, Medal, Download, QuestionFilled,
  Search, RefreshLeft, User, Connection, Trophy, Star, List, ArrowUp
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const statsLoading = ref(true)
const activeTab = ref('records')
const showHelpDialog = ref(false)

// 对话框状态
const showBatchDialog = ref(false)
const showWithdrawFormDialog = ref(false)
const showOrderDetailDialog = ref(false)
const showCommissionDetailDialog = ref(false)
const showWithdrawDetailDialog = ref(false)

// 选中数据
const selectedOrderNo = ref('')
const selectedCommissionRecord = ref(null)
const selectedWithdrawRecord = ref(null)
const batchSettleData = ref({ records: [], totalAmount: 0 })

// 表单数据
const withdrawForm = ref({
  amount: '',
  bank_name: '',
  account_name: '',
  account_no: '',
  remark: ''
})

// 帮助对话框相关数据
const activeGuides = ref(['view-commission'])

// 佣金统计卡片数据 - 与其他页面保持一致的设计
const commissionStatCards = ref([
  {
    key: 'total_commission',
    label: '累计佣金',
    value: '¥0',
    icon: 'Money',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15.6%'
  },
  {
    key: 'available_commission',
    label: '可提现佣金',
    value: '¥0',
    icon: 'Wallet',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '待结算 ¥1,200'
  },
  {
    key: 'withdrawn_commission',
    label: '已提现金额',
    value: '¥0',
    icon: 'CreditCard',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '本月 ¥3,200'
  },
  {
    key: 'pending_withdraws',
    label: '待审核提现',
    value: '0',
    icon: 'Clock',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '金额 ¥2,400'
  }
])

// 佣金统计数据
const commissionStats = reactive({
  totalCommission: 25680,
  availableCommission: 8900,
  withdrawnCommission: 16780,
  pendingCommission: 1200,
  monthGrowth: 15.6,
  monthWithdrawn: 3200,
  pendingWithdraws: 3,
  pendingAmount: 2400
})

// 佣金记录
const commissionRecords = ref([])
const recordFilters = reactive({
  dateRange: null,
  type: '',
  status: ''
})

const recordPagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 提现记录
const withdrawalRecords = ref([])

// 方法
const refreshData = async () => {
  await Promise.all([
    loadStats(),
    loadCommissionRecords(),
    loadWithdrawalRecords()
  ])
  ElMessage.success('数据刷新成功')
}

const loadStats = async () => {
  try {
    statsLoading.value = true
    console.log('加载佣金统计数据...')
    
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 更新统计卡片数据
    commissionStatCards.value[0].value = '¥' + commissionStats.totalCommission.toLocaleString()
    commissionStatCards.value[1].value = '¥' + commissionStats.availableCommission.toLocaleString()
    commissionStatCards.value[2].value = '¥' + commissionStats.withdrawnCommission.toLocaleString()
    commissionStatCards.value[3].value = commissionStats.pendingWithdraws.toString()
    
    console.log('佣金统计数据加载完成')
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

const handleExportReport = async () => {
  try {
    ElMessage.success('佣金报表导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const showBatchSettleDialog = () => {
  // 检查是否有待结算的佣金
  const pendingRecords = commissionRecords.value.filter(record => record.status === 'pending')
  if (pendingRecords.length === 0) {
    ElMessage.warning('没有待结算的佣金记录')
    return
  }

  batchSettleData.value = {
    records: pendingRecords,
    totalAmount: pendingRecords.reduce((sum, record) => sum + record.amount, 0)
  }
  showBatchDialog.value = true
}

const showWithdrawDialog = () => {
  // 检查可提现金额
  if (commissionStats.availableCommission <= 0) {
    ElMessage.warning('当前没有可提现的佣金')
    return
  }

  withdrawForm.value = {
    amount: '',
    bank_name: '',
    account_name: '',
    account_no: '',
    remark: ''
  }
  showWithdrawFormDialog.value = true
}

const searchRecords = () => {
  recordPagination.current = 1
  loadCommissionRecords()
}

const resetRecordFilters = () => {
  Object.assign(recordFilters, {
    dateRange: null,
    type: '',
    status: ''
  })
  searchRecords()
}

const handleRecordSizeChange = (size) => {
  recordPagination.size = size
  loadCommissionRecords()
}

const handleRecordCurrentChange = (current) => {
  recordPagination.current = current
  loadCommissionRecords()
}

const viewOrderDetail = (orderNo) => {
  // 显示订单详情对话框
  selectedOrderNo.value = orderNo
  loadOrderDetail(orderNo)
  showOrderDetailDialog.value = true
}

const viewCommissionDetail = (record) => {
  // 显示佣金详情对话框
  selectedCommissionRecord.value = record
  showCommissionDetailDialog.value = true
}

const settleCommission = async (record) => {
  try {
    await ElMessageBox.confirm(
      `确定要结算佣金 ¥${record.amount} 吗？`,
      '确认结算',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('佣金结算成功')
    await loadCommissionRecords()
  } catch {
    ElMessage.info('已取消结算')
  }
}

const viewWithdrawDetail = (record) => {
  // 显示提现详情对话框
  selectedWithdrawRecord.value = record
  showWithdrawDetailDialog.value = true
}

const getCommissionTypeTag = (type) => {
  const tagMap = {
    direct: 'success',
    team: 'primary',
    management: 'warning',
    special: 'danger'
  }
  return tagMap[type] || 'info'
}

const getCommissionTypeText = (type) => {
  const textMap = {
    direct: '直推佣金',
    team: '团队佣金',
    management: '管理奖励',
    special: '特殊奖励'
  }
  return textMap[type] || '未知类型'
}

const getStatusTag = (status) => {
  const tagMap = {
    pending: 'warning',
    settled: 'success',
    withdrawn: 'info'
  }
  return tagMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    pending: '待结算',
    settled: '已结算',
    withdrawn: '已提现'
  }
  return textMap[status] || '未知状态'
}

const getWithdrawStatusTag = (status) => {
  const tagMap = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    rejected: 'danger'
  }
  return tagMap[status] || 'info'
}

const getWithdrawStatusText = (status) => {
  const textMap = {
    pending: '待审核',
    processing: '处理中',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return textMap[status] || '未知状态'
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatNumber = (number) => {
  if (typeof number !== 'number') return '0'
  return number.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 新增功能方法
const loadOrderDetail = async (orderNo) => {
  try {
    // TODO: 调用API获取订单详情
    await new Promise(resolve => setTimeout(resolve, 500))
    console.log('加载订单详情:', orderNo)
  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败')
  }
}

const handleBatchSettle = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要批量结算 ${batchSettleData.value.records.length} 条佣金记录吗？总金额：¥${batchSettleData.value.totalAmount}`,
      '确认批量结算',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 调用API批量结算
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('批量结算成功')
    showBatchDialog.value = false
    await loadCommissionRecords()
  } catch {
    ElMessage.info('已取消批量结算')
  }
}

const handleWithdrawSubmit = async () => {
  try {
    // TODO: 表单验证和API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('提现申请提交成功')
    showWithdrawFormDialog.value = false
    await loadWithdrawalRecords()
  } catch (error) {
    console.error('提现申请失败:', error)
    ElMessage.error('提现申请失败')
  }
}

const loadCommissionRecords = async () => {
  loading.value = true
  
  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    commissionRecords.value = [
      {
        id: 1,
        order_no: 'ORD202401001',
        commission_type: 'direct',
        amount: 150.00,
        rate: 15,
        source_agent: {
          name: '李四',
          avatar: ''
        },
        status: 'settled',
        created_at: '2024-01-15 10:30:00',
        settled_at: '2024-01-16 09:00:00'
      },
      {
        id: 2,
        order_no: 'ORD202401002',
        commission_type: 'team',
        amount: 80.00,
        rate: 8,
        source_agent: {
          name: '王五',
          avatar: ''
        },
        status: 'pending',
        created_at: '2024-01-16 14:20:00',
        settled_at: null
      }
    ]
    
    recordPagination.total = 2
  } catch (error) {
    console.error('加载佣金记录失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadWithdrawalRecords = async () => {
  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    withdrawalRecords.value = [
      {
        id: 1,
        withdraw_no: 'WD202401001',
        amount: 1000.00,
        bank_info: {
          bank_name: '中国工商银行',
          account_no: '****1234'
        },
        status: 'completed',
        created_at: '2024-01-10 16:00:00',
        processed_at: '2024-01-11 10:30:00'
      },
      {
        id: 2,
        withdraw_no: 'WD202401002',
        amount: 500.00,
        bank_info: {
          bank_name: '中国建设银行',
          account_no: '****5678'
        },
        status: 'pending',
        created_at: '2024-01-15 09:20:00',
        processed_at: null
      }
    ]
  } catch (error) {
    console.error('加载提现记录失败:', error)
  }
}

// 页面初始化
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.commission-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-subtitle {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.available {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.withdrawn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 12px;
}

.trend-text {
  color: #059669;
  background: #ecfdf5;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 主卡片 */
.main-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.commission-tabs {
  --el-tabs-header-height: 48px;
}

.tab-content {
  padding: 20px 0;
}

/* 筛选条件 */
.filter-bar {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.filter-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

/* 表格样式 */
.records-table,
.withdrawals-table {
  --el-table-border-color: #e5e7eb;
  --el-table-bg-color: white;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.commission-amount {
  font-weight: 600;
  color: #059669;
}

.commission-rate {
  font-weight: 500;
  color: #7c3aed;
}

.withdraw-amount {
  font-weight: 600;
  color: #dc2626;
}

.bank-info {
  .account-no {
    font-size: 12px;
    color: #6b7280;
    margin-top: 2px;
  }
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 佣金规则 */
.rules-content {
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;

  h3 {
    margin: 0 0 16px 0;
    color: #1f2937;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .commission-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filter-form {
    .el-form-item {
      margin-right: 0;
      margin-bottom: 16px;
    }
  }
}
</style>
