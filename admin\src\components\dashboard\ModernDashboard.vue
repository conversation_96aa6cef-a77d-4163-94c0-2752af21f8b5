<template>
  <div class="modern-dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="welcome-section">
          <h1 class="page-title">
            <span class="title-icon">
              <el-icon><DataBoard /></el-icon>
            </span>
            管理控制台
          </h1>
          <p class="page-subtitle">
            欢迎回来，{{ userStore.nickname || '管理员' }}！
            今天是 {{ formatDate(new Date()) }}，让我们一起查看运营数据
          </p>
        </div>
        <div class="header-actions">
          <el-button type="primary" class="action-btn" @click="navigateToDataScreen">
            <el-icon><FullScreen /></el-icon>
            数据大屏
          </el-button>
          <el-button class="action-btn" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button text class="action-btn" @click="showHelpDialog = true">
            <el-icon><QuestionFilled /></el-icon>
            使用帮助
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="section-header">
        <h2 class="section-title">数据概览</h2>
        <div class="section-subtitle">实时业务数据统计</div>
      </div>
      
      <div class="stats-grid">
        <StatCard
          v-for="(stat, index) in statsData"
          :key="stat.key"
          :title="stat.title"
          :value="stat.value"
          :unit="stat.unit"
          :change="stat.change"
          :trend="stat.trend"
          :icon="stat.icon"
          :color="stat.color"
          :clickable="true"
          :class="`animate-fade-in-up delay-${index * 100}`"
          @click="handleStatClick(stat.key)"
        />
      </div>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-section">
      <div class="section-header">
        <h2 class="section-title">数据分析</h2>
        <div class="section-subtitle">趋势分析与数据洞察</div>
      </div>

      <div class="charts-grid">
        <!-- 收入趋势图 -->
        <div class="chart-card primary-chart">
          <div class="chart-header">
            <div class="chart-title">
              <el-icon><TrendCharts /></el-icon>
              收入趋势分析
            </div>
            <div class="chart-controls">
              <el-radio-group v-model="selectedPeriod" size="small">
                <el-radio-button label="1d">今日</el-radio-button>
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
                <el-radio-button label="90d">90天</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div class="chart-content">
            <LineChart
              :data="lineChartData"
              :options="lineChartOptions"
              :height="320"
            />
          </div>
        </div>

        <!-- 订单来源分布 -->
        <div class="chart-card secondary-chart">
          <div class="chart-header">
            <div class="chart-title">
              <el-icon><PieChart /></el-icon>
              订单来源分布
            </div>
          </div>
          <div class="chart-content">
            <DoughnutChart
              :data="pieChartData"
              :options="pieChartOptions"
              :height="320"
            />
          </div>
        </div>

        <!-- 用户增长趋势 -->
        <div class="chart-card tertiary-chart">
          <div class="chart-header">
            <div class="chart-title">
              <el-icon><User /></el-icon>
              用户增长趋势
            </div>
          </div>
          <div class="chart-content">
            <BarChart
              :data="barChartData"
              :options="barChartOptions"
              :height="320"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 信息面板区域 -->
    <div class="info-section">
      <div class="info-grid">
        <!-- 系统状态 -->
        <div class="info-card system-status">
          <div class="card-header">
            <div class="card-title">
              <el-icon><Monitor /></el-icon>
              系统状态
            </div>
            <div class="status-indicator online">
              <div class="status-dot"></div>
              运行正常
            </div>
          </div>
          <div class="card-content">
            <div class="status-list">
              <div class="status-item" v-for="status in systemStatus" :key="status.name">
                <div class="status-name">{{ status.name }}</div>
                <div class="status-value" :class="status.status">
                  {{ status.value }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="info-card recent-activity">
          <div class="card-header">
            <div class="card-title">
              <el-icon><Bell /></el-icon>
              最近活动
            </div>
            <el-button text size="small" @click="$router.push('/admin/system/logs')">
              查看全部
            </el-button>
          </div>
          <div class="card-content">
            <div class="activity-list">
              <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
                <div class="activity-icon" :class="activity.type">
                  <el-icon>
                    <component :is="activity.icon" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-text">{{ activity.text }}</div>
                  <div class="activity-time">{{ formatTime(activity.time) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="info-card quick-actions">
          <div class="card-header">
            <div class="card-title">
              <el-icon><Operation /></el-icon>
              快捷操作
            </div>
          </div>
          <div class="card-content">
            <div class="actions-grid">
              <div 
                class="action-item" 
                v-for="action in quickActions" 
                :key="action.key"
                @click="handleActionClick(action.key)"
              >
                <div class="action-icon" :class="action.color">
                  <el-icon>
                    <component :is="action.icon" />
                  </el-icon>
                </div>
                <div class="action-label">{{ action.label }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 待办事项 -->
        <div class="info-card todo-list">
          <div class="card-header">
            <div class="card-title">
              <el-icon><List /></el-icon>
              待办事项
            </div>
            <el-badge :value="todoItems.filter(item => !item.completed).length" class="todo-badge" />
          </div>
          <div class="card-content">
            <div class="todo-items">
              <div class="todo-item" v-for="todo in todoItems" :key="todo.id">
                <el-checkbox 
                  v-model="todo.completed" 
                  @change="handleTodoChange(todo)"
                  class="todo-checkbox"
                />
                <div class="todo-content" :class="{ completed: todo.completed }">
                  <div class="todo-text">{{ todo.text }}</div>
                  <div class="todo-meta">
                    <span class="todo-priority" :class="todo.priority">
                      {{ getPriorityText(todo.priority) }}
                    </span>
                    <span class="todo-deadline">{{ formatDate(todo.deadline) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 帮助对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="控制台使用帮助"
      width="800px"
      class="help-dialog"
    >
      <div class="help-content">
        <div class="help-section">
          <h3>🎯 功能概述</h3>
          <p>管理控制台是系统的核心管理中心，提供全面的数据监控、业务分析和快捷操作功能。</p>
        </div>
        
        <div class="help-section">
          <h3>📊 数据统计</h3>
          <ul>
            <li><strong>实时数据：</strong>收入、订单、用户等关键指标实时更新</li>
            <li><strong>趋势分析：</strong>支持多时间段对比，发现业务规律</li>
            <li><strong>可视化图表：</strong>直观展示数据变化和分布情况</li>
          </ul>
        </div>

        <div class="help-section">
          <h3>⚡ 快捷操作</h3>
          <ul>
            <li><strong>数据大屏：</strong>全屏展示核心数据，适合会议展示</li>
            <li><strong>快速跳转：</strong>点击统计卡片快速进入详细管理页面</li>
            <li><strong>系统监控：</strong>实时监控系统运行状态和性能指标</li>
          </ul>
        </div>

        <div class="help-section">
          <h3>🔧 使用技巧</h3>
          <ul>
            <li>定期查看系统状态，确保服务正常运行</li>
            <li>关注数据趋势变化，及时调整运营策略</li>
            <li>利用快捷操作提高日常管理效率</li>
            <li>通过待办事项管理重要任务</li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  DataBoard, FullScreen, Refresh, QuestionFilled, TrendCharts,
  PieChart, User, Monitor, Bell, Operation, List
} from '@element-plus/icons-vue'
import StatCard from './StatCard.vue'
import LineChart from '@/components/Charts/LineChart.vue'
import DoughnutChart from '@/components/Charts/DoughnutChart.vue'
import BarChart from '@/components/Charts/BarChart.vue'
import { getDashboardStats, getDashboardCharts } from '@/api/dashboard'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const showHelpDialog = ref(false)
const selectedPeriod = ref('7d')

// 统计数据
const statsData = ref([
  {
    key: 'income',
    title: '今日收入',
    value: 25680,
    unit: '元',
    change: 12.5,
    trend: 'up',
    icon: 'Money',
    color: 'primary'
  },
  {
    key: 'orders',
    title: '今日订单',
    value: 156,
    unit: '笔',
    change: 8.2,
    trend: 'up',
    icon: 'Tickets',
    color: 'success'
  },
  {
    key: 'users',
    title: '总用户数',
    value: 8924,
    unit: '人',
    change: 15.8,
    trend: 'up',
    icon: 'User',
    color: 'info'
  },
  {
    key: 'groups',
    title: '活跃群组',
    value: 342,
    unit: '个',
    change: -2.1,
    trend: 'down',
    icon: 'ChatDotRound',
    color: 'warning'
  }
])

// 图表数据
const lineChartData = ref({
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [{
    label: '收入趋势',
    data: [65000, 78000, 85000, 92000, 105000, 125000],
    borderColor: '#3b82f6',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    tension: 0.4,
    fill: true
  }]
})

const lineChartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        callback: function(value) {
          return '¥' + (value / 1000) + 'k'
        }
      }
    }
  }
})

const pieChartData = ref({
  labels: ['微信群', 'QQ群', '直接推广', '代理商', '其他'],
  datasets: [{
    data: [35, 25, 20, 15, 5],
    backgroundColor: [
      '#3b82f6',
      '#10b981',
      '#f59e0b',
      '#ef4444',
      '#8b5cf6'
    ]
  }]
})

const pieChartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
})

const barChartData = ref({
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [{
    label: '新增用户',
    data: [120, 190, 300, 500, 200, 300],
    backgroundColor: 'rgba(16, 185, 129, 0.8)',
    borderColor: '#10b981',
    borderWidth: 1
  }]
})

const barChartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true
    }
  }
})

// 系统状态
const systemStatus = ref([
  { name: '系统运行时间', value: '7天12小时', status: 'normal' },
  { name: '数据库连接', value: '正常', status: 'normal' },
  { name: '缓存服务', value: '正常', status: 'normal' },
  { name: '队列任务', value: '正常', status: 'normal' }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    text: '新用户注册',
    time: new Date(Date.now() - 2 * 60 * 1000),
    icon: 'User',
    type: 'success'
  },
  {
    id: 2,
    text: '订单支付成功',
    time: new Date(Date.now() - 5 * 60 * 1000),
    icon: 'Money',
    type: 'primary'
  },
  {
    id: 3,
    text: '群组创建',
    time: new Date(Date.now() - 10 * 60 * 1000),
    icon: 'ChatDotRound',
    type: 'warning'
  },
  {
    id: 4,
    text: '系统备份完成',
    time: new Date(Date.now() - 30 * 60 * 1000),
    icon: 'Check',
    type: 'info'
  }
])

// 快捷操作
const quickActions = ref([
  { key: 'users', label: '用户管理', icon: 'User', color: 'primary' },
  { key: 'orders', label: '订单管理', icon: 'Tickets', color: 'success' },
  { key: 'groups', label: '群组管理', icon: 'ChatDotRound', color: 'warning' },
  { key: 'finance', label: '财务管理', icon: 'Money', color: 'info' },
  { key: 'settings', label: '系统设置', icon: 'Setting', color: 'primary' },
  { key: 'analytics', label: '数据分析', icon: 'TrendCharts', color: 'success' }
])

// 待办事项
const todoItems = ref([
  {
    id: 1,
    text: '审核待处理订单',
    completed: false,
    priority: 'high',
    deadline: new Date(Date.now() + 2 * 60 * 60 * 1000)
  },
  {
    id: 2,
    text: '更新系统公告',
    completed: false,
    priority: 'medium',
    deadline: new Date(Date.now() + 24 * 60 * 60 * 1000)
  },
  {
    id: 3,
    text: '检查服务器状态',
    completed: true,
    priority: 'high',
    deadline: new Date(Date.now() - 60 * 60 * 1000)
  },
  {
    id: 4,
    text: '备份数据库',
    completed: false,
    priority: 'low',
    deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
  }
])

// 方法
const formatDate = (date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  }).format(date)
}

const formatTime = (date) => {
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}

const getPriorityText = (priority) => {
  const map = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return map[priority] || '普通'
}

const handleStatClick = (key) => {
  const routes = {
    income: '/admin/finance',
    orders: '/admin/orders',
    users: '/admin/users',
    groups: '/admin/groups'
  }

  if (routes[key]) {
    router.push(routes[key])
  }
}

const handleActionClick = (key) => {
  const routes = {
    users: '/admin/users',
    orders: '/admin/orders',
    groups: '/admin/groups',
    finance: '/admin/finance',
    settings: '/admin/settings',
    analytics: '/admin/analytics'
  }

  if (routes[key]) {
    router.push(routes[key])
  }
}

const handleTodoChange = (todo) => {
  ElMessage.success(todo.completed ? '任务已完成' : '任务已重新激活')
}

const navigateToDataScreen = () => {
  router.push('/data-screen')
}

const refreshData = async () => {
  loading.value = true
  try {
    // 模拟数据刷新
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

// 监听时间段变化
watch(selectedPeriod, (newPeriod) => {
  // 根据选择的时间段更新图表数据
  console.log('切换时间段:', newPeriod)
})

onMounted(() => {
  // 初始化数据
  refreshData()
})
</script>

<style lang="scss" scoped>
.modern-dashboard {
  padding: 24px;
  background: transparent;
  min-height: 100vh;
}

// 页面头部
.dashboard-header {
  margin-bottom: 32px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .welcome-section {
      .page-title {
        display: flex;
        align-items: center;
        font-size: 32px;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 8px 0;

        .title-icon {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          color: white;
        }
      }

      .page-subtitle {
        color: #64748b;
        font-size: 16px;
        margin: 0;
        line-height: 1.6;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .action-btn {
        height: 44px;
        padding: 0 20px;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
        }
      }
    }
  }
}

// 统计区域
.stats-section {
  margin-bottom: 32px;

  .section-header {
    margin-bottom: 20px;

    .section-title {
      font-size: 24px;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 4px 0;
    }

    .section-subtitle {
      color: #64748b;
      font-size: 14px;
      margin: 0;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
}

// 图表区域
.charts-section {
  margin-bottom: 32px;

  .section-header {
    margin-bottom: 20px;

    .section-title {
      font-size: 24px;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 4px 0;
    }

    .section-subtitle {
      color: #64748b;
      font-size: 14px;
      margin: 0;
    }
  }

  .charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: auto auto;
    gap: 24px;

    .primary-chart {
      grid-column: 1 / 2;
      grid-row: 1 / 3;
    }

    .secondary-chart {
      grid-column: 2 / 3;
      grid-row: 1 / 2;
    }

    .tertiary-chart {
      grid-column: 2 / 3;
      grid-row: 2 / 3;
    }

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      grid-template-rows: auto;

      .primary-chart,
      .secondary-chart,
      .tertiary-chart {
        grid-column: 1 / 2;
        grid-row: auto;
      }
    }
  }

  .chart-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
    }

    .chart-header {
      padding: 24px 24px 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .chart-title {
        display: flex;
        align-items: center;
        font-size: 18px;
        font-weight: 600;
        color: #1e293b;

        .el-icon {
          margin-right: 8px;
          color: #3b82f6;
        }
      }

      .chart-controls {
        :deep(.el-radio-group) {
          .el-radio-button__inner {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.3s ease;

            &:hover {
              border-color: #3b82f6;
              color: #3b82f6;
            }
          }

          .el-radio-button__original-radio:checked + .el-radio-button__inner {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
          }
        }
      }
    }

    .chart-content {
      padding: 24px;
    }
  }
}

// 信息面板区域
.info-section {
  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
    }
  }

  .info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    }

    .card-header {
      padding: 20px 24px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;

        .el-icon {
          margin-right: 8px;
          color: #3b82f6;
        }
      }

      .status-indicator {
        display: flex;
        align-items: center;
        font-size: 12px;
        font-weight: 600;
        color: #10b981;

        .status-dot {
          width: 8px;
          height: 8px;
          background: #10b981;
          border-radius: 50%;
          margin-right: 6px;
          animation: pulse 2s infinite;
        }
      }

      .todo-badge {
        :deep(.el-badge__content) {
          background: #ef4444;
          border: 2px solid white;
        }
      }
    }

    .card-content {
      padding: 24px;
    }
  }

  // 系统状态样式
  .system-status {
    .status-list {
      .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.04);

        &:last-child {
          border-bottom: none;
        }

        .status-name {
          color: #64748b;
          font-weight: 500;
        }

        .status-value {
          font-weight: 600;

          &.normal {
            color: #10b981;
          }

          &.warning {
            color: #f59e0b;
          }

          &.error {
            color: #ef4444;
          }
        }
      }
    }
  }

  // 最近活动样式
  .recent-activity {
    .activity-list {
      .activity-item {
        display: flex;
        align-items: flex-start;
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.04);

        &:last-child {
          border-bottom: none;
        }

        .activity-icon {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          flex-shrink: 0;

          &.success {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
          }

          &.primary {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
          }

          &.warning {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
          }

          &.info {
            background: rgba(139, 92, 246, 0.1);
            color: #8b5cf6;
          }
        }

        .activity-content {
          flex: 1;

          .activity-text {
            color: #1e293b;
            font-weight: 500;
            margin-bottom: 2px;
          }

          .activity-time {
            color: #64748b;
            font-size: 12px;
          }
        }
      }
    }
  }

  // 快捷操作样式
  .quick-actions {
    .actions-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 12px;
        border-radius: 12px;
        background: rgba(248, 250, 252, 0.5);
        border: 1px solid rgba(0, 0, 0, 0.04);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(59, 130, 246, 0.05);
          border-color: #3b82f6;
          transform: translateY(-2px);
        }

        .action-icon {
          width: 40px;
          height: 40px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          color: white;

          &.primary {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
          }

          &.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          }

          &.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
          }

          &.info {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
          }
        }

        .action-label {
          font-size: 12px;
          font-weight: 600;
          color: #64748b;
          text-align: center;
        }
      }
    }
  }

  // 待办事项样式
  .todo-list {
    .todo-items {
      .todo-item {
        display: flex;
        align-items: flex-start;
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.04);

        &:last-child {
          border-bottom: none;
        }

        .todo-checkbox {
          margin-right: 12px;
          margin-top: 2px;
        }

        .todo-content {
          flex: 1;
          transition: all 0.3s ease;

          &.completed {
            opacity: 0.6;

            .todo-text {
              text-decoration: line-through;
            }
          }

          .todo-text {
            color: #1e293b;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .todo-meta {
            display: flex;
            align-items: center;
            gap: 12px;

            .todo-priority {
              font-size: 10px;
              font-weight: 600;
              padding: 2px 6px;
              border-radius: 4px;
              text-transform: uppercase;

              &.high {
                background: rgba(239, 68, 68, 0.1);
                color: #ef4444;
              }

              &.medium {
                background: rgba(245, 158, 11, 0.1);
                color: #f59e0b;
              }

              &.low {
                background: rgba(16, 185, 129, 0.1);
                color: #10b981;
              }
            }

            .todo-deadline {
              color: #64748b;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

// 动画效果
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@for $i from 1 through 10 {
  .delay-#{$i * 100} {
    animation-delay: #{$i * 0.1}s;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 帮助对话框样式
:deep(.help-dialog) {
  .el-dialog {
    border-radius: 20px;
    overflow: hidden;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
  }

  .help-content {
    .help-section {
      margin-bottom: 24px;

      h3 {
        color: #1e293b;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }

      p {
        color: #64748b;
        line-height: 1.6;
        margin-bottom: 12px;
      }

      ul {
        color: #64748b;
        line-height: 1.6;
        padding-left: 20px;

        li {
          margin-bottom: 8px;

          strong {
            color: #1e293b;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modern-dashboard {
    padding: 16px;
  }

  .dashboard-header {
    .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .welcome-section {
        .page-title {
          font-size: 24px;
        }

        .page-subtitle {
          font-size: 14px;
        }
      }

      .header-actions {
        width: 100%;
        justify-content: flex-start;

        .action-btn {
          flex: 1;
          height: 40px;
          font-size: 14px;
        }
      }
    }
  }

  .info-section {
    .quick-actions {
      .actions-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}
</style>