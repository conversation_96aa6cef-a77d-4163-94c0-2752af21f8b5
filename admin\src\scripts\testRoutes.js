// 路由测试执行脚本
import { runCompleteTest } from '@/utils/routeTest.js'

/**
 * 执行完整的路由测试
 */
async function executeRouteTest() {
  console.log('🚀 开始执行路由优化后的完整测试...\n')
  
  try {
    const results = await runCompleteTest({
      verbose: true,
      timeout: 8000
    })
    
    // 输出测试结果摘要
    console.log('\n🎯 测试结果摘要:')
    console.log('='.repeat(50))
    
    const { routeTest, consistency } = results
    
    console.log(`📊 路由测试结果:`)
    console.log(`   - 总路由数: ${routeTest.total}`)
    console.log(`   - 通过数: ${routeTest.passed} ✅`)
    console.log(`   - 失败数: ${routeTest.failed} ${routeTest.failed > 0 ? '❌' : '✅'}`)
    console.log(`   - 通过率: ${routeTest.summary.passRate}`)
    
    console.log(`\n🔗 菜单一致性检查:`)
    console.log(`   - 状态: ${consistency.summary}`)
    if (consistency.missing.length > 0) {
      console.log(`   - 缺失路由: ${consistency.missing.length} 个`)
    }
    if (consistency.extra.length > 0) {
      console.log(`   - 多余路由: ${consistency.extra.length} 个`)
    }
    
    // 生成优化建议
    generateOptimizationSuggestions(results)
    
    return results
  } catch (error) {
    console.error('❌ 路由测试执行失败:', error)
    throw error
  }
}

/**
 * 生成优化建议
 */
function generateOptimizationSuggestions(results) {
  const { routeTest, consistency } = results
  const suggestions = []
  
  console.log('\n💡 优化建议:')
  console.log('='.repeat(50))
  
  // 检查失败的路由
  if (routeTest.failed > 0) {
    suggestions.push({
      level: 'error',
      title: '修复失败的路由',
      description: '以下路由存在问题，需要立即修复：',
      items: routeTest.errors.map(error => `${error.path}: ${error.error}`)
    })
  }
  
  // 检查菜单一致性
  if (consistency.missing.length > 0) {
    suggestions.push({
      level: 'warning',
      title: '修复缺失的路由',
      description: '菜单中引用了不存在的路由：',
      items: consistency.missing
    })
  }
  
  if (consistency.extra.length > 0) {
    suggestions.push({
      level: 'info',
      title: '考虑整理多余的路由',
      description: '以下路由存在但未在菜单中使用：',
      items: consistency.extra
    })
  }
  
  // 性能优化建议
  if (routeTest.total > 50) {
    suggestions.push({
      level: 'info',
      title: '考虑路由懒加载优化',
      description: '路由数量较多，建议对非核心路由实施懒加载优化'
    })
  }
  
  // 输出建议
  if (suggestions.length === 0) {
    console.log('✨ 恭喜！路由配置完美，无需优化')
  } else {
    suggestions.forEach((suggestion, index) => {
      const icon = suggestion.level === 'error' ? '❌' : 
                  suggestion.level === 'warning' ? '⚠️' : 'ℹ️'
      
      console.log(`\n${index + 1}. ${icon} ${suggestion.title}`)
      console.log(`   ${suggestion.description}`)
      
      if (suggestion.items) {
        suggestion.items.forEach(item => {
          console.log(`   • ${item}`)
        })
      }
    })
  }
}

/**
 * 生成测试报告文件
 */
function generateTestReport(results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const reportContent = `
# 路由优化测试报告

## 测试概况
- 测试时间: ${new Date().toLocaleString()}
- 测试范围: 全部路由及菜单一致性
- 优化版本: index-optimized.js

## 路由测试结果
${JSON.stringify(results.routeTest.summary, null, 2)}

## 菜单一致性检查
${JSON.stringify(results.consistency, null, 2)}

## 详细结果
${JSON.stringify(results, null, 2)}
`

  // 注意：在实际环境中，这里应该写入文件系统
  console.log(`\n📄 测试报告已生成: route-test-report-${timestamp}.md`)
  console.log('报告内容:')
  console.log(reportContent)
  
  return reportContent
}

// 如果直接运行此脚本
if (typeof window === 'undefined') {
  // Node.js 环境
  executeRouteTest().then(results => {
    generateTestReport(results)
    process.exit(results.routeTest.failed > 0 ? 1 : 0)
  }).catch(error => {
    console.error('测试执行失败:', error)
    process.exit(1)
  })
} else {
  // 浏览器环境
  window.executeRouteTest = executeRouteTest
  window.generateTestReport = generateTestReport
}

export {
  executeRouteTest,
  generateOptimizationSuggestions,
  generateTestReport
}