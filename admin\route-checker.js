/**
 * 路由检查脚本 - 检查所有Vue组件中的路由跳转
 */

import fs from 'fs';
import path from 'path';

// 旧路由模式 -> 新路由模式的映射
const routeMapping = {
  '/user/': '/admin/users/',
  '/users': '/admin/users/list',
  '/user/add': '/admin/users/list',
  '/user/analytics': '/admin/users/analytics',
  '/community/': '/admin/community/',
  '/community/groups': '/admin/community/groups',
  '/community/templates': '/admin/community/templates',
  '/agent/': '/admin/agents/',
  '/agent/list': '/admin/agents/list',
  '/finance/': '/admin/finance/',
  '/finance': '/admin/finance/dashboard',
  '/payment/': '/admin/payment/',
  '/payment': '/admin/payment/settings',
  '/orders': '/admin/orders',
  '/system/': '/admin/system/',
  '/system': '/admin/system/settings',
  '/distribution': '/admin/promotion/distributors',
  '/promotion': '/admin/promotion/links',
  '/anti-block': '/admin/anti-block/dashboard',
  '/permission': '/admin/permissions/roles',
  '/dashboard/reports': '/admin/analytics'
};

// 需要检查的路由模式
const problemRoutes = [
  /router\.push\(['\"`]\/[^\/]/g, // 匹配 router.push('/xxx') 这样的旧路由
  /\$router\.push\(['\"`]\/[^\/]/g, // 匹配 $router.push('/xxx') 这样的旧路由
  /@click="[^"]*push\(['\"`]\/[^\/]/g, // 匹配点击事件中的路由跳转
];

function findVueFiles(dir) {
  const result = [];
  
  function traverse(currentDir) {
    try {
      const files = fs.readdirSync(currentDir);
      
      for (const file of files) {
        const filePath = path.join(currentDir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
          traverse(filePath);
        } else if (file.endsWith('.vue')) {
          result.push(filePath);
        }
      }
    } catch (error) {
      // 忽略权限错误
    }
  }
  
  traverse(dir);
  return result;
}

function checkFileRoutes(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 检查各种路由模式
    problemRoutes.forEach((pattern, index) => {
      const matches = content.match(pattern) || [];
      matches.forEach(match => {
        // 排除已经是正确格式的路由 (/admin/xxx)
        if (!match.includes('/admin/') && !match.includes('/data-screen')) {
          issues.push({
            type: 'route',
            pattern: pattern.toString(),
            match: match,
            suggestion: getSuggestion(match)
          });
        }
      });
    });
    
    return issues;
  } catch (error) {
    return [];
  }
}

function getSuggestion(route) {
  // 从匹配的字符串中提取路由路径
  const routeMatch = route.match(/['\"`]([^'\"`]+)['\"`]/);
  if (!routeMatch) return '无法解析路由';
  
  const routePath = routeMatch[1];
  
  // 查找映射
  for (const [oldRoute, newRoute] of Object.entries(routeMapping)) {
    if (routePath.includes(oldRoute) || routePath === oldRoute.replace('/', '')) {
      return `建议修改为: ${newRoute}`;
    }
  }
  
  return '请检查路由是否正确';
}

async function main() {
  console.log('🔍 开始检查Vue组件中的路由...\n');
  
  try {
    const vueFiles = findVueFiles('src');
    let totalIssues = 0;
    
    for (const file of vueFiles) {
      const issues = checkFileRoutes(file);
      if (issues.length > 0) {
        console.log(`📁 ${file}:`);
        issues.forEach(issue => {
          console.log(`  ❌ ${issue.match}`);
          console.log(`     💡 ${issue.suggestion}`);
        });
        console.log('');
        totalIssues += issues.length;
      }
    }
    
    if (totalIssues === 0) {
      console.log('✅ 未发现路由问题！');
    } else {
      console.log(`⚠️  发现 ${totalIssues} 个可能的路由问题`);
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  }
}

// 直接运行
main();