/* empty css             *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                *//* empty css                  */import{z as e,m as a,A as l,G as t,C as s,E as r,q as o,r as i,c as n,M as u,w as d,l as c,F as m,Y as p,ag as g,o as f,D as h,W as v,B as b}from"./vue-vendor-BcnDv-68.js";import{a3 as _,a4 as w,a5 as y,a6 as k,_ as V,aL as U,a2 as C,a1 as j,Z as S,W as z,X as D,ah as x,V as E,aB as T,bz as N,bR as F,aD as L,a9 as B,aE as R,at as A,b1 as q,bS as I,b3 as P,aa as O,ab as M,az as $,aF as G,af as W,ax as K,au as Q,ag as X,u as Y,s as Z,ai as H,aj as J,aG as ee,ak as ae,a7 as le,an as te}from"./element-plus-C2UshkXo.js";/* empty css                   *//* empty css                     *//* empty css                       *//* empty css                 */import{_ as se}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const re={__name:"UserDialog",props:{modelValue:{type:Boolean,default:!1},userData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{expose:a,emit:l}){a();const t=e,s=l,r=i(),o=i(!1),c=n({get:()=>t.modelValue,set:e=>s("update:modelValue",e)}),m=n(()=>!!t.userData?.id),p=u({username:"",realName:"",email:"",phone:"",role:"user",status:"active",password:""});d(()=>t.userData,e=>{e&&Object.keys(e).length>0?Object.assign(p,{username:e.username||"",realName:e.realName||"",email:e.email||"",phone:e.phone||"",role:e.role||"user",status:e.status||"active",password:""}):Object.assign(p,{username:"",realName:"",email:"",phone:"",role:"user",status:"active",password:""})},{immediate:!0,deep:!0});const g=()=>{r.value?.resetFields(),c.value=!1},f={props:t,emit:s,formRef:r,loading:o,visible:c,isEdit:m,form:p,rules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],realName:[{required:!0,message:"请输入真实姓名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少6位",trigger:"blur"}]},handleSubmit:async()=>{try{await r.value.validate(),o.value=!0,await new Promise(e=>setTimeout(e,1e3)),D.success(m.value?"用户更新成功":"用户创建成功"),s("success"),g()}catch(e){}finally{o.value=!1}},handleClose:g,ref:i,reactive:u,computed:n,watch:d,get ElMessage(){return D}};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}},oe={class:"dialog-footer"};const ie=se(re,[["render",function(i,n,u,d,c,m){const p=w,g=_,f=k,h=y,v=U,b=V,D=C,x=j,E=z;return a(),e(E,{modelValue:d.visible,"onUpdate:modelValue":n[7]||(n[7]=e=>d.visible=e),title:d.isEdit?"编辑用户":"添加用户",width:"600px","before-close":d.handleClose,class:"modern-dialog"},{footer:l(()=>[o("div",oe,[t(x,{onClick:d.handleClose},{default:l(()=>n[10]||(n[10]=[r("取消",-1)])),_:1,__:[10]}),t(x,{type:"primary",onClick:d.handleSubmit,loading:d.loading},{default:l(()=>[r(S(d.isEdit?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:l(()=>[t(D,{ref:"formRef",model:d.form,rules:d.rules,"label-width":"100px",size:"large"},{default:l(()=>[t(g,{label:"用户名",prop:"username"},{default:l(()=>[t(p,{modelValue:d.form.username,"onUpdate:modelValue":n[0]||(n[0]=e=>d.form.username=e),placeholder:"请输入用户名",disabled:d.isEdit},null,8,["modelValue","disabled"])]),_:1}),t(g,{label:"真实姓名",prop:"realName"},{default:l(()=>[t(p,{modelValue:d.form.realName,"onUpdate:modelValue":n[1]||(n[1]=e=>d.form.realName=e),placeholder:"请输入真实姓名"},null,8,["modelValue"])]),_:1}),t(g,{label:"邮箱",prop:"email"},{default:l(()=>[t(p,{modelValue:d.form.email,"onUpdate:modelValue":n[2]||(n[2]=e=>d.form.email=e),placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1}),t(g,{label:"手机号",prop:"phone"},{default:l(()=>[t(p,{modelValue:d.form.phone,"onUpdate:modelValue":n[3]||(n[3]=e=>d.form.phone=e),placeholder:"请输入手机号码"},null,8,["modelValue"])]),_:1}),t(g,{label:"角色",prop:"role"},{default:l(()=>[t(h,{modelValue:d.form.role,"onUpdate:modelValue":n[4]||(n[4]=e=>d.form.role=e),placeholder:"请选择角色",style:{width:"100%"}},{default:l(()=>[t(f,{label:"管理员",value:"admin"}),t(f,{label:"分站管理员",value:"substation"}),t(f,{label:"代理商",value:"agent"}),t(f,{label:"分销员",value:"distributor"}),t(f,{label:"群主",value:"group_owner"}),t(f,{label:"普通用户",value:"user"})]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"状态",prop:"status"},{default:l(()=>[t(b,{modelValue:d.form.status,"onUpdate:modelValue":n[5]||(n[5]=e=>d.form.status=e)},{default:l(()=>[t(v,{label:"active"},{default:l(()=>n[8]||(n[8]=[r("启用",-1)])),_:1,__:[8]}),t(v,{label:"inactive"},{default:l(()=>n[9]||(n[9]=[r("禁用",-1)])),_:1,__:[9]})]),_:1},8,["modelValue"])]),_:1}),d.isEdit?s("",!0):(a(),e(g,{key:0,label:"密码",prop:"password"},{default:l(()=>[t(p,{modelValue:d.form.password,"onUpdate:modelValue":n[6]||(n[6]=e=>d.form.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1}))]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}],["__scopeId","data-v-9ff371d6"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/user/components/UserDialog.vue"]]),ne={class:"modern-user-list"},ue={class:"page-header"},de={class:"header-content"},ce={class:"header-left"},me={class:"header-icon"},pe={class:"header-actions"},ge={class:"stats-section"},fe={class:"stats-container"},he={class:"stat-content"},ve={class:"stat-value"},be={class:"stat-label"},_e={class:"filter-section"},we={class:"filter-content"},ye={class:"filter-left"},ke={class:"filter-right"},Ve={class:"table-section"},Ue={class:"table-header"},Ce={class:"table-title"},je={class:"table-actions"},Se={class:"user-info"},ze={class:"user-details"},De={class:"user-name"},xe={class:"user-email"},Ee={class:"time-info"},Te={key:0,class:"time-info"},Ne={key:1,class:"text-muted"},Fe={class:"action-buttons"},Le={class:"pagination-wrapper"};const Be=se({__name:"UserList",setup(e,{expose:a}){a();const l=g(),t=i(!1),s=i([]),r=i([]),o=i(!1),n=i({}),d=i(0),c=u({keyword:"",role:"",status:""}),m=u({page:1,size:20}),p=i([{key:"total",label:"总用户数",value:1234,icon:"UserFilled",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"ArrowUp",change:"+12.5%"},{key:"active",label:"活跃用户",value:1156,icon:"User",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"ArrowUp",change:"****%"},{key:"distributors",label:"分销员",value:89,icon:"Share",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"ArrowUp",change:"+15.2%"},{key:"agents",label:"代理商",value:23,icon:"Avatar",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"ArrowUp",change:"****%"}]),h=[{id:1,username:"admin",realName:"系统管理员",email:"<EMAIL>",phone:"13800138000",role:"admin",status:"active",avatar:"",created_at:"2024-01-01 10:00:00",last_login_at:"2024-08-06 15:30:00"},{id:2,username:"zhangsan",realName:"张三",email:"<EMAIL>",phone:"13800138001",role:"agent",status:"active",avatar:"",created_at:"2024-02-15 14:20:00",last_login_at:"2024-08-06 12:15:00"},{id:3,username:"lisi",realName:"李四",email:"<EMAIL>",phone:"13800138002",role:"distributor",status:"active",avatar:"",created_at:"2024-03-10 09:30:00",last_login_at:"2024-08-05 18:45:00"},{id:4,username:"wangwu",realName:"王五",email:"<EMAIL>",phone:"13800138003",role:"user",status:"inactive",avatar:"",created_at:"2024-04-20 16:10:00",last_login_at:null}],v=()=>{s.value=[...h],d.value=h.length};f(()=>{v()});const b={router:l,loading:t,userList:s,selectedUsers:r,editDialogVisible:o,currentUser:n,total:d,searchForm:c,pagination:m,userStats:p,mockUserList:h,getRoleLabel:e=>({admin:"管理员",substation:"分站管理员",agent:"代理商",distributor:"分销员",group_owner:"群主",user:"普通用户"}[e]||"未知角色"),getRoleTagType:e=>({admin:"danger",substation:"warning",agent:"primary",distributor:"success",group_owner:"info",user:""}[e]||""),formatDate:e=>e?new Date(e).toLocaleDateString("zh-CN"):"",formatTime:e=>e?new Date(e).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):"",handleCreate:()=>{n.value={},o.value=!0},handleEdit:e=>{n.value={...e},o.value=!0},handleDelete:async e=>{try{await X.confirm("确定要删除该用户吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"});const a=s.value.findIndex(a=>a.id===e.id);a>-1&&(s.value.splice(a,1),d.value--),D.success("删除成功")}catch(a){"cancel"!==a&&D.error("删除失败")}},handleToggleStatus:async e=>{const a="active"===e.status?"inactive":"active",l="inactive"===a?"禁用":"启用";try{await X.confirm(`确定要${l}该用户吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.status=a,D.success(`${l}成功`)}catch(t){"cancel"!==t&&D.error("操作失败")}},handleSelectionChange:e=>{r.value=e},handleBatchDelete:async()=>{if(0!==r.value.length)try{await X.confirm(`确定要批量删除选中的 ${r.value.length} 个用户吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),r.value.forEach(e=>{const a=s.value.findIndex(a=>a.id===e.id);a>-1&&(s.value.splice(a,1),d.value--)}),r.value=[],D.success("批量删除成功")}catch(e){"cancel"!==e&&D.error("批量删除失败")}else D.warning("请选择要删除的用户")},handleSearch:()=>{t.value=!0,setTimeout(()=>{let e=[...h];c.keyword&&(e=e.filter(e=>e.username.includes(c.keyword)||e.email.includes(c.keyword)||e.phone.includes(c.keyword))),c.role&&(e=e.filter(e=>e.role===c.role)),c.status&&(e=e.filter(e=>e.status===c.status)),s.value=e,d.value=e.length,t.value=!1},500)},handleReset:()=>{c.keyword="",c.role="",c.status="",s.value=[...h],d.value=h.length,D.info("搜索条件已重置")},handlePageChange:e=>{m.page=e},handleSizeChange:e=>{m.size=e,m.page=1},handleExport:async()=>{try{D.success("导出功能开发中...")}catch(e){D.error("导出失败")}},handleEditSuccess:()=>{s.value=[...h],D.success("用户信息更新成功")},initData:v,ref:i,reactive:u,onMounted:f,get useRouter(){return g},get ElMessage(){return D},get ElMessageBox(){return X},get UserFilled(){return Q},get Download(){return K},get Plus(){return W},get Search(){return G},get RefreshLeft(){return $},get Edit(){return M},get Delete(){return O},get Lock(){return P},get Unlock(){return I},get TrendCharts(){return q},get ArrowUp(){return A},get ArrowDown(){return R},get User(){return B},get Avatar(){return L},get Management(){return F},get Share(){return N},get Star(){return T},UserDialog:ie};return Object.defineProperty(b,"__isScriptSetup",{enumerable:!1,value:!0}),b}},[["render",function(i,n,u,d,g,f){const _=x,V=j,U=w,C=k,z=y,D=E,T=ae,N=J,F=ee,L=H,B=te,R=le;return a(),c("div",ne,[o("div",ue,[o("div",de,[o("div",ce,[o("div",me,[t(_,{size:"24"},{default:l(()=>[t(d.UserFilled)]),_:1})]),n[6]||(n[6]=o("div",{class:"header-text"},[o("h1",null,"用户管理"),o("p",null,"管理平台所有用户信息，包括用户权限、状态和基本资料")],-1))]),o("div",pe,[t(V,{onClick:d.handleExport,class:"action-btn secondary"},{default:l(()=>[t(_,null,{default:l(()=>[t(d.Download)]),_:1}),n[7]||(n[7]=r(" 导出数据 ",-1))]),_:1,__:[7]}),t(V,{type:"primary",onClick:d.handleCreate,class:"action-btn primary"},{default:l(()=>[t(_,null,{default:l(()=>[t(d.Plus)]),_:1}),n[8]||(n[8]=r(" 添加用户 ",-1))]),_:1,__:[8]})])])]),o("div",ge,[o("div",fe,[(a(!0),c(m,null,p(d.userStats,s=>(a(),c("div",{class:"stat-card",key:s.key},[o("div",{class:"stat-icon",style:Y({background:s.color})},[t(_,{size:"20"},{default:l(()=>[(a(),e(h(s.icon)))]),_:2},1024)],4),o("div",he,[o("div",ve,S(s.value),1),o("div",be,S(s.label),1)]),o("div",{class:Z(["stat-trend",s.trend])},[t(_,{size:"14"},{default:l(()=>[(a(),e(h(s.trendIcon)))]),_:2},1024),o("span",null,S(s.change),1)],2)]))),128))])]),o("div",_e,[t(D,{class:"filter-card",shadow:"never"},{default:l(()=>[o("div",we,[o("div",ye,[t(U,{modelValue:d.searchForm.keyword,"onUpdate:modelValue":n[0]||(n[0]=e=>d.searchForm.keyword=e),placeholder:"搜索用户名、邮箱、手机号","prefix-icon":"Search",clearable:"",class:"search-input",onKeyup:v(d.handleSearch,["enter"])},null,8,["modelValue"]),t(z,{modelValue:d.searchForm.role,"onUpdate:modelValue":n[1]||(n[1]=e=>d.searchForm.role=e),placeholder:"用户角色",clearable:"",class:"filter-select"},{default:l(()=>[t(C,{label:"全部角色",value:""}),t(C,{label:"管理员",value:"admin"}),t(C,{label:"分站管理员",value:"substation"}),t(C,{label:"代理商",value:"agent"}),t(C,{label:"分销员",value:"distributor"}),t(C,{label:"群主",value:"group_owner"}),t(C,{label:"普通用户",value:"user"})]),_:1},8,["modelValue"]),t(z,{modelValue:d.searchForm.status,"onUpdate:modelValue":n[2]||(n[2]=e=>d.searchForm.status=e),placeholder:"用户状态",clearable:"",class:"filter-select"},{default:l(()=>[t(C,{label:"全部状态",value:""}),t(C,{label:"正常",value:"active"}),t(C,{label:"禁用",value:"inactive"})]),_:1},8,["modelValue"])]),o("div",ke,[t(V,{onClick:d.handleSearch,type:"primary",class:"search-btn"},{default:l(()=>[t(_,null,{default:l(()=>[t(d.Search)]),_:1}),n[9]||(n[9]=r(" 搜索 ",-1))]),_:1,__:[9]}),t(V,{onClick:d.handleReset,class:"reset-btn"},{default:l(()=>[t(_,null,{default:l(()=>[t(d.RefreshLeft)]),_:1}),n[10]||(n[10]=r(" 重置 ",-1))]),_:1,__:[10]})])])]),_:1})]),o("div",Ve,[t(D,{class:"table-card",shadow:"never"},{header:l(()=>[o("div",Ue,[o("div",Ce,[n[11]||(n[11]=o("span",null,"用户列表",-1)),t(T,{size:"small",type:"info"},{default:l(()=>[r("共 "+S(d.total)+" 条记录",1)]),_:1})]),o("div",je,[d.selectedUsers.length>0?(a(),e(V,{key:0,onClick:d.handleBatchDelete,type:"danger",size:"small",plain:""},{default:l(()=>[t(_,null,{default:l(()=>[t(d.Delete)]),_:1}),r(" 批量删除 ("+S(d.selectedUsers.length)+") ",1)]),_:1})):s("",!0)])])]),default:l(()=>[b((a(),e(L,{data:d.userList,onSelectionChange:d.handleSelectionChange,class:"modern-table",stripe:"",border:""},{default:l(()=>[t(N,{type:"selection",width:"55",align:"center"}),t(N,{label:"用户信息","min-width":"200"},{default:l(({row:e})=>[o("div",Se,[t(F,{size:40,src:e.avatar,class:"user-avatar"},{default:l(()=>[t(_,null,{default:l(()=>[t(d.UserFilled)]),_:1})]),_:2},1032,["src"]),o("div",ze,[o("div",De,S(e.username),1),o("div",xe,S(e.email),1)])])]),_:1}),t(N,{label:"真实姓名",prop:"realName",width:"100"}),t(N,{label:"手机号码",prop:"phone",width:"120"}),t(N,{label:"用户角色",width:"120"},{default:l(({row:e})=>[t(T,{type:d.getRoleTagType(e.role),size:"small"},{default:l(()=>[r(S(d.getRoleLabel(e.role)),1)]),_:2},1032,["type"])]),_:1}),t(N,{label:"状态",width:"80"},{default:l(({row:e})=>[t(T,{type:"active"===e.status?"success":"danger",size:"small"},{default:l(()=>[r(S("active"===e.status?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(N,{label:"注册时间",width:"160"},{default:l(({row:e})=>[o("div",Ee,[o("div",null,S(d.formatDate(e.created_at)),1),o("small",null,S(d.formatTime(e.created_at)),1)])]),_:1}),t(N,{label:"最后登录",width:"160"},{default:l(({row:e})=>[e.last_login_at?(a(),c("div",Te,[o("div",null,S(d.formatDate(e.last_login_at)),1),o("small",null,S(d.formatTime(e.last_login_at)),1)])):(a(),c("span",Ne,"从未登录"))]),_:1}),t(N,{label:"操作",width:"200",fixed:"right"},{default:l(({row:s})=>[o("div",Fe,[t(V,{onClick:e=>d.handleEdit(s),type:"primary",size:"small",plain:""},{default:l(()=>[t(_,null,{default:l(()=>[t(d.Edit)]),_:1}),n[12]||(n[12]=r(" 编辑 ",-1))]),_:2,__:[12]},1032,["onClick"]),t(V,{onClick:e=>d.handleToggleStatus(s),type:"active"===s.status?"warning":"success",size:"small",plain:""},{default:l(()=>[t(_,null,{default:l(()=>[(a(),e(h("active"===s.status?"Lock":"Unlock")))]),_:2},1024),r(" "+S("active"===s.status?"禁用":"启用"),1)]),_:2},1032,["onClick","type"]),t(V,{onClick:e=>d.handleDelete(s),type:"danger",size:"small",plain:""},{default:l(()=>[t(_,null,{default:l(()=>[t(d.Delete)]),_:1}),n[13]||(n[13]=r(" 删除 ",-1))]),_:2,__:[13]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[R,d.loading]]),o("div",Le,[t(B,{"current-page":d.pagination.page,"onUpdate:currentPage":n[3]||(n[3]=e=>d.pagination.page=e),"page-size":d.pagination.size,"onUpdate:pageSize":n[4]||(n[4]=e=>d.pagination.size=e),total:d.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d.handleSizeChange,onCurrentChange:d.handlePageChange,class:"modern-pagination"},null,8,["current-page","page-size","total"])])]),_:1})]),t(d.UserDialog,{modelValue:d.editDialogVisible,"onUpdate:modelValue":n[5]||(n[5]=e=>d.editDialogVisible=e),"user-data":d.currentUser,onSuccess:d.handleEditSuccess},null,8,["modelValue","user-data"])])}],["__scopeId","data-v-74942c68"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/user/UserList.vue"]]);export{Be as default};
