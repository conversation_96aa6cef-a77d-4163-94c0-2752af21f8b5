/* empty css             *//* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{l as e,G as a,A as t,r as s,M as n,o as i,m as l,q as r,B as o,z as d,E as p}from"./vue-vendor-BcnDv-68.js";import{P as c}from"./PageLayout-DKvOdnm6.js";import{_ as u}from"./index-eUTsTR3J.js";import{X as m,ag as g,V as h,ai as f,aj as _,ak as j,Z as y,a1 as b,a7 as v,an as z}from"./element-plus-C2UshkXo.js";/* empty css                           */import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const C={class:"page-container"},w={class:"content-wrapper"},k={class:"card-body"},x={class:"pagination-wrapper"};const P=u({__name:"FileManagement",setup(e,{expose:a}){a();const t=s(!1),l=s([]),r=n({current:1,size:20,total:0}),o=async()=>{t.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),l.value=[{id:1,name:"示例数据1",status:"active",created_at:"2024-01-01 12:00:00"},{id:2,name:"示例数据2",status:"inactive",created_at:"2024-01-02 12:00:00"}],r.total=2}catch(e){m.error("加载数据失败")}finally{t.value=!1}};i(()=>{o()});const d={loading:t,tableData:l,pagination:r,handleRefresh:()=>{o()},handleAdd:()=>{m.info("新增功能待实现")},handleEdit:e=>{m.info(`编辑功能待实现: ${e.name}`)},handleDelete:async e=>{try{await g.confirm(`确定要删除 "${e.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),m.success("删除成功"),o()}catch{m.info("已取消删除")}},handleSizeChange:e=>{r.size=e,o()},handleCurrentChange:e=>{r.current=e,o()},loadData:o,ref:s,reactive:n,onMounted:i,get ElMessage(){return m},get ElMessageBox(){return g},PageLayout:c};return Object.defineProperty(d,"__isScriptSetup",{enumerable:!1,value:!0}),d}},[["render",function(s,n,i,c,u,m){const g=b,P=_,D=j,M=f,S=z,E=h,A=v;return l(),e("div",C,[a(c.PageLayout,{title:"文件管理",subtitle:"管理系统文件和资源",loading:c.loading},{actions:t(()=>[a(g,{class:"modern-btn secondary",onClick:c.handleRefresh},{default:t(()=>n[2]||(n[2]=[r("i",{class:"el-icon-refresh"},null,-1),p(" 刷新数据 ",-1)])),_:1,__:[2]}),a(g,{class:"modern-btn primary",onClick:c.handleAdd},{default:t(()=>n[3]||(n[3]=[r("i",{class:"el-icon-plus"},null,-1),p(" 新增 ",-1)])),_:1,__:[3]})]),default:t(()=>[r("div",w,[a(E,{class:"modern-card"},{default:t(()=>[n[6]||(n[6]=r("div",{class:"card-header"},[r("h3",null,"文件管理"),r("p",{class:"text-muted"},"管理系统文件和资源")],-1)),r("div",k,[o((l(),d(M,{data:c.tableData,style:{width:"100%"},class:"modern-table"},{default:t(()=>[a(P,{prop:"id",label:"ID",width:"80"}),a(P,{prop:"name",label:"名称","min-width":"150"}),a(P,{prop:"status",label:"状态",width:"100"},{default:t(({row:e})=>[a(D,{type:"active"===e.status?"success":"info",size:"small"},{default:t(()=>[p(y("active"===e.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(P,{prop:"created_at",label:"创建时间",width:"180"}),a(P,{label:"操作",width:"200",fixed:"right"},{default:t(({row:e})=>[a(g,{type:"primary",size:"small",onClick:a=>c.handleEdit(e)},{default:t(()=>n[4]||(n[4]=[p(" 编辑 ",-1)])),_:2,__:[4]},1032,["onClick"]),a(g,{type:"danger",size:"small",onClick:a=>c.handleDelete(e)},{default:t(()=>n[5]||(n[5]=[p(" 删除 ",-1)])),_:2,__:[5]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[A,c.loading]]),r("div",x,[a(S,{"current-page":c.pagination.current,"onUpdate:currentPage":n[0]||(n[0]=e=>c.pagination.current=e),"page-size":c.pagination.size,"onUpdate:pageSize":n[1]||(n[1]=e=>c.pagination.size=e),total:c.pagination.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:c.handleSizeChange,onCurrentChange:c.handleCurrentChange},null,8,["current-page","page-size","total"])])])]),_:1,__:[6]})])]),_:1},8,["loading"])])}],["__scopeId","data-v-94492ed2"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/FileManagement.vue"]]);export{P as default};
