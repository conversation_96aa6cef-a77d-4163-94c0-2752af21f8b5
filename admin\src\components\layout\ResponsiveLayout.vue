<template>
  <div class="responsive-layout" :class="{ 'mobile': isMobile }">
    <!-- 桌面端侧边栏 -->
    <aside 
      v-if="!isMobile" 
      class="sidebar enhanced-sidebar"
      :class="sidebarClasses"
      @mouseenter="handleSidebarMouseEnter"
      @mouseleave="handleSidebarMouseLeave"
    >
      <div class="sidebar-header enhanced">
        <div class="logo-container modern">
          <transition name="logo-switch" mode="out-in">
            <img 
              :key="sidebarCollapsed ? 'mini' : 'full'"
              :src="sidebarCollapsed ? '/assets/logo-mini.png' : '/assets/logo-full.png'" 
              class="logo enhanced"
              :alt="appName"
            />
          </transition>
        </div>
        <button 
          class="collapse-btn modern"
          @click="toggleSidebar"
          :title="sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'"
          :aria-label="sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'"
        >
          <transition name="icon-rotate" mode="out-in">
            <el-icon :key="sidebarCollapsed">
              <component :is="sidebarCollapsed ? 'ArrowRight' : 'ArrowLeft'" />
            </el-icon>
          </transition>
        </button>
      </div>
      
      <nav class="sidebar-nav">
        <div v-for="item in menuItems" :key="item.path" class="nav-item">
          <router-link 
            :to="item.path" 
            class="nav-link"
            :class="{ 
              'active': isActive(item.path),
              'has-children': item.children 
            }"
            @click="handleNavClick(item)"
          >
            <i :class="item.icon" class="nav-icon"></i>
            <span v-if="!sidebarCollapsed" class="nav-text">{{ item.title }}</span>
            
            <i 
              v-if="item.children && !sidebarCollapsed" 
              class="arrow-icon"
              :class="{ 'rotate': expandedItems.includes(item.path) }"
            ></i>
          </router-link>
          
          <transition name="expand">
            <div 
              v-if="item.children && !sidebarCollapsed" 
              class="submenu"
              v-show="expandedItems.includes(item.path)"
            >
              <router-link 
                v-for="child in item.children" 
                :key="child.path"
                :to="child.path"
                class="submenu-link"
                :class="{ 'active': isActive(child.path) }"
              >
                <span>{{ child.title }}</span>
              </router-link>
            </div>
          </transition>
        </div>
      </nav>
    </aside>

    <!-- 移动端导航 -->
    <mobile-navigation v-if="isMobile" />

    <!-- 主内容区域 -->
    <main class="main-content enhanced" :class="mainContentClasses">
      <div class="content-header enhanced">
        <div class="breadcrumb-container">
          <nav class="breadcrumb-nav">
            <router-link 
              v-for="(crumb, index) in breadcrumbs" 
              :key="index"
              :to="crumb.path"
              class="breadcrumb-item"
              :class="{ 'active': index === breadcrumbs.length - 1 }"
            >
              {{ crumb.title }}
              <el-icon v-if="index < breadcrumbs.length - 1" class="breadcrumb-separator">
                <ArrowRight />
              </el-icon>
            </router-link>
          </nav>
        </div>
        <h1 class="page-title enhanced">{{ currentPageTitle }}</h1>
        <div class="header-actions enhanced">
          <slot name="header-actions"></slot>
        </div>
      </div>
      
      <div class="content-body">
        <slot></slot>
      </div>
    </main>

    <!-- 移动端底部导航 -->
    <nav v-if="isMobile" class="mobile-bottom-nav">
      <router-link 
        v-for="item in bottomNavItems" 
        :key="item.path"
        :to="item.path"
        class="bottom-nav-item"
        :class="{ 'active': isActive(item.path) }"
      >
        <i :class="item.icon" class="bottom-nav-icon"></i>
        <span class="bottom-nav-text">{{ item.title }}</span>
      </router-link>
    </nav>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ArrowRight, ArrowLeft } from '@element-plus/icons-vue'
import MobileNavigation from './MobileNavigation.vue'

export default {
  name: 'ResponsiveLayout',
  components: {
    MobileNavigation
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const userStore = useUserStore()
    
    const isMobile = ref(false)
    const sidebarCollapsed = ref(false)
    const sidebarHovered = ref(false)
    const expandedItems = ref([])
    const appName = '晨鑫流量变现系统'
    const isLoading = ref(false)
    
    const menuItems = computed(() => [
      {
        title: '首页',
        path: '/dashboard',
        icon: 'icon-dashboard'
      },
      {
        title: '用户管理',
        path: '/users',
        icon: 'icon-users'
      },
      {
        title: '群组管理',
        path: '/groups',
        icon: 'icon-groups',
        children: [
          { title: '群组列表', path: '/groups/list' },
          { title: '创建群组', path: '/groups/create' }
        ]
      },
      {
        title: '财务管理',
        path: '/finance',
        icon: 'icon-finance'
      },
      {
        title: '系统设置',
        path: '/settings',
        icon: 'icon-settings'
      }
    ])
    
    const bottomNavItems = computed(() => [
      {
        title: '首页',
        path: '/dashboard',
        icon: 'icon-home'
      },
      {
        title: '用户',
        path: '/users',
        icon: 'icon-users'
      },
      {
        title: '群组',
        path: '/groups',
        icon: 'icon-groups'
      },
      {
        title: '财务',
        path: '/finance',
        icon: 'icon-finance'
      },
      {
        title: '我的',
        path: '/profile',
        icon: 'icon-user'
      }
    ])
    
    const currentPageTitle = computed(() => {
      const matched = route.matched
      if (matched.length > 0) {
        return matched[matched.length - 1].meta?.title || '页面'
      }
      return '页面'
    })
    
    const sidebarClasses = computed(() => ({
      'collapsed': sidebarCollapsed.value,
      'hovered': sidebarHovered.value,
      'enhanced-sidebar': true
    }))
    
    const mainContentClasses = computed(() => ({
      'with-sidebar': !isMobile.value,
      'sidebar-collapsed': sidebarCollapsed.value,
      'enhanced': true
    }))
    
    const breadcrumbs = computed(() => {
      const matched = route.matched.filter(item => item.meta?.title)
      return matched.map(item => ({
        title: item.meta.title,
        path: item.path
      }))
    })
    
    const checkMobile = () => {
      isMobile.value = window.innerWidth < 768
    }
    
    const toggleSidebar = () => {
      sidebarCollapsed.value = !sidebarCollapsed.value
      localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value)
    }
    
    const handleNavClick = (item) => {
      if (item.children) {
        const index = expandedItems.value.indexOf(item.path)
        if (index > -1) {
          expandedItems.value.splice(index, 1)
        } else {
          expandedItems.value.push(item.path)
        }
      }
    }
    
    const isActive = (path) => {
      return route.path.startsWith(path)
    }
    
    const handleSidebarMouseEnter = () => {
      if (sidebarCollapsed.value) {
        sidebarHovered.value = true
      }
    }
    
    const handleSidebarMouseLeave = () => {
      sidebarHovered.value = false
    }
    
    onMounted(() => {
      checkMobile()
      const collapsed = localStorage.getItem('sidebarCollapsed')
      sidebarCollapsed.value = collapsed === 'true'

      // 使用安全的事件监听器
      if (window && typeof window.addEventListener === 'function') {
        window.addEventListener('resize', checkMobile)
      }
    })

    onUnmounted(() => {
      // 安全地移除事件监听器
      if (window && typeof window.removeEventListener === 'function') {
        window.removeEventListener('resize', checkMobile)
      }
    })
    
    // 监听路由变化，自动更新页面标题
    watch(route, () => {
      document.title = `${currentPageTitle.value} - ${appName}`
    }, { immediate: true })
    
    return {
      isMobile,
      sidebarCollapsed,
      sidebarHovered,
      expandedItems,
      appName,
      isLoading,
      menuItems,
      bottomNavItems,
      currentPageTitle,
      sidebarClasses,
      mainContentClasses,
      breadcrumbs,
      toggleSidebar,
      handleNavClick,
      handleSidebarMouseEnter,
      handleSidebarMouseLeave,
      isActive,
      ArrowRight,
      ArrowLeft
    }
  }
}
</script>

<style lang="scss" scoped>
.responsive-layout {
  display: flex;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.responsive-layout.mobile {
  flex-direction: column;
  padding-top: 56px; // 移动端顶部导航高度
  padding-bottom: 56px; // 移动端底部导航高度
}

// 现代化侧边栏样式
.sidebar {
  width: 240px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: all var(--duration-normal) var(--ease-out);
  overflow: hidden;
  position: relative;
  
  &.collapsed {
    width: 64px;
    
    &:hover,
    &.hovered {
      width: 240px;
      z-index: var(--z-dropdown);
      
      .nav-text {
        opacity: 1;
        transform: translateX(0);
      }
    }
  }
  
  &.enhanced-sidebar {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      width: 1px;
      background: linear-gradient(180deg, transparent, var(--color-primary), transparent);
      opacity: 0.3;
    }
  }
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  height: 64px;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-muted) 100%);
  position: relative;
  
  &.enhanced {
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: var(--spacing-md);
      right: var(--spacing-md);
      height: 1px;
      background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
      opacity: 0.5;
    }
  }
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  
  &.modern {
    .logo {
      &.enhanced {
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        transition: all var(--duration-normal) var(--ease-out);
        
        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}

.logo {
  height: 32px;
  width: auto;
  transition: all 0.3s ease;
}

.collapse-btn {
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  padding: var(--spacing-sm);
  cursor: pointer;
  color: var(--text-muted);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--ease-out);
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.modern {
    &:hover {
      background: var(--color-primary);
      color: white;
      border-color: var(--color-primary);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

.sidebar-nav {
  padding: 16px 0;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: #666;
  text-decoration: none;
  transition: all 0.2s;
  position: relative;
  
  &:hover,
  &.active {
    background-color: #f0f7ff;
    color: #1890ff;
  }
  
  &.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #1890ff;
  }
}

.nav-icon {
  font-size: 18px;
  margin-right: 12px;
  width: 24px;
  text-align: center;
}

.nav-text {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
}

.arrow-icon {
  font-size: 12px;
  transition: transform 0.2s;
  
  &.rotate {
    transform: rotate(180deg);
  }
}

.submenu {
  background-color: #fafafa;
}

.submenu-link {
  display: block;
  padding: 10px 16px 10px 52px;
  color: #666;
  text-decoration: none;
  font-size: 13px;
  transition: all 0.2s;
  
  &:hover,
  &.active {
    background-color: #f0f7ff;
    color: #1890ff;
  }
}

// 现代化主内容区域
.main-content {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-secondary);
  
  &.enhanced {
    &.with-sidebar {
      margin-left: 240px;
      transition: all var(--duration-normal) var(--ease-out);
      
      &.sidebar-collapsed {
        margin-left: 64px;
      }
    }
  }
}

.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: var(--shadow-sm);
  min-height: 64px;
  
  &.enhanced {
    .breadcrumb-container {
      flex: 1;
      
      .breadcrumb-nav {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        
        .breadcrumb-item {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          color: var(--text-muted);
          text-decoration: none;
          font-size: var(--text-sm);
          transition: color var(--duration-normal) var(--ease-out);
          
          &:hover {
            color: var(--color-primary);
          }
          
          &.active {
            color: var(--text-primary);
            font-weight: var(--font-medium);
          }
          
          .breadcrumb-separator {
            color: var(--text-light);
            font-size: 12px;
          }
        }
      }
    }
  }
}

.page-title {
  margin: 0;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  
  &.enhanced {
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 30px;
      height: 2px;
      background: var(--gradient-primary);
      border-radius: var(--radius-full);
    }
  }
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
  
  &.enhanced {
    align-items: center;
    
    .action-button {
      padding: var(--spacing-sm);
      background: var(--bg-secondary);
      border: 1px solid var(--border-light);
      border-radius: var(--radius-lg);
      color: var(--text-muted);
      cursor: pointer;
      transition: all var(--duration-normal) var(--ease-out);
      
      &:hover {
        background: var(--color-primary);
        border-color: var(--color-primary);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }
    }
  }
}

.content-body {
  padding: 24px;
  > * {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }
}

// 移动端底部导航
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: white;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.bottom-nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 4px;
  text-decoration: none;
  color: #666;
  transition: all 0.2s;
  font-size: 12px;
  min-height: 56px;
  
  &.active {
    color: #1890ff;
    background-color: #f0f7ff;
  }
  
  &:hover:not(.active) {
    background-color: #f5f5f5;
  }
}

.bottom-nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.bottom-nav-text {
  font-size: 11px;
  line-height: 1.2;
}

// 现代化动画效果
.expand-enter-active,
.expand-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.expand-enter-to,
.expand-leave-from {
  opacity: 1;
  max-height: 200px;
  transform: translateY(0);
}

// Logo切换动画
.logo-switch-enter-active,
.logo-switch-leave-active {
  transition: all var(--duration-fast) var(--ease-out);
}

.logo-switch-enter-from,
.logo-switch-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

// 图标旋转动画
.icon-rotate-enter-active,
.icon-rotate-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.icon-rotate-enter-from,
.icon-rotate-leave-to {
  opacity: 0;
  transform: rotate(180deg) scale(0.8);
}

// 悬停时的光晕效果
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

// 响应式调整
@media (max-width: 991px) {
  .sidebar {
    display: none;
  }
  
  .main-content.with-sidebar {
    margin-left: 0;
  }
}

@media (min-width: 992px) {
  .mobile-bottom-nav {
    display: none;
  }
}

// 触摸优化
@media (hover: none) and (pointer: coarse) {
  .nav-link,
  .submenu-link,
  .bottom-nav-item {
    min-height: 44px;
    padding: 12px 16px;
  }
  
  .content-header {
    padding: 12px 16px;
  }
  
  .content-body {
    padding: 16px;
  }
}
</style>