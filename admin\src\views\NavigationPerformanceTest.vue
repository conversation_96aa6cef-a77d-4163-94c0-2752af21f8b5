<template>
  <div class="navigation-performance-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>🚀 导航性能测试</h2>
          <el-button type="primary" @click="runPerformanceTest">
            开始测试
          </el-button>
        </div>
      </template>

      <!-- 测试结果展示 -->
      <div class="test-results" v-if="testResults.length > 0">
        <h3>测试结果</h3>
        <el-table :data="testResults" style="width: 100%">
          <el-table-column prop="route" label="路由" width="200" />
          <el-table-column prop="loadTime" label="加载时间(ms)" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="cacheHit" label="缓存命中" width="100">
            <template #default="{ row }">
              <el-tag :type="row.cacheHit ? 'success' : 'info'">
                {{ row.cacheHit ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 实时导航测试 -->
      <div class="navigation-test">
        <h3>实时导航测试</h3>
        <div class="test-buttons">
          <el-button 
            v-for="route in testRoutes" 
            :key="route.path"
            @click="navigateToRoute(route.path)"
            :loading="currentTest === route.path"
          >
            {{ route.name }}
          </el-button>
        </div>
      </div>

      <!-- 优化统计 -->
      <div class="optimization-stats">
        <h3>导航优化统计</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ optimizationStats.preloaded }}</div>
              <div class="stat-label">预加载路由</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ averageLoadTime }}ms</div>
              <div class="stat-label">平均加载时间</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ cacheHitRate }}%</div>
              <div class="stat-label">缓存命中率</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ testResults.length }}</div>
              <div class="stat-label">测试次数</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { navigationOptimizer } from '@/utils/navigationOptimization.js'

const router = useRouter()

// 测试数据
const testResults = ref([])
const currentTest = ref('')
const optimizationStats = ref({
  preloaded: 0,
  loading: 0,
  routes: []
})

// 测试路由列表
const testRoutes = [
  { name: '仪表板', path: '/admin/dashboard' },
  { name: '用户管理', path: '/admin/users/list' },
  { name: '群组管理', path: '/admin/community/groups' },
  { name: '财务管理', path: '/admin/finance/dashboard' },
  { name: '防红系统', path: '/admin/anti-block/dashboard' }
]

// 计算属性
const averageLoadTime = computed(() => {
  if (testResults.value.length === 0) return 0
  const total = testResults.value.reduce((sum, result) => sum + result.loadTime, 0)
  return Math.round(total / testResults.value.length)
})

const cacheHitRate = computed(() => {
  if (testResults.value.length === 0) return 0
  const hits = testResults.value.filter(result => result.cacheHit).length
  return Math.round((hits / testResults.value.length) * 100)
})

// 方法
const runPerformanceTest = async () => {
  testResults.value = []
  
  for (const route of testRoutes) {
    const startTime = performance.now()
    
    try {
      currentTest.value = route.path
      
      // 测试路由导航性能
      await router.push(route.path)
      await new Promise(resolve => setTimeout(resolve, 100)) // 等待渲染完成
      
      const endTime = performance.now()
      const loadTime = Math.round(endTime - startTime)
      
      testResults.value.push({
        route: route.name,
        loadTime,
        status: 'success',
        cacheHit: navigationOptimizer.preloadCache.has(route.path)
      })
      
    } catch (error) {
      const endTime = performance.now()
      const loadTime = Math.round(endTime - startTime)
      
      testResults.value.push({
        route: route.name,
        loadTime,
        status: 'error',
        cacheHit: false
      })
    }
    
    currentTest.value = ''
  }
  
  ElMessage.success('性能测试完成')
}

const navigateToRoute = async (path) => {
  const startTime = performance.now()
  currentTest.value = path
  
  try {
    await router.push(path)
    const endTime = performance.now()
    const loadTime = Math.round(endTime - startTime)
    
    ElMessage.success(`导航完成，耗时: ${loadTime}ms`)
  } catch (error) {
    ElMessage.error('导航失败')
  } finally {
    currentTest.value = ''
  }
}

const updateOptimizationStats = () => {
  optimizationStats.value = navigationOptimizer.getStats()
}

// 生命周期
onMounted(() => {
  updateOptimizationStats()
  
  // 定时更新统计信息
  setInterval(updateOptimizationStats, 2000)
})
</script>

<style lang="scss" scoped>
.navigation-performance-test {
  padding: 20px;
  
  .test-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h2 {
        margin: 0;
        color: #303133;
      }
    }
  }
  
  .test-results, 
  .navigation-test, 
  .optimization-stats {
    margin: 30px 0;
    
    h3 {
      margin-bottom: 20px;
      color: #303133;
      font-size: 16px;
    }
  }
  
  .test-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }
  
  .optimization-stats {
    .stat-item {
      text-align: center;
      padding: 20px;
      background: #f5f7fa;
      border-radius: 8px;
      
      .stat-value {
        font-size: 28px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}
</style>