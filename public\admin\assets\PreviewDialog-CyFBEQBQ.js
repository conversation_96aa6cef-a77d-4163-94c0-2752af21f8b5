/* empty css             *//* empty css                   *//* empty css                         *//* empty css                        *//* empty css                  */import{z as e,m as a,A as s,q as l,G as i,E as t,r as o,c as r,w as n}from"./vue-vendor-BcnDv-68.js";import{Z as d,aK as c,a1 as p,ah as u,s as v,aX as m,aY as f,W as g,bt as w,bu as b,bv as _,bw as h,X as y}from"./element-plus-C2UshkXo.js";import D from"./LandingPagePreview-DIzIwpum.js";import{_ as j}from"./index-eUTsTR3J.js";/* empty css               */import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const C={__name:"PreviewDialog",props:{modelValue:{type:Boolean,default:!1},groupData:{type:Object,default:()=>({})},layoutConfig:{type:Object,default:()=>({sections:[]})}},emits:["update:modelValue","refresh"],setup(e,{expose:a,emit:s}){a();const l=e,i=s,t=o("mobile"),d=o(["info"]),c=r({get:()=>l.modelValue,set:e=>i("update:modelValue",e)}),p=r(()=>l.layoutConfig.sections?l.layoutConfig.sections.filter(e=>e.visible):[]),u=r(()=>`${window.location.origin}/landing/group/preview`),v=r(()=>`${l.groupData.title} - 群组邀请`),m=r(()=>l.groupData.description||`加入${l.groupData.title}，与志同道合的朋友一起交流学习`),f=r(()=>{const e=[l.groupData.title];return l.groupData.tags&&e.push(...l.groupData.tags),e.push("群组","交流","学习"),e.join(", ")});n(()=>l.modelValue,e=>{e&&(t.value="mobile",d.value=["info"])});const g={props:l,emit:i,viewMode:t,activeCollapse:d,visible:c,visibleSections:p,previewUrl:u,seoTitle:v,seoDescription:m,seoKeywords:f,setViewMode:e=>{t.value=e},refreshPreview:()=>{i("refresh"),y.success("预览已刷新")},copyPreviewUrl:async()=>{try{await navigator.clipboard.writeText(u.value),y.success("预览链接已复制到剪贴板")}catch(e){y.error("复制失败，请手动复制链接")}},formatPrice:e=>0===e||"0"===e?"免费":`¥${e}`,handleClose:()=>{i("update:modelValue",!1)},ref:o,computed:r,watch:n,get ElMessage(){return y},get Iphone(){return h},get Monitor(){return _},get RefreshRight(){return b},get Link(){return w},LandingPagePreview:D};return Object.defineProperty(g,"__isScriptSetup",{enumerable:!1,value:!0}),g}},V={class:"preview-dialog-content"},P={class:"preview-toolbar"},M={class:"toolbar-left"},k={class:"preview-title"},U={class:"toolbar-center"},z={class:"toolbar-right"},L={class:"preview-frame"},$={class:"frame-header"},S={class:"frame-url"},x={class:"frame-body"},K={class:"preview-info-panel"},O={class:"info-grid"},R={class:"info-item"},T={class:"info-value"},E={class:"info-item"},I={class:"info-value"},q={class:"info-item"},A={class:"info-value"},X={class:"info-item"},B={class:"info-value"},G={class:"seo-info"},N={class:"seo-item"},W={class:"seo-value"},Y={class:"seo-item"},Z={class:"seo-value"},F={class:"seo-item"},H={class:"seo-value"},J={class:"share-preview"},Q={class:"share-card"},ee={class:"share-image"},ae=["src"],se={class:"share-content"},le={class:"share-title"},ie={class:"share-description"},te={class:"share-url"};const oe=j(C,[["render",function(o,r,n,w,b,_){const h=u,y=p,D=c,j=f,C=m,oe=g;return a(),e(oe,{modelValue:w.visible,"onUpdate:modelValue":r[4]||(r[4]=e=>w.visible=e),title:"落地页预览",width:"90%",top:"5vh","append-to-body":"",class:"preview-dialog",onClose:w.handleClose},{default:s(()=>[l("div",V,[l("div",P,[l("div",M,[l("span",k,d(n.groupData.title||"群组落地页"),1)]),l("div",U,[i(D,null,{default:s(()=>[i(y,{type:"mobile"===w.viewMode?"primary":"",onClick:r[0]||(r[0]=e=>w.setViewMode("mobile")),size:"small"},{default:s(()=>[i(h,null,{default:s(()=>[i(w.Iphone)]),_:1}),r[5]||(r[5]=t(" 手机版 ",-1))]),_:1,__:[5]},8,["type"]),i(y,{type:"tablet"===w.viewMode?"primary":"",onClick:r[1]||(r[1]=e=>w.setViewMode("tablet")),size:"small"},{default:s(()=>[i(h,null,{default:s(()=>[i(w.Monitor)]),_:1}),r[6]||(r[6]=t(" 平板版 ",-1))]),_:1,__:[6]},8,["type"]),i(y,{type:"desktop"===w.viewMode?"primary":"",onClick:r[2]||(r[2]=e=>w.setViewMode("desktop")),size:"small"},{default:s(()=>[i(h,null,{default:s(()=>[i(w.Monitor)]),_:1}),r[7]||(r[7]=t(" 桌面版 ",-1))]),_:1,__:[7]},8,["type"])]),_:1})]),l("div",z,[i(y,{onClick:w.refreshPreview,size:"small",icon:w.RefreshRight},{default:s(()=>r[8]||(r[8]=[t(" 刷新 ",-1)])),_:1,__:[8]},8,["icon"]),i(y,{onClick:w.copyPreviewUrl,size:"small",icon:w.Link,type:"primary",plain:""},{default:s(()=>r[9]||(r[9]=[t(" 复制链接 ",-1)])),_:1,__:[9]},8,["icon"])])]),l("div",{class:v(["preview-content",`view-${w.viewMode}`])},[l("div",L,[l("div",$,[r[10]||(r[10]=l("div",{class:"frame-controls"},[l("span",{class:"control-dot red"}),l("span",{class:"control-dot yellow"}),l("span",{class:"control-dot green"})],-1)),l("div",S,[i(h,null,{default:s(()=>[i(w.Link)]),_:1}),l("span",null,d(w.previewUrl),1)])]),l("div",x,[i(w.LandingPagePreview,{"group-data":n.groupData,"layout-config":n.layoutConfig,"view-mode":w.viewMode},null,8,["group-data","layout-config","view-mode"])])])],2),l("div",K,[i(C,{modelValue:w.activeCollapse,"onUpdate:modelValue":r[3]||(r[3]=e=>w.activeCollapse=e)},{default:s(()=>[i(j,{title:"页面信息",name:"info"},{default:s(()=>[l("div",O,[l("div",R,[r[11]||(r[11]=l("span",{class:"info-label"},"页面标题：",-1)),l("span",T,d(n.groupData.title),1)]),l("div",E,[r[12]||(r[12]=l("span",{class:"info-label"},"群组价格：",-1)),l("span",I,d(w.formatPrice(n.groupData.price)),1)]),l("div",q,[r[13]||(r[13]=l("span",{class:"info-label"},"内容模块：",-1)),l("span",A,d(w.visibleSections.length)+" 个",1)]),l("div",X,[r[14]||(r[14]=l("span",{class:"info-label"},"预览链接：",-1)),l("span",B,d(w.previewUrl),1)])])]),_:1}),i(j,{title:"SEO 信息",name:"seo"},{default:s(()=>[l("div",G,[l("div",N,[r[15]||(r[15]=l("div",{class:"seo-label"},"页面标题 (Title)",-1)),l("div",W,d(w.seoTitle),1)]),l("div",Y,[r[16]||(r[16]=l("div",{class:"seo-label"},"页面描述 (Description)",-1)),l("div",Z,d(w.seoDescription),1)]),l("div",F,[r[17]||(r[17]=l("div",{class:"seo-label"},"关键词 (Keywords)",-1)),l("div",H,d(w.seoKeywords),1)])])]),_:1}),i(j,{title:"分享预览",name:"share"},{default:s(()=>[l("div",J,[l("div",Q,[l("div",ee,[l("img",{src:n.groupData.banner_image||"/default-share.jpg",alt:"分享图片"},null,8,ae)]),l("div",se,[l("div",le,d(n.groupData.title),1),l("div",ie,d(n.groupData.description),1),l("div",te,d(w.previewUrl),1)])])])]),_:1})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])}],["__scopeId","data-v-a2704451"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/PreviewDialog.vue"]]);export{oe as default};
