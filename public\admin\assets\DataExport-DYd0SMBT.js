/* empty css             *//* empty css                   *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                    *//* empty css                     *//* empty css                          *//* empty css                        *//* empty css                       *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                *//* empty css               */import{l as e,G as a,A as l,M as t,r as s,o,m as r,q as d,F as i,Y as n,z as u,E as p}from"./vue-vendor-BcnDv-68.js";import{p as c}from"./export-C8s0bFWZ.js";import{_ as m}from"./index-eUTsTR3J.js";import{V as _,W as f,ag as g,X as b,U as v,Y as y,Z as x,s as h,a1 as k,a2 as w,a3 as V,a5 as j,a6 as F,_ as E,aL as C,a0 as D,aq as z,b6 as T,b7 as S,a4 as U,ai as M,aj as P,ak as Y,bg as q,an as B,bj as I,al as H}from"./element-plus-C2UshkXo.js";import"./index-D4AyIzGN.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const O={class:"app-container"},$={class:"stat-item"},N={class:"stat-content"},A={class:"stat-number"},G={class:"stat-item"},J={class:"stat-content"},L={class:"stat-number"},Q={class:"stat-item"},R={class:"stat-content"},W={class:"stat-number"},X={class:"stat-item"},Z={class:"stat-content"},K={class:"stat-number"},ee={class:"export-template"},ae={class:"template-header"},le={class:"template-icon"},te={class:"template-info"},se={class:"template-name"},oe={class:"template-desc"},re={class:"template-stats"},de={class:"stat-row"},ie={class:"stat-value"},ne={class:"stat-row"},ue={class:"stat-value"},pe={class:"stat-row"},ce={class:"stat-value"},me={class:"template-actions"},_e={class:"card-header"},fe={class:"table-pagination"},ge={class:"dialog-footer"},be={key:0},ve={key:1,style:{"text-align":"center",padding:"40px"}},ye={class:"dialog-footer"};const xe=m({__name:"DataExport",setup(e,{expose:a}){a();const l=t({total_exports:1245,today_exports:23,total_size:"2.5GB",pending_exports:3}),r=s([{key:"users",name:"用户数据",description:"导出所有用户的基本信息",icon:"el-icon-user",count:"12,345",size:"2.5MB",last_updated:"2024-01-01 10:00",loading:!1},{key:"orders",name:"订单数据",description:"导出所有订单记录",icon:"el-icon-goods",count:"8,765",size:"5.2MB",last_updated:"2024-01-01 09:30",loading:!1},{key:"finance",name:"财务数据",description:"导出收支明细记录",icon:"el-icon-money",count:"3,456",size:"1.8MB",last_updated:"2024-01-01 08:45",loading:!1}]),d=t({data_type:"",format:"excel",date_range:[],limit:1e4,fields:[],filters:"",sort_by:"created_at",sort_order:"desc",name:""}),i=s([]),n=s([{id:1,name:"用户数据导出",data_type:"users",format:"excel",status:"completed",progress:100,file_size:"2.5MB",created_at:"2024-01-01 10:00:00"},{id:2,name:"订单数据导出",data_type:"orders",format:"csv",status:"processing",progress:65,file_size:"-",created_at:"2024-01-01 10:30:00"},{id:3,name:"财务数据导出",data_type:"finance",format:"json",status:"failed",progress:0,file_size:"-",created_at:"2024-01-01 09:15:00"}]),u=t({visible:!1,template:null}),p=t({visible:!1,data:[],columns:[]}),m=t({frequency:"daily",time:null,email:"",enabled:!0}),_=s(!1),f=s(1),v=s(10),y=s(0),x=()=>{b.success("任务列表已刷新")};o(()=>{y.value=n.value.length});const h={exportStats:l,exportTemplates:r,customExportForm:d,availableFields:i,exportTasks:n,scheduleDialog:u,previewDialog:p,scheduleForm:m,customExporting:_,taskPage:f,taskPageSize:v,taskTotal:y,onDataTypeChange:e=>{i.value={users:[{key:"id",label:"ID"},{key:"username",label:"用户名"},{key:"email",label:"邮箱"},{key:"phone",label:"手机号"},{key:"created_at",label:"创建时间"}],orders:[{key:"id",label:"订单ID"},{key:"user_id",label:"用户ID"},{key:"amount",label:"金额"},{key:"status",label:"状态"},{key:"created_at",label:"创建时间"}],finance:[{key:"id",label:"ID"},{key:"type",label:"类型"},{key:"amount",label:"金额"},{key:"balance",label:"余额"},{key:"created_at",label:"创建时间"}]}[e]||[],d.fields=i.value.map(e=>e.key)},quickExport:async e=>{e.loading=!0;try{await new Promise(e=>setTimeout(e,2e3)),b.success(`${e.name} 导出成功`)}catch(a){b.error(`${e.name} 导出失败`)}finally{e.loading=!1}},scheduleExport:e=>{u.template=e,u.visible=!0},startCustomExport:async()=>{if(d.data_type){_.value=!0;try{await new Promise(e=>setTimeout(e,3e3)),b.success("导出任务已创建"),x()}catch(e){b.error("导出任务创建失败")}finally{_.value=!1}}else b.warning("请选择数据类型")},resetCustomForm:()=>{Object.assign(d,{data_type:"",format:"excel",date_range:[],limit:1e4,fields:[],filters:"",sort_by:"created_at",sort_order:"desc",name:""}),i.value=[]},previewData:async()=>{if(d.data_type)try{b.info("正在获取预览数据...");const e={data_type:d.data_type,fields:d.fields,filters:d.filters,date_range:d.date_range,limit:10},a=await c(e);a.data.success?(p.visible=!0,p.data=a.data.data,p.columns=a.data.columns,b.success("预览数据获取成功")):b.error("预览数据获取失败")}catch(e){b.error("预览数据获取失败："+e.message)}else b.warning("请选择数据类型")},getStatusType:e=>({pending:"info",processing:"warning",completed:"success",failed:"danger"}[e]||"info"),getStatusText:e=>({pending:"待处理",processing:"处理中",completed:"已完成",failed:"失败"}[e]||e),downloadFile:e=>{b.success(`开始下载 ${e.name}`)},retryTask:e=>{b.info(`重试任务 ${e.name}`),e.status="pending",e.progress=0},deleteTask:e=>{g.confirm(`确定要删除任务 ${e.name} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{b.success("删除成功"),x()})},refreshTasks:x,handleTaskSizeChange:e=>{v.value=e,x()},handleTaskCurrentChange:e=>{f.value=e,x()},saveSchedule:()=>{b.success("定时导出设置已保存"),u.visible=!1},ref:s,reactive:t,onMounted:o,get ElMessage(){return b},get ElMessageBox(){return g},get previewExportData(){return c}};return Object.defineProperty(h,"__isScriptSetup",{enumerable:!1,value:!0}),h}},[["render",function(t,s,o,c,m,g){const b=y,xe=v,he=_,ke=k,we=F,Ve=j,je=V,Fe=C,Ee=E,Ce=D,De=z,ze=S,Te=T,Se=U,Ue=w,Me=P,Pe=Y,Ye=q,qe=M,Be=B,Ie=I,He=H,Oe=f;return r(),e("div",O,[a(he,{class:"overview-card"},{header:l(()=>s[19]||(s[19]=[d("div",{class:"card-header"},[d("span",null,"📊 数据导出概览")],-1)])),default:l(()=>[a(xe,{gutter:20},{default:l(()=>[a(b,{span:6},{default:l(()=>[d("div",$,[s[21]||(s[21]=d("div",{class:"stat-icon export-icon"},[d("i",{class:"el-icon-download"})],-1)),d("div",N,[d("div",A,x(c.exportStats.total_exports),1),s[20]||(s[20]=d("div",{class:"stat-label"},"总导出次数",-1))])])]),_:1}),a(b,{span:6},{default:l(()=>[d("div",G,[s[23]||(s[23]=d("div",{class:"stat-icon today-icon"},[d("i",{class:"el-icon-calendar-today"})],-1)),d("div",J,[d("div",L,x(c.exportStats.today_exports),1),s[22]||(s[22]=d("div",{class:"stat-label"},"今日导出",-1))])])]),_:1}),a(b,{span:6},{default:l(()=>[d("div",Q,[s[25]||(s[25]=d("div",{class:"stat-icon size-icon"},[d("i",{class:"el-icon-files"})],-1)),d("div",R,[d("div",W,x(c.exportStats.total_size),1),s[24]||(s[24]=d("div",{class:"stat-label"},"总文件大小",-1))])])]),_:1}),a(b,{span:6},{default:l(()=>[d("div",X,[s[27]||(s[27]=d("div",{class:"stat-icon pending-icon"},[d("i",{class:"el-icon-loading"})],-1)),d("div",Z,[d("div",K,x(c.exportStats.pending_exports),1),s[26]||(s[26]=d("div",{class:"stat-label"},"待处理任务",-1))])])]),_:1})]),_:1})]),_:1}),a(he,{style:{"margin-top":"20px"}},{header:l(()=>s[28]||(s[28]=[d("div",{class:"card-header"},[d("span",null,"⚡ 快速导出")],-1)])),default:l(()=>[a(xe,{gutter:20},{default:l(()=>[(r(!0),e(i,null,n(c.exportTemplates,e=>(r(),u(b,{span:8,key:e.key},{default:l(()=>[d("div",ee,[d("div",ae,[d("div",le,[d("i",{class:h(e.icon)},null,2)]),d("div",te,[d("div",se,x(e.name),1),d("div",oe,x(e.description),1)])]),d("div",re,[d("div",de,[s[29]||(s[29]=d("span",{class:"stat-label"},"数据量:",-1)),d("span",ie,x(e.count),1)]),d("div",ne,[s[30]||(s[30]=d("span",{class:"stat-label"},"预计大小:",-1)),d("span",ue,x(e.size),1)]),d("div",pe,[s[31]||(s[31]=d("span",{class:"stat-label"},"最后更新:",-1)),d("span",ce,x(e.last_updated),1)])]),d("div",me,[a(ke,{type:"primary",onClick:a=>c.quickExport(e),loading:e.loading},{default:l(()=>[...s[32]||(s[32]=[p(" 立即导出 ",-1)])]),_:2,__:[32]},1032,["onClick","loading"]),a(ke,{type:"info",onClick:a=>c.scheduleExport(e)},{default:l(()=>[...s[33]||(s[33]=[p(" 定时导出 ",-1)])]),_:2,__:[33]},1032,["onClick"])])])]),_:2},1024))),128))]),_:1})]),_:1}),a(he,{style:{"margin-top":"20px"}},{header:l(()=>s[34]||(s[34]=[d("div",{class:"card-header"},[d("span",null,"🎯 自定义导出")],-1)])),default:l(()=>[a(Ue,{model:c.customExportForm,"label-width":"120px"},{default:l(()=>[a(xe,{gutter:20},{default:l(()=>[a(b,{span:12},{default:l(()=>[a(je,{label:"导出数据类型"},{default:l(()=>[a(Ve,{modelValue:c.customExportForm.data_type,"onUpdate:modelValue":s[0]||(s[0]=e=>c.customExportForm.data_type=e),placeholder:"请选择数据类型",onChange:c.onDataTypeChange},{default:l(()=>[a(we,{label:"用户数据",value:"users"}),a(we,{label:"订单数据",value:"orders"}),a(we,{label:"财务数据",value:"finance"}),a(we,{label:"分销数据",value:"distribution"}),a(we,{label:"社群数据",value:"community"}),a(we,{label:"防红数据",value:"anti-block"}),a(we,{label:"系统日志",value:"logs"})]),_:1},8,["modelValue"])]),_:1}),a(je,{label:"导出格式"},{default:l(()=>[a(Ee,{modelValue:c.customExportForm.format,"onUpdate:modelValue":s[1]||(s[1]=e=>c.customExportForm.format=e)},{default:l(()=>[a(Fe,{label:"excel"},{default:l(()=>s[35]||(s[35]=[p("Excel (.xlsx)",-1)])),_:1,__:[35]}),a(Fe,{label:"csv"},{default:l(()=>s[36]||(s[36]=[p("CSV (.csv)",-1)])),_:1,__:[36]}),a(Fe,{label:"json"},{default:l(()=>s[37]||(s[37]=[p("JSON (.json)",-1)])),_:1,__:[37]})]),_:1},8,["modelValue"])]),_:1}),a(je,{label:"时间范围"},{default:l(()=>[a(Ce,{modelValue:c.customExportForm.date_range,"onUpdate:modelValue":s[2]||(s[2]=e=>c.customExportForm.date_range=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),a(je,{label:"数据量限制"},{default:l(()=>[a(De,{modelValue:c.customExportForm.limit,"onUpdate:modelValue":s[3]||(s[3]=e=>c.customExportForm.limit=e),min:1,max:1e5},null,8,["modelValue"]),s[38]||(s[38]=d("span",{class:"form-tip"},"建议单次导出不超过50000条数据",-1))]),_:1,__:[38]})]),_:1}),a(b,{span:12},{default:l(()=>[a(je,{label:"导出字段"},{default:l(()=>[a(Te,{modelValue:c.customExportForm.fields,"onUpdate:modelValue":s[4]||(s[4]=e=>c.customExportForm.fields=e)},{default:l(()=>[(r(!0),e(i,null,n(c.availableFields,e=>(r(),u(ze,{key:e.key,label:e.key},{default:l(()=>[p(x(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(je,{label:"筛选条件"},{default:l(()=>[a(Se,{type:"textarea",modelValue:c.customExportForm.filters,"onUpdate:modelValue":s[5]||(s[5]=e=>c.customExportForm.filters=e),placeholder:"请输入筛选条件（JSON格式）"},null,8,["modelValue"])]),_:1}),a(je,{label:"排序方式"},{default:l(()=>[a(Ve,{modelValue:c.customExportForm.sort_by,"onUpdate:modelValue":s[6]||(s[6]=e=>c.customExportForm.sort_by=e),placeholder:"选择排序字段"},{default:l(()=>[a(we,{label:"创建时间",value:"created_at"}),a(we,{label:"更新时间",value:"updated_at"}),a(we,{label:"ID",value:"id"})]),_:1},8,["modelValue"]),a(Ve,{modelValue:c.customExportForm.sort_order,"onUpdate:modelValue":s[7]||(s[7]=e=>c.customExportForm.sort_order=e),placeholder:"排序方式",style:{"margin-left":"10px"}},{default:l(()=>[a(we,{label:"升序",value:"asc"}),a(we,{label:"降序",value:"desc"})]),_:1},8,["modelValue"])]),_:1}),a(je,{label:"导出名称"},{default:l(()=>[a(Se,{modelValue:c.customExportForm.name,"onUpdate:modelValue":s[8]||(s[8]=e=>c.customExportForm.name=e),placeholder:"请输入导出文件名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(je,null,{default:l(()=>[a(ke,{type:"primary",onClick:c.startCustomExport,loading:c.customExporting},{default:l(()=>s[39]||(s[39]=[p(" 开始导出 ",-1)])),_:1,__:[39]},8,["loading"]),a(ke,{onClick:c.resetCustomForm},{default:l(()=>s[40]||(s[40]=[p("重置",-1)])),_:1,__:[40]}),a(ke,{type:"info",onClick:c.previewData},{default:l(()=>s[41]||(s[41]=[p("预览数据",-1)])),_:1,__:[41]})]),_:1})]),_:1},8,["model"])]),_:1}),a(he,{style:{"margin-top":"20px"}},{header:l(()=>[d("div",_e,[s[43]||(s[43]=d("span",null,"📋 导出任务列表",-1)),a(ke,{type:"primary",onClick:c.refreshTasks},{default:l(()=>s[42]||(s[42]=[p("刷新",-1)])),_:1,__:[42]})])]),default:l(()=>[a(qe,{data:c.exportTasks,style:{width:"100%"}},{default:l(()=>[a(Me,{prop:"id",label:"任务ID",width:"80"}),a(Me,{prop:"name",label:"任务名称"}),a(Me,{prop:"data_type",label:"数据类型",width:"100"}),a(Me,{prop:"format",label:"格式",width:"80"}),a(Me,{prop:"status",label:"状态",width:"100"},{default:l(e=>[a(Pe,{type:c.getStatusType(e.row.status)},{default:l(()=>[p(x(c.getStatusText(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),a(Me,{prop:"progress",label:"进度",width:"120"},{default:l(e=>[a(Ye,{percentage:e.row.progress,status:"failed"===e.row.status?"exception":""},null,8,["percentage","status"])]),_:1}),a(Me,{prop:"file_size",label:"文件大小",width:"100"}),a(Me,{prop:"created_at",label:"创建时间",width:"160"}),a(Me,{label:"操作",width:"200"},{default:l(e=>[a(ke,{type:"primary",size:"small",onClick:a=>c.downloadFile(e.row),disabled:"completed"!==e.row.status},{default:l(()=>s[44]||(s[44]=[p(" 下载 ",-1)])),_:2,__:[44]},1032,["onClick","disabled"]),a(ke,{type:"warning",size:"small",onClick:a=>c.retryTask(e.row),disabled:"failed"!==e.row.status},{default:l(()=>s[45]||(s[45]=[p(" 重试 ",-1)])),_:2,__:[45]},1032,["onClick","disabled"]),a(ke,{type:"danger",size:"small",onClick:a=>c.deleteTask(e.row)},{default:l(()=>s[46]||(s[46]=[p(" 删除 ",-1)])),_:2,__:[46]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),d("div",fe,[a(Be,{"current-page":c.taskPage,"onUpdate:currentPage":s[9]||(s[9]=e=>c.taskPage=e),"page-size":c.taskPageSize,"onUpdate:pageSize":s[10]||(s[10]=e=>c.taskPageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:c.taskTotal,onSizeChange:c.handleTaskSizeChange,onCurrentChange:c.handleTaskCurrentChange},null,8,["current-page","page-size","total"])])]),_:1}),a(Oe,{title:"定时导出设置",modelValue:c.scheduleDialog.visible,"onUpdate:modelValue":s[16]||(s[16]=e=>c.scheduleDialog.visible=e),width:"600px"},{footer:l(()=>[d("div",ge,[a(ke,{onClick:s[15]||(s[15]=e=>c.scheduleDialog.visible=!1)},{default:l(()=>s[47]||(s[47]=[p("取消",-1)])),_:1,__:[47]}),a(ke,{type:"primary",onClick:c.saveSchedule},{default:l(()=>s[48]||(s[48]=[p("保存",-1)])),_:1,__:[48]})])]),default:l(()=>[a(Ue,{model:c.scheduleForm,"label-width":"100px"},{default:l(()=>[a(je,{label:"导出频率"},{default:l(()=>[a(Ve,{modelValue:c.scheduleForm.frequency,"onUpdate:modelValue":s[11]||(s[11]=e=>c.scheduleForm.frequency=e),placeholder:"请选择导出频率"},{default:l(()=>[a(we,{label:"每日",value:"daily"}),a(we,{label:"每周",value:"weekly"}),a(we,{label:"每月",value:"monthly"})]),_:1},8,["modelValue"])]),_:1}),a(je,{label:"执行时间"},{default:l(()=>[a(Ie,{modelValue:c.scheduleForm.time,"onUpdate:modelValue":s[12]||(s[12]=e=>c.scheduleForm.time=e),placeholder:"选择时间"},null,8,["modelValue"])]),_:1}),a(je,{label:"邮件通知"},{default:l(()=>[a(Se,{modelValue:c.scheduleForm.email,"onUpdate:modelValue":s[13]||(s[13]=e=>c.scheduleForm.email=e),placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1}),a(je,{label:"是否启用"},{default:l(()=>[a(He,{modelValue:c.scheduleForm.enabled,"onUpdate:modelValue":s[14]||(s[14]=e=>c.scheduleForm.enabled=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(Oe,{title:"数据预览",modelValue:c.previewDialog.visible,"onUpdate:modelValue":s[18]||(s[18]=e=>c.previewDialog.visible=e),width:"80%",top:"5vh"},{footer:l(()=>[d("div",ye,[a(ke,{onClick:s[17]||(s[17]=e=>c.previewDialog.visible=!1)},{default:l(()=>s[51]||(s[51]=[p("关闭",-1)])),_:1,__:[51]})])]),default:l(()=>[c.previewDialog.data.length>0?(r(),e("div",be,[a(qe,{data:c.previewDialog.data,stripe:""},{default:l(()=>[(r(!0),e(i,null,n(c.previewDialog.columns,e=>(r(),u(Me,{key:e.key,prop:e.key,label:e.label,"show-overflow-tooltip":""},null,8,["prop","label"]))),128))]),_:1},8,["data"]),s[49]||(s[49]=d("div",{style:{"margin-top":"10px",color:"#666","font-size":"14px"}},[d("i",{class:"el-icon-info"}),p(" 预览数据仅显示前10条记录 ")],-1))])):(r(),e("div",ve,s[50]||(s[50]=[d("i",{class:"el-icon-document",style:{"font-size":"48px",color:"#ddd"}},null,-1),d("p",{style:{color:"#999","margin-top":"10px"}},"暂无数据",-1)])))]),_:1},8,["modelValue"])])}],["__scopeId","data-v-2271711a"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/DataExport.vue"]]);export{xe as default};
