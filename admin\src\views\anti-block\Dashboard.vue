<template>
  <div class="anti-block-dashboard">
    <!-- 页面标题和说明 -->
    <div class="page-header">
      <div class="page-title">
        <h1>🛡️ 防红系统管理</h1>
        <p class="page-desc">智能域名管理，自动检测切换，确保推广链接稳定可用</p>
      </div>
      
      <!-- 快速操作按钮 -->
      <div class="quick-actions">
        <el-button type="primary" @click="checkAllDomains" :loading="checking">
          <i class="el-icon-refresh"></i> 立即检测
        </el-button>
        <el-button type="success" @click="showAddDomainDialog" v-if="isAdmin">
          <i class="el-icon-plus"></i> 添加域名
        </el-button>
        <el-button type="info" @click="showHelpDialog">
          <i class="el-icon-question"></i> 使用说明
        </el-button>
      </div>
    </div>

    <!-- 系统状态概览 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon domain-icon">
              <i class="el-icon-connection"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.domain_stats.total }}</div>
              <div class="stat-label">总域名数</div>
              <div class="stat-detail">
                <span class="text-success">{{ stats.domain_stats.active }} 正常</span>
                <span class="text-warning">{{ stats.domain_stats.abnormal }} 异常</span>
                <span class="text-danger">{{ stats.domain_stats.blocked }} 封禁</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon link-icon">
              <i class="el-icon-link"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.link_stats.total }}</div>
              <div class="stat-label">短链接数</div>
              <div class="stat-detail">
                <span class="text-success">{{ stats.link_stats.active }} 激活</span>
                <span class="text-info">今日新增 {{ stats.link_stats.today_created }}</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon click-icon">
              <i class="el-icon-view"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.link_stats.today_clicks }}</div>
              <div class="stat-label">今日访问</div>
              <div class="stat-detail">
                <span class="text-primary">实时统计</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon health-icon">
              <i class="el-icon-success"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ systemHealthScore }}%</div>
              <div class="stat-label">系统健康度</div>
              <div class="stat-detail">
                <span :class="healthClass">{{ healthStatus }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 使用说明卡片 -->
    <div class="help-section">
      <el-card class="help-card">
        <div slot="header" class="card-header">
          <span>📖 快速使用指南</span>
          <el-button type="text" @click="showHelpDialog">查看详细说明</el-button>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="help-item">
              <div class="help-step">1</div>
              <div class="help-content">
                <h4>配置域名池</h4>
                <p>添加多个备用域名，系统会自动选择最佳域名生成短链接</p>
                <el-button type="text" @click="$router.push('/admin/anti-block/domains')">
                  管理域名池 →
                </el-button>
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="help-item">
              <div class="help-step">2</div>
              <div class="help-content">
                <h4>自动生成短链接</h4>
                <p>分销员推广链接将自动使用防红短链接，无需手动操作</p>
                <el-button type="text" @click="$router.push('/admin/anti-block/links')">
                  查看短链接 →
                </el-button>
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="help-item">
              <div class="help-step">3</div>
              <div class="help-content">
                <h4>监控和维护</h4>
                <p>系统每5分钟自动检测，异常域名自动切换，无需人工干预</p>
                <el-button type="text" @click="showMonitorDialog">
                  查看监控 →
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 域名状态表格 -->
    <div class="domain-status">
      <el-card>
        <div slot="header" class="card-header">
          <span>🌐 域名状态监控</span>
          <div>
            <el-button type="text" @click="refreshDomains">刷新</el-button>
            <el-button type="text" @click="$router.push('/admin/anti-block/domains')">
              查看全部 →
            </el-button>
          </div>
        </div>
        
        <el-table :data="recentDomains" style="width: 100%">
          <el-table-column prop="domain" label="域名" width="200">
            <template #default="scope">
              <span class="domain-text">{{ scope.row.domain }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="domain_type" label="类型" width="100">
            <template #default="scope">
              <el-tag size="small" :type="getDomainTypeColor(scope.row.domain_type)">
                {{ getDomainTypeName(scope.row.domain_type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="health_score" label="健康度" width="120">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.health_score"
                :color="getHealthColor(scope.row.health_score)"
                :stroke-width="8">
              </el-progress>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)" size="small">
                {{ getStatusName(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="use_count" label="使用次数" width="100"></el-table-column>
          
          <el-table-column prop="last_check_time" label="最后检测" width="160">
            <template #default="scope">
              <span>{{ formatTime(scope.row.last_check_time) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" v-if="isAdmin">
            <template #default="scope">
              <el-button
                type="text"
                size="small"
                @click="checkSingleDomain(scope.row)"
                :loading="scope.row.checking">
                检测
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="editDomain(scope.row)">
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 添加域名对话框 -->
    <el-dialog title="添加域名" :visible.sync="addDomainVisible" width="500px">
      <el-form :model="domainForm" :rules="domainRules" ref="domainForm" label-width="100px">
        <el-form-item label="域名" prop="domain">
          <el-input v-model="domainForm.domain" placeholder="例如: short.example.com"></el-input>
          <div class="form-tip">
            ⚠️ 请确保域名已正确解析到服务器，且已配置SSL证书
          </div>
        </el-form-item>
        
        <el-form-item label="域名类型" prop="domain_type">
          <el-select v-model="domainForm.domain_type" placeholder="选择域名类型">
            <el-option label="短链接域名" value="redirect"></el-option>
            <el-option label="中转页域名" value="landing"></el-option>
            <el-option label="API服务域名" value="api"></el-option>
          </el-select>
          <div class="form-tip">
            📝 建议：短链接域名用于生成短链接，中转页域名用于微信防红跳转
          </div>
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-slider v-model="domainForm.priority" :min="0" :max="100" show-input></el-slider>
          <div class="form-tip">
            💡 优先级越高，越优先使用。建议主域名设置90-100，备用域名设置60-80
          </div>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input v-model="domainForm.remarks" type="textarea" rows="2" placeholder="域名用途说明"></el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer">
        <el-button @click="addDomainVisible = false">取消</el-button>
        <el-button type="primary" @click="addDomain" :loading="submitting">添加域名</el-button>
      </div>
    </el-dialog>

    <!-- 帮助说明对话框 -->
    <el-dialog title="防红系统使用说明" :visible.sync="helpVisible" width="800px">
      <div class="help-content-detail">
        <h3>🛡️ 什么是防红系统？</h3>
        <p>防红系统是一套智能域名管理和短链接生成系统，专门用于防止推广链接被微信、QQ等平台检测和封禁。</p>
        
        <h3>🚀 主要功能特性</h3>
        <ul>
          <li><strong>智能域名轮换</strong>：自动选择最佳域名生成短链接</li>
          <li><strong>实时健康检测</strong>：每5分钟检测域名状态，发现异常立即处理</li>
          <li><strong>自动切换机制</strong>：域名被封时自动切换到备用域名</li>
          <li><strong>中转页面防护</strong>：微信/QQ访问自动跳转中转页面</li>
          <li><strong>详细访问统计</strong>：完整的点击数据和来源分析</li>
        </ul>
        
        <h3>⚙️ 配置步骤</h3>
        <ol>
          <li><strong>准备域名</strong>：至少准备3-5个域名，确保已解析和配置SSL</li>
          <li><strong>添加到域名池</strong>：在系统中添加域名，设置优先级</li>
          <li><strong>设置定时任务</strong>：确保服务器crontab已配置域名检测任务</li>
          <li><strong>测试功能</strong>：生成测试短链接，验证访问和跳转正常</li>
        </ol>
        
        <h3>📋 域名配置建议</h3>
        <el-table :data="domainRecommendations" style="margin: 10px 0;">
          <el-table-column prop="type" label="域名类型" width="120"></el-table-column>
          <el-table-column prop="count" label="建议数量" width="100"></el-table-column>
          <el-table-column prop="priority" label="优先级范围" width="120"></el-table-column>
          <el-table-column prop="purpose" label="主要用途"></el-table-column>
        </el-table>
        
        <h3>⚠️ 注意事项</h3>
        <el-alert type="warning" :closable="false" style="margin: 10px 0;">
          <ul style="margin: 0; padding-left: 20px;">
            <li>域名必须已备案并正确解析到服务器</li>
            <li>建议使用不同注册商、不同后缀的域名</li>
            <li>避免使用包含敏感词的域名</li>
            <li>定期检查域名到期时间，及时续费</li>
          </ul>
        </el-alert>
        
        <h3>🔧 定时任务配置</h3>
        <div class="code-block">
          <p>在服务器上添加以下crontab任务：</p>
          <pre>
# 每5分钟检测域名状态
*/5 * * * * cd /项目路径 && php artisan domains:check >/dev/null 2>&1

# 每小时全面检测
0 * * * * cd /项目路径 && php artisan domains:check --force >/dev/null 2>&1
          </pre>
        </div>
      </div>
    </el-dialog>

    <!-- 监控详情对话框 -->
    <el-dialog title="系统监控状态" :visible.sync="monitorVisible" width="600px">
      <div class="monitor-content">
        <h4>🔍 检测机制</h4>
        <ul>
          <li><strong>HTTP状态检测</strong>：检查域名是否能正常访问</li>
          <li><strong>DNS解析检测</strong>：验证域名解析是否正常</li>
          <li><strong>微信平台检测</strong>：模拟微信访问，检测是否被封</li>
          <li><strong>SSL证书检测</strong>：检查证书有效性和到期时间</li>
        </ul>
        
        <h4>⚡ 自动处理流程</h4>
        <div class="process-flow">
          <div class="flow-item">检测异常</div>
          <div class="flow-arrow">→</div>
          <div class="flow-item">标记状态</div>
          <div class="flow-arrow">→</div>
          <div class="flow-item">切换域名</div>
          <div class="flow-arrow">→</div>
          <div class="flow-item">发送告警</div>
        </div>
        
        <h4>📊 健康评分规则</h4>
        <el-table :data="healthRules" size="small">
          <el-table-column prop="condition" label="检测条件" width="150"></el-table-column>
          <el-table-column prop="penalty" label="扣分" width="80"></el-table-column>
          <el-table-column prop="description" label="说明"></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getAntiBlockStats, checkDomains, addDomain } from '@/api/anti-block'
import { useUserStore } from '@/stores/user'
import dayjs from 'dayjs'

const userStore = useUserStore()

// 响应式数据
const stats = ref({
  domain_stats: { total: 0, active: 0, abnormal: 0, blocked: 0 },
  link_stats: { total: 0, active: 0, today_created: 0, today_clicks: 0 }
})

const recentDomains = ref([])
const checking = ref(false)
const addDomainVisible = ref(false)
const helpVisible = ref(false)
const monitorVisible = ref(false)
const submitting = ref(false)

// 域名表单
const domainForm = ref({
  domain: '',
  domain_type: 'redirect',
  priority: 80,
  remarks: ''
})

const domainRules = {
  domain: [
    { required: true, message: '请输入域名', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/, message: '请输入有效的域名格式', trigger: 'blur' }
  ],
  domain_type: [
    { required: true, message: '请选择域名类型', trigger: 'change' }
  ]
}

// 配置建议数据
const domainRecommendations = [
  { type: '短链接域名', count: '3-5个', priority: '70-100', purpose: '生成防红短链接' },
  { type: '中转页域名', count: '2-3个', priority: '80-90', purpose: '微信/QQ跳转中转' },
  { type: 'API服务域名', count: '1-2个', priority: '90-100', purpose: 'API接口访问' }
]

// 健康评分规则
const healthRules = [
  { condition: 'HTTP访问失败', penalty: '-20分', description: '域名无法正常访问' },
  { condition: 'DNS解析失败', penalty: '-20分', description: '域名解析异常' },
  { condition: '微信检测封禁', penalty: '-50分', description: '被微信平台封禁' },
  { condition: '响应时间>5秒', penalty: '-10分', description: '访问速度过慢' }
]

// 计算属性
const isAdmin = computed(() => userStore.userInfo?.role === 'admin')

const systemHealthScore = computed(() => {
  if (!stats.value?.domain_stats?.total || stats.value.domain_stats.total === 0) return 0
  return Math.round((stats.value.domain_stats.active / stats.value.domain_stats.total) * 100)
})

const healthStatus = computed(() => {
  const score = systemHealthScore.value
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 60) return '一般'
  return '需要关注'
})

const healthClass = computed(() => {
  const score = systemHealthScore.value
  if (score >= 90) return 'text-success'
  if (score >= 80) return 'text-primary'
  if (score >= 60) return 'text-warning'
  return 'text-danger'
})

// 生命周期
onMounted(() => {
  loadStats()
  loadRecentDomains()
})
  
// 方法定义
const loadStats = async () => {
  try {
    console.log('🔄 开始加载防红系统统计数据...')

    // 临时使用Mock数据，匹配模板中的数据结构
    const mockData = {
      domain_stats: {
        total: 15,
        active: 12,
        abnormal: 2,
        blocked: 1
      },
      link_stats: {
        total: 1248,
        active: 1156,
        today_created: 23,
        today_clicks: 892,
        total_clicks: 45678
      },
      system_health: {
        score: 85,
        status: 'good'
      },
      domain_health: {
        excellent: 8,
        good: 4,
        warning: 2,
        poor: 1
      },
      recent_activity: [
        {
          id: 1,
          type: 'domain_check',
          domain: 'safe-domain-1.com',
          status: 'healthy',
          health_score: 95,
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          type: 'short_link_created',
          short_code: 'A6X8Y9Z0',
          original_url: 'https://example.com/landing/group/1',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          type: 'domain_warning',
          domain: 'backup-domain-1.com',
          status: 'warning',
          health_score: 75,
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
        }
      ]
    }

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    stats.value = mockData
    console.log('✅ 防红系统统计数据加载成功')

    // 同时尝试真实API调用（用于调试）
    try {
      const { data } = await getAntiBlockStats()
      console.log('✅ 真实API调用成功:', data)
      stats.value = data
    } catch (apiError) {
      console.error('❌ 真实API调用失败，使用Mock数据:', apiError)
    }
  } catch (error) {
    console.error('❌ 加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

const loadRecentDomains = async () => {
  // 这里应该调用获取域名列表的API
  // 暂时使用模拟数据
  recentDomains.value = [
    { 
      domain: 'short1.linkhub.pro', 
      domain_type: 'redirect', 
      health_score: 95, 
      status: 1, 
      use_count: 1250,
      last_check_time: new Date()
    },
    { 
      domain: 'short2.linkhub.pro', 
      domain_type: 'redirect', 
      health_score: 88, 
      status: 1, 
      use_count: 856,
      last_check_time: new Date()
    },
    { 
      domain: 'landing.linkhub.pro', 
      domain_type: 'landing', 
      health_score: 100, 
      status: 1, 
      use_count: 432,
      last_check_time: new Date()
    }
  ]
}

const checkAllDomains = async () => {
  checking.value = true
  try {
    await checkDomains({ limit: 20 })
    ElMessage.success('域名检测完成')
    loadStats()
    loadRecentDomains()
  } catch (error) {
    ElMessage.error('域名检测失败')
  } finally {
    checking.value = false
  }
}

const checkSingleDomain = async (domain) => {
  domain.checking = true
  try {
    // 调用单个域名检测API
    ElMessage.success(`${domain.domain} 检测完成`)
  } catch (error) {
    ElMessage.error('检测失败')
  } finally {
    domain.checking = false
  }
}

const showAddDomainDialog = () => {
  addDomainVisible.value = true
  domainForm.value = {
    domain: '',
    domain_type: 'redirect',
    priority: 80,
    remarks: ''
  }
}

const addDomainAction = async () => {
  try {
    // 使用 ref 获取表单实例进行验证
    await domainForm.value.validate()
    submitting.value = true
    
    await addDomain(domainForm.value)
    ElMessage.success('域名添加成功')
    addDomainVisible.value = false
    loadStats()
    loadRecentDomains()
  } catch (error) {
    if (error.fields) return // 表单验证错误
    ElMessage.error('添加域名失败')
  } finally {
    submitting.value = false
  }
}

const editDomain = (domain) => {
  // 这里需要使用 router 进行导航
  // 由于是 composition API，需要使用 useRouter
}

const refreshDomains = () => {
  loadRecentDomains()
}

const showHelpDialog = () => {
  helpVisible.value = true
}

const showMonitorDialog = () => {
  monitorVisible.value = true
}

// 辅助方法
const getDomainTypeName = (type) => {
  const types = {
    'redirect': '短链接',
    'landing': '中转页',
    'api': 'API服务'
  }
  return types[type] || type
}

const getDomainTypeColor = (type) => {
  const colors = {
    'redirect': 'primary',
    'landing': 'success',
    'api': 'warning'
  }
  return colors[type] || ''
}

const getStatusName = (status) => {
  const statuses = {
    1: '正常',
    2: '异常', 
    3: '封禁',
    4: '维护'
  }
  return statuses[status] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    1: 'success',
    2: 'warning',
    3: 'danger',
    4: 'info'
  }
  return colors[status] || ''
}

const getHealthColor = (score) => {
  if (score >= 90) return '#67c23a'
  if (score >= 80) return '#409eff'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}

const formatTime = (time) => {
  if (!time) return '-'
  return dayjs(time).format('MM-DD HH:mm')
}
</script>

<style lang="scss" scoped>
.anti-block-dashboard {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  
  .page-title {
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      color: #303133;
    }
    
    .page-desc {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .quick-actions {
    display: flex;
    gap: 10px;
  }
}

.stats-overview {
  margin-bottom: 30px;
  
  .stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      
      i {
        font-size: 24px;
        color: white;
      }
      
      &.domain-icon {
        background: linear-gradient(45deg, #409eff, #67c23a);
      }
      
      &.link-icon {
        background: linear-gradient(45deg, #67c23a, #e6a23c);
      }
      
      &.click-icon {
        background: linear-gradient(45deg, #e6a23c, #f56c6c);
      }
      
      &.health-icon {
        background: linear-gradient(45deg, #f56c6c, #409eff);
      }
    }
    
    .stat-content {
      flex: 1;
      
      .stat-number {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 5px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .stat-detail {
        font-size: 12px;
        
        span {
          margin-right: 10px;
          
          &.text-success { color: #67c23a; }
          &.text-warning { color: #e6a23c; }
          &.text-danger { color: #f56c6c; }
          &.text-info { color: #909399; }
          &.text-primary { color: #409eff; }
        }
      }
    }
  }
}

.help-section {
  margin-bottom: 30px;
  
  .help-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .help-item {
      display: flex;
      align-items: flex-start;
      
      .help-step {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #409eff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 15px;
        flex-shrink: 0;
      }
      
      .help-content {
        flex: 1;
        
        h4 {
          margin: 0 0 8px 0;
          color: #303133;
        }
        
        p {
          margin: 0 0 10px 0;
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }
}

.domain-status {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .domain-text {
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 13px;
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.help-content-detail {
  h3 {
    color: #303133;
    margin: 20px 0 10px 0;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  p {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 15px;
  }
  
  ul, ol {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 15px;
    
    li {
      margin-bottom: 5px;
    }
  }
  
  .code-block {
    background: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    margin: 10px 0;
    
    pre {
      margin: 10px 0 0 0;
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 12px;
      line-height: 1.4;
      color: #303133;
    }
  }
}

.monitor-content {
  h4 {
    color: #303133;
    margin: 20px 0 10px 0;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  ul {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 20px;
    
    li {
      margin-bottom: 8px;
    }
  }
  
  .process-flow {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
    
    .flow-item {
      padding: 8px 16px;
      background: #409eff;
      color: white;
      border-radius: 4px;
      font-size: 12px;
    }
    
    .flow-arrow {
      margin: 0 10px;
      color: #909399;
      font-weight: bold;
    }
  }
}
</style> 