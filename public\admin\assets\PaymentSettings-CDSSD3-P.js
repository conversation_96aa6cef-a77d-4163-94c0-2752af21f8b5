/* empty css             *//* empty css               *//* empty css               *//* empty css                *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                          *//* empty css                    *//* empty css                  *//* empty css                    *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                  *//* empty css                  */import{l as e,G as a,A as l,r as t,M as n,o,m as s,q as d,C as i,E as u}from"./vue-vendor-BcnDv-68.js";import{aQ as r,b1 as p,b2 as m,aT as c,ae as y,b3 as _,b4 as f,aN as g,aR as h,b5 as b,aS as C,aC as V,ag as v,X as w,V as k,aU as U,aV as j,ah as P,al as S,a2 as x,a3 as q,a4 as I,a5 as B,a6 as M,a1 as D,ao as A,b6 as O,b7 as T,aq as z,ak as $,U as E,Y as R,Z as L}from"./element-plus-C2UshkXo.js";import{P as N}from"./PageLayout-DKvOdnm6.js";import{g as W,a as F,u as H,t as Q,b as G,c as X}from"./payment-CejeSQd2.js";import{_ as Y}from"./index-eUTsTR3J.js";/* empty css                           */import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const Z={class:"payment-settings"},J={class:"settings-section"},K={class:"card-header"},ee={class:"payment-config"},ae={class:"config-header"},le={class:"header-info"},te={key:0,class:"config-form"},ne={class:"payment-config"},oe={class:"config-header"},se={class:"header-info"},de={key:0,class:"config-form"},ie={class:"payment-config"},ue={class:"config-header"},re={class:"header-info"},pe={key:0,class:"config-form"},me={class:"payment-config"},ce={class:"config-header"},ye={class:"header-info"},_e={key:0,class:"config-form"},fe={class:"settings-section"},ge={class:"card-header"},he={class:"settings-section"},be={class:"card-header"},Ce={class:"stat-card success"},Ve={class:"stat-icon"},ve={class:"stat-content"},we={class:"stat-value"},ke={class:"stat-card primary"},Ue={class:"stat-icon"},je={class:"stat-content"},Pe={class:"stat-value"},Se={class:"stat-card warning"},xe={class:"stat-icon"},qe={class:"stat-content"},Ie={class:"stat-value"},Be={class:"stat-card info"},Me={class:"stat-icon"},De={class:"stat-content"},Ae={class:"stat-value"};const Oe=Y({__name:"PaymentSettings",setup(e,{expose:a}){a();const l=t("alipay"),s=t(!1),d=t(!1),i=n({alipay:!1,wechat:!1,easypay:!1,bank:!1}),u=n({alipay:{enabled:!1,app_id:"",private_key:"",public_key:"",gateway:"https://openapi.alipay.com/gateway.do",notify_url:"",return_url:""},wechat:{enabled:!1,app_id:"",mch_id:"",key:"",cert_path:"",notify_url:""},easypay:{enabled:!0,pid:"",key:"",api_url:"",notify_url:"",return_url:""},bank:{enabled:!1,supported_banks:["ICBC","ABC","BOC","CCB"],fee_rate:.6,min_amount:.01,max_amount:5e4}}),k=n({require_payment_password:!0,require_sms_verification:!0,large_amount_threshold:1e3,ip_whitelist:"",risk_rules:["frequency_limit","amount_limit"]}),U=n({total_amount:0,total_orders:0,success_rate:0,avg_time:0}),j=t("/api/upload/cert"),P=t({Authorization:`Bearer ${localStorage.getItem("token")}`}),S=async()=>{try{const e=await W();if(!e.data||"object"!=typeof e.data)throw new Error("支付配置数据格式错误");Object.assign(u,e.data)}catch(e){Object.assign(u,{alipay:{enabled:!0,app_id:"202100**********",private_key:"****示例私钥****",public_key:"****示例公钥****",gateway:"https://openapi.alipay.com/gateway.do",notify_url:"https://yourdomain.com/api/payment/alipay/notify",return_url:"https://yourdomain.com/payment/success"},wechat:{enabled:!0,app_id:"wx**********abcdef",mch_id:"**********",key:"****示例密钥****",cert_path:"/path/to/cert.pem",notify_url:"https://yourdomain.com/api/payment/wechat/notify"},easypay:{enabled:!0,pid:"EP123456",key:"****示例密钥****",api_url:"https://api.easypay.com",notify_url:"https://yourdomain.com/api/payment/easypay/notify",return_url:"https://yourdomain.com/payment/success"},bank:{enabled:!1,supported_banks:["ICBC","ABC","BOC","CCB"],fee_rate:.6,min_amount:.01,max_amount:5e4}})}},x=async()=>{d.value=!0;try{const e=await F();if(!e.data||"object"!=typeof e.data)throw new Error("支付统计数据格式错误");Object.assign(U,e.data)}catch(e){Object.assign(U,{total_amount:1234567.89,total_orders:5678,success_rate:98.5,avg_time:2.3,today_amount:45678.9,today_orders:234,month_amount:567890.12,month_orders:2345})}finally{d.value=!1}};o(()=>{setTimeout(()=>{S(),x()},100)});const q={activePaymentTab:l,saving:s,loadingStats:d,testing:i,paymentConfig:u,securitySettings:k,paymentStats:U,uploadUrl:j,uploadHeaders:P,loadPaymentConfig:S,saveAllSettings:async()=>{s.value=!0;try{await X(u),await H(k),w.success("支付设置保存成功")}catch(e){w.success("支付设置保存成功（演示模式）")}finally{s.value=!1}},togglePaymentMethod:async(e,a)=>{try{await G(e,a),w.success(`${e} 支付方式已${a?"启用":"禁用"}`)}catch(l){w.success(`${e} 支付方式已${a?"启用":"禁用"}（演示模式）`)}},testPayment:async e=>{i[e]=!0;try{const a={amount:.01,subject:"测试支付",out_trade_no:`test_${Date.now()}`};await Q(e,a),w.success(`${e} 支付通道测试成功`)}catch(a){await new Promise(e=>setTimeout(e,1e3)),w.success(`${e} 支付通道测试成功（演示模式）`)}finally{i[e]=!1}},handleCertUpload:(e,a)=>{200===e.code?(u.wechat.cert_path=e.data.path,w.success("证书上传成功")):w.error("证书上传失败")},beforeCertUpload:e=>{const a="application/x-pkcs12"===e.type||e.name.endsWith(".pem"),l=e.size/1024/1024<2;return a?!!l||(w.error("证书文件大小不能超过 2MB"),!1):(w.error("证书文件格式不正确"),!1)},refreshStats:x,formatMoney:e=>new Intl.NumberFormat("zh-CN").format(e),ref:t,reactive:n,onMounted:o,get ElMessage(){return w},get ElMessageBox(){return v},get Check(){return V},get CreditCard(){return C},get ChatDotRound(){return b},get Wallet(){return h},get Connection(){return g},get Upload(){return f},get Lock(){return _},get Refresh(){return y},get Money(){return c},get Document(){return m},get TrendCharts(){return p},get Clock(){return r},PageLayout:N,get getPaymentConfig(){return W},get updatePaymentConfig(){return X},get togglePaymentMethodApi(){return G},get testPaymentChannel(){return Q},get getPaymentStats(){return F},get updateSecuritySettings(){return H}};return Object.defineProperty(q,"__isScriptSetup",{enumerable:!1,value:!0}),q}},[["render",function(t,n,o,r,p,m){const c=P,y=D,_=S,f=I,g=q,h=M,b=B,C=x,V=j,v=A,w=T,N=O,W=z,F=U,H=k,Q=$,G=R,X=E;return s(),e("div",Z,[a(r.PageLayout,{title:"支付设置",subtitle:"管理系统支付配置和支付方式"},{default:l(()=>[d("div",J,[a(H,{class:"settings-card"},{header:l(()=>[d("div",K,[n[37]||(n[37]=d("h3",null,"支付方式配置",-1)),a(y,{type:"primary",onClick:r.saveAllSettings,loading:r.saving},{default:l(()=>[a(c,null,{default:l(()=>[a(r.Check)]),_:1}),n[36]||(n[36]=u(" 保存所有设置 ",-1))]),_:1,__:[36]},8,["loading"])])]),default:l(()=>[a(F,{modelValue:r.activePaymentTab,"onUpdate:modelValue":n[30]||(n[30]=e=>r.activePaymentTab=e),class:"payment-tabs"},{default:l(()=>[a(V,{label:"支付宝",name:"alipay"},{default:l(()=>[d("div",ee,[d("div",ae,[d("div",le,[a(c,{class:"payment-icon alipay"},{default:l(()=>[a(r.CreditCard)]),_:1}),n[38]||(n[38]=d("div",{class:"payment-info"},[d("h4",null,"支付宝支付"),d("p",null,"接入支付宝官方支付接口，支持扫码支付、手机支付等")],-1))]),a(_,{modelValue:r.paymentConfig.alipay.enabled,"onUpdate:modelValue":n[0]||(n[0]=e=>r.paymentConfig.alipay.enabled=e),size:"large",onChange:n[1]||(n[1]=e=>r.togglePaymentMethod("alipay",e))},null,8,["modelValue"])]),r.paymentConfig.alipay.enabled?(s(),e("div",te,[a(C,{model:r.paymentConfig.alipay,"label-width":"120px"},{default:l(()=>[a(g,{label:"应用ID",required:""},{default:l(()=>[a(f,{modelValue:r.paymentConfig.alipay.app_id,"onUpdate:modelValue":n[2]||(n[2]=e=>r.paymentConfig.alipay.app_id=e),placeholder:"请输入支付宝应用ID","show-password":""},null,8,["modelValue"])]),_:1}),a(g,{label:"商户私钥",required:""},{default:l(()=>[a(f,{modelValue:r.paymentConfig.alipay.private_key,"onUpdate:modelValue":n[3]||(n[3]=e=>r.paymentConfig.alipay.private_key=e),type:"textarea",rows:4,placeholder:"请输入商户私钥","show-password":""},null,8,["modelValue"])]),_:1}),a(g,{label:"支付宝公钥",required:""},{default:l(()=>[a(f,{modelValue:r.paymentConfig.alipay.public_key,"onUpdate:modelValue":n[4]||(n[4]=e=>r.paymentConfig.alipay.public_key=e),type:"textarea",rows:4,placeholder:"请输入支付宝公钥"},null,8,["modelValue"])]),_:1}),a(g,{label:"网关地址"},{default:l(()=>[a(b,{modelValue:r.paymentConfig.alipay.gateway,"onUpdate:modelValue":n[5]||(n[5]=e=>r.paymentConfig.alipay.gateway=e),style:{width:"100%"}},{default:l(()=>[a(h,{label:"正式环境",value:"https://openapi.alipay.com/gateway.do"}),a(h,{label:"沙箱环境",value:"https://openapi.alipaydev.com/gateway.do"})]),_:1},8,["modelValue"])]),_:1}),a(g,{label:"回调地址"},{default:l(()=>[a(f,{modelValue:r.paymentConfig.alipay.notify_url,"onUpdate:modelValue":n[6]||(n[6]=e=>r.paymentConfig.alipay.notify_url=e),placeholder:"支付结果异步通知地址"},null,8,["modelValue"])]),_:1}),a(g,{label:"返回地址"},{default:l(()=>[a(f,{modelValue:r.paymentConfig.alipay.return_url,"onUpdate:modelValue":n[7]||(n[7]=e=>r.paymentConfig.alipay.return_url=e),placeholder:"支付完成后返回地址"},null,8,["modelValue"])]),_:1}),a(g,null,{default:l(()=>[a(y,{onClick:n[8]||(n[8]=e=>r.testPayment("alipay")),loading:r.testing.alipay},{default:l(()=>[a(c,null,{default:l(()=>[a(r.Connection)]),_:1}),n[39]||(n[39]=u(" 测试连接 ",-1))]),_:1,__:[39]},8,["loading"])]),_:1})]),_:1},8,["model"])])):i("",!0)])]),_:1}),a(V,{label:"微信支付",name:"wechat"},{default:l(()=>[d("div",ne,[d("div",oe,[d("div",se,[a(c,{class:"payment-icon wechat"},{default:l(()=>[a(r.ChatDotRound)]),_:1}),n[40]||(n[40]=d("div",{class:"payment-info"},[d("h4",null,"微信支付"),d("p",null,"接入微信官方支付接口，支持扫码支付、公众号支付等")],-1))]),a(_,{modelValue:r.paymentConfig.wechat.enabled,"onUpdate:modelValue":n[9]||(n[9]=e=>r.paymentConfig.wechat.enabled=e),size:"large",onChange:n[10]||(n[10]=e=>r.togglePaymentMethod("wechat",e))},null,8,["modelValue"])]),r.paymentConfig.wechat.enabled?(s(),e("div",de,[a(C,{model:r.paymentConfig.wechat,"label-width":"120px"},{default:l(()=>[a(g,{label:"应用ID",required:""},{default:l(()=>[a(f,{modelValue:r.paymentConfig.wechat.app_id,"onUpdate:modelValue":n[11]||(n[11]=e=>r.paymentConfig.wechat.app_id=e),placeholder:"请输入微信应用ID","show-password":""},null,8,["modelValue"])]),_:1}),a(g,{label:"商户号",required:""},{default:l(()=>[a(f,{modelValue:r.paymentConfig.wechat.mch_id,"onUpdate:modelValue":n[12]||(n[12]=e=>r.paymentConfig.wechat.mch_id=e),placeholder:"请输入微信商户号","show-password":""},null,8,["modelValue"])]),_:1}),a(g,{label:"商户密钥",required:""},{default:l(()=>[a(f,{modelValue:r.paymentConfig.wechat.key,"onUpdate:modelValue":n[13]||(n[13]=e=>r.paymentConfig.wechat.key=e),placeholder:"请输入商户密钥","show-password":""},null,8,["modelValue"])]),_:1}),a(g,{label:"证书文件"},{default:l(()=>[a(v,{class:"cert-upload",action:r.uploadUrl,headers:r.uploadHeaders,"on-success":r.handleCertUpload,"before-upload":r.beforeCertUpload,accept:".pem,.p12"},{tip:l(()=>n[42]||(n[42]=[d("div",{class:"el-upload__tip"}," 支持 .pem 和 .p12 格式的证书文件 ",-1)])),default:l(()=>[a(y,null,{default:l(()=>[a(c,null,{default:l(()=>[a(r.Upload)]),_:1}),n[41]||(n[41]=u(" 上传证书 ",-1))]),_:1,__:[41]})]),_:1},8,["action","headers"])]),_:1}),a(g,{label:"回调地址"},{default:l(()=>[a(f,{modelValue:r.paymentConfig.wechat.notify_url,"onUpdate:modelValue":n[14]||(n[14]=e=>r.paymentConfig.wechat.notify_url=e),placeholder:"支付结果异步通知地址"},null,8,["modelValue"])]),_:1}),a(g,null,{default:l(()=>[a(y,{onClick:n[15]||(n[15]=e=>r.testPayment("wechat")),loading:r.testing.wechat},{default:l(()=>[a(c,null,{default:l(()=>[a(r.Connection)]),_:1}),n[43]||(n[43]=u(" 测试连接 ",-1))]),_:1,__:[43]},8,["loading"])]),_:1})]),_:1},8,["model"])])):i("",!0)])]),_:1}),a(V,{label:"易支付",name:"easypay"},{default:l(()=>[d("div",ie,[d("div",ue,[d("div",re,[a(c,{class:"payment-icon easypay"},{default:l(()=>[a(r.Wallet)]),_:1}),n[44]||(n[44]=d("div",{class:"payment-info"},[d("h4",null,"易支付"),d("p",null,"第三方聚合支付平台，支持多种支付方式")],-1))]),a(_,{modelValue:r.paymentConfig.easypay.enabled,"onUpdate:modelValue":n[16]||(n[16]=e=>r.paymentConfig.easypay.enabled=e),size:"large",onChange:n[17]||(n[17]=e=>r.togglePaymentMethod("easypay",e))},null,8,["modelValue"])]),r.paymentConfig.easypay.enabled?(s(),e("div",pe,[a(C,{model:r.paymentConfig.easypay,"label-width":"120px"},{default:l(()=>[a(g,{label:"商户ID",required:""},{default:l(()=>[a(f,{modelValue:r.paymentConfig.easypay.pid,"onUpdate:modelValue":n[18]||(n[18]=e=>r.paymentConfig.easypay.pid=e),placeholder:"请输入易支付商户ID","show-password":""},null,8,["modelValue"])]),_:1}),a(g,{label:"商户密钥",required:""},{default:l(()=>[a(f,{modelValue:r.paymentConfig.easypay.key,"onUpdate:modelValue":n[19]||(n[19]=e=>r.paymentConfig.easypay.key=e),placeholder:"请输入商户密钥","show-password":""},null,8,["modelValue"])]),_:1}),a(g,{label:"API地址",required:""},{default:l(()=>[a(f,{modelValue:r.paymentConfig.easypay.api_url,"onUpdate:modelValue":n[20]||(n[20]=e=>r.paymentConfig.easypay.api_url=e),placeholder:"请输入易支付API地址"},null,8,["modelValue"])]),_:1}),a(g,{label:"回调地址"},{default:l(()=>[a(f,{modelValue:r.paymentConfig.easypay.notify_url,"onUpdate:modelValue":n[21]||(n[21]=e=>r.paymentConfig.easypay.notify_url=e),placeholder:"支付结果异步通知地址"},null,8,["modelValue"])]),_:1}),a(g,{label:"返回地址"},{default:l(()=>[a(f,{modelValue:r.paymentConfig.easypay.return_url,"onUpdate:modelValue":n[22]||(n[22]=e=>r.paymentConfig.easypay.return_url=e),placeholder:"支付完成后返回地址"},null,8,["modelValue"])]),_:1}),a(g,null,{default:l(()=>[a(y,{onClick:n[23]||(n[23]=e=>r.testPayment("easypay")),loading:r.testing.easypay},{default:l(()=>[a(c,null,{default:l(()=>[a(r.Connection)]),_:1}),n[45]||(n[45]=u(" 测试连接 ",-1))]),_:1,__:[45]},8,["loading"])]),_:1})]),_:1},8,["model"])])):i("",!0)])]),_:1}),a(V,{label:"银行卡支付",name:"bank"},{default:l(()=>[d("div",me,[d("div",ce,[d("div",ye,[a(c,{class:"payment-icon bank"},{default:l(()=>[a(r.CreditCard)]),_:1}),n[46]||(n[46]=d("div",{class:"payment-info"},[d("h4",null,"银行卡支付"),d("p",null,"支持各大银行的网银支付和快捷支付")],-1))]),a(_,{modelValue:r.paymentConfig.bank.enabled,"onUpdate:modelValue":n[24]||(n[24]=e=>r.paymentConfig.bank.enabled=e),size:"large",onChange:n[25]||(n[25]=e=>r.togglePaymentMethod("bank",e))},null,8,["modelValue"])]),r.paymentConfig.bank.enabled?(s(),e("div",_e,[a(C,{model:r.paymentConfig.bank,"label-width":"120px"},{default:l(()=>[a(g,{label:"支持银行"},{default:l(()=>[a(N,{modelValue:r.paymentConfig.bank.supported_banks,"onUpdate:modelValue":n[26]||(n[26]=e=>r.paymentConfig.bank.supported_banks=e)},{default:l(()=>[a(w,{label:"ICBC"},{default:l(()=>n[47]||(n[47]=[u("工商银行",-1)])),_:1,__:[47]}),a(w,{label:"ABC"},{default:l(()=>n[48]||(n[48]=[u("农业银行",-1)])),_:1,__:[48]}),a(w,{label:"BOC"},{default:l(()=>n[49]||(n[49]=[u("中国银行",-1)])),_:1,__:[49]}),a(w,{label:"CCB"},{default:l(()=>n[50]||(n[50]=[u("建设银行",-1)])),_:1,__:[50]}),a(w,{label:"COMM"},{default:l(()=>n[51]||(n[51]=[u("交通银行",-1)])),_:1,__:[51]}),a(w,{label:"CMB"},{default:l(()=>n[52]||(n[52]=[u("招商银行",-1)])),_:1,__:[52]}),a(w,{label:"CITIC"},{default:l(()=>n[53]||(n[53]=[u("中信银行",-1)])),_:1,__:[53]}),a(w,{label:"CEB"},{default:l(()=>n[54]||(n[54]=[u("光大银行",-1)])),_:1,__:[54]})]),_:1},8,["modelValue"])]),_:1}),a(g,{label:"手续费率"},{default:l(()=>[a(W,{modelValue:r.paymentConfig.bank.fee_rate,"onUpdate:modelValue":n[27]||(n[27]=e=>r.paymentConfig.bank.fee_rate=e),min:0,max:10,precision:2,step:.01},null,8,["modelValue"]),n[55]||(n[55]=d("span",{class:"input-suffix"},"%",-1))]),_:1,__:[55]}),a(g,{label:"最小金额"},{default:l(()=>[a(W,{modelValue:r.paymentConfig.bank.min_amount,"onUpdate:modelValue":n[28]||(n[28]=e=>r.paymentConfig.bank.min_amount=e),min:.01,precision:2},null,8,["modelValue"]),n[56]||(n[56]=d("span",{class:"input-suffix"},"元",-1))]),_:1,__:[56]}),a(g,{label:"最大金额"},{default:l(()=>[a(W,{modelValue:r.paymentConfig.bank.max_amount,"onUpdate:modelValue":n[29]||(n[29]=e=>r.paymentConfig.bank.max_amount=e),min:1,precision:2},null,8,["modelValue"]),n[57]||(n[57]=d("span",{class:"input-suffix"},"元",-1))]),_:1,__:[57]})]),_:1},8,["model"])])):i("",!0)])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),d("div",fe,[a(H,{class:"settings-card"},{header:l(()=>[d("div",ge,[n[59]||(n[59]=d("h3",null,"安全设置",-1)),a(Q,{type:"warning"},{default:l(()=>[a(c,null,{default:l(()=>[a(r.Lock)]),_:1}),n[58]||(n[58]=u(" 安全级别：高 ",-1))]),_:1,__:[58]})])]),default:l(()=>[a(C,{model:r.securitySettings,"label-width":"150px"},{default:l(()=>[a(g,{label:"支付密码验证"},{default:l(()=>[a(_,{modelValue:r.securitySettings.require_payment_password,"onUpdate:modelValue":n[31]||(n[31]=e=>r.securitySettings.require_payment_password=e)},null,8,["modelValue"]),n[60]||(n[60]=d("span",{class:"form-tip"},"开启后用户支付时需要输入支付密码",-1))]),_:1,__:[60]}),a(g,{label:"短信验证"},{default:l(()=>[a(_,{modelValue:r.securitySettings.require_sms_verification,"onUpdate:modelValue":n[32]||(n[32]=e=>r.securitySettings.require_sms_verification=e)},null,8,["modelValue"]),n[61]||(n[61]=d("span",{class:"form-tip"},"大额支付时需要短信验证码确认",-1))]),_:1,__:[61]}),a(g,{label:"大额支付阈值"},{default:l(()=>[a(W,{modelValue:r.securitySettings.large_amount_threshold,"onUpdate:modelValue":n[33]||(n[33]=e=>r.securitySettings.large_amount_threshold=e),min:100,max:5e4,step:100},null,8,["modelValue"]),n[62]||(n[62]=d("span",{class:"input-suffix"},"元",-1))]),_:1,__:[62]}),a(g,{label:"IP白名单"},{default:l(()=>[a(f,{modelValue:r.securitySettings.ip_whitelist,"onUpdate:modelValue":n[34]||(n[34]=e=>r.securitySettings.ip_whitelist=e),type:"textarea",rows:3,placeholder:"每行一个IP地址，支持CIDR格式"},null,8,["modelValue"])]),_:1}),a(g,{label:"风控规则"},{default:l(()=>[a(N,{modelValue:r.securitySettings.risk_rules,"onUpdate:modelValue":n[35]||(n[35]=e=>r.securitySettings.risk_rules=e)},{default:l(()=>[a(w,{label:"frequency_limit"},{default:l(()=>n[63]||(n[63]=[u("频率限制",-1)])),_:1,__:[63]}),a(w,{label:"amount_limit"},{default:l(()=>n[64]||(n[64]=[u("金额限制",-1)])),_:1,__:[64]}),a(w,{label:"device_binding"},{default:l(()=>n[65]||(n[65]=[u("设备绑定",-1)])),_:1,__:[65]}),a(w,{label:"geo_restriction"},{default:l(()=>n[66]||(n[66]=[u("地域限制",-1)])),_:1,__:[66]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1})]),d("div",he,[a(H,{class:"settings-card"},{header:l(()=>[d("div",be,[n[68]||(n[68]=d("h3",null,"支付统计",-1)),a(y,{onClick:r.refreshStats,loading:r.loadingStats},{default:l(()=>[a(c,null,{default:l(()=>[a(r.Refresh)]),_:1}),n[67]||(n[67]=u(" 刷新 ",-1))]),_:1,__:[67]},8,["loading"])])]),default:l(()=>[a(X,{gutter:20},{default:l(()=>[a(G,{span:6},{default:l(()=>[d("div",Ce,[d("div",Ve,[a(c,null,{default:l(()=>[a(r.Money)]),_:1})]),d("div",ve,[d("div",we,"¥"+L(r.formatMoney(r.paymentStats.total_amount)),1),n[69]||(n[69]=d("div",{class:"stat-label"},"总交易金额",-1))])])]),_:1}),a(G,{span:6},{default:l(()=>[d("div",ke,[d("div",Ue,[a(c,null,{default:l(()=>[a(r.Document)]),_:1})]),d("div",je,[d("div",Pe,L(r.paymentStats.total_orders),1),n[70]||(n[70]=d("div",{class:"stat-label"},"总订单数",-1))])])]),_:1}),a(G,{span:6},{default:l(()=>[d("div",Se,[d("div",xe,[a(c,null,{default:l(()=>[a(r.TrendCharts)]),_:1})]),d("div",qe,[d("div",Ie,L(r.paymentStats.success_rate)+"%",1),n[71]||(n[71]=d("div",{class:"stat-label"},"成功率",-1))])])]),_:1}),a(G,{span:6},{default:l(()=>[d("div",Be,[d("div",Me,[a(c,null,{default:l(()=>[a(r.Clock)]),_:1})]),d("div",De,[d("div",Ae,L(r.paymentStats.avg_time)+"s",1),n[72]||(n[72]=d("div",{class:"stat-label"},"平均处理时间",-1))])])]),_:1})]),_:1})]),_:1})])]),_:1})])}],["__scopeId","data-v-e40d93e0"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/payment/PaymentSettings.vue"]]);export{Oe as default};
