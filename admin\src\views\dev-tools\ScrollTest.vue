<template>
  <div class="scroll-test">
    <div class="page-header">
      <h1>📜 滚动测试页面</h1>
      <p>这个页面用于测试滚动功能是否正常工作</p>
    </div>

    <div class="content-sections">
      <div v-for="i in 20" :key="i" class="test-section">
        <h2>测试区域 {{ i }}</h2>
        <p>这是第 {{ i }} 个测试区域的内容。页面应该可以正常滚动。</p>
        <div class="test-content">
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
          <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>
        
        <div class="test-buttons">
          <button class="test-btn" @click="scrollToTop">回到顶部</button>
          <button class="test-btn" @click="scrollToBottom">滚动到底部</button>
          <button class="test-btn" @click="scrollToSection(i + 1)">下一个区域</button>
        </div>
      </div>
    </div>

    <div class="scroll-info">
      <h3>滚动信息</h3>
      <p>当前滚动位置: {{ scrollY }}px</p>
      <p>页面总高度: {{ pageHeight }}px</p>
      <p>可视区域高度: {{ viewportHeight }}px</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const scrollY = ref(0)
const pageHeight = ref(0)
const viewportHeight = ref(0)

const updateScrollInfo = () => {
  scrollY.value = window.scrollY || document.documentElement.scrollTop
  pageHeight.value = document.documentElement.scrollHeight
  viewportHeight.value = window.innerHeight
}

const scrollToTop = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

const scrollToBottom = () => {
  window.scrollTo({ top: document.documentElement.scrollHeight, behavior: 'smooth' })
}

const scrollToSection = (sectionNumber) => {
  const element = document.querySelector(`.test-section:nth-child(${sectionNumber})`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

onMounted(() => {
  updateScrollInfo()
  window.addEventListener('scroll', updateScrollInfo)
  window.addEventListener('resize', updateScrollInfo)
})

onUnmounted(() => {
  window.removeEventListener('scroll', updateScrollInfo)
  window.removeEventListener('resize', updateScrollInfo)
})
</script>

<style scoped>
.scroll-test {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
}

.page-header h1 {
  color: #1f2937;
  margin-bottom: 8px;
}

.page-header p {
  color: #6b7280;
}

.test-section {
  background: white;
  margin-bottom: 24px;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3b82f6;
}

.test-section h2 {
  color: #1f2937;
  margin-bottom: 16px;
}

.test-content {
  margin: 16px 0;
  line-height: 1.6;
  color: #4b5563;
}

.test-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 16px;
}

.test-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.test-btn:hover {
  background: #2563eb;
}

.scroll-info {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 16px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 1000;
}

.scroll-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.scroll-info p {
  margin: 4px 0;
}

@media (max-width: 768px) {
  .scroll-test {
    padding: 16px;
  }
  
  .scroll-info {
    position: relative;
    top: auto;
    right: auto;
    margin-top: 24px;
    background: #f3f4f6;
    color: #1f2937;
  }
  
  .test-buttons {
    flex-direction: column;
  }
  
  .test-btn {
    width: 100%;
  }
}
</style>
