/* empty css             *//* empty css                   *//* empty css                             *//* empty css                   *//* empty css                 *//* empty css                        *//* empty css                   *//* empty css                  *//* empty css                    *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css               *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                *//* empty css                  *//* empty css                       *//* empty css                        *//* empty css                *//* empty css                     *//* empty css                  */import{l as e,q as a,G as l,B as t,A as o,z as r,r as d,M as i,c as u,o as s,m as n,E as m,W as p,F as c,Y as _,C as f}from"./vue-vendor-BcnDv-68.js";import{a7 as g,X as v,a1 as b,V as y,W as V,a8 as w,a9 as h,aa as U,ab as k,ac as C,ad as j,ae as x,af as q,ag as T,ah as D,a2 as P,a3 as z,a4 as E,a5 as L,a6 as S,Z as M,_ as F,$ as B,ai as O,aj as R,ak as A,al as Q,am as W,U as $,Y as I,s as N,an as Z,ao as H,ap as K,aq as Y,ar as G,as as J}from"./element-plus-C2UshkXo.js";import{_ as X,g as ee}from"./index-eUTsTR3J.js";import{f as ae}from"./format-3eU4VJ9V.js";import{g as le,a as te,t as oe,d as re,u as de,c as ie,b as ue}from"./community-CUcF7leP.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";import"./chunk-KZPPZA2C-C8HwxGb3.js";const se={class:"template-management"},ne={class:"page-header"},me={class:"header-actions"},pe={class:"table-header"},ce={class:"table-title"},_e={class:"table-actions"},fe={class:"template-info"},ge={class:"template-cover"},ve=["src"],be={class:"template-details"},ye={class:"template-name"},Ve={class:"template-code"},we={class:"template-desc"},he={key:1,class:"card-view"},Ue={class:"card-cover"},ke=["src"],Ce={key:1,class:"no-image"},je={class:"card-overlay"},xe={class:"card-content"},qe={class:"card-header"},Te={class:"card-title"},De={class:"card-badges"},Pe={class:"card-desc"},ze={class:"card-stats"},Ee={class:"stat-item"},Le={class:"stat-item"},Se={class:"pagination-wrapper"},Me=["src"],Fe={class:"custom-fields-config"},Be={class:"dialog-footer"},Oe={class:"template-preview"},Re={class:"preview-header"},Ae={class:"preview-content"},Qe={key:0,class:"preview-description"};const We=X({__name:"TemplateManagement",setup(e,{expose:a}){a();const l=d(!1),t=d(!1),o=d([]),r=d(0),n=d([]),m=d({}),p=d("table"),c=i({page:1,per_page:20,keyword:"",category:"",is_preset:null,is_active:null}),_=i({visible:!1,title:"",isEdit:!1}),f=i({visible:!1,loading:!1,data:{}}),g=i({template_name:"",category:"",description:"",cover_image:"",template_data:{title:"",description:"",price:0,member_limit:500,virtual_members:0,virtual_orders:0,virtual_income:0,faq_content:"",member_reviews:""},custom_fields_config:[],sort_order:0,read_count:"10万+",like_count:3659,want_see_count:665,button_title:"加入群，学习更多副业知识",avatar_library:"default",group_intro_title:"群简介",group_intro_content:"",faq_title:"常见问题",faq_content:"",user_reviews:"",customer_service_qr:"",ad_image:"",extra_title1:"",extra_content1:"",extra_title2:"",extra_content2:""}),b=d(null),y=d("/api/v1/group-templates/upload-cover"),V=d({Authorization:`Bearer ${ee()}`}),D=u(()=>JSON.parse(localStorage.getItem("user")||"{}"));async function P(){l.value=!0;try{const e=await le(c);o.value=e.data.data,r.value=e.data.total}catch(e){v.error("获取模板列表失败")}finally{l.value=!1}}async function z(){try{const e=await te();m.value=e.data}catch(e){v.error("获取分类列表失败")}}function E(){Object.assign(g,{template_name:"",category:"",description:"",cover_image:"",template_data:{title:"",description:"",price:0,member_limit:500,virtual_members:0,virtual_orders:0,virtual_income:0,faq_content:"",member_reviews:""},custom_fields_config:[],sort_order:0})}function L(e){return"admin"===D.value.role||e.created_by===D.value.id&&!e.is_preset}function S(e){return!e.is_preset&&("admin"===D.value.role||e.created_by===D.value.id)}s(()=>{P(),z()});const M={loading:l,formLoading:t,templateList:o,total:r,selectedIds:n,categories:m,viewMode:p,queryParams:c,dialog:_,previewDialog:f,form:g,rules:{template_name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],category:[{required:!0,message:"请选择模板分类",trigger:"change"}],"template_data.title":[{required:!0,message:"请输入群名称",trigger:"blur"}],"template_data.description":[{required:!0,message:"请输入群描述",trigger:"blur"}],"template_data.price":[{required:!0,message:"请输入入群价格",trigger:"blur"}]},formRef:b,uploadUrl:y,uploadHeaders:V,currentUser:D,getList:P,getCategories:z,resetQuery:function(){Object.assign(c,{page:1,per_page:20,keyword:"",category:"",is_preset:null,is_active:null}),P()},refreshData:function(){P()},showCreateDialog:function(){_.title="新建模板",_.isEdit=!1,_.visible=!0,E()},resetForm:E,editTemplate:async function(e){if(L(e)){_.title="编辑模板",_.isEdit=!0,_.visible=!0;try{const a=await ue(e.id);Object.assign(g,a.data)}catch(a){v.error("加载模板详情失败")}}else v.warning("您无权编辑此模板")},saveTemplate:async function(){b.value&&await b.value.validate(async e=>{if(e){t.value=!0;try{_.isEdit?(await de(g.id,g),v.success("更新模板成功")):(await ie(g),v.success("创建模板成功")),_.visible=!1,P()}catch(a){v.error(_.isEdit?"更新模板失败":"创建模板失败")}finally{t.value=!1}}})},handleDeleteTemplate:async function(e){if(S(e))try{await T.confirm(`确定要删除模板 "${e.template_name}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await re(e.id),v.success("删除模板成功"),P()}catch(a){"cancel"!==a&&v.error("删除模板失败")}else v.warning("您无权删除此模板")},toggleStatus:async function(e){try{await oe(e.id,e.is_active),v.success((e.is_active?"启用":"禁用")+"模板成功")}catch(a){e.is_active=!e.is_active,v.error("操作失败")}},viewTemplate:function(e){f.data=e,f.visible=!0},copyTemplateToForm:function(e){Object.assign(g,{...e,template_name:e.template_name+" (副本)",id:void 0}),_.title="复制模板",_.isEdit=!1,_.visible=!0},useTemplate:function(e){this.$router.push({path:"/community/groups/create",query:{template_id:e.id}})},addCustomField:function(){g.custom_fields_config.push({key:"",type:"text",label:"",default_value:"",placeholder:""})},removeCustomField:function(e){g.custom_fields_config.splice(e,1)},canEdit:L,canDelete:S,getCategoryTagType:function(e){return{preset:"",custom:"info",business:"warning",education:"success",entertainment:"danger",technology:"primary"}[e]||"info"},handleSelectionChange:function(e){n.value=e.map(e=>e.id)},beforeCoverUpload:function(e){const a=e.type.startsWith("image/"),l=e.size/1024/1024<2;return a?!!l||(v.error("图片大小不能超过 2MB!"),!1):(v.error("只能上传图片文件!"),!1)},handleCoverUploadSuccess:function(e){200===e.code?(g.cover_image=e.data.url,v.success("上传成功")):v.error("上传失败")},ref:d,reactive:i,onMounted:s,computed:u,get ElMessage(){return v},get ElMessageBox(){return T},get Plus(){return q},get Refresh(){return x},get Picture(){return j},get View(){return C},get Edit(){return k},get Delete(){return U},get User(){return h},get Calendar(){return w},get getToken(){return ee},get formatDate(){return ae},get getTemplates(){return le},get getTemplate(){return ue},get createTemplate(){return ie},get updateTemplate(){return de},get deleteTemplate(){return re},get getTemplateCategories(){return te},get toggleTemplateStatus(){return oe}};return Object.defineProperty(M,"__isScriptSetup",{enumerable:!1,value:!0}),M}},[["render",function(d,i,u,s,v,w){const h=D,U=b,k=E,C=z,j=S,x=L,q=P,T=y,X=B,ee=F,ae=R,le=A,te=Q,oe=W,re=O,de=I,ie=$,ue=Z,We=H,$e=K,Ie=Y,Ne=V,Ze=J,He=G,Ke=g;return n(),e("div",se,[a("div",ne,[i[41]||(i[41]=a("div",{class:"header-title"},[a("h2",null,"📋 群组模板管理"),a("p",null,"管理群组模板，提高群组创建效率")],-1)),a("div",me,[l(U,{type:"primary",onClick:s.showCreateDialog},{default:o(()=>[l(h,null,{default:o(()=>[l(s.Plus)]),_:1}),i[39]||(i[39]=m(" 新建模板 ",-1))]),_:1,__:[39]}),l(U,{onClick:s.refreshData},{default:o(()=>[l(h,null,{default:o(()=>[l(s.Refresh)]),_:1}),i[40]||(i[40]=m(" 刷新 ",-1))]),_:1,__:[40]})])]),l(T,{class:"filter-card"},{default:o(()=>[l(q,{inline:!0,model:s.queryParams,class:"filter-form"},{default:o(()=>[l(C,{label:"关键词"},{default:o(()=>[l(k,{modelValue:s.queryParams.keyword,"onUpdate:modelValue":i[0]||(i[0]=e=>s.queryParams.keyword=e),placeholder:"模板名称/描述/代码",clearable:"",style:{width:"200px"},onClear:s.getList,onKeyup:p(s.getList,["enter"])},null,8,["modelValue"])]),_:1}),l(C,{label:"分类"},{default:o(()=>[l(x,{modelValue:s.queryParams.category,"onUpdate:modelValue":i[1]||(i[1]=e=>s.queryParams.category=e),placeholder:"全部分类",clearable:"",style:{width:"150px"}},{default:o(()=>[(n(!0),e(c,null,_(s.categories,(e,a)=>(n(),r(j,{key:a,label:e,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(C,{label:"类型"},{default:o(()=>[l(x,{modelValue:s.queryParams.is_preset,"onUpdate:modelValue":i[2]||(i[2]=e=>s.queryParams.is_preset=e),placeholder:"全部类型",clearable:"",style:{width:"120px"}},{default:o(()=>[l(j,{label:"预设模板",value:!0}),l(j,{label:"自定义模板",value:!1})]),_:1},8,["modelValue"])]),_:1}),l(C,{label:"状态"},{default:o(()=>[l(x,{modelValue:s.queryParams.is_active,"onUpdate:modelValue":i[3]||(i[3]=e=>s.queryParams.is_active=e),placeholder:"全部状态",clearable:"",style:{width:"120px"}},{default:o(()=>[l(j,{label:"启用",value:!0}),l(j,{label:"禁用",value:!1})]),_:1},8,["modelValue"])]),_:1}),l(C,null,{default:o(()=>[l(U,{type:"primary",onClick:s.getList},{default:o(()=>i[42]||(i[42]=[m("查询",-1)])),_:1,__:[42]}),l(U,{onClick:s.resetQuery},{default:o(()=>i[43]||(i[43]=[m("重置",-1)])),_:1,__:[43]})]),_:1})]),_:1},8,["model"])]),_:1}),t((n(),r(T,{class:"table-card"},{default:o(()=>[a("div",pe,[a("div",ce,[a("span",null,"模板列表 ("+M(s.total)+")",1)]),a("div",_e,[l(ee,{modelValue:s.viewMode,"onUpdate:modelValue":i[4]||(i[4]=e=>s.viewMode=e),size:"small"},{default:o(()=>[l(X,{label:"table"},{default:o(()=>i[44]||(i[44]=[m("列表",-1)])),_:1,__:[44]}),l(X,{label:"card"},{default:o(()=>i[45]||(i[45]=[m("卡片",-1)])),_:1,__:[45]})]),_:1},8,["modelValue"])])]),"table"===s.viewMode?(n(),r(re,{key:0,data:s.templateList,style:{width:"100%"},onSelectionChange:s.handleSelectionChange},{default:o(()=>[l(ae,{type:"selection",width:"55"}),l(ae,{label:"模板信息","min-width":"200"},{default:o(t=>[a("div",fe,[a("div",ge,[t.row.cover_image_url?(n(),e("img",{key:0,src:t.row.cover_image_url},null,8,ve)):(n(),r(h,{key:1},{default:o(()=>[l(s.Picture)]),_:1}))]),a("div",be,[a("div",ye,M(t.row.template_name),1),a("div",Ve,M(t.row.template_code),1),a("div",we,M(t.row.description||"No description"),1)])])]),_:1}),l(ae,{label:"分类",width:"120"},{default:o(e=>[l(le,{type:s.getCategoryTagType(e.row.category)},{default:o(()=>[m(M(e.row.category_name),1)]),_:2},1032,["type"])]),_:1}),l(ae,{label:"类型",width:"100"},{default:o(e=>[l(le,{type:e.row.is_preset?"warning":"info"},{default:o(()=>[m(M(e.row.is_preset?"预设":"自定义"),1)]),_:2},1032,["type"])]),_:1}),l(ae,{label:"状态",width:"80"},{default:o(e=>[l(te,{modelValue:e.row.is_active,"onUpdate:modelValue":a=>e.row.is_active=a,onChange:a=>s.toggleStatus(e.row),disabled:e.row.is_preset},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),l(ae,{label:"使用次数",width:"100"},{default:o(e=>[l(oe,{type:"primary"},{default:o(()=>[m(M(e.row.usage_count),1)]),_:2},1024)]),_:1}),l(ae,{label:"创建者",width:"120"},{default:o(e=>[m(M(e.row.creator?.username||"系统"),1)]),_:1}),l(ae,{label:"创建时间",width:"160"},{default:o(e=>[m(M(s.formatDate(e.row.created_at)),1)]),_:1}),l(ae,{label:"操作",width:"200",fixed:"right"},{default:o(e=>[l(U,{size:"small",onClick:a=>s.viewTemplate(e.row)},{default:o(()=>i[46]||(i[46]=[m("预览",-1)])),_:2,__:[46]},1032,["onClick"]),l(U,{size:"small",onClick:a=>s.editTemplate(e.row),disabled:!s.canEdit(e.row)},{default:o(()=>i[47]||(i[47]=[m("编辑",-1)])),_:2,__:[47]},1032,["onClick","disabled"]),l(U,{size:"small",onClick:a=>s.copyTemplateToForm(e.row)},{default:o(()=>i[48]||(i[48]=[m("复制",-1)])),_:2,__:[48]},1032,["onClick"]),l(U,{size:"small",type:"danger",onClick:a=>s.handleDeleteTemplate(e.row),disabled:!s.canDelete(e.row)},{default:o(()=>i[49]||(i[49]=[m(" 删除 ",-1)])),_:2,__:[49]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])):(n(),e("div",he,[l(ie,{gutter:20},{default:o(()=>[(n(!0),e(c,null,_(s.templateList,t=>(n(),r(de,{span:6,key:t.id},{default:o(()=>[a("div",{class:N(["template-card",{"template-disabled":!t.is_active}])},[a("div",Ue,[t.cover_image_url?(n(),e("img",{key:0,src:t.cover_image_url},null,8,ke)):(n(),e("div",Ce,[l(h,null,{default:o(()=>[l(s.Picture)]),_:1})])),a("div",je,[l(U,{size:"small",circle:"",onClick:e=>s.viewTemplate(t)},{default:o(()=>[l(h,null,{default:o(()=>[l(s.View)]),_:1})]),_:2},1032,["onClick"]),l(U,{size:"small",circle:"",onClick:e=>s.editTemplate(t),disabled:!s.canEdit(t)},{default:o(()=>[l(h,null,{default:o(()=>[l(s.Edit)]),_:1})]),_:2},1032,["onClick","disabled"]),l(U,{size:"small",circle:"",type:"danger",onClick:e=>s.handleDeleteTemplate(t),disabled:!s.canDelete(t)},{default:o(()=>[l(h,null,{default:o(()=>[l(s.Delete)]),_:1})]),_:2},1032,["onClick","disabled"])])]),a("div",xe,[a("div",qe,[a("h4",Te,M(t.template_name),1),a("div",De,[l(le,{size:"small",type:t.is_preset?"warning":"info"},{default:o(()=>[m(M(t.is_preset?"预设":"自定义"),1)]),_:2},1032,["type"]),l(le,{size:"small",type:s.getCategoryTagType(t.category)},{default:o(()=>[m(M(t.category_name),1)]),_:2},1032,["type"])])]),a("p",Pe,M(t.description||"暂无描述"),1),a("div",ze,[a("span",Ee,[l(h,null,{default:o(()=>[l(s.User)]),_:1}),m(" 使用 "+M(t.usage_count)+" 次 ",1)]),a("span",Le,[l(h,null,{default:o(()=>[l(s.Calendar)]),_:1}),m(" "+M(s.formatDate(t.created_at)),1)])])])],2)]),_:2},1024))),128))]),_:1})])),a("div",Se,[l(ue,{"current-page":s.queryParams.page,"onUpdate:currentPage":i[5]||(i[5]=e=>s.queryParams.page=e),"page-size":s.queryParams.per_page,"onUpdate:pageSize":i[6]||(i[6]=e=>s.queryParams.per_page=e),"page-sizes":[10,20,50,100],total:s.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:s.getList,onCurrentChange:s.getList},null,8,["current-page","page-size","total"])])]),_:1})),[[Ke,s.loading]]),l(Ne,{title:s.dialog.title,modelValue:s.dialog.visible,"onUpdate:modelValue":i[35]||(i[35]=e=>s.dialog.visible=e),width:"900px","close-on-click-modal":!1},{footer:o(()=>[a("div",Be,[l(U,{onClick:i[34]||(i[34]=e=>s.dialog.visible=!1)},{default:o(()=>i[57]||(i[57]=[m("取消",-1)])),_:1,__:[57]}),l(U,{type:"primary",onClick:s.saveTemplate,loading:s.formLoading},{default:o(()=>i[58]||(i[58]=[m("保存",-1)])),_:1,__:[58]},8,["loading"])])]),default:o(()=>[t((n(),r(q,{model:s.form,rules:s.rules,ref:"formRef","label-width":"120px"},{default:o(()=>[l(ie,{gutter:20},{default:o(()=>[l(de,{span:12},{default:o(()=>[l(C,{label:"模板名称",prop:"template_name"},{default:o(()=>[l(k,{modelValue:s.form.template_name,"onUpdate:modelValue":i[7]||(i[7]=e=>s.form.template_name=e),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1})]),_:1}),l(de,{span:12},{default:o(()=>[l(C,{label:"模板分类",prop:"category"},{default:o(()=>[l(x,{modelValue:s.form.category,"onUpdate:modelValue":i[8]||(i[8]=e=>s.form.category=e),placeholder:"请选择分类",style:{width:"100%"}},{default:o(()=>[(n(!0),e(c,null,_(s.categories,(e,a)=>(n(),r(j,{key:a,label:e,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(C,{label:"模板描述"},{default:o(()=>[l(k,{type:"textarea",modelValue:s.form.description,"onUpdate:modelValue":i[9]||(i[9]=e=>s.form.description=e),placeholder:"请输入模板描述",rows:3},null,8,["modelValue"])]),_:1}),l(C,{label:"封面图片"},{default:o(()=>[l(We,{action:s.uploadUrl,headers:s.uploadHeaders,"on-success":s.handleCoverUploadSuccess,"before-upload":s.beforeCoverUpload,"show-file-list":!1,class:"cover-uploader"},{default:o(()=>[s.form.cover_image?(n(),e("img",{key:0,src:s.form.cover_image,class:"cover-image"},null,8,Me)):(n(),r(h,{key:1,class:"cover-uploader-icon"},{default:o(()=>[l(s.Plus)]),_:1}))]),_:1},8,["action","headers"])]),_:1}),l($e,{"content-position":"left"},{default:o(()=>i[50]||(i[50]=[m("基础配置",-1)])),_:1,__:[50]}),l(ie,{gutter:20},{default:o(()=>[l(de,{span:12},{default:o(()=>[l(C,{label:"群名称",prop:"template_data.title"},{default:o(()=>[l(k,{modelValue:s.form.template_data.title,"onUpdate:modelValue":i[10]||(i[10]=e=>s.form.template_data.title=e),placeholder:"支持变量：{{city}}、{{username}}"},null,8,["modelValue"])]),_:1})]),_:1}),l(de,{span:12},{default:o(()=>[l(C,{label:"入群价格",prop:"template_data.price"},{default:o(()=>[l(Ie,{modelValue:s.form.template_data.price,"onUpdate:modelValue":i[11]||(i[11]=e=>s.form.template_data.price=e),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(C,{label:"群描述",prop:"template_data.description"},{default:o(()=>[l(k,{type:"textarea",modelValue:s.form.template_data.description,"onUpdate:modelValue":i[12]||(i[12]=e=>s.form.template_data.description=e),placeholder:"支持变量：{{city}}、{{username}}",rows:4},null,8,["modelValue"])]),_:1}),l(ie,{gutter:20},{default:o(()=>[l(de,{span:12},{default:o(()=>[l(C,{label:"成员上限"},{default:o(()=>[l(Ie,{modelValue:s.form.template_data.member_limit,"onUpdate:modelValue":i[13]||(i[13]=e=>s.form.template_data.member_limit=e),min:10,max:2e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),l(de,{span:12},{default:o(()=>[l(C,{label:"排序权重"},{default:o(()=>[l(Ie,{modelValue:s.form.sort_order,"onUpdate:modelValue":i[14]||(i[14]=e=>s.form.sort_order=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l($e,{"content-position":"left"},{default:o(()=>i[51]||(i[51]=[m("营销配置",-1)])),_:1,__:[51]}),l(ie,{gutter:20},{default:o(()=>[l(de,{span:8},{default:o(()=>[l(C,{label:"虚拟成员数"},{default:o(()=>[l(Ie,{modelValue:s.form.template_data.virtual_members,"onUpdate:modelValue":i[15]||(i[15]=e=>s.form.template_data.virtual_members=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),l(de,{span:8},{default:o(()=>[l(C,{label:"虚拟订单数"},{default:o(()=>[l(Ie,{modelValue:s.form.template_data.virtual_orders,"onUpdate:modelValue":i[16]||(i[16]=e=>s.form.template_data.virtual_orders=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),l(de,{span:8},{default:o(()=>[l(C,{label:"虚拟收益"},{default:o(()=>[l(Ie,{modelValue:s.form.template_data.virtual_income,"onUpdate:modelValue":i[17]||(i[17]=e=>s.form.template_data.virtual_income=e),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(ie,{gutter:20},{default:o(()=>[l(de,{span:8},{default:o(()=>[l(C,{label:"阅读数显示"},{default:o(()=>[l(k,{modelValue:s.form.read_count,"onUpdate:modelValue":i[18]||(i[18]=e=>s.form.read_count=e),placeholder:"如：10万+"},null,8,["modelValue"])]),_:1})]),_:1}),l(de,{span:8},{default:o(()=>[l(C,{label:"点赞数"},{default:o(()=>[l(Ie,{modelValue:s.form.like_count,"onUpdate:modelValue":i[19]||(i[19]=e=>s.form.like_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),l(de,{span:8},{default:o(()=>[l(C,{label:"想看数"},{default:o(()=>[l(Ie,{modelValue:s.form.want_see_count,"onUpdate:modelValue":i[20]||(i[20]=e=>s.form.want_see_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(ie,{gutter:20},{default:o(()=>[l(de,{span:12},{default:o(()=>[l(C,{label:"按键名称"},{default:o(()=>[l(k,{modelValue:s.form.button_title,"onUpdate:modelValue":i[21]||(i[21]=e=>s.form.button_title=e),placeholder:"如：加入群，学习更多副业知识"},null,8,["modelValue"])]),_:1})]),_:1}),l(de,{span:12},{default:o(()=>[l(C,{label:"头像库类型"},{default:o(()=>[l(x,{modelValue:s.form.avatar_library,"onUpdate:modelValue":i[22]||(i[22]=e=>s.form.avatar_library=e),placeholder:"选择头像库",style:{width:"100%"}},{default:o(()=>[l(j,{label:"默认头像",value:"default"}),l(j,{label:"商务头像",value:"business"}),l(j,{label:"交友头像",value:"dating"}),l(j,{label:"征婚头像",value:"marriage"}),l(j,{label:"健身头像",value:"fitness"}),l(j,{label:"家庭头像",value:"family"}),l(j,{label:"扩列头像",value:"qq"}),l(j,{label:"综合头像",value:"za"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l($e,{"content-position":"left"},{default:o(()=>i[52]||(i[52]=[m("内容区块配置",-1)])),_:1,__:[52]}),l(ie,{gutter:20},{default:o(()=>[l(de,{span:12},{default:o(()=>[l(C,{label:"群简介标题"},{default:o(()=>[l(k,{modelValue:s.form.group_intro_title,"onUpdate:modelValue":i[23]||(i[23]=e=>s.form.group_intro_title=e),placeholder:"如：群简介"},null,8,["modelValue"])]),_:1})]),_:1}),l(de,{span:12},{default:o(()=>[l(C,{label:"常见问题标题"},{default:o(()=>[l(k,{modelValue:s.form.faq_title,"onUpdate:modelValue":i[24]||(i[24]=e=>s.form.faq_title=e),placeholder:"如：常见问题"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(C,{label:"群简介内容"},{default:o(()=>[l(k,{type:"textarea",modelValue:s.form.group_intro_content,"onUpdate:modelValue":i[25]||(i[25]=e=>s.form.group_intro_content=e),placeholder:"输入群简介内容",rows:3},null,8,["modelValue"])]),_:1}),l(C,{label:"FAQ内容"},{default:o(()=>[l(k,{type:"textarea",modelValue:s.form.faq_content,"onUpdate:modelValue":i[26]||(i[26]=e=>s.form.faq_content=e),placeholder:"格式：问题----答案（每行一个）",rows:4},null,8,["modelValue"])]),_:1}),l(C,{label:"用户评论"},{default:o(()=>[l(k,{type:"textarea",modelValue:s.form.user_reviews,"onUpdate:modelValue":i[27]||(i[27]=e=>s.form.user_reviews=e),placeholder:"格式：用户名----评论内容----评分（每行一个）",rows:4},null,8,["modelValue"])]),_:1}),l($e,{"content-position":"left"},{default:o(()=>i[53]||(i[53]=[m("素材配置",-1)])),_:1,__:[53]}),l(ie,{gutter:20},{default:o(()=>[l(de,{span:12},{default:o(()=>[l(C,{label:"客服二维码"},{default:o(()=>[l(k,{modelValue:s.form.customer_service_qr,"onUpdate:modelValue":i[28]||(i[28]=e=>s.form.customer_service_qr=e),placeholder:"客服二维码URL"},null,8,["modelValue"])]),_:1})]),_:1}),l(de,{span:12},{default:o(()=>[l(C,{label:"广告图片"},{default:o(()=>[l(k,{modelValue:s.form.ad_image,"onUpdate:modelValue":i[29]||(i[29]=e=>s.form.ad_image=e),placeholder:"广告图片URL"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(ie,{gutter:20},{default:o(()=>[l(de,{span:12},{default:o(()=>[l(C,{label:"扩展区块1标题"},{default:o(()=>[l(k,{modelValue:s.form.extra_title1,"onUpdate:modelValue":i[30]||(i[30]=e=>s.form.extra_title1=e),placeholder:"扩展区块1标题"},null,8,["modelValue"])]),_:1})]),_:1}),l(de,{span:12},{default:o(()=>[l(C,{label:"扩展区块2标题"},{default:o(()=>[l(k,{modelValue:s.form.extra_title2,"onUpdate:modelValue":i[31]||(i[31]=e=>s.form.extra_title2=e),placeholder:"扩展区块2标题"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(C,{label:"扩展区块1内容"},{default:o(()=>[l(k,{type:"textarea",modelValue:s.form.extra_content1,"onUpdate:modelValue":i[32]||(i[32]=e=>s.form.extra_content1=e),placeholder:"扩展区块1内容",rows:3},null,8,["modelValue"])]),_:1}),l(C,{label:"扩展区块2内容"},{default:o(()=>[l(k,{type:"textarea",modelValue:s.form.extra_content2,"onUpdate:modelValue":i[33]||(i[33]=e=>s.form.extra_content2=e),placeholder:"扩展区块2内容",rows:3},null,8,["modelValue"])]),_:1}),l($e,{"content-position":"left"},{default:o(()=>i[54]||(i[54]=[m("自定义字段配置",-1)])),_:1,__:[54]}),a("div",Fe,[(n(!0),e(c,null,_(s.form.custom_fields_config,(a,t)=>(n(),e("div",{key:t,class:"custom-field-item"},[l(ie,{gutter:10},{default:o(()=>[l(de,{span:4},{default:o(()=>[l(k,{modelValue:a.key,"onUpdate:modelValue":e=>a.key=e,placeholder:"字段名"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(de,{span:3},{default:o(()=>[l(x,{modelValue:a.type,"onUpdate:modelValue":e=>a.type=e,placeholder:"类型"},{default:o(()=>[l(j,{label:"文本",value:"text"}),l(j,{label:"数字",value:"number"}),l(j,{label:"选择",value:"select"}),l(j,{label:"开关",value:"switch"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(de,{span:4},{default:o(()=>[l(k,{modelValue:a.label,"onUpdate:modelValue":e=>a.label=e,placeholder:"显示名称"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(de,{span:4},{default:o(()=>[l(k,{modelValue:a.default_value,"onUpdate:modelValue":e=>a.default_value=e,placeholder:"默认值"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(de,{span:6},{default:o(()=>[l(k,{modelValue:a.placeholder,"onUpdate:modelValue":e=>a.placeholder=e,placeholder:"提示文本"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(de,{span:3},{default:o(()=>[l(U,{type:"danger",size:"small",onClick:e=>s.removeCustomField(t)},{default:o(()=>[...i[55]||(i[55]=[m("删除",-1)])]),_:2,__:[55]},1032,["onClick"])]),_:2},1024)]),_:2},1024)]))),128)),l(U,{type:"primary",size:"small",onClick:s.addCustomField},{default:o(()=>i[56]||(i[56]=[m("添加自定义字段",-1)])),_:1,__:[56]})])]),_:1},8,["model"])),[[Ke,s.formLoading]])]),_:1},8,["title","modelValue"]),l(Ne,{title:"模板预览",modelValue:s.previewDialog.visible,"onUpdate:modelValue":i[38]||(i[38]=e=>s.previewDialog.visible=e),width:"600px"},{footer:o(()=>[l(U,{onClick:i[36]||(i[36]=e=>s.previewDialog.visible=!1)},{default:o(()=>i[60]||(i[60]=[m("关闭",-1)])),_:1,__:[60]}),l(U,{type:"primary",onClick:i[37]||(i[37]=e=>s.useTemplate(s.previewDialog.data))},{default:o(()=>i[61]||(i[61]=[m("使用模板",-1)])),_:1,__:[61]})]),default:o(()=>[t((n(),e("div",Oe,[a("div",Re,[a("h3",null,M(s.previewDialog.data.template_name),1),a("p",null,M(s.previewDialog.data.description),1)]),a("div",Ae,[l(He,{column:2,border:""},{default:o(()=>[l(Ze,{label:"群名称"},{default:o(()=>[m(M(s.previewDialog.data.template_data?.title),1)]),_:1}),l(Ze,{label:"入群价格"},{default:o(()=>[m(" ¥"+M(s.previewDialog.data.template_data?.price),1)]),_:1}),l(Ze,{label:"成员上限"},{default:o(()=>[m(M(s.previewDialog.data.template_data?.member_limit),1)]),_:1}),l(Ze,{label:"虚拟成员"},{default:o(()=>[m(M(s.previewDialog.data.template_data?.virtual_members),1)]),_:1}),l(Ze,{label:"分类"},{default:o(()=>[m(M(s.previewDialog.data.category_name),1)]),_:1}),l(Ze,{label:"使用次数"},{default:o(()=>[m(M(s.previewDialog.data.usage_count),1)]),_:1})]),_:1}),s.previewDialog.data.template_data?.description?(n(),e("div",Qe,[i[59]||(i[59]=a("h4",null,"群描述",-1)),a("p",null,M(s.previewDialog.data.template_data.description),1)])):f("",!0)])])),[[Ke,s.previewDialog.loading]])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-d46af176"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/community/TemplateManagement.vue"]]);export{We as default};
