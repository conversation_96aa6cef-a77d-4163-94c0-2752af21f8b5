<template>
  <div class="optimized-sidebar">
    <div class="sidebar-header">
      <div class="logo-container">
        <div class="logo-placeholder">L</div>
        <span class="brand-text">LinkHub Pro</span>
      </div>
      <el-button
        :icon="isCollapsed ? 'Expand' : 'Fold'"
        circle
        size="small"
        class="collapse-btn"
        @click="toggleCollapse"
      />
    </div>

    <div class="sidebar-search" v-if="!isCollapsed">
      <el-input
        v-model="searchQuery"
        placeholder="搜索菜单..."
        :prefix-icon="Search"
        size="small"
        clearable
        @input="handleSearch"
      />
    </div>

    <el-scrollbar class="sidebar-scrollbar">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapsed"
        :unique-opened="true"
        :collapse-transition="false"
        router
        class="sidebar-menu"
        @select="handleMenuSelect"
      >
        <template v-for="(item, index) in filteredMenu" :key="index">
          <!-- 分组标题 -->
          <div v-if="item.group && !isCollapsed" class="menu-group-title">
            {{ item.title }}
          </div>
          
          <!-- 菜单项 -->
          <template v-else-if="!item.group">
            <!-- 有子菜单 -->
            <el-sub-menu
              v-if="item.children && item.children.length > 0"
              :index="item.path"
              class="sidebar-submenu"
            >
              <template #title>
                <el-icon>
                  <component :is="item.icon" />
                </el-icon>
                <span class="menu-title">{{ item.title }}</span>
                <el-badge
                  v-if="item.badge"
                  :value="item.badge"
                  :type="item.badgeType || 'danger'"
                  class="menu-badge"
                />
                <el-icon v-if="item.isNew" class="new-indicator" title="新功能">
                  <StarFilled />
                </el-icon>
                <el-icon v-if="item.isHot" class="hot-indicator" title="热门">
                  <Promotion />
                </el-icon>
              </template>
              
              <el-menu-item
                v-for="child in item.children"
                :key="child.path"
                :index="child.path"
                :class="getMenuItemClass(child)"
              >
                <el-icon>
                  <component :is="child.icon" />
                </el-icon>
                <template #title>
                  <span class="menu-title">{{ child.title }}</span>
                  <el-badge
                    v-if="child.badge"
                    :value="child.badge"
                    :type="child.badgeType || 'danger'"
                    class="menu-badge"
                  />
                  <el-icon v-if="child.isNew" class="new-indicator" title="新功能">
                    <StarFilled />
                  </el-icon>
                  <el-icon v-if="child.isHot" class="hot-indicator" title="热门">
                    <Promotion />
                  </el-icon>
                </template>
              </el-menu-item>
            </el-sub-menu>
            
            <!-- 单级菜单 -->
            <el-menu-item
              v-else
              :index="item.path"
              :class="getMenuItemClass(item)"
            >
              <el-icon>
                <component :is="item.icon" />
              </el-icon>
              <template #title>
                <span class="menu-title">{{ item.title }}</span>
                <el-badge
                  v-if="item.badge"
                  :value="item.badge"
                  :type="item.badgeType || 'danger'"
                  class="menu-badge"
                />
                <el-icon v-if="item.isNew" class="new-indicator" title="新功能">
                  <StarFilled />
                </el-icon>
                <el-icon v-if="item.isHot" class="hot-indicator" title="热门">
                  <Promotion />
                </el-icon>
              </template>
            </el-menu-item>
          </template>
        </template>
      </el-menu>
    </el-scrollbar>

    <!-- 用户信息 -->
    <div class="sidebar-footer" v-if="!isCollapsed">
      <div class="user-info">
        <el-avatar :size="32" :icon="UserFilled" />
        <div class="user-details">
          <div class="username">管理员</div>
          <div class="user-role">超级管理员</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { 
  Search, 
  StarFilled, 
  Promotion, 
  UserFilled,
  Expand,
  Fold
} from '@element-plus/icons-vue'
import { 
  sidebarMenuConfig, 
  filterMenuByRole, 
  flattenMenu 
} from './SidebarConfig.js'

// Props
const props = defineProps({
  collapse: {
    type: Boolean,
    default: false
  },
  userRoles: {
    type: Array,
    default: () => ['admin']
  }
})

// Emits
const emit = defineEmits(['collapse-change', 'menu-select'])

// 响应式数据
const route = useRoute()
const searchQuery = ref('')
const isCollapsed = ref(props.collapse)

// 计算属性
const activeMenu = computed(() => {
  return route.path
})

const filteredMenuByRole = computed(() => {
  return filterMenuByRole(sidebarMenuConfig, props.userRoles)
})

const filteredMenu = computed(() => {
  if (!searchQuery.value) {
    return filteredMenuByRole.value
  }
  
  const query = searchQuery.value.toLowerCase()
  const flatMenu = flattenMenu(filteredMenuByRole.value)
  
  return flatMenu.filter(item => {
    return item.title.toLowerCase().includes(query) ||
           (item.name && item.name.toLowerCase().includes(query))
  })
})

// 方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  emit('collapse-change', isCollapsed.value)
}

const handleSearch = (value) => {
  // 搜索逻辑已在计算属性中处理
}

const handleMenuSelect = (index, indexPath) => {
  emit('menu-select', { index, indexPath })
}

const getMenuItemClass = (item) => {
  return {
    'is-new': item.isNew,
    'is-hot': item.isHot,
    'has-badge': item.badge && item.badge > 0,
    'is-active': route.path === item.path
  }
}

// 监听 props 变化
watch(
  () => props.collapse,
  (newValue) => {
    isCollapsed.value = newValue
  }
)
</script>

<style lang="scss" scoped>
.optimized-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #001529 0%, #0a1a2a 100%);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  .logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .logo-placeholder {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background: linear-gradient(135deg, #409eff, #67c23a);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 16px;
    }
    
    .brand-text {
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;
      transition: opacity 0.3s ease;
    }
  }
  
  .collapse-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: rgba(255, 255, 255, 0.7);
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      color: #ffffff;
    }
  }
}

.sidebar-search {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  :deep(.el-input__wrapper) {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    .el-input__inner {
      color: #ffffff;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
    }
    
    .el-input__prefix {
      color: rgba(255, 255, 255, 0.5);
    }
  }
}

.sidebar-scrollbar {
  flex: 1;
  
  :deep(.el-scrollbar__view) {
    padding: 8px 0;
  }
}

.menu-group-title {
  padding: 16px 20px 8px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.sidebar-menu {
  background: transparent;
  border: none;
  
  :deep(.el-menu-item),
  :deep(.el-sub-menu__title) {
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 4px 12px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      transform: translateX(4px);
    }
    
    &.is-active {
      background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
      color: #ffffff;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
      
      &::before {
        display: none;
      }
    }
    
    .el-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      transition: all 0.3s ease;
    }
  }
  
  :deep(.el-sub-menu) {
    .el-menu {
      background: rgba(0, 0, 0, 0.2);
      
      .el-menu-item {
        padding-left: 48px !important;
        
        &::before {
          content: '';
          position: absolute;
          left: 32px;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.4);
          transition: all 0.3s ease;
        }
        
        &:hover::before,
        &.is-active::before {
          background: #ffffff;
          transform: translateY(-50%) scale(1.5);
        }
      }
    }
  }
}

.menu-badge {
  margin-left: auto;
}

.new-indicator {
  color: #F59E0B;
  font-size: 12px;
  margin-left: 4px;
  animation: sparkle 2s infinite;
}

.hot-indicator {
  color: #EF4444;
  font-size: 12px;
  margin-left: 4px;
  animation: pulse 2s infinite;
}

@keyframes sparkle {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.5; 
    transform: scale(1.1); 
  }
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
  }
  50% { 
    opacity: 0.6; 
  }
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .user-details {
      flex: 1;
      
      .username {
        color: #ffffff;
        font-size: 14px;
        font-weight: 500;
      }
      
      .user-role {
        color: rgba(255, 255, 255, 0.5);
        font-size: 12px;
      }
    }
  }
}

// 折叠状态样式
.sidebar-menu.el-menu--collapse {
  :deep(.el-menu-item),
  :deep(.el-sub-menu__title) {
    margin: 4px 8px;
    justify-content: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .optimized-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &.is-open {
      transform: translateX(0);
    }
  }
}

// 特殊菜单项样式
.sidebar-menu {
  :deep(.el-menu-item.is-new) {
    position: relative;
    
    &::after {
      content: 'NEW';
      position: absolute;
      top: 4px;
      right: 8px;
      background: #F59E0B;
      color: white;
      font-size: 9px;
      padding: 1px 4px;
      border-radius: 6px;
      font-weight: bold;
    }
  }
  
  :deep(.el-menu-item.is-hot) {
    position: relative;
    
    &::after {
      content: 'HOT';
      position: absolute;
      top: 4px;
      right: 8px;
      background: #EF4444;
      color: white;
      font-size: 9px;
      padding: 1px 4px;
      border-radius: 6px;
      font-weight: bold;
      animation: bounce 2s infinite;
    }
  }
  
  :deep(.el-menu-item.has-badge) {
    .menu-title {
      font-weight: 600;
      color: var(--el-color-primary);
    }
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0,-8px,0);
  }
  70% {
    transform: translate3d(0,-4px,0);
  }
  90% {
    transform: translate3d(0,-2px,0);
  }
}
</style>