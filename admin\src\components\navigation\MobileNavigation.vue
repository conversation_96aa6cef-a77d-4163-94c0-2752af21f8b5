<!-- 移动端导航组件 -->
<!-- admin/src/components/navigation/MobileNavigation.vue -->

<template>
  <div class="mobile-navigation" :class="navigationClasses">
    <!-- 移动端顶部导航栏 -->
    <div class="mobile-header">
      <div class="header-left">
        <!-- 菜单按钮 -->
        <el-button 
          class="menu-toggle"
          @click="toggleMenu"
          type="text"
          size="large"
        >
          <transition name="menu-icon-rotate" mode="out-in">
            <el-icon :key="showMenu">
              <component :is="showMenu ? 'Close' : 'Menu'" />
            </el-icon>
          </transition>
        </el-button>
        
        <!-- Logo -->
        <div class="mobile-logo" @click="navigateHome">
          <el-icon class="logo-icon"><DataLine /></el-icon>
          <span class="logo-text">LinkHub</span>
        </div>
      </div>
      
      <div class="header-right">
        <!-- 搜索按钮 -->
        <el-button 
          class="search-toggle"
          @click="showSearchDrawer = true"
          type="text"
          size="large"
        >
          <el-icon><Search /></el-icon>
        </el-button>
        
        <!-- 通知按钮 -->
        <el-button 
          class="notifications-btn"
          @click="showNotificationsDrawer = true"
          type="text"
          size="large"
        >
          <el-badge :value="unreadCount" :hidden="unreadCount === 0">
            <el-icon><Bell /></el-icon>
          </el-badge>
        </el-button>
        
        <!-- 用户头像 -->
        <el-avatar 
          :src="userInfo.avatar" 
          :size="32"
          @click="showUserDrawer = true"
          class="user-avatar"
        >
          <img src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" alt="用户头像"/>
        </el-avatar>
      </div>
    </div>
    
    <!-- 移动端主菜单 -->
    <transition name="menu-slide">
      <div v-if="showMenu" class="mobile-menu" @click="handleMenuClick">
        <div class="menu-content" @click.stop>
          <!-- 用户信息卡片 -->
          <div class="user-card">
            <el-avatar :src="userInfo.avatar" :size="48">
              <img src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" alt="用户头像"/>
            </el-avatar>
            <div class="user-info">
              <div class="user-name">{{ userInfo.name || '用户' }}</div>
              <div class="user-role">{{ getRoleText(userInfo.role) }}</div>
            </div>
            <el-button type="text" size="small" @click="showUserDrawer = true">
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          
          <!-- 快速操作 -->
          <div class="quick-actions">
            <div 
              v-for="action in quickActions"
              :key="action.key"
              class="quick-action"
              @click="handleQuickAction(action)"
            >
              <div class="action-icon" :style="{ backgroundColor: action.color }">
                <el-icon><component :is="action.icon" /></el-icon>
              </div>
              <span class="action-title">{{ action.title }}</span>
            </div>
          </div>
          
          <!-- 导航菜单 -->
          <div class="navigation-list">
            <div 
              v-for="section in mobileMenuSections"
              :key="section.key"
              class="menu-section"
            >
              <div class="section-header" @click="toggleSection(section.key)">
                <div class="section-info">
                  <el-icon class="section-icon" :style="{ color: section.color }">
                    <component :is="section.icon" />
                  </el-icon>
                  <span class="section-title">{{ section.title }}</span>
                  <el-badge 
                    v-if="getSectionBadge(section)" 
                    :value="getSectionBadge(section)"
                    class="section-badge"
                  />
                </div>
                <el-icon 
                  class="section-toggle"
                  :class="{ rotated: expandedSections.includes(section.key) }"
                >
                  <ArrowRight />
                </el-icon>
              </div>
              
              <transition name="section-expand">
                <div v-show="expandedSections.includes(section.key)" class="section-items">
                  <router-link
                    v-for="item in section.items"
                    :key="item.key"
                    :to="item.path || '#'"
                    class="menu-item"
                    :class="{ active: isItemActive(item) }"
                    @click="handleMenuItemClick(item)"
                  >
                    <el-icon class="item-icon">
                      <component :is="item.icon" />
                    </el-icon>
                    <span class="item-title">{{ item.title }}</span>
                    <el-badge 
                      v-if="item.badge" 
                      :value="item.badge" 
                      class="item-badge"
                    />
                    <div class="item-indicator" v-if="isItemActive(item)"></div>
                  </router-link>
                </div>
              </transition>
            </div>
          </div>
          
          <!-- 底部操作 -->
          <div class="menu-footer">
            <div class="footer-actions">
              <el-button 
                v-for="action in footerActions"
                :key="action.key"
                :type="action.type || 'text'"
                size="small"
                @click="handleFooterAction(action)"
                class="footer-action"
              >
                <el-icon><component :is="action.icon" /></el-icon>
                <span>{{ action.title }}</span>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- 移动端底部导航栏 -->
    <div class="mobile-bottom-nav" v-if="showBottomNav">
      <div
        v-for="tab in bottomNavTabs"
        :key="tab.key"
        class="nav-tab"
        :class="{ active: activeTab === tab.key }"
        @click="handleTabClick(tab)"
      >
        <div class="tab-icon">
          <el-badge :value="tab.badge" :hidden="!tab.badge">
            <el-icon><component :is="tab.icon" /></el-icon>
          </el-badge>
        </div>
        <span class="tab-label">{{ tab.label }}</span>
      </div>
    </div>
    
    <!-- 搜索抽屉 -->
    <el-drawer
      v-model="showSearchDrawer"
      direction="ttb"
      size="100%"
      :show-close="false"
      class="search-drawer"
    >
      <template #header>
        <div class="drawer-header">
          <el-button @click="showSearchDrawer = false" type="text" size="large">
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <span class="drawer-title">全局搜索</span>
        </div>
      </template>
      <div class="search-content">
        <el-input
          v-model="mobileSearchQuery"
          placeholder="搜索功能、页面、用户..."
          :prefix-icon="Search"
          size="large"
          clearable
          autofocus
          @input="handleMobileSearch"
          class="mobile-search-input"
        />
        
        <!-- 搜索结果 -->
        <div class="mobile-search-results" v-if="mobileSearchResults.length">
          <div class="results-header">
            <span>找到 {{ mobileSearchResults.length }} 个结果</span>
          </div>
          <div class="results-list">
            <div
              v-for="result in mobileSearchResults"
              :key="result.id"
              class="result-item"
              @click="selectMobileSearchResult(result)"
            >
              <div class="result-icon">
                <el-icon><component :is="result.icon" /></el-icon>
              </div>
              <div class="result-content">
                <div class="result-title" v-html="highlightText(result.title, mobileSearchQuery)"></div>
                <div class="result-description">{{ result.description }}</div>
              </div>
              <div class="result-type">{{ result.type }}</div>
            </div>
          </div>
        </div>
        
        <!-- 搜索建议 -->
        <div class="search-suggestions" v-else-if="!mobileSearchQuery">
          <div class="suggestions-section">
            <h4>热门功能</h4>
            <div class="suggestions-grid">
              <div
                v-for="suggestion in popularSuggestions"
                :key="suggestion.key"
                class="suggestion-item"
                @click="handleSuggestionClick(suggestion)"
              >
                <el-icon><component :is="suggestion.icon" /></el-icon>
                <span>{{ suggestion.title }}</span>
              </div>
            </div>
          </div>
          
          <div class="suggestions-section">
            <h4>最近搜索</h4>
            <div class="recent-searches">
              <div
                v-for="search in recentMobileSearches"
                :key="search.id"
                class="recent-search-item"
                @click="applyRecentSearch(search)"
              >
                <el-icon><Clock /></el-icon>
                <span>{{ search.query }}</span>
                <small>{{ formatTimeAgo(search.timestamp) }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
    
    <!-- 通知抽屉 -->
    <el-drawer
      v-model="showNotificationsDrawer"
      direction="rtl"
      size="80%"
      :show-close="false"
      class="notifications-drawer"
    >
      <template #header>
        <div class="drawer-header">
          <span class="drawer-title">通知中心</span>
          <el-button @click="showNotificationsDrawer = false" type="text" size="large">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>
      <div class="notifications-content">
        <div class="notifications-actions">
          <el-button type="text" size="small" @click="markAllRead">全部已读</el-button>
          <el-button type="text" size="small" @click="clearAllNotifications">清空</el-button>
        </div>
        
        <div class="notifications-list">
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ unread: !notification.read }"
            @click="handleNotificationClick(notification)"
          >
            <div class="notification-icon" :class="notification.type">
              <el-icon><component :is="getNotificationIcon(notification.type)" /></el-icon>
            </div>
            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.message }}</div>
              <div class="notification-time">{{ formatTimeAgo(notification.timestamp) }}</div>
            </div>
            <div class="notification-actions">
              <el-button 
                v-if="!notification.read"
                type="text" 
                size="small"
                @click.stop="markAsRead(notification)"
              >
                <el-icon><Check /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
    
    <!-- 用户信息抽屉 -->
    <el-drawer
      v-model="showUserDrawer"
      direction="rtl"
      size="80%"
      :show-close="false"
      class="user-drawer"
    >
      <template #header>
        <div class="drawer-header">
          <span class="drawer-title">个人中心</span>
          <el-button @click="showUserDrawer = false" type="text" size="large">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>
      <div class="user-content">
        <!-- 用户信息 -->
        <div class="user-profile-card">
          <el-avatar :src="userInfo.avatar" :size="64">
            <img src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" alt="用户头像"/>
          </el-avatar>
          <div class="profile-info">
            <h3 class="profile-name">{{ userInfo.name || '用户' }}</h3>
            <p class="profile-email">{{ userInfo.email || '<EMAIL>' }}</p>
            <el-tag :type="getRoleType(userInfo.role)" size="small">
              {{ getRoleText(userInfo.role) }}
            </el-tag>
          </div>
        </div>
        
        <!-- 用户操作菜单 -->
        <div class="user-menu-list">
          <div
            v-for="menuItem in userMenuItems"
            :key="menuItem.key"
            class="user-menu-item"
            @click="handleUserMenuClick(menuItem)"
          >
            <div class="menu-item-icon" :style="{ backgroundColor: menuItem.color }">
              <el-icon><component :is="menuItem.icon" /></el-icon>
            </div>
            <div class="menu-item-content">
              <div class="menu-item-title">{{ menuItem.title }}</div>
              <div class="menu-item-description">{{ menuItem.description }}</div>
            </div>
            <el-icon class="menu-item-arrow"><ArrowRight /></el-icon>
          </div>
        </div>
        
        <!-- 退出登录 -->
        <div class="logout-section">
          <el-button 
            type="danger" 
            size="large" 
            @click="handleLogout"
            class="logout-btn"
          >
            <el-icon><SwitchButton /></el-icon>
            退出登录
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Menu,
  Close,
  Search,
  Bell,
  DataLine,
  ArrowRight,
  ArrowLeft,
  Clock,
  Check,
  User,
  Setting,
  Lock,
  SwitchButton,
  Plus,
  Document,
  Star,
  TrendCharts,
  Monitor,
  Notification,
  Warning,
  Success,
  InfoFilled
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  showBottomNav: {
    type: Boolean,
    default: true
  }
})

// Emits  
const emit = defineEmits(['menu-toggle', 'tab-change'])

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const showMenu = ref(false)
const showSearchDrawer = ref(false)
const showNotificationsDrawer = ref(false)
const showUserDrawer = ref(false)
const expandedSections = ref(['dashboard', 'community'])
const activeTab = ref('dashboard')
const mobileSearchQuery = ref('')
const mobileSearchResults = ref([])
const recentMobileSearches = ref([])

// 计算属性
const navigationClasses = computed(() => ({
  'menu-open': showMenu.value
}))

const userInfo = computed(() => userStore.userInfo || {})

const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.read).length
})

// 快速操作
const quickActions = computed(() => [
  { key: 'add-group', title: '创建群组', icon: 'Plus', color: '#3b82f6' },
  { key: 'scan-qr', title: '扫码加群', icon: 'Scan', color: '#10b981' },
  { key: 'data-export', title: '数据导出', icon: 'Document', color: '#f59e0b' },
  { key: 'help', title: '帮助中心', icon: 'QuestionFilled', color: '#8b5cf6' }
])

// 移动端菜单结构
const mobileMenuSections = computed(() => [
  {
    key: 'dashboard',
    title: '仪表板',
    icon: 'DataLine',
    color: '#3b82f6',
    items: [
      { key: 'overview', title: '概览', icon: 'TrendCharts', path: '/dashboard', badge: null },
      { key: 'analytics', title: '数据分析', icon: 'DataLine', path: '/dashboard/analytics', badge: 2 }
    ]
  },
  {
    key: 'community',
    title: '社群管理',
    icon: 'Menu',
    color: '#10b981',
    items: [
      { key: 'groups', title: '群组列表', icon: 'Menu', path: '/community/groups', badge: 5 },
      { key: 'templates', title: '模板管理', icon: 'Document', path: '/community/templates', badge: null }
    ]
  },
  {
    key: 'users',
    title: '用户管理',
    icon: 'User',
    color: '#f59e0b',
    items: [
      { key: 'user-list', title: '用户列表', icon: 'User', path: '/users', badge: 12 },
      { key: 'user-analytics', title: '用户分析', icon: 'DataLine', path: '/users/analytics', badge: null }
    ]
  },
  {
    key: 'system',
    title: '系统设置',
    icon: 'Setting',
    color: '#8b5cf6',
    items: [
      { key: 'settings', title: '基础设置', icon: 'Setting', path: '/system/settings', badge: null },
      { key: 'monitor', title: '系统监控', icon: 'Monitor', path: '/system/monitor', badge: 1 }
    ]
  }
])

// 底部导航标签
const bottomNavTabs = computed(() => [
  { key: 'dashboard', label: '首页', icon: 'House', path: '/dashboard' },
  { key: 'community', label: '社群', icon: 'Menu', path: '/community', badge: 5 },
  { key: 'users', label: '用户', icon: 'User', path: '/users', badge: 12 },
  { key: 'notifications', label: '通知', icon: 'Bell', path: '/notifications', badge: unreadCount.value },
  { key: 'profile', label: '我的', icon: 'User', path: '/profile' }
])

// 底部操作
const footerActions = computed(() => [
  { key: 'settings', title: '设置', icon: 'Setting', type: 'text' },
  { key: 'help', title: '帮助', icon: 'QuestionFilled', type: 'text' },
  { key: 'feedback', title: '反馈', icon: 'ChatDotSquare', type: 'text' }
])

// 用户菜单项
const userMenuItems = computed(() => [
  {
    key: 'profile',
    title: '个人资料',
    description: '查看和编辑个人信息',
    icon: 'User',
    color: '#3b82f6'
  },
  {
    key: 'settings',
    title: '账户设置',
    description: '管理账户和偏好设置',
    icon: 'Setting',
    color: '#10b981'
  },
  {
    key: 'security',
    title: '安全设置',
    description: '密码和安全选项',
    icon: 'Lock',
    color: '#f59e0b'
  },
  {
    key: 'notifications',
    title: '通知设置',
    description: '管理通知偏好',
    icon: 'Bell',
    color: '#8b5cf6'
  },
  {
    key: 'help',
    title: '帮助中心',
    description: '获取帮助和支持',
    icon: 'QuestionFilled',
    color: '#06b6d4'
  }
])

// 通知数据
const notifications = ref([
  {
    id: 1,
    type: 'info',
    title: '系统更新',
    message: '新版本已发布，包含多项功能改进',
    timestamp: Date.now() - 300000,
    read: false
  },
  {
    id: 2,
    type: 'warning',
    title: '域名即将过期',
    message: 'example.com 将在7天后到期',
    timestamp: Date.now() - 1800000,
    read: false
  },
  {
    id: 3,
    type: 'success',
    title: '数据备份完成',
    message: '今日数据备份已成功完成',
    timestamp: Date.now() - 3600000,
    read: true
  }
])

// 热门搜索建议
const popularSuggestions = computed(() => [
  { key: 'groups', title: '群组管理', icon: 'Menu' },
  { key: 'users', title: '用户分析', icon: 'User' },
  { key: 'data', title: '数据导出', icon: 'Document' },
  { key: 'settings', title: '系统设置', icon: 'Setting' }
])

// 方法
const toggleMenu = () => {
  showMenu.value = !showMenu.value
  emit('menu-toggle', showMenu.value)
}

const handleMenuClick = () => {
  showMenu.value = false
}

const navigateHome = () => {
  router.push('/admin/dashboard')
  showMenu.value = false
}

const toggleSection = (sectionKey) => {
  const index = expandedSections.value.indexOf(sectionKey)
  if (index > -1) {
    expandedSections.value.splice(index, 1)
  } else {
    expandedSections.value.push(sectionKey)
  }
}

const handleQuickAction = (action) => {
  switch (action.key) {
    case 'add-group':
      router.push('/admin/community/groups')
      break
    case 'scan-qr':
      // 启动扫码功能
      ElMessage.info('扫码功能开发中...')
      break
    case 'data-export':
      router.push('/admin/system/data-export')
      break
    case 'help':
      router.push('/help')
      break
  }
  showMenu.value = false
}

const handleMenuItemClick = (item) => {
  if (item.path) {
    router.push(item.path)
  }
  showMenu.value = false
}

const handleTabClick = (tab) => {
  activeTab.value = tab.key
  if (tab.path) {
    router.push(tab.path)
  }
  emit('tab-change', tab)
}

const handleFooterAction = (action) => {
  switch (action.key) {
    case 'settings':
      router.push('/settings')
      break
    case 'help':
      router.push('/help')
      break
    case 'feedback':
      router.push('/feedback')
      break
  }
  showMenu.value = false
}

// 搜索相关
const handleMobileSearch = (query) => {
  if (!query.trim()) {
    mobileSearchResults.value = []
    return
  }

  // 模拟搜索结果
  const allItems = []
  mobileMenuSections.value.forEach(section => {
    section.items.forEach(item => {
      allItems.push({
        ...item,
        type: section.title,
        description: `${section.title} > ${item.title}`
      })
    })
  })

  mobileSearchResults.value = allItems.filter(item =>
    item.title.toLowerCase().includes(query.toLowerCase()) ||
    item.type.toLowerCase().includes(query.toLowerCase())
  )
}

const selectMobileSearchResult = (result) => {
  if (result.path) {
    router.push(result.path)
  }
  
  // 添加到搜索历史
  addToMobileSearchHistory(mobileSearchQuery.value)
  
  showSearchDrawer.value = false
  mobileSearchQuery.value = ''
}

const handleSuggestionClick = (suggestion) => {
  mobileSearchQuery.value = suggestion.title
  handleMobileSearch(suggestion.title)
}

const addToMobileSearchHistory = (query) => {
  if (!query.trim()) return
  
  const history = {
    id: Date.now(),
    query,
    timestamp: Date.now()
  }
  
  recentMobileSearches.value.unshift(history)
  recentMobileSearches.value = recentMobileSearches.value.slice(0, 5)
  
  localStorage.setItem('mobile-search-history', JSON.stringify(recentMobileSearches.value))
}

const applyRecentSearch = (search) => {
  mobileSearchQuery.value = search.query
  handleMobileSearch(search.query)
}

// 通知相关
const handleNotificationClick = (notification) => {
  if (!notification.read) {
    markAsRead(notification)
  }
  // 可以根据通知类型导航到相关页面
}

const markAsRead = (notification) => {
  notification.read = true
}

const markAllRead = () => {
  notifications.value.forEach(n => n.read = true)
}

const clearAllNotifications = () => {
  notifications.value = []
}

const getNotificationIcon = (type) => {
  const icons = {
    info: 'InfoFilled',
    warning: 'Warning',
    success: 'Success',
    error: 'Warning'
  }
  return icons[type] || 'InfoFilled'
}

// 用户相关
const handleUserMenuClick = (menuItem) => {
  switch (menuItem.key) {
    case 'profile':
      router.push('/user/profile')
      break
    case 'settings':
      router.push('/user/settings')
      break
    case 'security':
      router.push('/user/security')
      break
    case 'notifications':
      router.push('/user/notifications')
      break
    case 'help':
      router.push('/help')
      break
  }
  showUserDrawer.value = false
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await userStore.logout()
    router.push('/login')
    ElMessage.success('已安全退出')
  } catch (error) {
    // 用户取消
  }
}

const getRoleText = (role) => {
  const roles = {
    admin: '系统管理员',
    manager: '管理员',
    user: '普通用户',
    distributor: '分销商'
  }
  return roles[role] || '用户'
}

const getRoleType = (role) => {
  const types = {
    admin: 'danger',
    manager: 'warning',
    user: 'primary',
    distributor: 'success'
  }
  return types[role] || 'primary'
}

// 工具方法
const isItemActive = (item) => {
  return route.path === item.path
}

const getSectionBadge = (section) => {
  return section.items.reduce((total, item) => total + (item.badge || 0), 0)
}

const highlightText = (text, query) => {
  if (!query) return text
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const formatTimeAgo = (timestamp) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 监听路由变化更新活跃标签
watch(() => route.path, (newPath) => {
  const activeTabItem = bottomNavTabs.value.find(tab => 
    newPath.startsWith(tab.path)
  )
  if (activeTabItem) {
    activeTab.value = activeTabItem.key
  }
})

// 处理点击外部关闭菜单
const handleClickOutside = (event) => {
  if (showMenu.value) {
    // 使用安全的DOM工具
    import('@/utils/dom-safe.js').then(({ safeClosest }) => {
      if (!safeClosest(event.target, '.mobile-navigation')) {
        showMenu.value = false
      }
    }).catch(() => {
      // 如果工具加载失败，直接关闭菜单
      showMenu.value = false
    })
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  
  // 加载搜索历史
  const savedHistory = JSON.parse(localStorage.getItem('mobile-search-history') || '[]')
  recentMobileSearches.value = savedHistory
  
  // 根据当前路由设置活跃标签
  const activeTabItem = bottomNavTabs.value.find(tab => 
    route.path.startsWith(tab.path)
  )
  if (activeTabItem) {
    activeTab.value = activeTabItem.key
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
.mobile-navigation {
  position: relative;
  
  &.menu-open {
    .mobile-header {
      .menu-toggle {
        background: var(--color-primary);
        color: white;
      }
    }
  }
}

// 移动端头部
.mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 0 var(--spacing-md);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  position: relative;
  z-index: var(--z-fixed);
  
  .header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .menu-toggle {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-lg);
      background: var(--bg-secondary);
      border: 1px solid var(--border-light);
      transition: all var(--duration-normal) var(--ease-out);
      
      &:hover {
        background: var(--color-primary);
        color: white;
        transform: scale(1.05);
      }
    }
    
    .mobile-logo {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      cursor: pointer;
      
      .logo-icon {
        width: 32px;
        height: 32px;
        background: var(--gradient-primary);
        color: white;
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
      }
      
      .logo-text {
        font-size: var(--text-base);
        font-weight: var(--font-bold);
        background: var(--gradient-primary);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .search-toggle,
    .notifications-btn {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-lg);
      background: var(--bg-secondary);
      border: 1px solid var(--border-light);
      
      &:hover {
        background: var(--color-primary);
        color: white;
      }
    }
    
    .user-avatar {
      cursor: pointer;
      border: 2px solid var(--border-light);
      transition: all var(--duration-normal) var(--ease-out);
      
      &:hover {
        border-color: var(--color-primary);
        transform: scale(1.05);
      }
    }
  }
}

// 移动端菜单
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal);
  
  .menu-content {
    width: 80%;
    height: 100%;
    background: var(--bg-primary);
    box-shadow: var(--shadow-lg);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }
}

// 用户卡片
.user-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-muted) 100%);
  border-bottom: 1px solid var(--border-light);
  
  .user-info {
    flex: 1;
    
    .user-name {
      font-size: var(--text-base);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin-bottom: 2px;
    }
    
    .user-role {
      font-size: var(--text-sm);
      color: var(--text-muted);
    }
  }
}

// 快速操作
.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  
  .quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    
    .action-icon {
      width: 48px;
      height: 48px;
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
    }
    
    .action-title {
      font-size: var(--text-xs);
      color: var(--text-secondary);
      text-align: center;
    }
  }
}

// 导航列表
.navigation-list {
  flex: 1;
  padding: var(--spacing-md) 0;
  
  .menu-section {
    margin-bottom: var(--spacing-sm);
    
    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-sm) var(--spacing-md);
      cursor: pointer;
      
      .section-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        
        .section-icon {
          font-size: 18px;
        }
        
        .section-title {
          font-size: var(--text-sm);
          font-weight: var(--font-semibold);
          color: var(--text-primary);
        }
        
        .section-badge {
          :deep(.el-badge__content) {
            font-size: 10px;
          }
        }
      }
      
      .section-toggle {
        color: var(--text-light);
        transition: transform var(--duration-normal) var(--ease-out);
        
        &.rotated {
          transform: rotate(90deg);
        }
      }
    }
    
    .section-items {
      .menu-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-xl);
        color: var(--text-secondary);
        text-decoration: none;
        transition: all var(--duration-normal) var(--ease-out);
        position: relative;
        
        &:hover {
          background: var(--bg-secondary);
          color: var(--color-primary);
        }
        
        &.active {
          background: var(--primary-50);
          color: var(--color-primary);
          
          .item-indicator {
            opacity: 1;
          }
        }
        
        .item-icon {
          font-size: 16px;
        }
        
        .item-title {
          flex: 1;
          font-size: var(--text-sm);
        }
        
        .item-badge {
          :deep(.el-badge__content) {
            font-size: 10px;
          }
        }
        
        .item-indicator {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background: var(--color-primary);
          border-radius: 2px 0 0 2px;
          opacity: 0;
          transition: opacity var(--duration-normal) var(--ease-out);
        }
      }
    }
  }
}

// 菜单底部
.menu-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-light);
  
  .footer-actions {
    display: flex;
    justify-content: space-around;
    
    .footer-action {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      font-size: var(--text-xs);
    }
  }
}

// 底部导航
.mobile-bottom-nav {
  display: flex;
  height: 60px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--glass-border);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  
  .nav-tab {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    
    &.active {
      color: var(--color-primary);
      
      .tab-icon {
        transform: scale(1.1);
      }
    }
    
    .tab-icon {
      font-size: 20px;
      transition: transform var(--duration-normal) var(--ease-out);
    }
    
    .tab-label {
      font-size: var(--text-xs);
      color: var(--text-muted);
    }
  }
}

// 抽屉样式
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  
  .drawer-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
  }
}

// 搜索抽屉
.search-content {
  padding: var(--spacing-md);
  
  .mobile-search-input {
    margin-bottom: var(--spacing-lg);
    
    :deep(.el-input__wrapper) {
      border-radius: var(--radius-lg);
      background: var(--bg-secondary);
    }
  }
  
  .mobile-search-results {
    .results-header {
      font-size: var(--text-sm);
      color: var(--text-muted);
      margin-bottom: var(--spacing-md);
    }
    
    .result-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-md);
      background: var(--bg-primary);
      border: 1px solid var(--border-light);
      border-radius: var(--radius-lg);
      margin-bottom: var(--spacing-sm);
      cursor: pointer;
      
      &:hover {
        background: var(--bg-secondary);
      }
      
      .result-icon {
        width: 32px;
        height: 32px;
        background: var(--primary-50);
        color: var(--color-primary);
        border-radius: var(--radius-sm);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .result-content {
        flex: 1;
        
        .result-title {
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          color: var(--text-primary);
          
          :deep(mark) {
            background: var(--warning-100);
            color: var(--warning-600);
          }
        }
        
        .result-description {
          font-size: var(--text-xs);
          color: var(--text-muted);
          margin-top: 2px;
        }
      }
      
      .result-type {
        font-size: var(--text-xs);
        color: var(--text-light);
      }
    }
  }
  
  .search-suggestions {
    .suggestions-section {
      margin-bottom: var(--spacing-xl);
      
      h4 {
        font-size: var(--text-base);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-md) 0;
      }
    }
    
    .suggestions-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--spacing-sm);
      
      .suggestion-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
        background: var(--bg-secondary);
        border-radius: var(--radius-lg);
        cursor: pointer;
        
        &:hover {
          background: var(--primary-50);
          color: var(--color-primary);
        }
      }
    }
    
    .recent-searches {
      .recent-search-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
        border-radius: var(--radius-sm);
        cursor: pointer;
        
        &:hover {
          background: var(--bg-secondary);
        }
        
        small {
          margin-left: auto;
          color: var(--text-light);
        }
      }
    }
  }
}

// 通知抽屉
.notifications-content {
  .notifications-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
  }
  
  .notifications-list {
    .notification-item {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-sm);
      padding: var(--spacing-md);
      border-bottom: 1px solid var(--border-light);
      cursor: pointer;
      
      &:hover {
        background: var(--bg-secondary);
      }
      
      &.unread {
        background: var(--primary-50);
        border-left: 3px solid var(--color-primary);
      }
      
      .notification-icon {
        width: 32px;
        height: 32px;
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.info {
          background: var(--primary-100);
          color: var(--color-primary);
        }
        
        &.warning {
          background: var(--warning-100);
          color: var(--warning-600);
        }
        
        &.success {
          background: var(--success-100);
          color: var(--success-600);
        }
      }
      
      .notification-content {
        flex: 1;
        
        .notification-title {
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          color: var(--text-primary);
          margin-bottom: 2px;
        }
        
        .notification-message {
          font-size: var(--text-xs);
          color: var(--text-secondary);
          margin-bottom: 4px;
        }
        
        .notification-time {
          font-size: var(--text-xs);
          color: var(--text-light);
        }
      }
    }
  }
}

// 用户抽屉
.user-content {
  .user-profile-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-muted) 100%);
    margin-bottom: var(--spacing-lg);
    
    .profile-info {
      margin-top: var(--spacing-md);
      text-align: center;
      
      .profile-name {
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
      }
      
      .profile-email {
        font-size: var(--text-sm);
        color: var(--text-muted);
        margin: 0 0 var(--spacing-sm) 0;
      }
    }
  }
  
  .user-menu-list {
    .user-menu-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      cursor: pointer;
      transition: background-color var(--duration-normal) var(--ease-out);
      
      &:hover {
        background: var(--bg-secondary);
      }
      
      .menu-item-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }
      
      .menu-item-content {
        flex: 1;
        
        .menu-item-title {
          font-size: var(--text-base);
          font-weight: var(--font-medium);
          color: var(--text-primary);
          margin-bottom: 2px;
        }
        
        .menu-item-description {
          font-size: var(--text-sm);
          color: var(--text-muted);
        }
      }
      
      .menu-item-arrow {
        color: var(--text-light);
      }
    }
  }
  
  .logout-section {
    padding: var(--spacing-lg);
    
    .logout-btn {
      width: 100%;
      height: 48px;
      font-size: var(--text-base);
    }
  }
}

// 动画
.menu-slide-enter-active,
.menu-slide-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.menu-slide-enter-from,
.menu-slide-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}

.menu-icon-rotate-enter-active,
.menu-icon-rotate-leave-active {
  transition: all var(--duration-fast) var(--ease-out);
}

.menu-icon-rotate-enter-from,
.menu-icon-rotate-leave-to {
  opacity: 0;
  transform: rotate(180deg) scale(0.8);
}

.section-expand-enter-active,
.section-expand-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  overflow: hidden;
}

.section-expand-enter-from,
.section-expand-leave-to {
  max-height: 0;
  opacity: 0;
}

.section-expand-enter-to,
.section-expand-leave-from {
  max-height: 300px;
  opacity: 1;
}

// 响应式调整
@media (max-width: 480px) {
  .mobile-menu .menu-content {
    width: 90%;
  }
  
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }
  
  .search-suggestions .suggestions-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 360px) {
  .mobile-header {
    padding: 0 var(--spacing-sm);
    
    .header-right {
      gap: var(--spacing-xs);
    }
  }
  
  .mobile-bottom-nav .nav-tab .tab-label {
    display: none;
  }
}
</style>