/* empty css             *//* empty css                    *//* empty css                 *//* empty css                   *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                  */import{l as e,m as a,q as l,G as t,A as s,F as o,Y as r,z as u,E as n,C as i,r as c,M as d,c as m,o as p}from"./vue-vendor-BcnDv-68.js";import{a6 as v,Z as g,a5 as f,ah as y,a1 as h,s as _,W as b,a4 as A,b7 as w,ax as C,aa as V,aF as k,aC as x,af as S,ag as j,X as D}from"./element-plus-C2UshkXo.js";import U from"./MediaUploader-CWV5P0gW.js";import{_ as M}from"./index-eUTsTR3J.js";/* empty css                  *//* empty css                    */import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const L={__name:"AvatarLibrarySelector",props:{modelValue:{type:[String,Array],default:()=>[]},maxSelection:{type:Number,default:0},multiple:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(e,{expose:a,emit:l}){a();const t=e,s=l,o=c("default"),r=c([]),u=c(!1),n=c(!1),i=c([]),v=c(""),g=c([]),f=c([{value:"default",label:"默认头像",count:50},{value:"business",label:"商务头像",count:30},{value:"cartoon",label:"卡通头像",count:40},{value:"animal",label:"动物头像",count:25},{value:"custom",label:"自定义头像",count:0}]),y=d({default:[],business:[],cartoon:[],animal:[],custom:[]}),h=m(()=>y[o.value]||[]),_=m(()=>v.value?h.value.filter(e=>e.name&&e.name.toLowerCase().includes(v.value.toLowerCase())):h.value),b=m(()=>g.value.length>0),A=async e=>{try{const a=await fetch(`/api/avatars/${e}`),l=await a.json();l.success&&(y[e]=l.data)}catch(a){y[e]=w(e)}},w=e=>{const a=f.value.find(a=>a.value===e)?.count||20,l=[];for(let t=1;t<=a;t++)l.push({url:`/avatars/${e}/${t}.jpg`,name:`${e}_${t}`,size:"100x100"});return l};p(()=>{A("default"),t.modelValue&&(Array.isArray(t.modelValue)?r.value=[...t.modelValue]:r.value=[t.modelValue])});const M={props:t,emit:s,selectedLibrary:o,selectedAvatars:r,showUploadDialog:u,showManageDialog:n,customAvatars:i,searchKeyword:v,managementSelection:g,avatarLibraries:f,avatarData:y,currentAvatars:h,filteredAvatars:_,hasSelection:b,handleLibraryChange:e=>{o.value=e,A(e)},loadAvatars:A,generateMockAvatars:w,toggleAvatar:e=>{if(!t.multiple)return r.value=[e],s("update:modelValue",e),void s("change",e);const a=r.value.indexOf(e);if(a>-1)r.value.splice(a,1);else{if(t.maxSelection>0&&r.value.length>=t.maxSelection)return void D.warning(`最多只能选择 ${t.maxSelection} 个头像`);r.value.push(e)}s("update:modelValue",[...r.value]),s("change",[...r.value])},selectAll:()=>{t.maxSelection>0?r.value=h.value.slice(0,t.maxSelection).map(e=>e.url):r.value=h.value.map(e=>e.url),s("update:modelValue",[...r.value]),s("change",[...r.value])},clearSelection:()=>{r.value=[],s("update:modelValue",[]),s("change",[])},handleCustomUpload:e=>{i.value=e},saveCustomAvatars:()=>{if(i.value.length>0){const e=f.value.find(e=>"custom"===e.value);e&&(e.count=i.value.length),y.custom=i.value.map((e,a)=>({url:e,name:`custom_${a+1}`,size:"100x100"})),o.value="custom",D.success("自定义头像保存成功")}u.value=!1},batchDelete:async()=>{try{await j.confirm("确定要删除选中的头像吗？","批量删除",{type:"warning"});const e=y[o.value];y[o.value]=e.filter(e=>!g.value.includes(e.url)),g.value=[],D.success("删除成功")}catch{}},deleteAvatar:async(e,a)=>{try{await j.confirm("确定要删除这个头像吗？","删除头像",{type:"warning"}),y[o.value].splice(a,1),D.success("删除成功")}catch{}},previewAvatar:e=>{window.open(e.url,"_blank")},exportAvatars:()=>{D.info("导出功能开发中...")},ref:c,reactive:d,computed:m,onMounted:p,get ElMessage(){return D},get ElMessageBox(){return j},get Plus(){return S},get Check(){return x},get Search(){return k},get Delete(){return V},get Download(){return C},MediaUploader:U};return Object.defineProperty(M,"__isScriptSetup",{enumerable:!1,value:!0}),M}},$={class:"avatar-library-selector"},z={class:"library-selector"},P={class:"library-option"},E={class:"library-name"},G={class:"library-count"},K={class:"avatar-grid"},N=["onClick"],q=["src","alt"],B={class:"avatar-overlay"},F={class:"selection-stats"},O={class:"stats-text"},I={class:"stats-actions"},J={class:"custom-upload-section"},Q={class:"avatar-management"},W={class:"management-header"},X={class:"management-actions"},Y={class:"management-grid"},Z=["src","alt"],H={class:"item-overlay"},R={class:"item-actions"};const T=M(L,[["render",function(c,d,m,p,C,V){const k=v,x=f,S=y,j=h,D=b,U=A,M=w;return a(),e("div",$,[l("div",z,[t(x,{modelValue:p.selectedLibrary,"onUpdate:modelValue":d[0]||(d[0]=e=>p.selectedLibrary=e),placeholder:"选择头像库",onChange:p.handleLibraryChange},{default:s(()=>[(a(!0),e(o,null,r(p.avatarLibraries,e=>(a(),u(k,{key:e.value,label:e.label,value:e.value},{default:s(()=>[l("div",P,[l("span",E,g(e.label),1),l("span",G,g(e.count)+"个头像",1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),t(j,{onClick:d[1]||(d[1]=e=>p.showUploadDialog=!0),type:"primary",plain:""},{default:s(()=>[t(S,null,{default:s(()=>[t(p.Plus)]),_:1}),d[9]||(d[9]=n(" 自定义上传 ",-1))]),_:1,__:[9]})]),l("div",K,[(a(!0),e(o,null,r(p.currentAvatars,(o,r)=>(a(),e("div",{key:r,class:_(["avatar-item",{selected:p.selectedAvatars.includes(o.url)}]),onClick:e=>p.toggleAvatar(o.url)},[l("img",{src:o.url,alt:`头像${r+1}`},null,8,q),l("div",B,[p.selectedAvatars.includes(o.url)?(a(),u(S,{key:0},{default:s(()=>[t(p.Check)]),_:1})):i("",!0)])],10,N))),128))]),l("div",F,[l("span",O,[n(" 已选择 "+g(p.selectedAvatars.length)+" 个头像 ",1),m.maxSelection>0?(a(),e(o,{key:0},[n(" / 最多 "+g(m.maxSelection)+" 个 ",1)],64)):i("",!0)]),l("div",I,[t(j,{onClick:p.selectAll,size:"small",type:"primary",plain:""},{default:s(()=>d[10]||(d[10]=[n(" 全选 ",-1)])),_:1,__:[10]}),t(j,{onClick:p.clearSelection,size:"small"},{default:s(()=>d[11]||(d[11]=[n(" 清空 ",-1)])),_:1,__:[11]})])]),t(D,{modelValue:p.showUploadDialog,"onUpdate:modelValue":d[4]||(d[4]=e=>p.showUploadDialog=e),title:"自定义头像上传",width:"600px","append-to-body":""},{footer:s(()=>[t(j,{onClick:d[3]||(d[3]=e=>p.showUploadDialog=!1)},{default:s(()=>d[13]||(d[13]=[n("取消",-1)])),_:1,__:[13]}),t(j,{type:"primary",onClick:p.saveCustomAvatars},{default:s(()=>d[14]||(d[14]=[n("确定",-1)])),_:1,__:[14]})]),default:s(()=>[l("div",J,[t(p.MediaUploader,{modelValue:p.customAvatars,"onUpdate:modelValue":d[2]||(d[2]=e=>p.customAvatars=e),type:"image",multiple:!0,limit:20,accept:"image/*","list-type":"picture-card",onChange:p.handleCustomUpload},{tip:s(()=>d[12]||(d[12]=[l("div",{class:"upload-tip"}," 建议尺寸：100x100px，支持JPG、PNG格式，最多上传20个 ",-1)])),_:1},8,["modelValue"])])]),_:1},8,["modelValue"]),t(D,{modelValue:p.showManageDialog,"onUpdate:modelValue":d[8]||(d[8]=e=>p.showManageDialog=e),title:"头像库管理",width:"800px","append-to-body":""},{footer:s(()=>[t(j,{onClick:d[7]||(d[7]=e=>p.showManageDialog=!1)},{default:s(()=>d[19]||(d[19]=[n("关闭",-1)])),_:1,__:[19]})]),default:s(()=>[l("div",Q,[l("div",W,[t(U,{modelValue:p.searchKeyword,"onUpdate:modelValue":d[5]||(d[5]=e=>p.searchKeyword=e),placeholder:"搜索头像...",style:{width:"200px"}},{prefix:s(()=>[t(S,null,{default:s(()=>[t(p.Search)]),_:1})]),_:1},8,["modelValue"]),l("div",X,[t(j,{onClick:p.batchDelete,disabled:!p.hasSelection,type:"danger",plain:""},{default:s(()=>[t(S,null,{default:s(()=>[t(p.Delete)]),_:1}),d[15]||(d[15]=n(" 批量删除 ",-1))]),_:1,__:[15]},8,["disabled"]),t(j,{onClick:p.exportAvatars,type:"primary",plain:""},{default:s(()=>[t(S,null,{default:s(()=>[t(p.Download)]),_:1}),d[16]||(d[16]=n(" 导出头像 ",-1))]),_:1,__:[16]})])]),l("div",Y,[(a(!0),e(o,null,r(p.filteredAvatars,(o,r)=>(a(),e("div",{key:r,class:_(["management-item",{selected:p.managementSelection.includes(o.url)}])},[l("img",{src:o.url,alt:`头像${r+1}`},null,8,Z),l("div",H,[t(M,{modelValue:p.managementSelection,"onUpdate:modelValue":d[6]||(d[6]=e=>p.managementSelection=e),label:o.url},null,8,["modelValue","label"])]),l("div",R,[t(j,{onClick:e=>p.previewAvatar(o),size:"small",type:"primary",plain:""},{default:s(()=>[...d[17]||(d[17]=[n(" 预览 ",-1)])]),_:2,__:[17]},1032,["onClick"]),t(j,{onClick:e=>p.deleteAvatar(o,r),size:"small",type:"danger",plain:""},{default:s(()=>[...d[18]||(d[18]=[n(" 删除 ",-1)])]),_:2,__:[18]},1032,["onClick"])])],2))),128))])])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-4e5c18d8"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/AvatarLibrarySelector.vue"]]);export{T as default};
