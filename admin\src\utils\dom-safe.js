/**
 * DOM安全操作工具
 * 提供安全的DOM方法调用，避免运行时错误
 */

/**
 * 安全的closest方法
 * @param {Element} element - 目标元素
 * @param {string} selector - 选择器
 * @returns {Element|null} 匹配的祖先元素或null
 */
export function safeClosest(element, selector) {
  if (!element || typeof element.closest !== 'function') {
    return null
  }
  
  try {
    return element.closest(selector)
  } catch (error) {
    console.debug('safeClosest error:', error.message)
    return null
  }
}

/**
 * 安全的querySelector方法
 * @param {Element|Document} parent - 父元素
 * @param {string} selector - 选择器
 * @returns {Element|null} 匹配的元素或null
 */
export function safeQuerySelector(parent, selector) {
  if (!parent || typeof parent.querySelector !== 'function') {
    return null
  }
  
  try {
    return parent.querySelector(selector)
  } catch (error) {
    console.debug('safeQuerySelector error:', error.message)
    return null
  }
}

/**
 * 安全的querySelectorAll方法
 * @param {Element|Document} parent - 父元素
 * @param {string} selector - 选择器
 * @returns {NodeList|Array} 匹配的元素列表
 */
export function safeQuerySelectorAll(parent, selector) {
  if (!parent || typeof parent.querySelectorAll !== 'function') {
    return []
  }
  
  try {
    return parent.querySelectorAll(selector)
  } catch (error) {
    console.debug('safeQuerySelectorAll error:', error.message)
    return []
  }
}

/**
 * 安全的addEventListener方法
 * @param {Element} element - 目标元素
 * @param {string} event - 事件类型
 * @param {Function} handler - 事件处理函数
 * @param {Object|boolean} options - 事件选项
 * @returns {boolean} 是否成功添加监听器
 */
export function safeAddEventListener(element, event, handler, options) {
  if (!element || typeof element.addEventListener !== 'function') {
    return false
  }
  
  try {
    element.addEventListener(event, handler, options)
    return true
  } catch (error) {
    console.debug('safeAddEventListener error:', error.message)
    return false
  }
}

/**
 * 安全的removeEventListener方法
 * @param {Element} element - 目标元素
 * @param {string} event - 事件类型
 * @param {Function} handler - 事件处理函数
 * @param {Object|boolean} options - 事件选项
 * @returns {boolean} 是否成功移除监听器
 */
export function safeRemoveEventListener(element, event, handler, options) {
  if (!element || typeof element.removeEventListener !== 'function') {
    return false
  }
  
  try {
    element.removeEventListener(event, handler, options)
    return true
  } catch (error) {
    console.debug('safeRemoveEventListener error:', error.message)
    return false
  }
}

/**
 * 安全的getBoundingClientRect方法
 * @param {Element} element - 目标元素
 * @returns {DOMRect|Object} 元素的边界矩形
 */
export function safeGetBoundingClientRect(element) {
  if (!element || typeof element.getBoundingClientRect !== 'function') {
    return { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 }
  }
  
  try {
    return element.getBoundingClientRect()
  } catch (error) {
    console.debug('safeGetBoundingClientRect error:', error.message)
    return { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 }
  }
}

/**
 * 安全的getComputedStyle方法
 * @param {Element} element - 目标元素
 * @param {string} pseudoElement - 伪元素
 * @returns {CSSStyleDeclaration|Object} 计算样式
 */
export function safeGetComputedStyle(element, pseudoElement) {
  if (!element || typeof window.getComputedStyle !== 'function') {
    return {}
  }
  
  try {
    return window.getComputedStyle(element, pseudoElement)
  } catch (error) {
    console.debug('safeGetComputedStyle error:', error.message)
    return {}
  }
}

/**
 * 检查元素是否存在且为Element节点
 * @param {any} element - 要检查的元素
 * @returns {boolean} 是否为有效的Element
 */
export function isValidElement(element) {
  return element && 
         element.nodeType === Node.ELEMENT_NODE && 
         typeof element.tagName === 'string'
}

/**
 * 检查元素是否在视口中
 * @param {Element} element - 目标元素
 * @returns {boolean} 是否在视口中
 */
export function isElementInViewport(element) {
  if (!isValidElement(element)) {
    return false
  }
  
  const rect = safeGetBoundingClientRect(element)
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  )
}

/**
 * 安全的事件委托
 * @param {Element} parent - 父元素
 * @param {string} selector - 子元素选择器
 * @param {string} event - 事件类型
 * @param {Function} handler - 事件处理函数
 * @param {Object|boolean} options - 事件选项
 * @returns {Function|null} 清理函数
 */
export function safeDelegateEvent(parent, selector, event, handler, options) {
  if (!isValidElement(parent)) {
    return null
  }
  
  const delegateHandler = (e) => {
    const target = safeClosest(e.target, selector)
    if (target) {
      handler.call(target, e)
    }
  }
  
  if (safeAddEventListener(parent, event, delegateHandler, options)) {
    return () => safeRemoveEventListener(parent, event, delegateHandler, options)
  }
  
  return null
}

/**
 * 创建安全的DOM操作上下文
 * @param {Element} element - 根元素
 * @returns {Object} DOM操作方法集合
 */
export function createSafeDOMContext(element) {
  if (!isValidElement(element)) {
    console.warn('createSafeDOMContext: 无效的根元素')
    return null
  }
  
  return {
    element,
    find: (selector) => safeQuerySelector(element, selector),
    findAll: (selector) => safeQuerySelectorAll(element, selector),
    closest: (selector) => safeClosest(element, selector),
    on: (event, handler, options) => safeAddEventListener(element, event, handler, options),
    off: (event, handler, options) => safeRemoveEventListener(element, event, handler, options),
    delegate: (selector, event, handler, options) => safeDelegateEvent(element, selector, event, handler, options),
    getBounds: () => safeGetBoundingClientRect(element),
    getStyle: (pseudoElement) => safeGetComputedStyle(element, pseudoElement),
    isInViewport: () => isElementInViewport(element),
    isValid: () => isValidElement(element)
  }
}

// 默认导出
export default {
  safeClosest,
  safeQuerySelector,
  safeQuerySelectorAll,
  safeAddEventListener,
  safeRemoveEventListener,
  safeGetBoundingClientRect,
  safeGetComputedStyle,
  isValidElement,
  isElementInViewport,
  safeDelegateEvent,
  createSafeDOMContext
}
