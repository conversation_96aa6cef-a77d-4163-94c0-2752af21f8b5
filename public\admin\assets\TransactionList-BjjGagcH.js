/* empty css             *//* empty css                   *//* empty css                   *//* empty css                             *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css               *//* empty css               *//* empty css                *//* empty css                     *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                  */import{l as a,G as e,A as t,r as l,M as o,a3 as d,o as r,m as i,K as s,W as n,F as u,Y as p,E as m,B as c,z as f,q as g,H as y,C as _}from"./vue-vendor-BcnDv-68.js";import{k as v}from"./finance-CGlhYFvA.js";import{s as b,_ as w}from"./index-eUTsTR3J.js";import{P as h}from"./index-B0-VWyD5.js";import{V as j,U as k,W as x,a2 as P,a3 as V,a4 as C,a5 as q,a6 as T,a0 as U,a1 as L,Y as M,ai as R,aj as Q,ak as D,Z as S,s as Y,a7 as E,ar as N,as as O}from"./element-plus-C2UshkXo.js";import"./chunk-KZPPZA2C-C8HwxGb3.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";/* empty css                      */function A(a,e={},t="export.xlsx"){return new Promise((l,o)=>{b({url:a,method:"get",params:e,responseType:"blob"}).then(a=>{const e=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),o=window.URL.createObjectURL(e),d=document.createElement("a");d.href=o,d.download=t,document.body.appendChild(d),d.click(),document.body.removeChild(d),window.URL.revokeObjectURL(o),l(a)}).catch(a=>{o(a)})})}const F={class:"app-container"};const K=w({__name:"TransactionList",setup(a,{expose:e}){e();const t=l(!0),i=l(!1),s=l([]),n=l(0),u=l([]),p=o({queryParams:{page:1,limit:10,keyword:void 0,type:void 0,status:void 0,start_date:void 0,end_date:void 0},dialog:{visible:!1,data:null}}),{queryParams:m,dialog:c}=d(p);async function f(){t.value=!0;try{u.value&&2===u.value.length?(m.value.start_date=u.value[0],m.value.end_date=u.value[1]):(m.value.start_date=void 0,m.value.end_date=void 0);const a=await v(m.value);s.value=a.data.data,n.value=a.data.total}finally{t.value=!1}}function g(){m.value.page=1,f()}r(()=>{f()});const y={loading:t,exportLoading:i,transactionList:s,total:n,dateRange:u,transactionTypeMap:{recharge:"充值",withdraw:"提现",commission:"佣金",order_payment:"订单支付",refund:"退款",system_adjust:"系统调账"},statusMap:{pending:"处理中",completed:"已完成",failed:"已失败",cancelled:"已取消"},data:p,queryParams:m,dialog:c,formatNumber:a=>a?parseFloat(a).toFixed(2):"0.00",getTransactionTypeColor:a=>({recharge:"success",withdraw:"warning",commission:"primary",order_payment:"info",refund:"danger",system_adjust:""}[a]||"info"),getStatusColor:a=>({pending:"warning",completed:"success",failed:"danger",cancelled:"info"}[a]||"info"),getList:f,handleQuery:g,resetQuery:function(){u.value=[],m.value={page:1,limit:10,keyword:void 0,type:void 0,status:void 0,start_date:void 0,end_date:void 0},g()},handleExport:async function(){i.value=!0;try{await A("/finance/transactions/export",m.value,`transactions_${Date.now()}.xlsx`)}finally{i.value=!1}},handleView:function(a){c.value.data=a,c.value.visible=!0},ref:l,reactive:o,onMounted:r,toRefs:d,get getTransactionList(){return v},get exportData(){return A},Pagination:h};return Object.defineProperty(y,"__isScriptSetup",{enumerable:!1,value:!0}),y}},[["render",function(l,o,d,r,v,b){const w=C,h=V,A=T,K=q,W=U,Z=L,B=P,I=j,z=M,G=k,H=Q,$=D,J=R,X=O,aa=N,ea=x,ta=E;return i(),a("div",F,[e(I,{class:"filter-card"},{default:t(()=>[e(B,{inline:!0,model:r.queryParams,onSubmit:s(r.handleQuery,["prevent"])},{default:t(()=>[e(h,{label:"关键字"},{default:t(()=>[e(w,{modelValue:r.queryParams.keyword,"onUpdate:modelValue":o[0]||(o[0]=a=>r.queryParams.keyword=a),placeholder:"订单号/用户昵称",clearable:"",onKeyup:n(r.handleQuery,["enter"])},null,8,["modelValue"])]),_:1}),e(h,{label:"交易类型"},{default:t(()=>[e(K,{modelValue:r.queryParams.type,"onUpdate:modelValue":o[1]||(o[1]=a=>r.queryParams.type=a),placeholder:"全部类型",clearable:""},{default:t(()=>[(i(),a(u,null,p(r.transactionTypeMap,(a,t)=>e(A,{key:t,label:a,value:t},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(h,{label:"交易状态"},{default:t(()=>[e(K,{modelValue:r.queryParams.status,"onUpdate:modelValue":o[2]||(o[2]=a=>r.queryParams.status=a),placeholder:"全部状态",clearable:""},{default:t(()=>[(i(),a(u,null,p(r.statusMap,(a,t)=>e(A,{key:t,label:a,value:t},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(h,{label:"创建时间"},{default:t(()=>[e(W,{modelValue:r.dateRange,"onUpdate:modelValue":o[3]||(o[3]=a=>r.dateRange=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(h,null,{default:t(()=>[e(Z,{type:"primary",icon:"el-icon-search",onClick:r.handleQuery},{default:t(()=>o[8]||(o[8]=[m("查询",-1)])),_:1,__:[8]}),e(Z,{icon:"el-icon-refresh",onClick:r.resetQuery},{default:t(()=>o[9]||(o[9]=[m("重置",-1)])),_:1,__:[9]})]),_:1})]),_:1},8,["model"])]),_:1}),e(G,{gutter:10,class:"mb8"},{default:t(()=>[e(z,{span:1.5},{default:t(()=>[e(Z,{type:"success",plain:"",icon:"el-icon-download",onClick:r.handleExport,loading:r.exportLoading},{default:t(()=>o[10]||(o[10]=[m("导出",-1)])),_:1,__:[10]},8,["loading"])]),_:1})]),_:1}),e(I,null,{default:t(()=>[c((i(),f(J,{data:r.transactionList},{default:t(()=>[e(H,{label:"交易号",prop:"id",width:"100"}),e(H,{label:"用户",prop:"user.nickname",width:"150"}),e(H,{label:"交易类型",align:"center",width:"120"},{default:t(a=>[e($,{type:r.getTransactionTypeColor(a.row.type)},{default:t(()=>[m(S(r.transactionTypeMap[a.row.type]||"未知"),1)]),_:2},1032,["type"])]),_:1}),e(H,{label:"金额",width:"150"},{default:t(a=>[g("span",{class:Y(a.row.amount>0?"text-green":"text-red")},S(a.row.amount>0?"+":"")+" ¥"+S(r.formatNumber(a.row.amount)),3)]),_:1}),e(H,{label:"状态",align:"center",width:"100"},{default:t(a=>[e($,{type:r.getStatusColor(a.row.status)},{default:t(()=>[m(S(r.statusMap[a.row.status]||"未知"),1)]),_:2},1032,["type"])]),_:1}),e(H,{label:"描述",prop:"description","show-overflow-tooltip":""}),e(H,{label:"创建时间",prop:"created_at",width:"160"}),e(H,{label:"操作",width:"100",fixed:"right"},{default:t(a=>[e(Z,{link:"",type:"primary",onClick:e=>r.handleView(a.row)},{default:t(()=>o[11]||(o[11]=[m("详情",-1)])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ta,r.loading]]),c(e(r.Pagination,{total:r.total,page:r.queryParams.page,"onUpdate:page":o[4]||(o[4]=a=>r.queryParams.page=a),limit:r.queryParams.limit,"onUpdate:limit":o[5]||(o[5]=a=>r.queryParams.limit=a),onPagination:r.getList},null,8,["total","page","limit"]),[[y,r.total>0]])]),_:1}),e(ea,{title:"交易详情",modelValue:r.dialog.visible,"onUpdate:modelValue":o[7]||(o[7]=a=>r.dialog.visible=a),width:"600px"},{footer:t(()=>[e(Z,{onClick:o[6]||(o[6]=a=>r.dialog.visible=!1)},{default:t(()=>o[12]||(o[12]=[m("关闭",-1)])),_:1,__:[12]})]),default:t(()=>[r.dialog.data?(i(),f(aa,{key:0,column:2,border:""},{default:t(()=>[e(X,{label:"交易号"},{default:t(()=>[m(S(r.dialog.data.id),1)]),_:1}),e(X,{label:"用户"},{default:t(()=>[m(S(r.dialog.data.user?.nickname)+" (ID: "+S(r.dialog.data.user_id)+")",1)]),_:1}),e(X,{label:"交易类型"},{default:t(()=>[e($,{type:r.getTransactionTypeColor(r.dialog.data.type)},{default:t(()=>[m(S(r.transactionTypeMap[r.dialog.data.type]||"未知"),1)]),_:1},8,["type"])]),_:1}),e(X,{label:"交易状态"},{default:t(()=>[e($,{type:r.getStatusColor(r.dialog.data.status)},{default:t(()=>[m(S(r.statusMap[r.dialog.data.status]||"未知"),1)]),_:1},8,["type"])]),_:1}),e(X,{label:"交易金额"},{default:t(()=>[g("span",{class:Y(r.dialog.data.amount>0?"text-green":"text-red")},S(r.dialog.data.amount>0?"+":"")+" ¥"+S(r.formatNumber(r.dialog.data.amount)),3)]),_:1}),e(X,{label:"关联订单号"},{default:t(()=>[m(S(r.dialog.data.order_id||"无"),1)]),_:1}),e(X,{label:"创建时间"},{default:t(()=>[m(S(r.dialog.data.created_at),1)]),_:1}),e(X,{label:"更新时间"},{default:t(()=>[m(S(r.dialog.data.updated_at),1)]),_:1}),e(X,{label:"描述",span:2},{default:t(()=>[m(S(r.dialog.data.description),1)]),_:1})]),_:1})):_("",!0)]),_:1},8,["modelValue"])])}],["__scopeId","data-v-83522770"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/finance/TransactionList.vue"]]);export{K as default};
