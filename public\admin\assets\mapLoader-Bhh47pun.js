import{r as t}from"./echarts-D6CUuNS9.js";async function a(e="local"){try{let a;switch(e){case"datav":a="https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json";break;case"github":a="https://raw.githubusercontent.com/apache/echarts/master/map/json/china.json";break;default:a="/data/china.json"}const n=await fetch(a);if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const o=await n.json();return t("china",o),!0}catch(n){return"local"!==e&&await a("local")}}function e(){return{"北京":15420,"上海":12680,"广东":25680,"江苏":18950,"山东":16780,"浙江":14320,"河南":13560,"四川":12890,"湖北":11450,"湖南":10980,"安徽":9870,"河北":9560,"福建":8790,"江西":8340,"辽宁":7890,"山西":7450,"陕西":7120,"吉林":6890,"黑龙江":6560,"内蒙古":6230,"新疆":5890,"西藏":2340,"青海":2890,"甘肃":4560,"宁夏":3450,"云南":8900,"贵州":6780,"广西":7890,"重庆":5670,"天津":4890,"海南":3450}}function n(t){return Object.entries(t).map(([t,a])=>({name:t,value:a}))}function o(t,a){return{min:t,max:a,left:20,bottom:30,text:["高","低"],textStyle:{color:"#fff",fontSize:12},inRange:{color:["#94a3b8","#4ade80","#fbbf24","#f87171","#dc2626"]},calculable:!0,orient:"horizontal",itemWidth:20,itemHeight:140,textGap:10,precision:0,formatter:function(t){return t>=1e4?(t/1e4).toFixed(1)+"w":t>=1e3?(t/1e3).toFixed(1)+"k":t.toString()}}}async function r(t="datav"){try{const a={datav:"https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json",github:"https://raw.githubusercontent.com/apache/echarts/master/map/json/china.json",backup:"https://geo.datav.aliyun.com/areas_v2/bound/100000_full.json"},e=await fetch(a[t]||a.datav);if(!e.ok)throw new Error(`下载失败: ${e.status}`);const n=await e.json(),o=JSON.stringify(n,null,2),r=new Blob([o],{type:"application/json"}),c=URL.createObjectURL(r),s=document.createElement("a");return s.href=c,s.download=`china-map-${t}-${Date.now()}.json`,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(c),!0}catch(a){return!1}}function c(){return[{name:"datav",title:"阿里云DataV",url:"https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json",description:"高精度地图数据，包含详细边界信息",recommended:!0},{name:"github",title:"ECharts官方",url:"https://raw.githubusercontent.com/apache/echarts/master/map/json/china.json",description:"ECharts官方维护的地图数据",recommended:!1},{name:"local",title:"本地简化版",url:"/data/china.json",description:"项目内置的简化地图数据",recommended:!1}]}export{e as a,c as b,r as d,n as f,o as g,a as l};
