<template>
  <div class="modern-layout">
    <!-- 优化后的侧边栏组件 -->
    <div class="sidebar-container" :class="{ 'is-collapsed': isCollapsed }">
      <OptimizedSidebar 
        :collapse="isCollapsed"
        :user-roles="userRoles"
        @collapse-change="handleCollapseChange"
        @menu-select="handleMenuSelect"
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container" :class="{ 'sidebar-collapsed': isCollapsed }">
      <!-- 顶部导航栏 -->
      <header class="main-header">
        <div class="header-left">
          <el-button
            :icon="isCollapsed ? 'Expand' : 'Fold'"
            circle
            size="small"
            @click="toggleSidebar"
            class="collapse-btn"
          />
          
          <!-- 面包屑导航 -->
          <div class="breadcrumb-container">
            <el-breadcrumb separator="/" class="app-breadcrumb">
              <el-breadcrumb-item
                v-for="(item, index) in breadcrumbList"
                :key="item.path"
                :to="index === breadcrumbList.length - 1 ? undefined : item.path"
              >
                <el-icon v-if="item.icon" class="breadcrumb-icon">
                  <component :is="item.icon" />
                </el-icon>
                {{ item.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
        </div>

        <div class="header-right">
          <!-- 全屏切换 -->
          <el-button
            :icon="isFullscreen ? 'Aim' : 'FullScreen'"
            circle
            size="small"
            @click="toggleFullscreen"
            class="header-btn"
          />
          
          <!-- 通知 -->
          <el-badge :value="notificationCount" :hidden="notificationCount === 0">
            <el-button
              :icon="Bell"
              circle
              size="small"
              class="header-btn"
              @click="showNotifications"
            />
          </el-badge>

          <!-- 用户菜单 -->
          <el-dropdown class="user-dropdown" trigger="click">
            <div class="user-avatar">
              <el-avatar :size="32" :src="userInfo.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="user-info" v-if="!isCollapsed">
                <div class="user-name">{{ userInfo.name }}</div>
                <div class="user-role">{{ userInfo.role }}</div>
              </div>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="goToProfile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item @click="goToSettings">
                  <el-icon><Setting /></el-icon>
                  系统设置
                </el-dropdown-item>
                <el-dropdown-item divided @click="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="main-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component, route }">
            <transition name="page-fade" mode="out-in">
              <keep-alive :include="cacheableComponents">
                <component :is="Component" :key="route.fullPath" />
              </keep-alive>
            </transition>
          </router-view>
        </div>
      </main>

      <!-- 页脚 -->
      <footer class="main-footer">
        <div class="footer-content">
          <span>© 2024 晨鑫流量变现系统. All rights reserved.</span>
          <span>Version 1.0.0</span>
        </div>
      </footer>
    </div>

    <!-- 移动端遮罩 -->
    <div 
      v-if="isMobile && !isCollapsed" 
      class="mobile-overlay" 
      @click="closeSidebar"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { 
  Expand, 
  Fold, 
  FullScreen, 
  Aim, 
  Bell, 
  User, 
  Setting, 
  SwitchButton 
} from '@element-plus/icons-vue'
import OptimizedSidebar from './OptimizedSidebar.vue'
import { getBreadcrumb } from './SidebarConfig.js'
import { sidebarMenuConfig } from './SidebarConfig.js'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const isCollapsed = ref(false)
const isFullscreen = ref(false)
const isMobile = ref(false)
const notificationCount = ref(3)

// 用户信息
const userInfo = computed(() => ({
  name: '管理员',
  role: '超级管理员',
  avatar: ''
}))

// 用户角色（临时使用默认值，后续可替换为 Pinia store）
const userRoles = computed(() => {
  // TODO: 替换为实际的用户角色获取逻辑
  return ['admin'] // 默认管理员权限
})

// 可缓存的组件列表（提高导航性能）
const cacheableComponents = ref([
  'Dashboard',
  'ModernDashboard', 
  'UserList',
  'GroupList',
  'OrderManagement',
  'FinanceDashboard',
  'AntiBlockDashboard'
])

// 面包屑导航
const breadcrumbList = computed(() => {
  return getBreadcrumb(sidebarMenuConfig, route.path)
})

// 方法
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const closeSidebar = () => {
  if (isMobile.value) {
    isCollapsed.value = true
  }
}

const handleCollapseChange = (collapsed) => {
  isCollapsed.value = collapsed
}

const handleMenuSelect = (menuInfo) => {
  console.log('菜单选择:', menuInfo)
  if (isMobile.value) {
    isCollapsed.value = true
  }
}

const toggleFullscreen = () => {
  if (isFullscreen.value) {
    document.exitFullscreen?.()
  } else {
    document.documentElement.requestFullscreen?.()
  }
}

const showNotifications = () => {
  router.push('/admin/system/notifications')
}

const goToProfile = () => {
  router.push('/admin/users/profile')
}

const goToSettings = () => {
  router.push('/admin/system/settings')
}

const logout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 清除用户数据 - 这里可以添加实际的退出逻辑
    // TODO: 添加实际的用户登出处理
    localStorage.removeItem('token')
    sessionStorage.clear()
    router.push('/login')
  }).catch(() => {
    // 取消退出
  })
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 监听窗口大小变化
const handleResize = () => {
  const width = window.innerWidth
  isMobile.value = width < 768
  
  if (isMobile.value) {
    isCollapsed.value = true
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  window.addEventListener('resize', handleResize)
  handleResize() // 初始化检查
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.modern-layout {
  display: flex;
  height: 100vh;
  background: #f5f7fa;
  
  .sidebar-container {
    position: relative;
    width: 260px;
    transition: width 0.3s ease;
    z-index: 1001;
    
    &.is-collapsed {
      width: 80px;
    }
    
    @media (max-width: 768px) {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      
      &:not(.is-collapsed) {
        transform: translateX(0);
      }
    }
  }
  
  .main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    transition: margin-left 0.3s ease;
    
    @media (max-width: 768px) {
      margin-left: 0 !important;
    }
  }
}

.main-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 24px;
  background: #ffffff;
  border-bottom: 1px solid #e8eaec;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .collapse-btn {
      background: rgba(0, 0, 0, 0.04);
      border: 1px solid rgba(0, 0, 0, 0.06);
      
      &:hover {
        background: rgba(0, 0, 0, 0.06);
      }
    }
    
    .breadcrumb-container {
      .app-breadcrumb {
        font-size: 14px;
        
        :deep(.el-breadcrumb__item) {
          .el-breadcrumb__inner {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #606266;
            
            &:hover {
              color: var(--el-color-primary);
            }
          }
          
          &:last-child .el-breadcrumb__inner {
            color: var(--el-color-primary);
            font-weight: 500;
          }
        }
        
        .breadcrumb-icon {
          font-size: 14px;
        }
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .header-btn {
      background: rgba(0, 0, 0, 0.04);
      border: 1px solid rgba(0, 0, 0, 0.06);
      
      &:hover {
        background: rgba(0, 0, 0, 0.06);
      }
    }
    
    .user-dropdown {
      cursor: pointer;
      
      .user-avatar {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 6px 12px;
        border-radius: 8px;
        transition: background 0.3s ease;
        
        &:hover {
          background: rgba(0, 0, 0, 0.04);
        }
        
        .user-info {
          text-align: right;
          
          .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            line-height: 1.2;
          }
          
          .user-role {
            font-size: 12px;
            color: #909399;
            line-height: 1.2;
          }
        }
      }
    }
  }
}

.main-content {
  flex: 1;
  overflow: auto;
  
  .content-wrapper {
    min-height: calc(100vh - 60px - 50px); // 减去 header 和 footer 高度
    padding: 24px;
  }
}

.main-footer {
  height: 50px;
  background: #ffffff;
  border-top: 1px solid #e8eaec;
  
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 24px;
    font-size: 12px;
    color: #909399;
  }
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

// 优化的页面切换动画 - 更轻量快速
.page-fade-enter-active {
  transition: opacity 0.15s ease-in-out;
}

.page-fade-leave-active {
  transition: opacity 0.1s ease-in-out;
}

.page-fade-enter-from,
.page-fade-leave-to {
  opacity: 0;
}

// 内容容器优化
.content-wrapper {
  will-change: auto; // 避免不必要的合成层
  contain: layout style;
}

// 响应式设计
@media (max-width: 768px) {
  .main-header {
    padding: 0 16px;
    
    .header-left {
      gap: 12px;
    }
    
    .header-right {
      gap: 12px;
      
      .user-info {
        display: none;
      }
    }
  }
  
  .main-content {
    .content-wrapper {
      padding: 16px;
    }
  }
  
  .main-footer {
    .footer-content {
      padding: 0 16px;
      font-size: 11px;
    }
  }
}
</style>