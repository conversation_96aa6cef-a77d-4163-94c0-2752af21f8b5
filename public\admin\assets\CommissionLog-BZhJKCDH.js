/* empty css             *//* empty css                   *//* empty css                   *//* empty css                 *//* empty css                        *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                  *//* empty css               *//* empty css                        *//* empty css                 *//* empty css                *//* empty css                     *//* empty css                       *//* empty css                  *//* empty css                  */import{l as e,q as a,G as t,C as l,A as s,B as r,F as o,Y as i,r as d,M as n,c as u,o as c,m,E as p,z as g,D as _,K as f,W as v,H as h}from"./vue-vendor-BcnDv-68.js";import{g as y,a as b,s as w,b as C,c as k,d as j,e as V}from"./finance-CGlhYFvA.js";import{a7 as S,X as U,ah as x,a1 as L,V as F,aZ as Q,W as D,a_ as A,a$ as O,aP as M,av as R,aw as T,at as z,aR as P,aS as B,au as E,ac as I,ab as $,az as q,af as N,ax as K,aC as W,ae as G,aF as Z,ag as H,u as X,Z as Y,s as J,a2 as ee,a3 as ae,a4 as te,a5 as le,a6 as se,a0 as re,ai as oe,aj as ie,aG as de,ak as ne,aK as ue,aq as ce}from"./element-plus-C2UshkXo.js";import{e as me}from"./export-C8s0bFWZ.js";import{a as pe,b as ge,f as _e}from"./format-3eU4VJ9V.js";import{P as fe}from"./index-B0-VWyD5.js";import{_ as ve}from"./index-eUTsTR3J.js";import"./chunk-KZPPZA2C-C8HwxGb3.js";import"./index-D4AyIzGN.js";import"./utils-SdQ7DxjY.js";/* empty css                      */import"./echarts-D6CUuNS9.js";const he={class:"modern-commission-management"},ye={class:"page-header"},be={class:"header-content"},we={class:"header-left"},Ce={class:"header-icon"},ke={class:"header-actions"},je={class:"stats-section"},Ve={class:"stats-container"},Se={class:"stat-content"},Ue={class:"stat-value"},xe={class:"stat-label"},Le={key:0,class:"batch-actions"},Fe={class:"batch-buttons"},Qe={class:"card-header"},De={class:"header-left"},Ae={class:"header-right"},Oe={class:"commission-info"},Me={class:"commission-details"},Re={class:"order-no"},Te={class:"order-amount"},ze={class:"commission-id"},Pe={class:"user-info"},Be={class:"user-details"},Ee={class:"user-name"},Ie={class:"user-email"},$e={class:"user-level"},qe={class:"commission-detail"},Ne={class:"commission-rate"},Ke={class:"commission-amount"},We={class:"time-info"},Ge={class:"time-detail"},Ze={class:"dialog-footer"};const He=ve({__name:"CommissionLog",setup(e,{expose:a}){a();const t=d([]),l=d(0),s=d(!0),r=d(!1),o=d(!1),i=d([]),m=d(!0),p=d("table"),g=n({page:1,limit:15,order_no:"",user_name:"",status:"",date_range:[]}),_=d({}),f=d(!1),v=d(null),h=n({order_id:"",user_id:"",commission_rate:0,commission_amount:0,remark:""}),S=d([]),x=d([]),L=d([{key:"total",label:"总佣金",value:"¥0",icon:"Medal",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"ArrowUp",change:"+12%"},{key:"paid",label:"已发放",value:"¥0",icon:"Check",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"ArrowUp",change:"+8%"},{key:"pending",label:"待发放",value:"¥0",icon:"Timer",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"ArrowUp",change:"+15%"},{key:"count",label:"总笔数",value:"0",icon:"DocumentCopy",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"ArrowUp",change:"+89"}]),F=u(()=>i.value.filter(e=>2===e.status).length);c(()=>{Q(),D()});const Q=async()=>{s.value=!0;try{const e={...g};e.date_range&&2===e.date_range.length&&(e.start_date=e.date_range[0],e.end_date=e.date_range[1]),delete e.date_range;const a=await y(e);t.value=a.data.data,l.value=a.data.total}catch(e){U.error("获取佣金列表失败")}finally{s.value=!1}},D=async()=>{m.value=!0;try{const e=await b();_.value=e.data.basic,L.value[0].value="¥"+pe(_.value.total_amount||0),L.value[1].value="¥"+pe(_.value.paid_amount||0),L.value[2].value="¥"+pe(_.value.pending_amount||0),L.value[3].value=(_.value.total_count||0).toString()}catch(e){U.error("获取统计数据失败")}finally{m.value=!1}},X=()=>{v.value&&v.value.resetFields(),Object.assign(h,{order_id:"",user_id:"",commission_rate:0,commission_amount:0,remark:""})},Y={list:t,total:l,listLoading:s,exportLoading:r,addLoading:o,multipleSelection:i,statsLoading:m,viewMode:p,listQuery:g,commissionStats:_,addDialogVisible:f,addFormRef:v,addForm:h,addFormRules:{order_id:[{required:!0,message:"请选择订单",trigger:"change"}],user_id:[{required:!0,message:"请选择用户",trigger:"change"}],commission_rate:[{required:!0,message:"请输入佣金比例",trigger:"blur"}],commission_amount:[{required:!0,message:"请输入佣金金额",trigger:"blur"}]},orderOptions:S,userOptions:x,commissionStatCards:L,pendingCount:F,fetchList:Q,fetchStats:D,handleQuery:()=>{g.page=1,Q()},resetQuery:()=>{Object.assign(g,{page:1,limit:15,order_no:"",user_name:"",status:"",date_range:[]}),Q()},handleSelectionChange:e=>{const a=Array.isArray(e)?e:[];i.value=a},handleSettle:e=>{H.confirm(`确定要结算这笔佣金吗 (ID: ${e.id})?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await V(e.id),U.success("结算成功"),Q(),D()}catch(a){U.error("结算失败")}})},handleBatchSettle:()=>{const e=i.value.filter(e=>2===e.status);if(0===e.length)return void U.warning("请选择待结算的佣金记录");const a=e.map(e=>e.id);H.confirm(`确定要结算选中的 ${a.length} 笔佣金吗?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await j({ids:a}),U.success("批量结算成功"),Q(),D()}catch(e){U.error("批量结算失败")}})},handleAdd:()=>{X(),f.value=!0},submitAdd:()=>{v.value.validate(async e=>{if(e){o.value=!0;try{await k(h),U.success("添加成功"),f.value=!1,Q()}catch(a){U.error("添加失败")}finally{o.value=!1}}})},resetAddForm:X,remoteSearchOrders:async e=>{if(e)try{const a=await C({keyword:e});S.value=a.data}catch(a){}},remoteSearchUsers:async e=>{if(e)try{const a=await w({keyword:e});x.value=a.data}catch(a){}},handleExportCommissions:async()=>{r.value=!0;try{const e={...g,format:"excel",fields:["id","order_no","order_amount","commission_rate","commission_amount","user_name","status","created_at"]},a=await me(e),t=window.URL.createObjectURL(new Blob([a.data])),l=document.createElement("a");l.href=t,l.download=`佣金明细_${(new Date).toLocaleDateString()}.xlsx`,document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(t),U.success("导出成功")}catch(e){U.error("导出失败")}finally{r.value=!1}},viewDetail:e=>{U.info(`查看佣金详情：${e.id}`)},editCommission:e=>{U.info(`编辑佣金：${e.id}`)},getStatusName:e=>({1:"已结算",2:"待结算",3:"已取消"}[e]||"未知"),getStatusTagType:e=>({1:"success",2:"warning",3:"info"}[e]||"info"),getUserLevel:e=>({1:"初级分销商",2:"中级分销商",3:"高级分销商",4:"金牌分销商"}[e]||"普通用户"),ref:d,reactive:n,onMounted:c,computed:u,get getCommissionList(){return y},get getCommissionStats(){return b},get settleCommission(){return V},get batchSettleCommissions(){return j},get addCommission(){return k},get searchOrders(){return C},get searchUsers(){return w},get ElMessage(){return U},get ElMessageBox(){return H},get Search(){return Z},get Refresh(){return G},get Check(){return W},get Download(){return K},get Plus(){return N},get RefreshLeft(){return q},get Edit(){return $},get View(){return I},get UserFilled(){return E},get CreditCard(){return B},get Wallet(){return P},get ArrowUp(){return z},get List(){return T},get Grid(){return R},get Medal(){return M},get Timer(){return O},get DocumentCopy(){return A},get exportCommissions(){return me},get formatDate(){return _e},get formatTime(){return ge},get formatNumber(){return pe},Pagination:fe};return Object.defineProperty(Y,"__isScriptSetup",{enumerable:!1,value:!0}),Y}},[["render",function(d,n,u,c,y,b){const w=x,C=L,k=te,j=ae,V=se,U=le,A=re,O=ee,M=F,R=Q,T=ne,z=ue,P=ie,B=de,E=oe,I=ce,$=D,q=S;return m(),e("div",he,[a("div",ye,[a("div",be,[a("div",we,[a("div",Ce,[t(w,{size:"24"},{default:s(()=>[t(c.CreditCard)]),_:1})]),n[15]||(n[15]=a("div",{class:"header-text"},[a("h1",null,"代理商佣金管理"),a("p",null,"管理代理商佣金结算，跟踪佣金发放状态，统计佣金数据")],-1))]),a("div",ke,[t(C,{onClick:c.handleExportCommissions,loading:c.exportLoading,class:"action-btn secondary"},{default:s(()=>[t(w,null,{default:s(()=>[t(c.Download)]),_:1}),n[16]||(n[16]=p(" 导出数据 ",-1))]),_:1,__:[16]},8,["loading"]),t(C,{type:"primary",onClick:c.handleAdd,class:"action-btn primary"},{default:s(()=>[t(w,null,{default:s(()=>[t(c.Plus)]),_:1}),n[17]||(n[17]=p(" 手动添加佣金 ",-1))]),_:1,__:[17]})])])]),a("div",je,[r((m(),e("div",Ve,[(m(!0),e(o,null,i(c.commissionStatCards,l=>(m(),e("div",{class:"stat-card",key:l.key},[a("div",{class:"stat-icon",style:X({background:l.color})},[t(w,{size:"20"},{default:s(()=>[(m(),g(_(l.icon)))]),_:2},1024)],4),a("div",Se,[a("div",Ue,Y(l.value),1),a("div",xe,Y(l.label),1)]),a("div",{class:J(["stat-trend",l.trend])},[t(w,{size:"14"},{default:s(()=>[(m(),g(_(l.trendIcon)))]),_:2},1024),a("span",null,Y(l.change),1)],2)]))),128))])),[[q,c.statsLoading]])]),t(M,{class:"filter-card"},{default:s(()=>[t(O,{inline:!0,model:c.listQuery,onSubmit:f(c.handleQuery,["prevent"])},{default:s(()=>[t(j,{label:"订单号"},{default:s(()=>[t(k,{modelValue:c.listQuery.order_no,"onUpdate:modelValue":n[0]||(n[0]=e=>c.listQuery.order_no=e),placeholder:"请输入订单号",clearable:"",onKeyup:v(c.handleQuery,["enter"]),class:"search-input"},null,8,["modelValue"])]),_:1}),t(j,{label:"受益人"},{default:s(()=>[t(k,{modelValue:c.listQuery.user_name,"onUpdate:modelValue":n[1]||(n[1]=e=>c.listQuery.user_name=e),placeholder:"请输入用户名或昵称",clearable:"",onKeyup:v(c.handleQuery,["enter"]),class:"search-input"},null,8,["modelValue"])]),_:1}),t(j,{label:"状态"},{default:s(()=>[t(U,{modelValue:c.listQuery.status,"onUpdate:modelValue":n[2]||(n[2]=e=>c.listQuery.status=e),placeholder:"全部状态",clearable:"",class:"filter-select"},{default:s(()=>[t(V,{label:"全部",value:""}),t(V,{label:"已结算",value:1}),t(V,{label:"待结算",value:2}),t(V,{label:"已取消",value:3})]),_:1},8,["modelValue"])]),_:1}),t(j,{label:"时间范围"},{default:s(()=>[t(A,{modelValue:c.listQuery.date_range,"onUpdate:modelValue":n[3]||(n[3]=e=>c.listQuery.date_range=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",class:"date-picker"},null,8,["modelValue"])]),_:1}),t(j,null,{default:s(()=>[t(C,{type:"primary",onClick:c.handleQuery,class:"search-btn"},{default:s(()=>[t(w,null,{default:s(()=>[t(c.Search)]),_:1}),n[18]||(n[18]=p(" 查询 ",-1))]),_:1,__:[18]}),t(C,{onClick:c.resetQuery,class:"reset-btn"},{default:s(()=>[t(w,null,{default:s(()=>[t(c.RefreshLeft)]),_:1}),n[19]||(n[19]=p(" 重置 ",-1))]),_:1,__:[19]})]),_:1})]),_:1},8,["model"])]),_:1}),c.multipleSelection.length>0?(m(),e("div",Le,[t(R,{type:"info",closable:!1,class:"selection-alert"},{title:s(()=>[p(" 已选择 "+Y(c.multipleSelection.length)+" 个佣金记录，其中 "+Y(c.pendingCount)+" 个待结算 ",1)]),_:1}),a("div",Fe,[t(C,{type:"success",onClick:c.handleBatchSettle,disabled:0===c.pendingCount},{default:s(()=>[t(w,null,{default:s(()=>[t(c.Check)]),_:1}),p(" 批量结算 ("+Y(c.pendingCount)+") ",1)]),_:1},8,["disabled"])])])):l("",!0),t(M,{class:"table-card"},{header:s(()=>[a("div",Qe,[a("div",De,[n[20]||(n[20]=a("h3",null,"佣金记录",-1)),t(T,{size:"small",type:"info"},{default:s(()=>[p("共 "+Y(c.total)+" 条记录",1)]),_:1})]),a("div",Ae,[t(z,null,{default:s(()=>[t(C,{size:"small",type:"table"===c.viewMode?"primary":"",onClick:n[4]||(n[4]=e=>c.viewMode="table")},{default:s(()=>[t(w,null,{default:s(()=>[t(c.List)]),_:1})]),_:1},8,["type"]),t(C,{size:"small",type:"card"===c.viewMode?"primary":"",onClick:n[5]||(n[5]=e=>c.viewMode="card")},{default:s(()=>[t(w,null,{default:s(()=>[t(c.Grid)]),_:1})]),_:1},8,["type"])]),_:1})])])]),default:s(()=>["table"===c.viewMode?r((m(),g(E,{key:0,data:c.list,onSelectionChange:c.handleSelectionChange,class:"modern-table"},{default:s(()=>[t(P,{type:"selection",width:"55",align:"center"}),t(P,{label:"ID",prop:"id",align:"center",width:"80"}),t(P,{label:"订单信息",width:"220"},{default:s(({row:e})=>[a("div",Oe,[t(B,{size:40,class:"commission-avatar"},{default:s(()=>[t(w,null,{default:s(()=>[t(c.Wallet)]),_:1})]),_:1}),a("div",Me,[a("div",Re,Y(e.order?.order_no||"-"),1),a("div",Te,"订单金额: ¥"+Y(c.formatNumber(e.order_amount)),1),a("div",ze,"ID: "+Y(e.id),1)])])]),_:1}),t(P,{label:"受益人信息",width:"220"},{default:s(({row:e})=>[a("div",Pe,[t(B,{size:40,class:"user-avatar"},{default:s(()=>[t(w,null,{default:s(()=>[t(c.UserFilled)]),_:1})]),_:1}),a("div",Be,[a("div",Ee,Y(e.user?.name||"-"),1),a("div",Ie,Y(e.user?.email||"-"),1),a("div",$e,Y(c.getUserLevel(e.user?.level)),1)])])]),_:1}),t(P,{label:"佣金详情",align:"center"},{default:s(({row:e})=>[a("div",qe,[a("div",Ne,"比例: "+Y(e.commission_rate)+"%",1),a("div",Ke,"¥"+Y(c.formatNumber(e.commission_amount)),1)])]),_:1}),t(P,{label:"状态",align:"center"},{default:s(({row:e})=>[t(T,{type:c.getStatusTagType(e.status),size:"large"},{default:s(()=>[p(Y(c.getStatusName(e.status)),1)]),_:2},1032,["type"])]),_:1}),t(P,{label:"备注",prop:"remark","min-width":"120","show-overflow-tooltip":""}),t(P,{label:"创建时间",width:"160",align:"center"},{default:s(({row:e})=>[a("div",We,[a("div",null,Y(c.formatDate(e.created_at)),1),a("div",Ge,Y(c.formatTime(e.created_at)),1)])]),_:1}),t(P,{label:"操作",width:"220",fixed:"right"},{default:s(({row:e})=>[t(C,{link:"",type:"primary",onClick:a=>c.viewDetail(e)},{default:s(()=>[t(w,null,{default:s(()=>[t(c.View)]),_:1}),n[21]||(n[21]=p(" 详情 ",-1))]),_:2,__:[21]},1032,["onClick"]),2===e.status?(m(),g(C,{key:0,link:"",type:"success",onClick:a=>c.handleSettle(e)},{default:s(()=>[t(w,null,{default:s(()=>[t(c.Check)]),_:1}),n[22]||(n[22]=p(" 结算 ",-1))]),_:2,__:[22]},1032,["onClick"])):l("",!0),t(C,{link:"",type:"warning",onClick:a=>c.editCommission(e)},{default:s(()=>[t(w,null,{default:s(()=>[t(c.Edit)]),_:1}),n[23]||(n[23]=p(" 编辑 ",-1))]),_:2,__:[23]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[q,c.listLoading]]):l("",!0),r(t(c.Pagination,{total:c.total,page:c.listQuery.page,"onUpdate:page":n[6]||(n[6]=e=>c.listQuery.page=e),limit:c.listQuery.limit,"onUpdate:limit":n[7]||(n[7]=e=>c.listQuery.limit=e),onPagination:c.fetchList},null,8,["total","page","limit"]),[[h,c.total>0]])]),_:1}),t($,{title:"添加佣金记录",modelValue:c.addDialogVisible,"onUpdate:modelValue":n[14]||(n[14]=e=>c.addDialogVisible=e),width:"600px"},{footer:s(()=>[a("div",Ze,[t(C,{onClick:n[13]||(n[13]=e=>c.addDialogVisible=!1)},{default:s(()=>n[25]||(n[25]=[p("取消",-1)])),_:1,__:[25]}),t(C,{type:"primary",onClick:c.submitAdd,loading:c.addLoading},{default:s(()=>n[26]||(n[26]=[p("确定",-1)])),_:1,__:[26]},8,["loading"])])]),default:s(()=>[t(O,{model:c.addForm,rules:c.addFormRules,ref:"addFormRef","label-width":"120px"},{default:s(()=>[t(j,{label:"订单号",prop:"order_id"},{default:s(()=>[t(U,{modelValue:c.addForm.order_id,"onUpdate:modelValue":n[8]||(n[8]=e=>c.addForm.order_id=e),placeholder:"请选择订单",filterable:"",remote:"","remote-method":c.remoteSearchOrders,style:{width:"100%"}},{default:s(()=>[(m(!0),e(o,null,i(c.orderOptions,e=>(m(),g(V,{key:e.id,label:e.order_no,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(j,{label:"受益用户",prop:"user_id"},{default:s(()=>[t(U,{modelValue:c.addForm.user_id,"onUpdate:modelValue":n[9]||(n[9]=e=>c.addForm.user_id=e),placeholder:"请选择用户",filterable:"",remote:"","remote-method":c.remoteSearchUsers,style:{width:"100%"}},{default:s(()=>[(m(!0),e(o,null,i(c.userOptions,e=>(m(),g(V,{key:e.id,label:`${e.name} (${e.username})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(j,{label:"佣金比例",prop:"commission_rate"},{default:s(()=>[t(I,{modelValue:c.addForm.commission_rate,"onUpdate:modelValue":n[10]||(n[10]=e=>c.addForm.commission_rate=e),min:0,max:100,precision:2,style:{width:"100%"}},null,8,["modelValue"]),n[24]||(n[24]=a("span",{style:{"margin-left":"8px",color:"#909399"}},"%",-1))]),_:1,__:[24]}),t(j,{label:"佣金金额",prop:"commission_amount"},{default:s(()=>[t(I,{modelValue:c.addForm.commission_amount,"onUpdate:modelValue":n[11]||(n[11]=e=>c.addForm.commission_amount=e),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(j,{label:"备注"},{default:s(()=>[t(k,{modelValue:c.addForm.remark,"onUpdate:modelValue":n[12]||(n[12]=e=>c.addForm.remark=e),type:"textarea",placeholder:"请输入备注",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-35c2e2d9"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/finance/CommissionLog.vue"]]);export{He as default};
