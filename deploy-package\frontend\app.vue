<template>
  <div id="app">
    <!-- 全局loading -->
    <div 
      v-if="pending" 
      class="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50"
    >
      <div class="spinner text-primary-600"></div>
    </div>
    
    <!-- 页面内容 -->
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    
    <!-- 全局提示 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>
  </div>
</template>

<script setup>
// 应用初始化
const pending = ref(false)

// 初始化应用
onMounted(async () => {
  pending.value = true
  try {
    // 检查用户登录状态
    await nextTick()
    console.log('应用初始化完成')
  } catch (error) {
    console.error('应用初始化失败:', error)
  } finally {
    pending.value = false
  }
})

// 页面标题和SEO
useHead({
  title: '晨鑫流量变现系统 - 智能社群营销平台',
  meta: [
    { name: 'description', content: '新一代智能社群营销与多级分销平台' },
    { name: 'keywords', content: '微信群,社群营销,分销系统,推广平台,防红系统' },
    { name: 'author', content: '晨鑫流量变现系统 Team' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { property: 'og:title', content: '晨鑫流量变现系统 - 智能社群营销平台' },
    { property: 'og:description', content: '新一代智能社群营销与多级分销平台' },
    { property: 'og:type', content: 'website' },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '晨鑫流量变现系统 - 智能社群营销平台' },
    { name: 'twitter:description', content: '新一代智能社群营销与多级分销平台' },
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
    { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
    { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap' },
  ]
})

// 全局错误处理
const handleError = (error) => {
  console.error('全局错误:', error)
}

// 监听全局错误
onMounted(() => {
  window.addEventListener('error', handleError)
  window.addEventListener('unhandledrejection', handleError)
})

onUnmounted(() => {
  window.removeEventListener('error', handleError)
  window.removeEventListener('unhandledrejection', handleError)
})
</script>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  background-color: #f9fafb;
}

/* 全局链接样式 */
a {
  text-decoration: none;
  color: inherit;
}

/* 全局按钮样式 */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

/* 全局输入框样式 */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

/* 全局列表样式 */
ul, ol {
  list-style: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 响应式图片 */
img {
  max-width: 100%;
  height: auto;
}

/* 无障碍访问 */
:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 全局动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* 媒体查询 */
@media (max-width: 768px) {
  body {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  body {
    font-size: 12px;
  }
}
</style> 