<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <!-- 设置菜单 -->
        <el-card>
          <template #header>
            <h3>系统设置</h3>
          </template>
          <el-menu
            v-model="activeMenu"
            class="settings-menu"
            @select="handleMenuSelect"
          >
            <el-menu-item index="basic">
              <el-icon><Setting /></el-icon>
              <span>基础设置</span>
            </el-menu-item>

            <el-menu-item index="security">
              <el-icon><Lock /></el-icon>
              <span>安全设置</span>
            </el-menu-item>
            <el-sub-menu index="integrations">
               <template #title>
                <el-icon><Connection /></el-icon>
                <span>集成设置</span>
              </template>
              <el-menu-item index="payment">支付快速配置</el-menu-item>
              <el-menu-item index="notification">通知设置</el-menu-item>
              <el-menu-item index="storage">存储设置</el-menu-item>
            </el-sub-menu>
             <el-sub-menu index="modules">
               <template #title>
                <el-icon><Grid /></el-icon>
                <span>模块设置</span>
              </template>
              <el-menu-item index="community_settings">社群设置</el-menu-item>
            </el-sub-menu>
            <el-menu-item index="audit_logs">
              <el-icon><Document /></el-icon>
              <span>系统日志</span>
            </el-menu-item>
            <el-menu-item index="backup">
              <el-icon><DataLine /></el-icon>
              <span>备份与恢复</span>
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-col>
      
      <el-col :span="18">
        <!-- 基础设置 -->
        <el-card v-show="activeMenu === 'basic'">
          <template #header>
            <h3>基础设置</h3>
          </template>
          <el-form
            ref="basicFormRef"
            :model="basicForm"
            :rules="basicRules"
            label-width="120px"
          >
            <el-form-item label="网站名称" prop="site_name">
              <el-input v-model="basicForm.site_name" placeholder="请输入网站名称" />
            </el-form-item>
            <el-form-item label="网站描述" prop="site_description">
              <el-input
                v-model="basicForm.site_description"
                type="textarea"
                :rows="3"
                placeholder="请输入网站描述"
              />
            </el-form-item>
            <el-form-item label="网站关键词" prop="site_keywords">
              <el-input v-model="basicForm.site_keywords" placeholder="请输入网站关键词，用逗号分隔" />
            </el-form-item>
            <el-form-item label="网站Logo" prop="site_logo">
              <ImageUpload v-model="basicForm.site_logo" :limit="1" />
            </el-form-item>
            <el-form-item label="网站图标" prop="site_favicon">
              <ImageUpload v-model="basicForm.site_favicon" :limit="1" />
            </el-form-item>
            <el-form-item label="联系邮箱" prop="contact_email">
              <el-input v-model="basicForm.contact_email" placeholder="请输入联系邮箱" />
            </el-form-item>
            <el-form-item label="联系电话" prop="contact_phone">
              <el-input v-model="basicForm.contact_phone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="备案号" prop="icp_number">
              <el-input v-model="basicForm.icp_number" placeholder="请输入备案号" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveBasicSettings">保存设置</el-button>
              <el-button @click="resetBasicForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 支付快速配置 -->
        <el-card v-show="activeMenu === 'payment'">
          <template #header>
            <h3>支付快速配置</h3>
            <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">快速启用和配置基本支付功能，详细配置请前往 <el-link type="primary" href="/payment" target="_blank">支付管理</el-link></p>
          </template>
          <el-form
            ref="paymentFormRef"
            :model="paymentForm"
            label-width="120px"
          >
            <el-divider content-position="left">微信支付</el-divider>
            <el-form-item label="启用微信支付">
              <el-switch v-model="paymentForm.wechat_enabled" />
            </el-form-item>
            <template v-if="paymentForm.wechat_enabled">
              <el-form-item label="应用ID" prop="wechat_app_id">
                <el-input v-model="paymentForm.wechat_app_id" placeholder="请输入微信应用ID" />
              </el-form-item>
              <el-form-item label="商户号" prop="wechat_mch_id">
                <el-input v-model="paymentForm.wechat_mch_id" placeholder="请输入微信商户号" />
              </el-form-item>
              <el-form-item label="API密钥" prop="wechat_key">
                <el-input
                  v-model="paymentForm.wechat_key"
                  type="password"
                  placeholder="请输入微信API密钥"
                  show-password
                />
              </el-form-item>
            </template>

            <el-divider content-position="left">支付宝支付</el-divider>
            <el-form-item label="启用支付宝">
              <el-switch v-model="paymentForm.alipay_enabled" />
            </el-form-item>
            <template v-if="paymentForm.alipay_enabled">
              <el-form-item label="应用ID" prop="alipay_app_id">
                <el-input v-model="paymentForm.alipay_app_id" placeholder="请输入支付宝应用ID" />
              </el-form-item>
              <el-form-item label="应用私钥" prop="alipay_private_key">
                <el-input
                  v-model="paymentForm.alipay_private_key"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入支付宝应用私钥"
                />
              </el-form-item>
              <el-form-item label="支付宝公钥" prop="alipay_public_key">
                <el-input
                  v-model="paymentForm.alipay_public_key"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入支付宝公钥"
                />
              </el-form-item>
            </template>

            <el-form-item>
              <el-button type="primary" @click="savePaymentSettings">保存设置</el-button>
              <el-button @click="testPayment">测试支付</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 通知设置 -->
        <el-card v-show="activeMenu === 'notification'">
          <template #header>
            <div class="card-header">
              <h3>通知配置中心</h3>
              <div class="header-actions">
                <el-button type="info" size="small" @click="openTemplateManager">
                  <el-icon><Document /></el-icon>
                  模板管理
                </el-button>
                <el-button type="primary" size="small" @click="openAdvancedNotificationSettings">
                  <el-icon><Setting /></el-icon>
                  高级配置
                </el-button>
              </div>
            </div>
          </template>

          <el-tabs v-model="notificationActiveTab" type="card">
            <!-- 渠道配置 -->
            <el-tab-pane label="通知渠道" name="channels">
              <!-- 配置概览 -->
              <el-alert 
                title="配置提醒" 
                type="warning" 
                :closable="false"
                style="margin-bottom: 20px;"
                v-if="!hasAnyServiceConfigured"
              >
                <template #default>
                  检测到您还未配置任何通知服务，请先点击右侧"配置"按钮完成基础服务配置，才能正常发送通知。
                </template>
              </el-alert>

              <el-alert 
                title="✨ 配置完成" 
                type="success" 
                :closable="false"
                style="margin-bottom: 20px;"
                v-if="hasAnyServiceConfigured"
              >
                <template #default>
                  通知服务配置完成！您现在可以正常发送通知了。建议测试各个渠道的连通性，确保服务正常运行。
                </template>
              </el-alert>

              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="channel-config-section">
                    <h4>通知渠道开关</h4>
                    <div class="channel-list">
                      <div class="channel-item">
                        <div class="channel-info">
                          <el-icon><Message /></el-icon>
                          <div class="channel-details">
                            <span class="channel-name">邮件通知</span>
                            <span class="channel-desc">通过SMTP发送邮件通知</span>
                          </div>
                        </div>
                        <el-switch 
                          v-model="notificationForm.email_enabled"
                          @change="handleChannelChange('email', $event)"
                        />
                      </div>

                      <div class="channel-item">
                        <div class="channel-info">
                          <el-icon><ChatDotRound /></el-icon>
                          <div class="channel-details">
                            <span class="channel-name">短信通知</span>
                            <span class="channel-desc">通过短信平台发送通知</span>
                          </div>
                        </div>
                        <el-switch 
                          v-model="notificationForm.sms_enabled"
                          @change="handleChannelChange('sms', $event)"
                        />
                      </div>

                      <div class="channel-item">
                        <div class="channel-info">
                          <el-icon><ChatSquare /></el-icon>
                          <div class="channel-details">
                            <span class="channel-name">微信通知</span>
                            <span class="channel-desc">通过企业微信发送通知</span>
                          </div>
                        </div>
                        <el-switch 
                          v-model="notificationForm.wechat_enabled"
                          @change="handleChannelChange('wechat', $event)"
                        />
                      </div>

                      <div class="channel-item">
                        <div class="channel-info">
                          <el-icon><Bell /></el-icon>
                          <div class="channel-details">
                            <span class="channel-name">系统通知</span>
                            <span class="channel-desc">站内系统消息通知</span>
                          </div>
                        </div>
                        <el-switch 
                          v-model="notificationForm.system_enabled"
                          @change="handleChannelChange('system', $event)"
                        />
                      </div>
                    </div>
                  </div>
                </el-col>

                <el-col :span="12">
                  <div class="channel-config-section">
                    <h4>服务配置 & 状态</h4>
                    <div class="channel-status-list">
                      <el-alert
                        v-if="!notificationForm.email_enabled && !notificationForm.sms_enabled && !notificationForm.wechat_enabled"
                        title="警告"
                        type="warning"
                        description="所有外部通知渠道均已禁用，用户将无法收到重要通知"
                        show-icon
                        :closable="false"
                      />
                      
                      <!-- 邮件服务配置 -->
                      <div class="status-item">
                        <div class="service-header">
                          <span>邮件服务：</span>
                          <el-tag :type="notificationChannelStatus.email ? 'success' : 'danger'" size="small">
                            {{ notificationChannelStatus.email ? '已配置' : '未配置' }}
                          </el-tag>
                          <el-button type="text" size="small" @click="showEmailConfigDialog = true">
                            <el-icon><Setting /></el-icon>
                            配置
                          </el-button>
                          <el-button v-if="notificationForm.email_enabled" type="text" size="small" @click="testEmailNotification">
                            <el-icon><Position /></el-icon>
                            测试
                          </el-button>
                        </div>
                      </div>

                      <!-- 短信服务配置 -->
                      <div class="status-item">
                        <div class="service-header">
                          <span>短信服务：</span>
                          <el-tag :type="notificationChannelStatus.sms ? 'success' : 'danger'" size="small">
                            {{ notificationChannelStatus.sms ? '已配置' : '未配置' }}
                          </el-tag>
                          <el-button type="text" size="small" @click="showSmsConfigDialog = true">
                            <el-icon><Setting /></el-icon>
                            配置
                          </el-button>
                          <el-button v-if="notificationForm.sms_enabled" type="text" size="small" @click="testSmsNotification">
                            <el-icon><Position /></el-icon>
                            测试
                          </el-button>
                        </div>
                      </div>

                      <!-- 微信服务配置 -->
                      <div class="status-item">
                        <div class="service-header">
                          <span>微信服务：</span>
                          <el-tag :type="notificationChannelStatus.wechat ? 'success' : 'danger'" size="small">
                            {{ notificationChannelStatus.wechat ? '已配置' : '未配置' }}
                          </el-tag>
                          <el-button type="text" size="small" @click="showWechatConfigDialog = true">
                            <el-icon><Setting /></el-icon>
                            配置
                          </el-button>
                          <el-button v-if="notificationForm.wechat_enabled" type="text" size="small" @click="testWechatNotification">
                            <el-icon><Position /></el-icon>
                            测试
                          </el-button>
                        </div>
                      </div>

                      <!-- 系统服务 -->
                      <div class="status-item">
                        <div class="service-header">
                          <span>系统服务：</span>
                          <el-tag type="success" size="small">正常</el-tag>
                          <span style="margin-left: 8px; color: #909399; font-size: 12px;">无需配置</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 事件配置 -->
            <el-tab-pane label="事件规则" name="events">
              <div class="events-config">
                <div class="events-header">
                  <h4>通知事件配置</h4>
                  <el-button type="primary" size="small" @click="showAddEventDialog = true">
                    <el-icon><Plus /></el-icon>
                    添加事件
                  </el-button>
                  <el-button type="info" size="small" @click="showRulesConfigDialog = true">
                    <el-icon><Setting /></el-icon>
                    智能规则
                  </el-button>
                </div>

                <el-table :data="notificationEvents" style="width: 100%">
                  <el-table-column prop="name" label="事件名称" width="180" />
                  <el-table-column prop="description" label="描述" />
                  <el-table-column label="通知渠道" width="200">
                    <template #default="scope">
                      <el-checkbox-group v-model="scope.row.channels" @change="updateEventChannels(scope.row.id, $event)">
                        <el-checkbox label="email" :disabled="!notificationForm.email_enabled">邮件</el-checkbox>
                        <el-checkbox label="sms" :disabled="!notificationForm.sms_enabled">短信</el-checkbox>
                        <el-checkbox label="wechat" :disabled="!notificationForm.wechat_enabled">微信</el-checkbox>
                        <el-checkbox label="system">系统</el-checkbox>
                      </el-checkbox-group>
                    </template>
                  </el-table-column>
                  <el-table-column prop="enabled" label="启用" width="80">
                    <template #default="scope">
                      <el-switch 
                        v-model="scope.row.enabled"
                        @change="updateEventStatus(scope.row.id, $event)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button size="small" @click="editNotificationEvent(scope.row)">编辑</el-button>
                      <el-button size="small" type="danger" @click="deleteNotificationEvent(scope.row.id)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <!-- 全局设置 -->
            <el-tab-pane label="全局设置" name="global">
              <!-- 服务配置摘要 -->
              <el-card class="config-summary" style="margin-bottom: 20px;">
                <template #header>
                  <div class="card-header">
                    <h4 style="margin: 0;">📊 当前配置摘要</h4>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-statistic title="已配置服务" :value="configuredServicesCount" suffix="个">
                      <template #prefix>
                        <el-icon style="vertical-align: -0.125em"><Setting /></el-icon>
                      </template>
                    </el-statistic>
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="活跃通知事件" :value="activeEventsCount" suffix="个">
                      <template #prefix>
                        <el-icon style="vertical-align: -0.125em"><Bell /></el-icon>
                      </template>
                    </el-statistic>
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="智能规则" :value="activeRulesCount" suffix="条">
                      <template #prefix>
                        <el-icon style="vertical-align: -0.125em"><Check /></el-icon>
                      </template>
                    </el-statistic>
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="系统状态" value="运行正常">
                      <template #prefix>
                        <el-icon style="vertical-align: -0.125em; color: #67c23a;"><CircleCheckFilled /></el-icon>
                      </template>
                    </el-statistic>
                  </el-col>
                </el-row>
              </el-card>

              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="global-settings-section">
                    <h4>发送控制</h4>
                    <el-form :model="notificationGlobalSettings" label-width="120px">
                      <el-form-item label="频率限制">
                        <el-switch v-model="notificationGlobalSettings.rate_limit_enabled" />
                        <div v-if="notificationGlobalSettings.rate_limit_enabled" style="margin-top: 8px;">
                          每用户每小时最多 
                          <el-input-number 
                            v-model="notificationGlobalSettings.max_per_hour"
                            :min="1"
                            :max="100"
                            size="small"
                            style="width: 80px; margin: 0 5px;"
                          /> 条通知
                        </div>
                      </el-form-item>

                      <el-form-item label="夜间免打扰">
                        <el-switch v-model="notificationGlobalSettings.night_mode_enabled" />
                        <div v-if="notificationGlobalSettings.night_mode_enabled" style="margin-top: 8px;">
                          <el-time-picker
                            v-model="notificationGlobalSettings.night_start"
                            format="HH:mm"
                            placeholder="开始时间"
                            size="small"
                            style="width: 100px; margin-right: 8px;"
                          />
                          至
                          <el-time-picker
                            v-model="notificationGlobalSettings.night_end"
                            format="HH:mm"
                            placeholder="结束时间"
                            size="small"
                            style="width: 100px; margin-left: 8px;"
                          />
                        </div>
                      </el-form-item>

                      <el-form-item label="失败重试">
                        <el-switch v-model="notificationGlobalSettings.retry_enabled" />
                        <div v-if="notificationGlobalSettings.retry_enabled" style="margin-top: 8px;">
                          失败后最多重试 
                          <el-input-number 
                            v-model="notificationGlobalSettings.max_retries"
                            :min="1"
                            :max="5"
                            size="small"
                            style="width: 60px; margin: 0 5px;"
                          /> 次
                        </div>
                      </el-form-item>
                    </el-form>
                  </div>
                </el-col>

                <el-col :span="12">
                  <div class="global-settings-section">
                    <h4>其他设置</h4>
                    <el-form :model="notificationGlobalSettings" label-width="120px">
                      <el-form-item label="通知日志">
                        <el-switch v-model="notificationGlobalSettings.enable_logging" />
                        <div style="margin-top: 4px; font-size: 12px; color: #909399;">
                          记录所有通知发送状态和结果
                        </div>
                      </el-form-item>

                      <el-form-item label="队列处理">
                        <el-switch v-model="notificationGlobalSettings.queue_enabled" />
                        <div style="margin-top: 4px; font-size: 12px; color: #909399;">
                          使用队列异步处理通知发送
                        </div>
                      </el-form-item>

                      <el-form-item label="模板缓存">
                        <el-switch v-model="notificationGlobalSettings.template_cache" />
                        <div style="margin-top: 4px; font-size: 12px; color: #909399;">
                          缓存通知模板提高发送速度
                        </div>
                      </el-form-item>
                    </el-form>
                  </div>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>

          <div class="notification-actions" style="margin-top: 20px; text-align: center;">
            <el-button type="primary" @click="saveNotificationSettings" :loading="notificationSaving">
              <el-icon><Check /></el-icon>
              保存所有设置
            </el-button>
            <el-button @click="resetNotificationSettings">重置设置</el-button>
            <el-button type="info" @click="exportNotificationConfig">导出配置</el-button>
          </div>
        </el-card>

        <!-- 安全设置 -->
        <el-card v-show="activeMenu === 'security'">
          <template #header>
            <h3>安全设置</h3>
          </template>
          <el-form
            ref="securityFormRef"
            :model="securityForm"
            label-width="120px"
          >
            <el-form-item label="登录验证码">
              <el-switch v-model="securityForm.login_captcha" />
            </el-form-item>
            <el-form-item label="注册验证码">
              <el-switch v-model="securityForm.register_captcha" />
            </el-form-item>
            <el-form-item label="密码强度检查">
              <el-switch v-model="securityForm.password_strength" />
            </el-form-item>
            <el-form-item label="登录失败限制">
              <el-input-number
                v-model="securityForm.login_attempts"
                :min="3"
                :max="10"
                placeholder="登录失败次数限制"
              />
              <span style="margin-left: 10px;">次后锁定账户</span>
            </el-form-item>
            <el-form-item label="会话超时时间">
              <el-input-number
                v-model="securityForm.session_timeout"
                :min="30"
                :max="1440"
                placeholder="会话超时时间"
              />
              <span style="margin-left: 10px;">分钟</span>
            </el-form-item>
            <el-form-item label="IP白名单">
              <el-input
                v-model="securityForm.ip_whitelist"
                type="textarea"
                :rows="3"
                placeholder="请输入IP白名单，每行一个IP或IP段"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveSecuritySettings">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 新增的功能面板 -->
        <AuditLogs v-if="activeMenu === 'audit_logs'" />
        <BackupSettings v-if="activeMenu === 'backup'" />
        <CommunitySettings v-if="activeMenu === 'community_settings'" />
        
        <!-- 通知模板管理对话框 -->
        <el-dialog 
          title="通知模板管理" 
          v-model="templateManagerDialog.visible" 
          width="90%"
          :close-on-click-modal="false"
          top="5vh"
        >
          <NotificationTemplateManager />
        </el-dialog>

        <!-- 智能通知规则配置对话框 -->
        <el-dialog 
          title="智能通知规则配置" 
          v-model="rulesConfigDialog.visible" 
          width="800px"
          :close-on-click-modal="false"
        >
          <div class="rules-config-container">
            <!-- 规则概述 -->
            <el-alert 
              title="智能规则帮助" 
              type="info" 
              :closable="false"
              style="margin-bottom: 20px;"
            >
              <template #default>
                智能规则可以根据条件自动选择通知渠道、发送时间和频率，提升通知的精准性和用户体验。
              </template>
            </el-alert>

            <el-tabs v-model="rulesActiveTab" type="card">
              <!-- 频率控制规则 -->
              <el-tab-pane label="频率控制" name="frequency">
                <div class="rule-section">
                  <h4>发送频率规则</h4>
                  <el-form :model="frequencyRules" label-width="120px">
                    <el-form-item label="同类通知间隔">
                      <el-input-number 
                        v-model="frequencyRules.same_type_interval" 
                        :min="1" 
                        :max="1440"
                        style="width: 120px;"
                      /> 分钟
                      <span style="margin-left: 10px; color: #909399; font-size: 12px;">
                        相同类型通知的最小发送间隔
                      </span>
                    </el-form-item>
                    
                    <el-form-item label="用户每日限额">
                      <el-input-number 
                        v-model="frequencyRules.daily_user_limit" 
                        :min="1" 
                        :max="100"
                        style="width: 120px;"
                      /> 条
                      <span style="margin-left: 10px; color: #909399; font-size: 12px;">
                        每个用户每天最多接收的通知数量
                      </span>
                    </el-form-item>
                    
                    <el-form-item label="高峰期限制">
                      <el-switch v-model="frequencyRules.peak_hour_limit" />
                      <div v-if="frequencyRules.peak_hour_limit" style="margin-top: 10px;">
                        高峰期时间：
                        <el-time-picker 
                          v-model="frequencyRules.peak_start" 
                          format="HH:mm"
                          style="width: 100px; margin: 0 8px;"
                        /> 至 
                        <el-time-picker 
                          v-model="frequencyRules.peak_end" 
                          format="HH:mm"
                          style="width: 100px; margin: 0 8px;"
                        />
                        <br>
                        <span style="margin-top: 5px; display: inline-block;">
                          高峰期降低发送频率至 
                          <el-input-number 
                            v-model="frequencyRules.peak_reduction_rate" 
                            :min="10" 
                            :max="90"
                            :step="10"
                            style="width: 80px; margin: 0 5px;"
                          />%
                        </span>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>

              <!-- 智能渠道选择 -->
              <el-tab-pane label="渠道选择" name="channel">
                <div class="rule-section">
                  <h4>智能渠道选择规则</h4>
                  <el-form :model="channelRules" label-width="120px">
                    <el-form-item label="用户偏好优先">
                      <el-switch v-model="channelRules.user_preference_priority" />
                      <div style="margin-top: 5px; color: #909399; font-size: 12px;">
                        优先使用用户设置的首选通知渠道
                      </div>
                    </el-form-item>
                    
                    <el-form-item label="渠道降级策略">
                      <el-switch v-model="channelRules.fallback_enabled" />
                      <div v-if="channelRules.fallback_enabled" style="margin-top: 10px;">
                        <div>渠道优先级（拖拽排序）：</div>
                        <div class="channel-priority-list">
                          <div 
                            v-for="(channel, index) in channelRules.priority_order" 
                            :key="channel"
                            class="priority-item"
                          >
                            <el-icon><Rank /></el-icon>
                            <span>{{ getChannelName(channel) }}</span>
                            <el-button-group size="small">
                              <el-button 
                                :disabled="index === 0" 
                                @click="moveChannelUp(index)"
                                size="small"
                              >
                                <el-icon><ArrowUp /></el-icon>
                              </el-button>
                              <el-button 
                                :disabled="index === channelRules.priority_order.length - 1"
                                @click="moveChannelDown(index)"
                                size="small"
                              >
                                <el-icon><ArrowDown /></el-icon>
                              </el-button>
                            </el-button-group>
                          </div>
                        </div>
                      </div>
                    </el-form-item>
                    
                    <el-form-item label="紧急消息处理">
                      <el-switch v-model="channelRules.urgent_multi_channel" />
                      <div style="margin-top: 5px; color: #909399; font-size: 12px;">
                        紧急消息同时通过多个渠道发送
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>

              <!-- 时间规则 -->
              <el-tab-pane label="时间规则" name="timing">
                <div class="rule-section">
                  <h4>发送时间智能规则</h4>
                  <el-form :model="timingRules" label-width="120px">
                    <el-form-item label="免打扰模式">
                      <el-switch v-model="timingRules.do_not_disturb" />
                      <div v-if="timingRules.do_not_disturb" style="margin-top: 10px;">
                        免打扰时间：
                        <el-time-picker 
                          v-model="timingRules.dnd_start" 
                          format="HH:mm"
                          style="width: 100px; margin: 0 8px;"
                        /> 至 
                        <el-time-picker 
                          v-model="timingRules.dnd_end" 
                          format="HH:mm"
                          style="width: 100px; margin: 0 8px;"
                        />
                        <div style="margin-top: 8px;">
                          <el-checkbox v-model="timingRules.dnd_except_urgent">
                            紧急消息例外
                          </el-checkbox>
                        </div>
                      </div>
                    </el-form-item>
                    
                    <el-form-item label="最佳发送时间">
                      <el-switch v-model="timingRules.optimal_time" />
                      <div v-if="timingRules.optimal_time" style="margin-top: 10px;">
                        <el-checkbox-group v-model="timingRules.optimal_hours">
                          <el-checkbox label="09">9:00-10:00</el-checkbox>
                          <el-checkbox label="14">14:00-15:00</el-checkbox>
                          <el-checkbox label="19">19:00-20:00</el-checkbox>
                          <el-checkbox label="21">21:00-22:00</el-checkbox>
                        </el-checkbox-group>
                        <div style="margin-top: 8px; color: #909399; font-size: 12px;">
                          非最佳时间的消息将延迟到最近的最佳时间发送
                        </div>
                      </div>
                    </el-form-item>
                    
                    <el-form-item label="节假日处理">
                      <el-switch v-model="timingRules.holiday_handling" />
                      <div v-if="timingRules.holiday_handling" style="margin-top: 10px;">
                        <el-radio-group v-model="timingRules.holiday_strategy">
                          <el-radio label="delay">延迟到工作日</el-radio>
                          <el-radio label="reduce">减少发送频率</el-radio>
                          <el-radio label="normal">正常发送</el-radio>
                        </el-radio-group>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>

              <!-- 内容优化 -->
              <el-tab-pane label="内容优化" name="content">
                <div class="rule-section">
                  <h4>内容智能优化规则</h4>
                  <el-form :model="contentRules" label-width="120px">
                    <el-form-item label="个性化内容">
                      <el-switch v-model="contentRules.personalization" />
                      <div style="margin-top: 5px; color: #909399; font-size: 12px;">
                        根据用户偏好和行为自动调整通知内容
                      </div>
                    </el-form-item>
                    
                    <el-form-item label="多语言支持">
                      <el-switch v-model="contentRules.multi_language" />
                      <div v-if="contentRules.multi_language" style="margin-top: 10px;">
                        <el-checkbox-group v-model="contentRules.supported_languages">
                          <el-checkbox label="zh-CN">简体中文</el-checkbox>
                          <el-checkbox label="zh-TW">繁体中文</el-checkbox>
                          <el-checkbox label="en">English</el-checkbox>
                          <el-checkbox label="ja">日本語</el-checkbox>
                        </el-checkbox-group>
                      </div>
                    </el-form-item>
                    
                    <el-form-item label="内容长度优化">
                      <el-switch v-model="contentRules.length_optimization" />
                      <div v-if="contentRules.length_optimization" style="margin-top: 10px;">
                        短信渠道自动截取前 
                        <el-input-number 
                          v-model="contentRules.sms_max_length" 
                          :min="50" 
                          :max="200"
                          style="width: 80px; margin: 0 5px;"
                        /> 字符
                        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
                          超长内容自动生成摘要或添加"查看详情"链接
                        </div>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="rulesConfigDialog.visible = false">取消</el-button>
              <el-button type="primary" @click="saveRulesConfig" :loading="rulesSaving">
                保存规则配置
              </el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 邮件服务配置对话框 -->
        <el-dialog
          title="邮件服务配置"
          v-model="showEmailConfigDialog"
          width="600px"
          :close-on-click-modal="false"
        >
          <el-form :model="emailConfig" :rules="emailRules" ref="emailFormRef" label-width="120px">
            <el-alert
              title="配置说明"
              type="info"
              :closable="false"
              style="margin-bottom: 20px;"
            >
              <template #default>
                请配置SMTP邮件服务器信息，用于发送系统通知邮件。支持主流邮件服务商如QQ邮箱、163邮箱、Gmail等。
                <br>
                <el-button type="text" @click="showEmailTemplateDialog = true" style="margin-top: 5px;">
                  <el-icon><Document /></el-icon>
                  使用常用邮件模板快速配置
                </el-button>
              </template>
            </el-alert>

            <el-form-item label="SMTP服务器" prop="host">
              <el-input v-model="emailConfig.host" placeholder="例如: smtp.qq.com" />
            </el-form-item>

            <el-form-item label="端口号" prop="port">
              <el-input-number v-model="emailConfig.port" :min="1" :max="65535" style="width: 100%" />
              <div style="margin-top: 5px; color: #909399; font-size: 12px;">
                常用端口：25 (非加密)、465 (SSL)、587 (TLS)
              </div>
            </el-form-item>

            <el-form-item label="加密方式" prop="encryption">
              <el-select v-model="emailConfig.encryption" placeholder="选择加密方式" style="width: 100%">
                <el-option label="无加密" value="" />
                <el-option label="SSL" value="ssl" />
                <el-option label="TLS" value="tls" />
              </el-select>
            </el-form-item>

            <el-form-item label="发件人邮箱" prop="username">
              <el-input v-model="emailConfig.username" placeholder="例如: <EMAIL>" />
            </el-form-item>

            <el-form-item label="邮箱密码" prop="password">
              <el-input
                v-model="emailConfig.password"
                type="password"
                placeholder="邮箱密码或授权码"
                show-password
              />
              <div style="margin-top: 5px; color: #909399; font-size: 12px;">
                QQ邮箱和163邮箱请使用授权码，Gmail需开启应用专用密码
              </div>
            </el-form-item>

            <el-form-item label="发件人名称">
              <el-input v-model="emailConfig.from_name" placeholder="例如: 系统通知" />
            </el-form-item>

            <el-form-item label="测试邮箱">
              <el-input v-model="emailConfig.test_email" placeholder="用于测试的收件邮箱" />
            </el-form-item>
          </el-form>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="showEmailConfigDialog = false">取消</el-button>
              <el-button type="info" @click="testEmailConfig" :loading="emailTesting">测试连接</el-button>
              <el-button type="primary" @click="saveEmailConfig" :loading="emailSaving">保存配置</el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 短信服务配置对话框 -->
        <el-dialog
          title="短信服务配置"
          v-model="showSmsConfigDialog"
          width="600px"
          :close-on-click-modal="false"
        >
          <el-form :model="smsConfig" :rules="smsRules" ref="smsFormRef" label-width="120px">
            <el-alert
              title="服务商选择"
              type="info"
              :closable="false"
              style="margin-bottom: 20px;"
            >
              <template #default>
                支持阿里云短信、腾讯云短信、华为云短信等主流服务商。请先到对应平台申请短信服务并获取API密钥。
              </template>
            </el-alert>

            <el-form-item label="服务商" prop="provider">
              <el-select v-model="smsConfig.provider" placeholder="选择短信服务商" style="width: 100%" @change="handleSmsProviderChange">
                <el-option label="阿里云短信" value="aliyun" />
                <el-option label="腾讯云短信" value="tencent" />
                <el-option label="华为云短信" value="huawei" />
                <el-option label="七牛云短信" value="qiniu" />
              </el-select>
            </el-form-item>

            <template v-if="smsConfig.provider === 'aliyun'">
              <el-form-item label="Access Key ID" prop="access_key">
                <el-input v-model="smsConfig.access_key" placeholder="阿里云Access Key ID" />
              </el-form-item>
              <el-form-item label="Access Key Secret" prop="access_secret">
                <el-input
                  v-model="smsConfig.access_secret"
                  type="password"
                  placeholder="阿里云Access Key Secret"
                  show-password
                />
              </el-form-item>
              <el-form-item label="短信签名" prop="sign_name">
                <el-input v-model="smsConfig.sign_name" placeholder="例如: 您的网站名" />
              </el-form-item>
            </template>

            <template v-else-if="smsConfig.provider === 'tencent'">
              <el-form-item label="Secret ID" prop="secret_id">
                <el-input v-model="smsConfig.secret_id" placeholder="腾讯云Secret ID" />
              </el-form-item>
              <el-form-item label="Secret Key" prop="secret_key">
                <el-input
                  v-model="smsConfig.secret_key"
                  type="password"
                  placeholder="腾讯云Secret Key"
                  show-password
                />
              </el-form-item>
              <el-form-item label="SDK App ID" prop="sdk_app_id">
                <el-input v-model="smsConfig.sdk_app_id" placeholder="短信应用SDK App ID" />
              </el-form-item>
              <el-form-item label="短信签名" prop="sign_name">
                <el-input v-model="smsConfig.sign_name" placeholder="例如: 您的网站名" />
              </el-form-item>
            </template>

            <el-form-item label="测试手机号">
              <el-input v-model="smsConfig.test_phone" placeholder="用于测试的手机号" />
            </el-form-item>

            <el-form-item label="模板配置">
              <el-button type="text" @click="showSmsTemplateDialog = true">
                <el-icon><Document /></el-icon>
                管理短信模板
              </el-button>
              <div style="margin-top: 5px; color: #909399; font-size: 12px;">
                配置常用的短信模板，如验证码、通知等
              </div>
            </el-form-item>
          </el-form>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="showSmsConfigDialog = false">取消</el-button>
              <el-button type="info" @click="testSmsConfig" :loading="smsTesting">测试发送</el-button>
              <el-button type="primary" @click="saveSmsConfig" :loading="smsSaving">保存配置</el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 微信服务配置对话框 -->
        <el-dialog
          title="微信通知服务配置"
          v-model="showWechatConfigDialog"
          width="600px"
          :close-on-click-modal="false"
        >
          <el-form :model="wechatConfig" :rules="wechatRules" ref="wechatFormRef" label-width="120px">
            <el-alert
              title="配置说明"
              type="info"
              :closable="false"
              style="margin-bottom: 20px;"
            >
              <template #default>
                支持企业微信应用消息、微信公众号模板消息等。请确保已创建相应应用并获取必要的API权限。
              </template>
            </el-alert>

            <el-form-item label="通知类型" prop="type">
              <el-select v-model="wechatConfig.type" placeholder="选择微信通知类型" style="width: 100%" @change="handleWechatTypeChange">
                <el-option label="企业微信应用消息" value="work_wechat" />
                <el-option label="微信公众号模板消息" value="mp_template" />
                <el-option label="微信小程序订阅消息" value="miniprogram_subscribe" />
              </el-select>
            </el-form-item>

            <template v-if="wechatConfig.type === 'work_wechat'">
              <el-form-item label="企业ID" prop="corp_id">
                <el-input v-model="wechatConfig.corp_id" placeholder="企业微信CorpId" />
              </el-form-item>
              <el-form-item label="应用Secret" prop="corp_secret">
                <el-input
                  v-model="wechatConfig.corp_secret"
                  type="password"
                  placeholder="企业微信应用Secret"
                  show-password
                />
              </el-form-item>
              <el-form-item label="应用AgentId" prop="agent_id">
                <el-input-number v-model="wechatConfig.agent_id" :min="1" style="width: 100%" />
              </el-form-item>
            </template>

            <template v-else-if="wechatConfig.type === 'mp_template'">
              <el-form-item label="公众号AppId" prop="app_id">
                <el-input v-model="wechatConfig.app_id" placeholder="微信公众号AppId" />
              </el-form-item>
              <el-form-item label="公众号AppSecret" prop="app_secret">
                <el-input
                  v-model="wechatConfig.app_secret"
                  type="password"
                  placeholder="微信公众号AppSecret"
                  show-password
                />
              </el-form-item>
            </template>

            <el-form-item label="测试用户">
              <el-input v-model="wechatConfig.test_user" placeholder="测试用户OpenId或企业微信UserId" />
            </el-form-item>
          </el-form>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="showWechatConfigDialog = false">取消</el-button>
              <el-button type="info" @click="testWechatConfig" :loading="wechatTesting">测试发送</el-button>
              <el-button type="primary" @click="saveWechatConfig" :loading="wechatSaving">保存配置</el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 邮件模板选择对话框 -->
        <el-dialog 
          title="选择邮件服务模板" 
          v-model="showEmailTemplateDialog" 
          width="900px"
          :close-on-click-modal="false"
          top="5vh"
        >
          <div class="email-templates">
            <el-alert
              title="快速配置提示"
              type="info"
              :closable="false"
              style="margin-bottom: 20px;"
            >
              <template #default>
                选择邮件服务商模板可快速配置SMTP服务器信息，选择后仍需填写邮箱账号和密码。
              </template>
            </el-alert>
            
            <el-radio-group v-model="selectedEmailTemplate" class="template-radio-group">
              <div class="template-option" v-for="template in emailTemplates" :key="template.key">
                <el-radio :label="template.key" class="template-radio">
                  <div class="template-info">
                    <div class="template-name">{{ template.name }}</div>
                    <div class="template-desc">{{ template.desc }}</div>
                    <div class="template-config" v-if="template.key !== 'custom'">
                      <span class="config-item">{{ template.config.host }}:{{ template.config.port }}</span>
                      <el-tag size="small" type="info">{{ template.config.encryption.toUpperCase() }}</el-tag>
                    </div>
                  </div>
                </el-radio>
              </div>
            </el-radio-group>
          </div>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="showEmailTemplateDialog = false">取消</el-button>
              <el-button type="primary" @click="confirmEmailTemplate" :disabled="!selectedEmailTemplate">
                应用模板
              </el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 短信模板管理对话框 -->
        <el-dialog
          title="短信模板管理"
          v-model="showSmsTemplateDialog"
          width="700px"
          :close-on-click-modal="false"
        >
          <div class="sms-template-manager">
            <el-alert
              title="模板说明"
              type="info"
              :closable="false"
              style="margin-bottom: 20px;"
            >
              <template #default>
                短信模板需要在对应服务商平台预先申请和审核通过。这里配置模板ID和变量，用于系统调用发送。
              </template>
            </el-alert>

            <div class="template-actions" style="margin-bottom: 16px;">
              <el-button type="primary" size="small" @click="addSmsTemplate">
                <el-icon><Plus /></el-icon>
                添加模板
              </el-button>
            </div>

            <el-table :data="smsTemplates" style="width: 100%">
              <el-table-column prop="name" label="模板名称" width="150" />
              <el-table-column prop="type" label="类型" width="100">
                <template #default="scope">
                  <el-tag size="small" :type="getSmsTemplateTypeColor(scope.row.type)">
                    {{ getSmsTemplateTypeName(scope.row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="template_id" label="模板ID" width="120" />
              <el-table-column prop="content" label="模板内容" show-overflow-tooltip />
              <el-table-column prop="variables" label="变量" width="120" show-overflow-tooltip />
              <el-table-column prop="status" label="状态" width="80">
                <template #default="scope">
                  <el-switch v-model="scope.row.active" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button type="text" size="small" @click="editSmsTemplate(scope.row)">编辑</el-button>
                  <el-button type="text" size="small" @click="testSmsTemplate(scope.row)">测试</el-button>
                  <el-button type="text" size="small" @click="deleteSmsTemplate(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="showSmsTemplateDialog = false">关闭</el-button>
            </div>
          </template>
        </el-dialog>
        
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, CreditCard, Bell, Lock, Message, FolderOpened, User, Connection, Grid, Document, DataLine, ChatDotRound, ChatSquare, Plus, Check, Rank, ArrowUp, ArrowDown, Position, CircleCheckFilled } from '@element-plus/icons-vue'
import ImageUpload from '@/components/Upload/ImageUpload.vue'
import { getSystemSettings, updateSystemSettings, testPaymentConfig } from '@/api/system'

// 导入新的组件
import AuditLogs from './components/AuditLogs.vue'
import BackupSettings from './components/BackupSettings.vue'
import CommunitySettings from './components/CommunitySettings.vue'
import NotificationTemplateManager from './components/NotificationTemplateManager.vue'

// 响应式数据
const activeMenu = ref('basic')
const basicFormRef = ref()
const paymentFormRef = ref()
const notificationFormRef = ref()
const securityFormRef = ref()

// 基础设置表单
const basicForm = ref({
  site_name: '',
  site_description: '',
  site_keywords: '',
  site_logo: '',
  site_favicon: '',
  contact_email: '',
  contact_phone: '',
  icp_number: ''
})

// 支付设置表单
const paymentForm = ref({
  wechat_enabled: false,
  wechat_app_id: '',
  wechat_mch_id: '',
  wechat_key: '',
  alipay_enabled: false,
  alipay_app_id: '',
  alipay_private_key: '',
  alipay_public_key: ''
})

// 通知设置表单
const notificationForm = ref({
  email_enabled: true,
  sms_enabled: false,
  wechat_enabled: false,
  system_enabled: true,
  user_register: ['system'],
  new_order: ['email', 'system'],
  withdrawal_request: ['email', 'system']
})

// 通知设置相关的新增响应式数据
const notificationActiveTab = ref('channels')
const notificationSaving = ref(false)
const showAddEventDialog = ref(false)
const showRulesConfigDialog = ref(false)

// 模板管理对话框
const templateManagerDialog = reactive({
  visible: false
})

// 智能规则配置对话框
const rulesConfigDialog = reactive({
  visible: false
})

const rulesActiveTab = ref('frequency')
const rulesSaving = ref(false)

// 频率控制规则
const frequencyRules = reactive({
  same_type_interval: 30,
  daily_user_limit: 20,
  peak_hour_limit: true,
  peak_start: '09:00',
  peak_end: '18:00',
  peak_reduction_rate: 50
})

// 渠道选择规则
const channelRules = reactive({
  user_preference_priority: true,
  fallback_enabled: true,
  priority_order: ['system', 'email', 'sms', 'wechat'],
  urgent_multi_channel: true
})

// 时间规则
const timingRules = reactive({
  do_not_disturb: true,
  dnd_start: '22:00',
  dnd_end: '08:00',
  dnd_except_urgent: true,
  optimal_time: true,
  optimal_hours: ['09', '14', '19'],
  holiday_handling: true,
  holiday_strategy: 'delay'
})

// 内容优化规则
const contentRules = reactive({
  personalization: true,
  multi_language: true,
  supported_languages: ['zh-CN', 'zh-TW'],
  length_optimization: true,
  sms_max_length: 70
})

// 服务配置对话框显示状态
const showEmailConfigDialog = ref(false)
const showSmsConfigDialog = ref(false)
const showWechatConfigDialog = ref(false)
const showSmsTemplateDialog = ref(false)
const showEmailTemplateDialog = ref(false)

// 各服务保存和测试状态
const emailSaving = ref(false)
const emailTesting = ref(false)
const smsSaving = ref(false)
const smsTesting = ref(false)
const wechatSaving = ref(false)
const wechatTesting = ref(false)

// 邮件模板选择
const selectedEmailTemplate = ref('')
const emailTemplates = ref([
  {
    key: 'qq',
    name: 'QQ邮箱',
    desc: '适用于QQ邮箱、QQ企业邮箱',
    config: {
      host: 'smtp.qq.com',
      port: 587,
      encryption: 'tls'
    }
  },
  {
    key: '163',
    name: '网易163邮箱',
    desc: '适用于163邮箱、126邮箱、yeah.net',
    config: {
      host: 'smtp.163.com',
      port: 587,
      encryption: 'tls'
    }
  },
  {
    key: 'gmail',
    name: 'Gmail',
    desc: '适用于Gmail、Google Workspace',
    config: {
      host: 'smtp.gmail.com',
      port: 587,
      encryption: 'tls'
    }
  },
  {
    key: 'outlook',
    name: 'Outlook/Hotmail',
    desc: '适用于Outlook.com、Hotmail、Live邮箱',
    config: {
      host: 'smtp-mail.outlook.com',
      port: 587,
      encryption: 'tls'
    }
  },
  {
    key: 'aliyun',
    name: '阿里云邮件推送',
    desc: '适用于阿里云邮件推送服务',
    config: {
      host: 'smtpdm.aliyun.com',
      port: 465,
      encryption: 'ssl'
    }
  },
  {
    key: 'tencent',
    name: '腾讯云邮件推送',
    desc: '适用于腾讯云SES服务',
    config: {
      host: 'smtp.qcloudmail.com',
      port: 587,
      encryption: 'tls'
    }
  },
  {
    key: 'custom',
    name: '自定义配置',
    desc: '手动输入SMTP服务器信息',
    config: {
      host: '',
      port: 587,
      encryption: 'tls'
    }
  }
])

// 短信模板数据
const smsTemplates = ref([
  {
    id: 1,
    name: '登录验证码',
    type: 'verification',
    template_id: 'SMS_123456',
    content: '您的登录验证码是：${code}，5分钟内有效，请勿泄露。',
    variables: 'code',
    active: true
  },
  {
    id: 2,
    name: '订单通知',
    type: 'notification',
    template_id: 'SMS_789012',
    content: '您的订单${order_no}已确认，金额${amount}元。',
    variables: 'order_no,amount',
    active: true
  },
  {
    id: 3,
    name: '提现通知',
    type: 'notification',
    template_id: 'SMS_345678',
    content: '您的提现申请已处理，金额${amount}元已到账。',
    variables: 'amount',
    active: false
  }
])

// 邮件服务配置
const emailConfig = reactive({
  host: '',
  port: 587,
  encryption: 'tls',
  username: '',
  password: '',
  from_name: '系统通知',
  test_email: ''
})

const emailRules = {
  host: [{ required: true, message: '请输入SMTP服务器地址', trigger: 'blur' }],
  port: [{ required: true, message: '请输入端口号', trigger: 'blur' }],
  username: [
    { required: true, message: '请输入发件人邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [{ required: true, message: '请输入邮箱密码或授权码', trigger: 'blur' }]
}

// 短信服务配置
const smsConfig = reactive({
  provider: '',
  access_key: '',
  access_secret: '',
  secret_id: '',
  secret_key: '',
  sdk_app_id: '',
  sign_name: '',
  test_phone: ''
})

const smsRules = {
  provider: [{ required: true, message: '请选择短信服务商', trigger: 'change' }],
  access_key: [{ required: true, message: '请输入Access Key', trigger: 'blur' }],
  access_secret: [{ required: true, message: '请输入Access Secret', trigger: 'blur' }],
  sign_name: [{ required: true, message: '请输入短信签名', trigger: 'blur' }]
}

// 微信服务配置
const wechatConfig = reactive({
  type: '',
  corp_id: '',
  corp_secret: '',
  agent_id: null,
  app_id: '',
  app_secret: '',
  test_user: ''
})

const wechatRules = {
  type: [{ required: true, message: '请选择微信通知类型', trigger: 'change' }]
}

// 通知渠道状态
const notificationChannelStatus = ref({
  email: false,
  sms: false,
  wechat: false,
  system: true
})

// 计算属性：是否有任何服务已配置
const hasAnyServiceConfigured = computed(() => {
  return notificationChannelStatus.value.email || 
         notificationChannelStatus.value.sms || 
         notificationChannelStatus.value.wechat
})

// 计算属性：已配置服务数量
const configuredServicesCount = computed(() => {
  let count = 1 // 系统服务总是可用的
  if (notificationChannelStatus.value.email) count++
  if (notificationChannelStatus.value.sms) count++
  if (notificationChannelStatus.value.wechat) count++
  return count
})

// 计算属性：活跃通知事件数量
const activeEventsCount = computed(() => {
  return notificationEvents.value.filter(event => event.enabled).length
})

// 计算属性：活跃智能规则数量
const activeRulesCount = computed(() => {
  let count = 0
  if (frequencyRules.peak_hour_limit) count++
  if (channelRules.fallback_enabled) count++
  if (timingRules.do_not_disturb) count++
  if (timingRules.optimal_time) count++
  if (contentRules.personalization) count++
  if (contentRules.multi_language) count++
  return count
})

// 通知事件配置
const notificationEvents = ref([
  {
    id: 1,
    name: '用户注册',
    description: '新用户注册时发送欢迎通知',
    channels: ['system'],
    enabled: true
  },
  {
    id: 2,
    name: '订单创建',
    description: '用户创建新订单时通知管理员',
    channels: ['email', 'system'],
    enabled: true
  },
  {
    id: 3,
    name: '提现申请',
    description: '用户提交提现申请时通知',
    channels: ['email', 'sms', 'system'],
    enabled: true
  },
  {
    id: 4,
    name: '系统维护',
    description: '系统维护前通知所有用户',
    channels: ['system', 'email'],
    enabled: false
  }
])

// 全局通知设置
const notificationGlobalSettings = ref({
  rate_limit_enabled: true,
  max_per_hour: 50,
  night_mode_enabled: true,
  night_start: '22:00',
  night_end: '08:00',
  retry_enabled: true,
  max_retries: 3,
  enable_logging: true,
  queue_enabled: true,
  template_cache: true
})

// 安全设置表单
const securityForm = ref({
  login_captcha: true,
  register_captcha: true,
  password_strength: true,
  login_attempts: 5,
  session_timeout: 120,
  ip_whitelist: ''
})

// 表单验证规则
const basicRules = {
  site_name: [
    { required: true, message: '请输入网站名称', trigger: 'blur' }
  ],
  contact_email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 获取系统设置
const fetchSettings = async () => {
  try {
    const { data } = await getSystemSettings()
    basicForm.value = { ...basicForm.value, ...data.basic }
    paymentForm.value = { ...paymentForm.value, ...data.payment }
    notificationForm.value = { ...notificationForm.value, ...data.notification }
    securityForm.value = { ...securityForm.value, ...data.security }
  } catch (error) {
    console.error('获取系统设置失败:', error)
    ElMessage.error('获取系统设置失败')
  }
}

// 菜单选择
const handleMenuSelect = (key) => {
  activeMenu.value = key
}

// 保存基础设置
const saveBasicSettings = async () => {
  try {
    await basicFormRef.value.validate()
    await updateSystemSettings('basic', basicForm.value)
    ElMessage.success('基础设置保存成功')
  } catch (error) {
    console.error('保存基础设置失败:', error)
    ElMessage.error('保存基础设置失败')
  }
}

// 重置基础表单
const resetBasicForm = () => {
  basicFormRef.value.resetFields()
}

// 保存支付设置
const savePaymentSettings = async () => {
  try {
    await updateSystemSettings('payment', paymentForm.value)
    ElMessage.success('支付设置保存成功')
  } catch (error) {
    console.error('保存支付设置失败:', error)
    ElMessage.error('保存支付设置失败')
  }
}

// 测试支付配置
const testPayment = async () => {
  try {
    await testPaymentConfig(paymentForm.value)
    ElMessage.success('支付配置测试通过')
  } catch (error) {
    console.error('支付配置测试失败:', error)
    ElMessage.error('支付配置测试失败')
  }
}

// 保存通知设置
const saveNotificationSettings = async () => {
  notificationSaving.value = true
  try {
    // 合并所有通知相关设置
    const allNotificationSettings = {
      ...notificationForm.value,
      channel_status: notificationChannelStatus.value,
      events: notificationEvents.value,
      global_settings: notificationGlobalSettings.value
    }
    
    await updateSystemSettings('notification', allNotificationSettings)
    ElMessage.success('通知设置保存成功')
  } catch (error) {
    console.error('保存通知设置失败:', error)
    ElMessage.error('保存通知设置失败')
  } finally {
    notificationSaving.value = false
  }
}

// 重置通知设置
const resetNotificationSettings = () => {
  // 重置基础设置
  Object.assign(notificationForm.value, {
    email_enabled: true,
    sms_enabled: false,
    wechat_enabled: false,
    system_enabled: true
  })
  
  // 重置全局设置
  Object.assign(notificationGlobalSettings.value, {
    rate_limit_enabled: true,
    max_per_hour: 50,
    night_mode_enabled: true,
    night_start: '22:00',
    night_end: '08:00',
    retry_enabled: true,
    max_retries: 3,
    enable_logging: true,
    queue_enabled: true,
    template_cache: true
  })
  
  ElMessage.success('通知设置已重置')
}

// 导出通知配置
const exportNotificationConfig = () => {
  const config = {
    basic: notificationForm.value,
    channels: notificationChannelStatus.value,
    events: notificationEvents.value,
    global: notificationGlobalSettings.value,
    export_time: new Date().toISOString()
  }
  
  const dataStr = JSON.stringify(config, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `notification-config-${new Date().toISOString().slice(0, 10)}.json`
  link.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('通知配置已导出')
}

// 处理通知渠道变更
const handleChannelChange = async (channel, enabled) => {
  try {
    // 检查渠道配置状态
    if (enabled) {
      // 这里可以添加渠道配置检查逻辑
      await checkChannelConfiguration(channel)
      notificationChannelStatus.value[channel] = true
      ElMessage.success(`${getChannelName(channel)}已启用`)
    } else {
      notificationChannelStatus.value[channel] = false
      ElMessage.info(`${getChannelName(channel)}已禁用`)
    }
  } catch (error) {
    // 如果配置检查失败，恢复原状态
    notificationForm.value[`${channel}_enabled`] = false
    ElMessage.error(`${getChannelName(channel)}配置有误，请检查设置`)
  }
}

// 获取渠道名称
const getChannelName = (channel) => {
  const names = {
    email: '邮件通知',
    sms: '短信通知',
    wechat: '微信通知',
    system: '系统通知'
  }
  return names[channel] || channel
}

// 检查渠道配置（模拟异步检查）
const checkChannelConfiguration = async (channel) => {
  return new Promise((resolve, reject) => {
    // 模拟配置检查
    setTimeout(() => {
      if (channel === 'email' && !notificationChannelStatus.value.email) {
        // 模拟邮件配置检查
        resolve()
      } else if (channel === 'sms' && !notificationChannelStatus.value.sms) {
        // 模拟短信配置检查
        resolve()
      } else {
        resolve()
      }
    }, 500)
  })
}

// 测试通知功能
const testEmailNotification = async () => {
  try {
    // 这里调用后端API测试邮件发送
    ElMessage.success('邮件测试发送成功，请检查收件箱')
  } catch (error) {
    ElMessage.error('邮件测试发送失败')
  }
}

const testSmsNotification = async () => {
  try {
    // 这里调用后端API测试短信发送
    ElMessage.success('短信测试发送成功')
  } catch (error) {
    ElMessage.error('短信测试发送失败')
  }
}

const testWechatNotification = async () => {
  try {
    // 这里调用后端API测试微信通知
    ElMessage.success('微信通知测试发送成功')
  } catch (error) {
    ElMessage.error('微信通知测试发送失败')
  }
}

// 更新事件渠道配置
const updateEventChannels = (eventId, channels) => {
  const event = notificationEvents.value.find(e => e.id === eventId)
  if (event) {
    event.channels = channels
    ElMessage.success('事件通知渠道已更新')
  }
}

// 更新事件状态
const updateEventStatus = (eventId, enabled) => {
  const event = notificationEvents.value.find(e => e.id === eventId)
  if (event) {
    event.enabled = enabled
    const statusText = enabled ? '启用' : '禁用'
    ElMessage.success(`事件 ${event.name} 已${statusText}`)
  }
}

// 编辑通知事件
const editNotificationEvent = (event) => {
  ElMessage.info(`编辑事件: ${event.name}`)
  // 这里可以打开编辑对话框
}

// 删除通知事件
const deleteNotificationEvent = (eventId) => {
  const index = notificationEvents.value.findIndex(e => e.id === eventId)
  if (index > -1) {
    const event = notificationEvents.value[index]
    notificationEvents.value.splice(index, 1)
    ElMessage.success(`事件 ${event.name} 已删除`)
  }
}

// 打开高级通知设置
const openAdvancedNotificationSettings = () => {
  ElMessage.info('打开高级通知配置界面')
  // 这里可以打开单独的高级配置页面或对话框
}

// 打开模板管理器
const openTemplateManager = () => {
  templateManagerDialog.visible = true
}

// 渠道优先级调整
const moveChannelUp = (index) => {
  if (index > 0) {
    const temp = channelRules.priority_order[index]
    channelRules.priority_order[index] = channelRules.priority_order[index - 1]
    channelRules.priority_order[index - 1] = temp
  }
}

const moveChannelDown = (index) => {
  if (index < channelRules.priority_order.length - 1) {
    const temp = channelRules.priority_order[index]
    channelRules.priority_order[index] = channelRules.priority_order[index + 1]
    channelRules.priority_order[index + 1] = temp
  }
}

// 保存规则配置
const saveRulesConfig = async () => {
  rulesSaving.value = true
  try {
    const rulesConfig = {
      frequency: frequencyRules,
      channel: channelRules,
      timing: timingRules,
      content: contentRules
    }
    
    // 这里调用API保存规则配置
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success('智能规则配置已保存')
    rulesConfigDialog.visible = false
  } catch (error) {
    ElMessage.error('保存规则配置失败')
  } finally {
    rulesSaving.value = false
  }
}

// 邮件服务配置相关方法
const saveEmailConfig = async () => {
  emailSaving.value = true
  try {
    // 这里调用API保存邮件配置
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 更新渠道状态
    notificationChannelStatus.value.email = true
    
    ElMessage.success('邮件服务配置已保存')
    showEmailConfigDialog.value = false
  } catch (error) {
    ElMessage.error('保存邮件配置失败')
  } finally {
    emailSaving.value = false
  }
}

const testEmailConfig = async () => {
  if (!emailConfig.test_email) {
    ElMessage.warning('请先填写测试邮箱')
    return
  }
  
  emailTesting.value = true
  try {
    // 这里调用API测试邮件发送
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(`测试邮件已发送到 ${emailConfig.test_email}，请检查收件箱`)
  } catch (error) {
    ElMessage.error('邮件发送测试失败，请检查配置')
  } finally {
    emailTesting.value = false
  }
}

// 短信服务配置相关方法
const handleSmsProviderChange = (provider) => {
  // 重置配置
  Object.assign(smsConfig, {
    provider,
    access_key: '',
    access_secret: '',
    secret_id: '',
    secret_key: '',
    sdk_app_id: '',
    sign_name: '',
    test_phone: smsConfig.test_phone
  })
}

const saveSmsConfig = async () => {
  smsSaving.value = true
  try {
    // 这里调用API保存短信配置
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 更新渠道状态
    notificationChannelStatus.value.sms = true
    
    ElMessage.success('短信服务配置已保存')
    showSmsConfigDialog.value = false
  } catch (error) {
    ElMessage.error('保存短信配置失败')
  } finally {
    smsSaving.value = false
  }
}

const testSmsConfig = async () => {
  if (!smsConfig.test_phone) {
    ElMessage.warning('请先填写测试手机号')
    return
  }
  
  smsTesting.value = true
  try {
    // 这里调用API测试短信发送
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(`测试短信已发送到 ${smsConfig.test_phone}`)
  } catch (error) {
    ElMessage.error('短信发送测试失败，请检查配置')
  } finally {
    smsTesting.value = false
  }
}

// 微信服务配置相关方法
const handleWechatTypeChange = (type) => {
  // 重置配置
  Object.assign(wechatConfig, {
    type,
    corp_id: '',
    corp_secret: '',
    agent_id: null,
    app_id: '',
    app_secret: '',
    test_user: wechatConfig.test_user
  })
}

const saveWechatConfig = async () => {
  wechatSaving.value = true
  try {
    // 这里调用API保存微信配置
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 更新渠道状态
    notificationChannelStatus.value.wechat = true
    
    ElMessage.success('微信服务配置已保存')
    showWechatConfigDialog.value = false
  } catch (error) {
    ElMessage.error('保存微信配置失败')
  } finally {
    wechatSaving.value = false
  }
}

const testWechatConfig = async () => {
  if (!wechatConfig.test_user) {
    ElMessage.warning('请先填写测试用户')
    return
  }
  
  wechatTesting.value = true
  try {
    // 这里调用API测试微信通知
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(`测试通知已发送到用户 ${wechatConfig.test_user}`)
  } catch (error) {
    ElMessage.error('微信通知测试失败，请检查配置')
  } finally {
    wechatTesting.value = false
  }
}

// 邮件模板处理方法
const applyEmailTemplate = (templateKey) => {
  const template = emailTemplates.value.find(t => t.key === templateKey)
  if (template && template.key !== 'custom') {
    Object.assign(emailConfig, template.config)
    ElMessage.success(`已应用${template.name}模板配置`)
  }
}

const confirmEmailTemplate = () => {
  const template = emailTemplates.value.find(t => t.key === selectedEmailTemplate.value)
  if (template) {
    if (template.key === 'custom') {
      // 自定义配置，不覆盖现有配置
      ElMessage.info('请手动填写SMTP服务器信息')
    } else {
      Object.assign(emailConfig, template.config)
      ElMessage.success(`已应用${template.name}模板，请填写邮箱和密码信息`)
    }
    showEmailTemplateDialog.value = false
  }
}

// 短信模板管理方法
const getSmsTemplateTypeColor = (type) => {
  const colors = {
    verification: 'primary',
    notification: 'success',
    marketing: 'warning',
    alert: 'danger'
  }
  return colors[type] || 'info'
}

const getSmsTemplateTypeName = (type) => {
  const names = {
    verification: '验证码',
    notification: '通知',
    marketing: '营销',
    alert: '告警'
  }
  return names[type] || type
}

const addSmsTemplate = () => {
  ElMessage.info('添加短信模板功能开发中...')
}

const editSmsTemplate = (template) => {
  ElMessage.info(`编辑模板：${template.name}`)
}

const testSmsTemplate = async (template) => {
  if (!smsConfig.test_phone) {
    ElMessage.warning('请先在短信配置中填写测试手机号')
    return
  }
  
  ElMessage.info(`使用模板 ${template.name} 发送测试短信到 ${smsConfig.test_phone}`)
  
  // 模拟发送测试
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('测试短信发送成功')
  } catch (error) {
    ElMessage.error('测试短信发送失败')
  }
}

const deleteSmsTemplate = (template) => {
  ElMessageBox.confirm(`确定要删除短信模板"${template.name}"吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = smsTemplates.value.findIndex(t => t.id === template.id)
    if (index > -1) {
      smsTemplates.value.splice(index, 1)
      ElMessage.success('短信模板删除成功')
    }
  })
}

// 保存安全设置
const saveSecuritySettings = async () => {
  try {
    await updateSystemSettings('security', securityForm.value)
    ElMessage.success('安全设置保存成功')
  } catch (error) {
    console.error('保存安全设置失败:', error)
    ElMessage.error('保存安全设置失败')
  }
}

// 初始化
onMounted(() => {
  fetchSettings()
})
</script>

<style lang="scss" scoped>
.settings-menu {
  border: none;
  
  .el-menu-item {
    border-radius: 8px;
    margin-bottom: 4px;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    &.is-active {
      background-color: #ecf5ff;
      color: #409eff;
    }
  }
}

.el-divider {
  margin: 24px 0;
}

// 通知配置界面样式
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.channel-config-section {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
  
  h4 {
    margin: 0 0 16px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }
}

.channel-list {
  .channel-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #e4e7ed;
    
    &:last-child {
      border-bottom: none;
    }
    
    .channel-info {
      display: flex;
      align-items: center;
      
      .el-icon {
        margin-right: 12px;
        font-size: 18px;
        color: #606266;
      }
      
      .channel-details {
        display: flex;
        flex-direction: column;
        
        .channel-name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 2px;
        }
        
        .channel-desc {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

.channel-status-list {
  .status-item {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .service-header {
      display: flex;
      align-items: center;
      gap: 8px;
      
      span:first-child {
        font-size: 14px;
        color: #606266;
        min-width: 80px;
      }
      
      .el-button {
        margin-left: auto;
      }
    }
  }
}

.events-config {
  .events-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h4 {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.global-settings-section {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
  
  h4 {
    margin: 0 0 16px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }
}

.notification-actions {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

// 智能规则配置样式
.rules-config-container {
  .rule-section {
    padding: 16px 0;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.channel-priority-list {
  .priority-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 8px;
    background: #fafafa;
    
    span {
      flex: 1;
      margin-left: 8px;
      color: #606266;
    }
    
    .el-icon {
      color: #909399;
    }
  }
}

// 邮件模板选择样式
.email-templates {
  padding: 8px 4px;
  
  .template-radio-group {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    
    .template-option {
      margin-bottom: 0;
      
      .template-radio {
        width: 100%;
        margin: 0;
        border: 2px solid #e4e7ed;
        border-radius: 16px;
        padding: 0;
        transition: all 0.3s ease;
        position: relative;
        background: #ffffff;
        min-height: 140px;
        height: 100%;
        
        &:hover {
          border-color: #409eff;
          box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
          transform: translateY(-2px);
        }
        
        &.is-checked {
          border-color: #409eff;
          background-color: #f0f9ff;
          box-shadow: 0 4px 20px rgba(64, 158, 255, 0.25);
        }
        
        :deep(.el-radio__input) {
          position: absolute;
          top: 20px;
          right: 20px;
          z-index: 2;
          
          .el-radio__inner {
            width: 18px;
            height: 18px;
            border: 2px solid #dcdfe6;
            
            &::after {
              width: 6px;
              height: 6px;
            }
          }
        }
        
        :deep(.el-radio__label) {
          width: 100%;
          padding: 24px 56px 24px 24px;
          color: inherit;
          font-weight: normal;
          display: flex;
          flex-direction: column;
          height: 100%;
          line-height: 1.6;
        }
        
        .template-info {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          
          .template-name {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 10px;
            line-height: 1.4;
            display: block;
          }
          
          .template-desc {
            font-size: 13px;
            color: #606266;
            line-height: 1.5;
            margin-bottom: 12px;
            display: block;
            word-break: break-word;
            flex: 1;
          }
          
          .template-config {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: auto;
            flex-wrap: wrap;
            
            .config-item {
              font-size: 11px;
              color: #666;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              background: #f8f9fa;
              padding: 4px 8px;
              border-radius: 4px;
              border: 1px solid #e9ecef;
              white-space: nowrap;
            }
            
            .el-tag {
              font-size: 10px;
              padding: 2px 6px;
              font-weight: 500;
              border-radius: 3px;
            }
          }
        }
      }
    }
    
    // 响应式设计：小屏幕时回到单列布局
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
}
</style>