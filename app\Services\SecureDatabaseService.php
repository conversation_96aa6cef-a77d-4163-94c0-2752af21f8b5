<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

/**
 * 安全数据库操作服务
 * 防止SQL注入，提供安全的数据库操作方法
 */
class SecureDatabaseService
{
    /**
     * 允许的表名白名单
     */
    private const ALLOWED_TABLES = [
        'users',
        'orders',
        'wechat_groups',
        'commission_logs',
        'payment_channels',
        'payment_configs',
        'domain_pools',
        'short_links',
        'substations',
        'templates',
        'promotion_links',
        'operation_logs',
        'balance_logs',
        'transactions',
        'notifications',
        'system_settings',
    ];

    /**
     * 允许的索引操作
     */
    private const ALLOWED_INDEX_OPERATIONS = [
        'CREATE', 'DROP', 'ANALYZE', 'OPTIMIZE', 'REPAIR'
    ];

    /**
     * 安全执行表优化
     *
     * @param string $tableName 表名
     * @throws InvalidArgumentException
     */
    public function optimizeTable(string $tableName): void
    {
        $this->validateTableName($tableName);
        
        try {
            DB::statement("OPTIMIZE TABLE `{$tableName}`");
            Log::info("表优化成功: {$tableName}");
        } catch (\Exception $e) {
            Log::error("表优化失败: {$tableName}, 错误: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 安全执行表分析
     *
     * @param string $tableName 表名
     * @throws InvalidArgumentException
     */
    public function analyzeTable(string $tableName): void
    {
        $this->validateTableName($tableName);
        
        try {
            DB::statement("ANALYZE TABLE `{$tableName}`");
            Log::info("表分析成功: {$tableName}");
        } catch (\Exception $e) {
            Log::error("表分析失败: {$tableName}, 错误: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 安全执行表修复
     *
     * @param string $tableName 表名
     * @throws InvalidArgumentException
     */
    public function repairTable(string $tableName): void
    {
        $this->validateTableName($tableName);
        
        try {
            DB::statement("REPAIR TABLE `{$tableName}`");
            Log::info("表修复成功: {$tableName}");
        } catch (\Exception $e) {
            Log::error("表修复失败: {$tableName}, 错误: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取表的记录数（安全方式）
     *
     * @param string $tableName 表名
     * @return int
     * @throws InvalidArgumentException
     */
    public function getTableCount(string $tableName): int
    {
        $this->validateTableName($tableName);
        
        return DB::table($tableName)->count();
    }

    /**
     * 获取表的状态信息
     *
     * @param string $tableName 表名
     * @return array
     * @throws InvalidArgumentException
     */
    public function getTableStatus(string $tableName): array
    {
        $this->validateTableName($tableName);
        
        $result = DB::select("SHOW TABLE STATUS LIKE ?", [$tableName]);
        
        return !empty($result) ? (array) $result[0] : [];
    }

    /**
     * 安全检查索引是否存在
     *
     * @param string $tableName 表名
     * @param string $indexName 索引名
     * @return bool
     * @throws InvalidArgumentException
     */
    public function indexExists(string $tableName, string $indexName): bool
    {
        $this->validateTableName($tableName);
        $this->validateIndexName($indexName);
        
        $indexes = DB::select("SHOW INDEXES FROM `{$tableName}` WHERE Key_name = ?", [$indexName]);
        
        return !empty($indexes);
    }

    /**
     * 安全创建索引
     *
     * @param string $tableName 表名
     * @param string $indexName 索引名
     * @param array $columns 列名数组
     * @param string $indexType 索引类型 (INDEX, UNIQUE, FULLTEXT)
     * @throws InvalidArgumentException
     */
    public function createIndex(string $tableName, string $indexName, array $columns, string $indexType = 'INDEX'): void
    {
        $this->validateTableName($tableName);
        $this->validateIndexName($indexName);
        $this->validateIndexType($indexType);
        
        if (empty($columns)) {
            throw new InvalidArgumentException('索引列不能为空');
        }

        // 验证列名
        foreach ($columns as $column) {
            $this->validateColumnName($column);
        }

        $columnsStr = '`' . implode('`, `', $columns) . '`';
        
        try {
            DB::statement("CREATE {$indexType} `{$indexName}` ON `{$tableName}` ({$columnsStr})");
            Log::info("索引创建成功: {$tableName}.{$indexName}");
        } catch (\Exception $e) {
            Log::error("索引创建失败: {$tableName}.{$indexName}, 错误: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 批量优化表
     *
     * @param array $tableNames 表名数组，如果为空则优化所有允许的表
     * @return array 操作结果
     */
    public function batchOptimizeTables(array $tableNames = []): array
    {
        if (empty($tableNames)) {
            $tableNames = self::ALLOWED_TABLES;
        }

        $results = [];
        
        foreach ($tableNames as $tableName) {
            try {
                $this->optimizeTable($tableName);
                $results[$tableName] = ['status' => 'success', 'message' => '优化成功'];
            } catch (\Exception $e) {
                $results[$tableName] = ['status' => 'error', 'message' => $e->getMessage()];
            }
        }

        return $results;
    }

    /**
     * 获取数据库状态信息
     *
     * @return array
     */
    public function getDatabaseStatus(): array
    {
        try {
            $status = DB::select("SHOW STATUS LIKE 'Threads_connected'");
            $variables = DB::select("SHOW VARIABLES LIKE 'max_connections'");
            
            return [
                'connected_threads' => $status[0]->Value ?? 0,
                'max_connections' => $variables[0]->Value ?? 0,
                'database_name' => DB::connection()->getDatabaseName(),
            ];
        } catch (\Exception $e) {
            Log::error("获取数据库状态失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 验证表名是否在白名单中
     *
     * @param string $tableName
     * @throws InvalidArgumentException
     */
    private function validateTableName(string $tableName): void
    {
        if (!in_array($tableName, self::ALLOWED_TABLES, true)) {
            throw new InvalidArgumentException("不允许操作的表: {$tableName}");
        }
    }

    /**
     * 验证索引名称
     *
     * @param string $indexName
     * @throws InvalidArgumentException
     */
    private function validateIndexName(string $indexName): void
    {
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $indexName)) {
            throw new InvalidArgumentException("无效的索引名: {$indexName}");
        }
    }

    /**
     * 验证索引类型
     *
     * @param string $indexType
     * @throws InvalidArgumentException
     */
    private function validateIndexType(string $indexType): void
    {
        $allowedTypes = ['INDEX', 'UNIQUE', 'FULLTEXT'];
        if (!in_array(strtoupper($indexType), $allowedTypes, true)) {
            throw new InvalidArgumentException("无效的索引类型: {$indexType}");
        }
    }

    /**
     * 验证列名
     *
     * @param string $columnName
     * @throws InvalidArgumentException
     */
    private function validateColumnName(string $columnName): void
    {
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $columnName)) {
            throw new InvalidArgumentException("无效的列名: {$columnName}");
        }
    }
}