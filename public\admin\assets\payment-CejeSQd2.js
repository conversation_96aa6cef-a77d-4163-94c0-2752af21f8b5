import{s as e}from"./index-eUTsTR3J.js";const t={getPaymentConfig:()=>e({url:"/admin/system/payment/config",method:"get"}),updatePaymentConfig:t=>e({url:"/admin/system/payment/config",method:"put",data:t}),togglePaymentMethod:(t,a)=>e({url:`/admin/system/payment/${t}/toggle`,method:"post",data:{enabled:a}}),testPaymentChannel:(t,a)=>e({url:`/admin/system/payment/${t}/test`,method:"post",data:a}),getPaymentStats:()=>e({url:"/admin/system/payment/stats",method:"get"}),updateSecuritySettings:t=>e({url:"/admin/system/payment/security",method:"put",data:t}),refundOrder:(t,a)=>e({url:`/admin/payment/orders/${t}/refund`,method:"post",data:a}),batchProcessOrders:(t,a)=>e({url:"/admin/payment/orders/batch",method:"post",data:{orderIds:t,action:a}}),cancelOrder:(t,a)=>e({url:`/admin/payment/orders/${t}/cancel`,method:"post",data:{reason:a}}),getPaymentOrders:t=>e({url:"/admin/payment/orders",method:"get",params:t}),exportPaymentData:t=>e({url:"/admin/payment/orders/export",method:"post",data:t,responseType:"blob"})},a={getChannels:t=>e({url:"/admin/payment/channels",method:"get",params:t}),getChannelDetail:t=>e({url:`/admin/payment/channels/${t}`,method:"get"}),createChannel:t=>e({url:"/admin/payment/channels",method:"post",data:t}),updateChannel:(t,a)=>e({url:`/admin/payment/channels/${t}`,method:"put",data:a}),deleteChannel:t=>e({url:`/admin/payment/channels/${t}`,method:"delete"}),toggleChannel:(t,a)=>e({url:`/admin/payment/channels/${t}/toggle`,method:"post",data:{enabled:a}}),testChannel:(t,a)=>e({url:`/admin/payment/channels/${t}/test`,method:"post",data:a}),getChannelStats:t=>e({url:`/admin/payment/channels/${t}/stats`,method:"get"}),batchOperateChannels:(t,a)=>e({url:"/admin/payment/channels/batch",method:"post",data:{channelIds:t,operation:a}}),exportChannelData:t=>e({url:"/admin/payment/channels/export",method:"post",data:t,responseType:"blob"})},n=t.getPaymentConfig,d=t.updatePaymentConfig,s=t.togglePaymentMethod,m=t.testPaymentChannel,r=t.getPaymentStats,o=t.updateSecuritySettings,l=t.refundOrder,p=t.batchProcessOrders,h=t.cancelOrder,y=t.getPaymentOrders,i=t.exportPaymentData;export{r as a,s as b,d as c,p as d,i as e,h as f,n as g,y as h,a as p,l as r,m as t,o as u};
