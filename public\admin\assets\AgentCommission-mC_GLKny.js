/* empty css             *//* empty css                   *//* empty css                   *//* empty css                         *//* empty css                 *//* empty css               *//* empty css                    *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                *//* empty css                *//* empty css                     *//* empty css                       *//* empty css                  */import{l as e,q as a,G as t,A as l,B as s,F as i,Y as n,ag as r,r as o,M as d,o as c,m as u,E as m,z as p,D as g,K as _,C as h}from"./vue-vendor-BcnDv-68.js";import{a7 as f,ah as w,a1 as v,V as b,W as y,X as C,at as k,aw as D,aB as T,aM as S,aN as j,a9 as R,az as z,aF as x,aO as W,ax as V,aP as U,aQ as F,aR as P,aS as A,aT as M,ae as B,ag as L,u as O,Z as Y,s as N,a2 as G,a3 as I,a0 as E,a5 as H,a6 as Q,aU as $,aV as q,ai as X,aj as Z,aW as K,ak as J,aG as ee,an as ae,U as te,Y as le,aX as se,aY as ie,aZ as ne}from"./element-plus-C2UshkXo.js";import{_ as re}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const oe={class:"modern-agent-commission commission-container"},de={class:"page-header"},ce={class:"header-content"},ue={class:"header-left"},me={class:"header-icon"},pe={class:"header-actions"},ge={class:"stats-section"},_e={class:"stats-grid"},he={class:"stat-content"},fe={class:"stat-value"},we={class:"stat-label"},ve={class:"card-header"},be={class:"header-left"},ye={class:"header-right"},Ce={class:"commission-amount"},ke={class:"commission-rate"},De={class:"agent-info"},Te={class:"agent-details"},Se={class:"agent-name"},je={class:"agent-code"},Re={class:"pagination"},ze={class:"withdraw-amount"},xe={class:"bank-info"},We={class:"bank-name"},Ve={class:"account-no"},Ue={class:"rules-content"},Fe={class:"rule-section"},Pe={class:"rule-grid"},Ae={class:"rule-item"},Me={class:"rule-icon direct"},Be={class:"rule-item"},Le={class:"rule-icon team"},Oe={class:"rule-item"},Ye={class:"rule-icon management"},Ne={class:"rule-item"},Ge={class:"rule-icon special"},Ie={class:"help-content"},Ee={class:"help-section"},He={class:"feature-item"},Qe={class:"feature-icon"},$e={class:"feature-item"},qe={class:"feature-icon"},Xe={class:"feature-item"},Ze={class:"feature-icon"},Ke={class:"help-section"},Je={class:"guide-content"},ea={class:"guide-content"};const aa=re({__name:"AgentCommission",setup(e,{expose:a}){a();const t=r(),l=o(!1),s=o(!0),i=o("records"),n=o(!1),u=o(!1),m=o(!1),p=o(!1),g=o(!1),_=o(!1),h=o(""),f=o(null),w=o(null),v=o({records:[],totalAmount:0}),b=o({amount:"",bank_name:"",account_name:"",account_no:"",remark:""}),y=o(["view-commission"]),O=o([{key:"total_commission",label:"累计佣金",value:"¥0",icon:"Money",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"ArrowUp",change:"+15.6%"},{key:"available_commission",label:"可提现佣金",value:"¥0",icon:"Wallet",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"ArrowUp",change:"待结算 ¥1,200"},{key:"withdrawn_commission",label:"已提现金额",value:"¥0",icon:"CreditCard",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"ArrowUp",change:"本月 ¥3,200"},{key:"pending_withdraws",label:"待审核提现",value:"0",icon:"Clock",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"ArrowUp",change:"金额 ¥2,400"}]),Y=d({totalCommission:25680,availableCommission:8900,withdrawnCommission:16780,pendingCommission:1200,monthGrowth:15.6,monthWithdrawn:3200,pendingWithdraws:3,pendingAmount:2400}),N=o([]),G=d({dateRange:null,type:"",status:""}),I=d({current:1,size:20,total:0}),E=o([]),H=async()=>{await Promise.all([Q(),X(),Z()]),C.success("数据刷新成功")},Q=async()=>{try{s.value=!0,await new Promise(e=>setTimeout(e,300)),O.value[0].value="¥"+Y.totalCommission.toLocaleString(),O.value[1].value="¥"+Y.availableCommission.toLocaleString(),O.value[2].value="¥"+Y.withdrawnCommission.toLocaleString(),O.value[3].value=Y.pendingWithdraws.toString()}catch(e){C.error("加载统计数据失败")}finally{s.value=!1}},$=()=>{I.current=1,X()},q=async e=>{try{await new Promise(e=>setTimeout(e,500))}catch(a){C.error("加载订单详情失败")}},X=async()=>{l.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),N.value=[{id:1,order_no:"ORD202401001",commission_type:"direct",amount:150,rate:15,source_agent:{name:"李四",avatar:""},status:"settled",created_at:"2024-01-15 10:30:00",settled_at:"2024-01-16 09:00:00"},{id:2,order_no:"ORD202401002",commission_type:"team",amount:80,rate:8,source_agent:{name:"王五",avatar:""},status:"pending",created_at:"2024-01-16 14:20:00",settled_at:null}],I.total=2}catch(e){C.error("加载数据失败")}finally{l.value=!1}},Z=async()=>{try{await new Promise(e=>setTimeout(e,500)),E.value=[{id:1,withdraw_no:"WD202401001",amount:1e3,bank_info:{bank_name:"中国工商银行",account_no:"****1234"},status:"completed",created_at:"2024-01-10 16:00:00",processed_at:"2024-01-11 10:30:00"},{id:2,withdraw_no:"WD202401002",amount:500,bank_info:{bank_name:"中国建设银行",account_no:"****5678"},status:"pending",created_at:"2024-01-15 09:20:00",processed_at:null}]}catch(e){}};c(()=>{H()});const K={router:t,loading:l,statsLoading:s,activeTab:i,showHelpDialog:n,showBatchDialog:u,showWithdrawFormDialog:m,showOrderDetailDialog:p,showCommissionDetailDialog:g,showWithdrawDetailDialog:_,selectedOrderNo:h,selectedCommissionRecord:f,selectedWithdrawRecord:w,batchSettleData:v,withdrawForm:b,activeGuides:y,commissionStatCards:O,commissionStats:Y,commissionRecords:N,recordFilters:G,recordPagination:I,withdrawalRecords:E,refreshData:H,loadStats:Q,handleExportReport:async()=>{try{C.success("佣金报表导出成功")}catch(e){C.error("导出失败")}},showBatchSettleDialog:()=>{const e=N.value.filter(e=>"pending"===e.status);0!==e.length?(v.value={records:e,totalAmount:e.reduce((e,a)=>e+a.amount,0)},u.value=!0):C.warning("没有待结算的佣金记录")},showWithdrawDialog:()=>{Y.availableCommission<=0?C.warning("当前没有可提现的佣金"):(b.value={amount:"",bank_name:"",account_name:"",account_no:"",remark:""},m.value=!0)},searchRecords:$,resetRecordFilters:()=>{Object.assign(G,{dateRange:null,type:"",status:""}),$()},handleRecordSizeChange:e=>{I.size=e,X()},handleRecordCurrentChange:e=>{I.current=e,X()},viewOrderDetail:e=>{h.value=e,q(e),p.value=!0},viewCommissionDetail:e=>{f.value=e,g.value=!0},settleCommission:async e=>{try{await L.confirm(`确定要结算佣金 ¥${e.amount} 吗？`,"确认结算",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),C.success("佣金结算成功"),await X()}catch{C.info("已取消结算")}},viewWithdrawDetail:e=>{w.value=e,_.value=!0},getCommissionTypeTag:e=>({direct:"success",team:"primary",management:"warning",special:"danger"}[e]||"info"),getCommissionTypeText:e=>({direct:"直推佣金",team:"团队佣金",management:"管理奖励",special:"特殊奖励"}[e]||"未知类型"),getStatusTag:e=>({pending:"warning",settled:"success",withdrawn:"info"}[e]||"info"),getStatusText:e=>({pending:"待结算",settled:"已结算",withdrawn:"已提现"}[e]||"未知状态"),getWithdrawStatusTag:e=>({pending:"warning",processing:"primary",completed:"success",rejected:"danger"}[e]||"info"),getWithdrawStatusText:e=>({pending:"待审核",processing:"处理中",completed:"已完成",rejected:"已拒绝"}[e]||"未知状态"),formatDateTime:e=>e?new Date(e).toLocaleString("zh-CN"):"-",formatNumber:e=>"number"!=typeof e?"0":e.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),loadOrderDetail:q,handleBatchSettle:async()=>{try{await L.confirm(`确定要批量结算 ${v.value.records.length} 条佣金记录吗？总金额：¥${v.value.totalAmount}`,"确认批量结算",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await new Promise(e=>setTimeout(e,1e3)),C.success("批量结算成功"),u.value=!1,await X()}catch{C.info("已取消批量结算")}},handleWithdrawSubmit:async()=>{try{await new Promise(e=>setTimeout(e,1e3)),C.success("提现申请提交成功"),m.value=!1,await Z()}catch(e){C.error("提现申请失败")}},loadCommissionRecords:X,loadWithdrawalRecords:Z,ref:o,reactive:d,onMounted:c,get useRouter(){return r},get ElMessage(){return C},get ElMessageBox(){return L},get Refresh(){return B},get Money(){return M},get CreditCard(){return A},get Wallet(){return P},get Clock(){return F},get Medal(){return U},get Download(){return V},get QuestionFilled(){return W},get Search(){return x},get RefreshLeft(){return z},get User(){return R},get Connection(){return j},get Trophy(){return S},get Star(){return T},get List(){return D},get ArrowUp(){return k}};return Object.defineProperty(K,"__isScriptSetup",{enumerable:!1,value:!0}),K}},[["render",function(r,o,d,c,C,k){const D=w,T=v,S=E,j=I,R=Q,z=H,x=G,W=b,V=J,U=K,F=Z,P=ee,A=X,M=ae,B=q,L=$,re=le,aa=te,ta=ne,la=ie,sa=se,ia=y,na=f;return u(),e("div",oe,[a("div",de,[a("div",ce,[a("div",ue,[a("div",me,[t(D,{size:"24"},{default:l(()=>[t(c.Medal)]),_:1})]),o[9]||(o[9]=a("div",{class:"header-text"},[a("h1",{class:"page-title"},"代理商佣金管理"),a("p",{class:"page-subtitle"},"全面管理代理商佣金计算、结算和提现，实时跟踪收益状况")],-1))]),a("div",pe,[t(T,{onClick:c.handleExportReport,class:"action-btn secondary"},{default:l(()=>[t(D,null,{default:l(()=>[t(c.Download)]),_:1}),o[10]||(o[10]=m(" 导出报表 ",-1))]),_:1,__:[10]}),t(T,{onClick:o[0]||(o[0]=e=>c.showHelpDialog=!0),class:"action-btn secondary"},{default:l(()=>[t(D,null,{default:l(()=>[t(c.QuestionFilled)]),_:1}),o[11]||(o[11]=m(" 功能说明 ",-1))]),_:1,__:[11]}),t(T,{onClick:c.showBatchSettleDialog,class:"action-btn secondary"},{default:l(()=>[t(D,null,{default:l(()=>[t(c.Money)]),_:1}),o[12]||(o[12]=m(" 批量结算 ",-1))]),_:1,__:[12]}),t(T,{type:"primary",onClick:c.refreshData,class:"action-btn primary",loading:c.loading},{default:l(()=>[t(D,null,{default:l(()=>[t(c.Refresh)]),_:1}),o[13]||(o[13]=m(" 刷新数据 ",-1))]),_:1,__:[13]},8,["loading"])])])]),a("div",ge,[s((u(),e("div",_e,[(u(!0),e(i,null,n(c.commissionStatCards,s=>(u(),e("div",{class:"stat-card",key:s.key},[a("div",{class:"stat-icon",style:O({background:s.color})},[t(D,{size:"20"},{default:l(()=>[(u(),p(g(s.icon)))]),_:2},1024)],4),a("div",he,[a("div",fe,Y(s.value),1),a("div",we,Y(s.label),1)]),a("div",{class:N(["stat-trend",s.trend])},[t(D,{size:"14"},{default:l(()=>[(u(),p(g(s.trendIcon)))]),_:2},1024),a("span",null,Y(s.change),1)],2)]))),128))])),[[na,c.statsLoading]])]),t(W,{class:"filter-card"},{default:l(()=>[t(x,{model:c.recordFilters,inline:"",onSubmit:_(c.searchRecords,["prevent"])},{default:l(()=>[t(j,{label:"时间范围"},{default:l(()=>[t(S,{modelValue:c.recordFilters.dateRange,"onUpdate:modelValue":o[1]||(o[1]=e=>c.recordFilters.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",class:"date-picker"},null,8,["modelValue"])]),_:1}),t(j,{label:"佣金类型"},{default:l(()=>[t(z,{modelValue:c.recordFilters.type,"onUpdate:modelValue":o[2]||(o[2]=e=>c.recordFilters.type=e),placeholder:"全部类型",clearable:"",class:"filter-select"},{default:l(()=>[t(R,{label:"直推佣金",value:"direct"}),t(R,{label:"团队佣金",value:"team"}),t(R,{label:"管理奖励",value:"management"}),t(R,{label:"特殊奖励",value:"special"})]),_:1},8,["modelValue"])]),_:1}),t(j,{label:"状态"},{default:l(()=>[t(z,{modelValue:c.recordFilters.status,"onUpdate:modelValue":o[3]||(o[3]=e=>c.recordFilters.status=e),placeholder:"全部状态",clearable:"",class:"filter-select"},{default:l(()=>[t(R,{label:"待结算",value:"pending"}),t(R,{label:"已结算",value:"settled"}),t(R,{label:"已提现",value:"withdrawn"})]),_:1},8,["modelValue"])]),_:1}),t(j,null,{default:l(()=>[t(T,{type:"primary",onClick:c.searchRecords,class:"search-btn"},{default:l(()=>[t(D,null,{default:l(()=>[t(c.Search)]),_:1}),o[14]||(o[14]=m(" 查询 ",-1))]),_:1,__:[14]}),t(T,{onClick:c.resetRecordFilters,class:"reset-btn"},{default:l(()=>[t(D,null,{default:l(()=>[t(c.RefreshLeft)]),_:1}),o[15]||(o[15]=m(" 重置 ",-1))]),_:1,__:[15]})]),_:1})]),_:1},8,["model"])]),_:1}),t(W,{class:"table-card"},{header:l(()=>[a("div",ve,[a("div",be,[o[16]||(o[16]=a("h3",null,"佣金管理",-1)),t(V,{size:"small",type:"info"},{default:l(()=>[m("共 "+Y(c.recordPagination.total)+" 条记录",1)]),_:1})]),a("div",ye,[t(T,{onClick:c.showWithdrawDialog,type:"primary",size:"small"},{default:l(()=>[t(D,null,{default:l(()=>[t(c.CreditCard)]),_:1}),o[17]||(o[17]=m(" 申请提现 ",-1))]),_:1,__:[17]})])])]),default:l(()=>[t(L,{modelValue:c.activeTab,"onUpdate:modelValue":o[6]||(o[6]=e=>c.activeTab=e),class:"commission-tabs"},{default:l(()=>[t(B,{label:"佣金记录",name:"records"},{default:l(()=>[s((u(),p(A,{data:c.commissionRecords,class:"modern-table"},{default:l(()=>[t(F,{prop:"order_no",label:"订单号",width:"140"},{default:l(({row:e})=>[t(U,{type:"primary",onClick:a=>c.viewOrderDetail(e.order_no)},{default:l(()=>[m(Y(e.order_no),1)]),_:2},1032,["onClick"])]),_:1}),t(F,{prop:"commission_type",label:"佣金类型",width:"100"},{default:l(({row:e})=>[t(V,{type:c.getCommissionTypeTag(e.commission_type),size:"small"},{default:l(()=>[m(Y(c.getCommissionTypeText(e.commission_type)),1)]),_:2},1032,["type"])]),_:1}),t(F,{prop:"amount",label:"佣金金额",width:"120",align:"right"},{default:l(({row:e})=>[a("span",Ce,"¥"+Y(c.formatNumber(e.amount)),1)]),_:1}),t(F,{prop:"rate",label:"佣金比例",width:"100",align:"center"},{default:l(({row:e})=>[a("span",ke,Y(e.rate)+"%",1)]),_:1}),t(F,{prop:"source_agent",label:"来源代理",width:"150"},{default:l(({row:e})=>[a("div",De,[t(P,{size:32,src:e.source_agent?.avatar},{default:l(()=>[m(Y(e.source_agent?.name?.charAt(0)),1)]),_:2},1032,["src"]),a("div",Te,[a("div",Se,Y(e.source_agent?.name),1),a("div",je,Y(e.source_agent?.code||"N/A"),1)])])]),_:1}),t(F,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(({row:e})=>[t(V,{type:c.getStatusTag(e.status),size:"small"},{default:l(()=>[m(Y(c.getStatusText(e.status)),1)]),_:2},1032,["type"])]),_:1}),t(F,{prop:"created_at",label:"产生时间",width:"140"},{default:l(({row:e})=>[m(Y(c.formatDateTime(e.created_at)),1)]),_:1}),t(F,{prop:"settled_at",label:"结算时间",width:"140"},{default:l(({row:e})=>[m(Y(e.settled_at?c.formatDateTime(e.settled_at):"-"),1)]),_:1}),t(F,{label:"操作",width:"150",fixed:"right"},{default:l(({row:e})=>[t(T,{size:"small",type:"primary",link:"",onClick:a=>c.viewCommissionDetail(e)},{default:l(()=>o[18]||(o[18]=[m(" 详情 ",-1)])),_:2,__:[18]},1032,["onClick"]),"pending"===e.status?(u(),p(T,{key:0,size:"small",type:"success",link:"",onClick:a=>c.settleCommission(e)},{default:l(()=>o[19]||(o[19]=[m(" 结算 ",-1)])),_:2,__:[19]},1032,["onClick"])):h("",!0)]),_:1})]),_:1},8,["data"])),[[na,c.loading]]),a("div",Re,[t(M,{"current-page":c.recordPagination.current,"onUpdate:currentPage":o[4]||(o[4]=e=>c.recordPagination.current=e),"page-size":c.recordPagination.size,"onUpdate:pageSize":o[5]||(o[5]=e=>c.recordPagination.size=e),total:c.recordPagination.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:c.handleRecordSizeChange,onCurrentChange:c.handleRecordCurrentChange},null,8,["current-page","page-size","total"])])]),_:1}),t(B,{label:"提现记录",name:"withdrawals"},{default:l(()=>[s((u(),p(A,{data:c.withdrawalRecords,class:"modern-table"},{default:l(()=>[t(F,{prop:"withdraw_no",label:"提现单号",width:"140"}),t(F,{prop:"amount",label:"提现金额",width:"120",align:"right"},{default:l(({row:e})=>[a("span",ze,"¥"+Y(c.formatNumber(e.amount)),1)]),_:1}),t(F,{prop:"bank_info",label:"收款账户","min-width":"200"},{default:l(({row:e})=>[a("div",xe,[a("div",We,Y(e.bank_info?.bank_name),1),a("div",Ve,Y(e.bank_info?.account_no),1)])]),_:1}),t(F,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(({row:e})=>[t(V,{type:c.getWithdrawStatusTag(e.status),size:"small"},{default:l(()=>[m(Y(c.getWithdrawStatusText(e.status)),1)]),_:2},1032,["type"])]),_:1}),t(F,{prop:"created_at",label:"申请时间",width:"140"},{default:l(({row:e})=>[m(Y(c.formatDateTime(e.created_at)),1)]),_:1}),t(F,{prop:"processed_at",label:"处理时间",width:"140"},{default:l(({row:e})=>[m(Y(e.processed_at?c.formatDateTime(e.processed_at):"-"),1)]),_:1}),t(F,{label:"操作",width:"100",fixed:"right"},{default:l(({row:e})=>[t(T,{size:"small",type:"primary",link:"",onClick:a=>c.viewWithdrawDetail(e)},{default:l(()=>o[20]||(o[20]=[m(" 详情 ",-1)])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[na,c.loading]])]),_:1}),t(B,{label:"佣金规则",name:"rules"},{default:l(()=>[a("div",Ue,[a("div",Fe,[o[25]||(o[25]=a("h3",null,"💰 佣金计算规则",-1)),a("div",Pe,[a("div",Ae,[a("div",Me,[t(D,null,{default:l(()=>[t(c.User)]),_:1})]),o[21]||(o[21]=a("div",{class:"rule-content"},[a("h4",null,"直推佣金"),a("p",null,"直接推广用户产生订单的佣金"),a("div",{class:"rule-rate"},"佣金比例：10-15%")],-1))]),a("div",Be,[a("div",Le,[t(D,null,{default:l(()=>[t(c.Connection)]),_:1})]),o[22]||(o[22]=a("div",{class:"rule-content"},[a("h4",null,"团队佣金"),a("p",null,"下级代理商推广产生的团队佣金"),a("div",{class:"rule-rate"},"佣金比例：3-5%")],-1))]),a("div",Oe,[a("div",Ye,[t(D,null,{default:l(()=>[t(c.Trophy)]),_:1})]),o[23]||(o[23]=a("div",{class:"rule-content"},[a("h4",null,"管理奖励"),a("p",null,"团队管理和业绩达标的额外奖励"),a("div",{class:"rule-rate"},"奖励比例：1-3%")],-1))]),a("div",Ne,[a("div",Ge,[t(D,null,{default:l(()=>[t(c.Star)]),_:1})]),o[24]||(o[24]=a("div",{class:"rule-content"},[a("h4",null,"特殊奖励"),a("p",null,"平台活动和特殊任务的奖励佣金"),a("div",{class:"rule-rate"},"奖励金额：不定")],-1))])])]),o[26]||(o[26]=a("div",{class:"rule-section"},[a("h3",null,"📋 结算规则"),a("ul",{class:"rule-list"},[a("li",null,"佣金每日统计，T+1结算到账"),a("li",null,"最低提现金额：100元"),a("li",null,"提现手续费：2%（平台承担）"),a("li",null,"工作日提现当日处理，非工作日顺延"),a("li",null,"单次提现限额：50,000元")])],-1))])]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(ia,{modelValue:c.showHelpDialog,"onUpdate:modelValue":o[8]||(o[8]=e=>c.showHelpDialog=e),title:"代理商佣金管理功能说明",width:"1000px",class:"help-dialog"},{default:l(()=>[a("div",Ie,[o[36]||(o[36]=a("div",{class:"help-section"},[a("h3",null,"💰 功能概述"),a("p",null,"代理商佣金管理是平台分销体系的核心功能，提供全面的佣金计算、结算、提现管理，帮助代理商实时掌握收益状况，高效管理资金流转。")],-1)),a("div",Ee,[o[30]||(o[30]=a("h3",null,"🚀 核心功能模块",-1)),t(aa,{gutter:20},{default:l(()=>[t(re,{span:8},{default:l(()=>[a("div",He,[a("div",Qe,[t(D,null,{default:l(()=>[t(c.Medal)]),_:1})]),o[27]||(o[27]=a("div",{class:"feature-content"},[a("h4",null,"佣金统计"),a("p",null,"实时统计累计佣金、可提现金额、已提现金额等关键指标")],-1))])]),_:1}),t(re,{span:8},{default:l(()=>[a("div",$e,[a("div",qe,[t(D,null,{default:l(()=>[t(c.List)]),_:1})]),o[28]||(o[28]=a("div",{class:"feature-content"},[a("h4",null,"佣金记录"),a("p",null,"详细记录每笔佣金的来源、类型、金额和结算状态")],-1))])]),_:1}),t(re,{span:8},{default:l(()=>[a("div",Xe,[a("div",Ze,[t(D,null,{default:l(()=>[t(c.CreditCard)]),_:1})]),o[29]||(o[29]=a("div",{class:"feature-content"},[a("h4",null,"提现管理"),a("p",null,"在线申请提现，查看提现记录和处理状态")],-1))])]),_:1})]),_:1})]),a("div",Ke,[o[35]||(o[35]=a("h3",null,"📝 操作指南",-1)),t(sa,{modelValue:c.activeGuides,"onUpdate:modelValue":o[7]||(o[7]=e=>c.activeGuides=e)},{default:l(()=>[t(la,{title:"如何查看佣金记录？",name:"view-commission"},{default:l(()=>[a("div",Je,[o[32]||(o[32]=a("ol",null,[a("li",null,'在"佣金记录"标签页查看所有佣金明细'),a("li",null,"使用筛选条件按时间、类型、状态查询"),a("li",null,'点击"详情"查看具体的佣金计算信息'),a("li",null,"关注佣金状态：待结算、已结算、已提现")],-1)),t(ta,{type:"info",closable:!1},{default:l(()=>o[31]||(o[31]=[m(" 💡 提示：佣金记录按产生时间倒序排列，最新的记录在前 ",-1)])),_:1,__:[31]})])]),_:1}),t(la,{title:"如何申请提现？",name:"withdraw-process"},{default:l(()=>[a("div",ea,[o[34]||(o[34]=a("ol",null,[a("li",null,"确保有足够的可提现佣金（≥100元）"),a("li",null,'点击"申请提现"按钮'),a("li",null,"填写提现金额和收款账户信息"),a("li",null,"提交申请后等待平台审核"),a("li",null,"审核通过后资金将转入指定账户")],-1)),t(ta,{type:"warning",closable:!1},{default:l(()=>o[33]||(o[33]=[m(" ⚠️ 注意：请确保收款账户信息准确，避免提现失败 ",-1)])),_:1,__:[33]})])]),_:1})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-557c53f8"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/agent/AgentCommission.vue"]]);export{aa as default};
