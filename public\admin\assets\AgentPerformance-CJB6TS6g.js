/* empty css             *//* empty css                   *//* empty css                   *//* empty css                         *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                *//* empty css                    *//* empty css                  *//* empty css                        *//* empty css               *//* empty css                       *//* empty css                        *//* empty css                *//* empty css                     *//* empty css                  */import{l as e,m as a,q as t,r as l,o as r,I as s,w as o,G as n,A as i,B as d,F as c,Y as u,ag as p,M as m,c as g,E as f,K as _,z as h,D as v,C as b}from"./vue-vendor-BcnDv-68.js";import{u as y,a7 as w,ah as C,a1 as k,V as j,U as A,W as D,X as F,aE as x,at as S,aB as V,a9 as z,aT as U,aD as P,av as T,aw as E,az as M,aF as L,ae as R,aO as G,ax as O,b1 as I,ag as $,a2 as q,a3 as N,a5 as H,a6 as B,Z as Q,s as Y,Y as Z,_ as K,$ as X,ai as J,aj as W,aG as ee,bg as ae,bp as te,an as le,ak as re,aK as se,aX as oe,aY as ne,aZ as ie}from"./element-plus-C2UshkXo.js";import{C as de,A as ce,D as ue,p as pe,a as me,L as ge}from"./LineChart-tPEDyfYE.js";import{_ as fe}from"./index-eUTsTR3J.js";import{a as _e}from"./agent-CxsSQ9z5.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const he={ref:"chartRef"};const ve=fe({__name:"DoughnutChart",props:{data:{type:Object,required:!0},options:{type:Object,default:()=>({})},height:{type:String,default:"400px"}},setup(e,{expose:a}){a(),de.register(ce,ue,pe,me);const t=e,n=l();let i=null;const d={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}},tooltip:{callbacks:{label:function(e){const a=e.label||"",t=e.parsed;return`${a}: ${t} (${(t/e.dataset.data.reduce((e,a)=>e+a,0)*100).toFixed(1)}%)`}}}},cutout:"60%"},c=()=>{i&&i.destroy();const e=n.value.getContext("2d");i=new de(e,{type:"doughnut",data:t.data,options:{...d,...t.options}})},u=()=>{i&&(i.data=t.data,i.options={...d,...t.options},i.update())};r(()=>{c()}),s(()=>{i&&i.destroy()}),o(()=>t.data,u,{deep:!0}),o(()=>t.options,u,{deep:!0});const p={props:t,chartRef:n,get chartInstance(){return i},set chartInstance(e){i=e},defaultOptions:d,createChart:c,updateChart:u,ref:l,onMounted:r,watch:o,onUnmounted:s,get ChartJS(){return de},get ArcElement(){return ce},get DoughnutController(){return ue},get Tooltip(){return pe},get Legend(){return me}};return Object.defineProperty(p,"__isScriptSetup",{enumerable:!1,value:!0}),p}},[["render",function(l,r,s,o,n,i){return a(),e("div",{class:"doughnut-chart",style:y({height:s.height})},[t("canvas",he,null,512)],4)}],["__scopeId","data-v-ee655039"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/Charts/DoughnutChart.vue"]]),be={class:"modern-agent-performance performance-container"},ye={class:"page-header"},we={class:"header-content"},Ce={class:"header-left"},ke={class:"header-icon"},je={class:"header-actions"},Ae={class:"stats-section"},De={class:"stats-grid"},Fe={class:"stat-content"},xe={class:"stat-value"},Se={class:"stat-label"},Ve={class:"card-header"},ze={class:"chart-controls"},Ue={class:"card-header"},Pe={class:"header-left"},Te={class:"header-right"},Ee={class:"agent-info"},Me={class:"agent-details"},Le={class:"agent-name"},Re={class:"agent-code"},Ge={class:"agent-type"},Oe={class:"amount primary"},Ie={class:"count"},$e={class:"conversion-rate"},qe={class:"rate-text"},Ne={class:"performance-score"},He={class:"score-text"},Be={class:"pagination"},Qe={class:"help-content"},Ye={class:"help-section"},Ze={class:"metric-item"},Ke={class:"metric-icon"},Xe={class:"metric-item"},Je={class:"metric-icon"},We={class:"metric-item"},ea={class:"metric-icon"},aa={class:"metric-item"},ta={class:"metric-icon"},la={class:"help-section"},ra={class:"help-section"},sa={class:"guide-content"},oa={class:"guide-content"};const na=fe({__name:"AgentPerformance",setup(e,{expose:a}){a();const t=p(),s=l(!1),o=l(!0),n=l(!1),i=l(1),d=l(20),c=l("table"),u=l("month"),f=m({period:"month",agentType:"",ranking:""}),_=l([{key:"total_agents",label:"总代理商数",value:"0",icon:"User",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"ArrowUp",change:"+8%"},{key:"top_performers",label:"优秀代理商",value:"0",icon:"Star",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"ArrowUp",change:"+12%"},{key:"total_performance",label:"总绩效得分",value:"0",icon:"TrendCharts",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"ArrowUp",change:"+15.3%"},{key:"avg_conversion",label:"平均转化率",value:"0%",icon:"Money",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"ArrowUp",change:"+3.2%"}]),h=l({data:[],total:0}),v=l({labels:[],datasets:[{label:"绩效得分",data:[],borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]}),b=l({labels:["优秀","良好","一般","需改进"],datasets:[{data:[0,0,0,0],backgroundColor:["#67C23A","#409EFF","#E6A23C","#F56C6C"]}]}),y=l(["trend"]),w=l([{grade:"优秀",color:"success",score_range:"90-100",stars:5,description:"各项指标表现卓越",benefits:"最高佣金比例、优先推广资源、专属客服"},{grade:"良好",color:"primary",score_range:"80-89",stars:4,description:"大部分指标表现良好",benefits:"较高佣金比例、推广支持、定期培训"},{grade:"一般",color:"warning",score_range:"70-79",stars:3,description:"基本达到要求",benefits:"标准佣金比例、基础推广工具"},{grade:"需改进",color:"danger",score_range:"60-69",stars:2,description:"多项指标需要改进",benefits:"基础佣金、改进计划指导"}]),C=()=>{i.value=1,j()},k=async()=>{try{o.value=!0,await new Promise(e=>setTimeout(e,300));const e={total_agents:156,top_performers:23,total_performance:8750,avg_conversion:12.5};_.value[0].value=e.total_agents.toString(),_.value[1].value=e.top_performers.toString(),_.value[2].value=e.total_performance.toString(),_.value[3].value=e.avg_conversion.toFixed(1)+"%"}catch(e){F.error("加载统计数据失败")}finally{o.value=!1}},j=async()=>{try{s.value=!0,await new Promise(e=>setTimeout(e,500)),h.value={data:[{id:1,agent_name:"张三代理",agent_code:"AG001",agent_type:"platform",total_commission:15678.9,total_users:234,conversion_rate:15.8,monthly_growth:18.5,performance_score:95,star_rating:5,last_active:"2024-01-15"},{id:2,agent_name:"李四企业",agent_code:"AG002",agent_type:"substation",total_commission:12450.3,total_users:189,conversion_rate:12.3,monthly_growth:12.8,performance_score:88,star_rating:4,last_active:"2024-01-14"},{id:3,agent_name:"王五渠道",agent_code:"AG003",agent_type:"platform",total_commission:8956.78,total_users:156,conversion_rate:9.5,monthly_growth:-2.3,performance_score:76,star_rating:3,last_active:"2024-01-13"}],total:3}}catch(e){F.error("加载绩效数据失败")}finally{s.value=!1}},A=async()=>{try{const e={week:{labels:["周一","周二","周三","周四","周五","周六","周日"],data:[75,78,82,85,87,89,92]},month:{labels:Array.from({length:30},(e,a)=>`${a+1}日`),data:Array.from({length:30},()=>Math.floor(20*Math.random())+75)},quarter:{labels:["1月","2月","3月"],data:[78,85,92]}}[u.value];v.value={labels:e.labels,datasets:[{label:"绩效得分",data:e.data,borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]},b.value={labels:["优秀","良好","一般","需改进"],datasets:[{data:[23,45,67,21],backgroundColor:["#67C23A","#409EFF","#E6A23C","#F56C6C"]}]}}catch(e){F.error("加载趋势数据失败")}};r(()=>{k(),j(),A()});const D={router:t,loading:s,statsLoading:o,showHelpDialog:n,currentPage:i,pageSize:d,viewMode:c,chartPeriod:u,filterForm:f,performanceStatCards:_,agents:h,performanceTrendData:v,performanceDistributionData:b,chartOptions:{responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0}}},doughnutOptions:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}},activeGuides:y,performanceGrades:w,handleFilter:C,resetFilter:()=>{Object.keys(f).forEach(e=>{f[e]="period"===e?"month":""}),C()},handleExport:async()=>{try{F.success("绩效报表导出成功")}catch(e){F.error("导出失败")}},refreshAllData:async()=>{await Promise.all([k(),j(),A()]),F.success("数据刷新成功")},loadStats:k,loadAgentPerformance:j,loadPerformanceTrend:A,viewDetails:e=>{t.push(`/admin/agents/detail/${e.id}`)},viewAnalytics:e=>{t.push(`/admin/agents/analytics/${e.id}`)},handleSizeChange:e=>{d.value=e,j()},handleCurrentChange:e=>{i.value=e,j()},formatNumber:e=>e.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),formatDate:e=>new Date(e).toLocaleDateString("zh-CN"),getAgentTypeText:e=>({platform:"平台代理",substation:"分站代理"}[e]||"未知"),getRankClass:e=>e<=3?"rank-top":e<=10?"rank-good":"rank-normal",getProgressColor:e=>e>=15?"#67C23A":e>=10?"#409EFF":e>=5?"#E6A23C":"#F56C6C",ref:l,reactive:m,onMounted:r,computed:g,get useRouter(){return p},get ElMessage(){return F},get ElMessageBox(){return $},get TrendCharts(){return I},get Download(){return O},get QuestionFilled(){return G},get Refresh(){return R},get Search(){return L},get RefreshLeft(){return M},get List(){return E},get Grid(){return T},get Avatar(){return P},get Money(){return U},get User(){return z},get Star(){return V},get ArrowUp(){return S},get ArrowDown(){return x},LineChart:ge,DoughnutChart:ve,get agentApi(){return _e}};return Object.defineProperty(D,"__isScriptSetup",{enumerable:!1,value:!0}),D}},[["render",function(l,r,s,o,p,m){const g=C,F=k,x=B,S=H,V=N,z=q,U=j,P=X,T=K,E=Z,M=A,L=re,R=se,G=W,O=ee,I=ae,$=te,de=J,ce=le,ue=ie,pe=ne,me=oe,ge=D,fe=w;return a(),e("div",be,[t("div",ye,[t("div",we,[t("div",Ce,[t("div",ke,[n(g,{size:"24"},{default:i(()=>[n(o.TrendCharts)]),_:1})]),r[11]||(r[11]=t("div",{class:"header-text"},[t("h1",null,"代理商绩效分析"),t("p",null,"深度分析代理商业绩表现，提供数据驱动的业务洞察")],-1))]),t("div",je,[n(F,{onClick:o.handleExport,class:"action-btn secondary"},{default:i(()=>[n(g,null,{default:i(()=>[n(o.Download)]),_:1}),r[12]||(r[12]=f(" 导出报表 ",-1))]),_:1,__:[12]}),n(F,{onClick:r[0]||(r[0]=e=>o.showHelpDialog=!0),class:"action-btn secondary"},{default:i(()=>[n(g,null,{default:i(()=>[n(o.QuestionFilled)]),_:1}),r[13]||(r[13]=f(" 功能说明 ",-1))]),_:1,__:[13]}),n(F,{type:"primary",onClick:o.refreshAllData,class:"action-btn primary"},{default:i(()=>[n(g,null,{default:i(()=>[n(o.Refresh)]),_:1}),r[14]||(r[14]=f(" 刷新数据 ",-1))]),_:1,__:[14]})])])]),n(U,{class:"filter-card"},{default:i(()=>[n(z,{inline:!0,model:o.filterForm,onSubmit:_(o.handleFilter,["prevent"])},{default:i(()=>[n(V,{label:"统计周期"},{default:i(()=>[n(S,{modelValue:o.filterForm.period,"onUpdate:modelValue":r[1]||(r[1]=e=>o.filterForm.period=e),placeholder:"选择统计周期",class:"filter-select"},{default:i(()=>[n(x,{label:"今日",value:"today"}),n(x,{label:"近7天",value:"week"}),n(x,{label:"近30天",value:"month"}),n(x,{label:"近90天",value:"quarter"}),n(x,{label:"近1年",value:"year"})]),_:1},8,["modelValue"])]),_:1}),n(V,{label:"代理商类型"},{default:i(()=>[n(S,{modelValue:o.filterForm.agentType,"onUpdate:modelValue":r[2]||(r[2]=e=>o.filterForm.agentType=e),placeholder:"全部类型",clearable:"",class:"filter-select"},{default:i(()=>[n(x,{label:"平台代理商",value:"platform"}),n(x,{label:"分站代理商",value:"substation"})]),_:1},8,["modelValue"])]),_:1}),n(V,{label:"绩效排名"},{default:i(()=>[n(S,{modelValue:o.filterForm.ranking,"onUpdate:modelValue":r[3]||(r[3]=e=>o.filterForm.ranking=e),placeholder:"全部排名",clearable:"",class:"filter-select"},{default:i(()=>[n(x,{label:"前10名",value:"top10"}),n(x,{label:"前20名",value:"top20"}),n(x,{label:"前50名",value:"top50"})]),_:1},8,["modelValue"])]),_:1}),n(V,null,{default:i(()=>[n(F,{type:"primary",onClick:o.handleFilter,class:"search-btn"},{default:i(()=>[n(g,null,{default:i(()=>[n(o.Search)]),_:1}),r[15]||(r[15]=f(" 查询 ",-1))]),_:1,__:[15]}),n(F,{onClick:o.resetFilter,class:"reset-btn"},{default:i(()=>[n(g,null,{default:i(()=>[n(o.RefreshLeft)]),_:1}),r[16]||(r[16]=f(" 重置 ",-1))]),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),_:1}),t("div",Ae,[d((a(),e("div",De,[(a(!0),e(c,null,u(o.performanceStatCards,l=>(a(),e("div",{class:"stat-card",key:l.key},[t("div",{class:"stat-icon",style:y({background:l.color})},[n(g,{size:"20"},{default:i(()=>[(a(),h(v(l.icon)))]),_:2},1024)],4),t("div",Fe,[t("div",xe,Q(l.value),1),t("div",Se,Q(l.label),1)]),t("div",{class:Y(["stat-trend",l.trend])},[n(g,{size:"14"},{default:i(()=>[(a(),h(v(l.trendIcon)))]),_:2},1024),t("span",null,Q(l.change),1)],2)]))),128))])),[[fe,o.statsLoading]])]),n(M,{gutter:20,class:"chart-section"},{default:i(()=>[n(E,{span:16},{default:i(()=>[n(U,null,{header:i(()=>[t("div",Ve,[r[20]||(r[20]=t("h3",null,"绩效趋势分析",-1)),t("div",ze,[n(T,{modelValue:o.chartPeriod,"onUpdate:modelValue":r[4]||(r[4]=e=>o.chartPeriod=e),size:"small",onChange:o.loadPerformanceTrend},{default:i(()=>[n(P,{label:"week"},{default:i(()=>r[17]||(r[17]=[f("近7天",-1)])),_:1,__:[17]}),n(P,{label:"month"},{default:i(()=>r[18]||(r[18]=[f("近30天",-1)])),_:1,__:[18]}),n(P,{label:"quarter"},{default:i(()=>r[19]||(r[19]=[f("近3个月",-1)])),_:1,__:[19]})]),_:1},8,["modelValue"])])])]),default:i(()=>[n(o.LineChart,{data:o.performanceTrendData,options:o.chartOptions,height:"350px"},null,8,["data"])]),_:1})]),_:1}),n(E,{span:8},{default:i(()=>[n(U,null,{header:i(()=>r[21]||(r[21]=[t("div",{class:"card-header"},[t("h3",null,"绩效分布")],-1)])),default:i(()=>[n(o.DoughnutChart,{data:o.performanceDistributionData,options:o.doughnutOptions,height:"350px"},null,8,["data"])]),_:1})]),_:1})]),_:1}),n(U,{class:"table-card"},{header:i(()=>[t("div",Ue,[t("div",Pe,[r[22]||(r[22]=t("h3",null,"代理商绩效排行榜",-1)),n(L,{size:"small",type:"info"},{default:i(()=>[f("共 "+Q(o.agents.total||0)+" 个代理商",1)]),_:1})]),t("div",Te,[n(R,null,{default:i(()=>[n(F,{size:"small",type:"table"===o.viewMode?"primary":"",onClick:r[5]||(r[5]=e=>o.viewMode="table")},{default:i(()=>[n(g,null,{default:i(()=>[n(o.List)]),_:1})]),_:1},8,["type"]),n(F,{size:"small",type:"card"===o.viewMode?"primary":"",onClick:r[6]||(r[6]=e=>o.viewMode="card")},{default:i(()=>[n(g,null,{default:i(()=>[n(o.Grid)]),_:1})]),_:1},8,["type"])]),_:1})])])]),default:i(()=>["table"===o.viewMode?d((a(),h(de,{key:0,data:o.agents.data,class:"modern-table"},{default:i(()=>[n(G,{label:"排名",width:"80",align:"center"},{default:i(({$index:e})=>[t("div",{class:Y(["rank-badge",o.getRankClass(e+1)])},Q(e+1),3)]),_:1}),n(G,{label:"代理商信息",width:"220"},{default:i(({row:e})=>[t("div",Ee,[n(O,{size:40,class:"agent-avatar"},{default:i(()=>[n(g,null,{default:i(()=>[n(o.Avatar)]),_:1})]),_:1}),t("div",Me,[t("div",Le,Q(e.agent_name),1),t("div",Re,"编码: "+Q(e.agent_code),1),t("div",Ge,Q(o.getAgentTypeText(e.agent_type)),1)])])]),_:1}),n(G,{prop:"total_commission",label:"总佣金",width:"120",sortable:""},{default:i(({row:e})=>[t("span",Oe,"¥"+Q(o.formatNumber(e.total_commission)),1)]),_:1}),n(G,{prop:"total_users",label:"推广用户",width:"100",sortable:""},{default:i(({row:e})=>[t("span",Ie,Q(e.total_users),1)]),_:1}),n(G,{prop:"conversion_rate",label:"转化率",width:"100",sortable:""},{default:i(({row:e})=>[t("div",$e,[n(I,{percentage:e.conversion_rate,"stroke-width":6,"show-text":!1,color:o.getProgressColor(e.conversion_rate)},null,8,["percentage","color"]),t("span",qe,Q(e.conversion_rate)+"%",1)])]),_:1}),n(G,{prop:"monthly_growth",label:"月增长率",width:"120"},{default:i(({row:e})=>[t("span",{class:Y(["growth-rate",e.monthly_growth>=0?"positive":"negative"])},Q(e.monthly_growth>=0?"+":"")+Q(e.monthly_growth)+"% ",3)]),_:1}),n(G,{prop:"performance_score",label:"绩效评分",width:"120",sortable:""},{default:i(({row:e})=>[t("div",Ne,[n($,{modelValue:e.star_rating,"onUpdate:modelValue":a=>e.star_rating=a,disabled:"","show-score":"","text-color":"#ff9900","score-template":"{value}"},null,8,["modelValue","onUpdate:modelValue"]),t("div",He,Q(e.performance_score)+"分",1)])]),_:1}),n(G,{prop:"last_active",label:"最后活跃",width:"120"},{default:i(({row:e})=>[f(Q(o.formatDate(e.last_active)),1)]),_:1}),n(G,{label:"操作",width:"150",fixed:"right"},{default:i(({row:e})=>[n(F,{link:"",type:"primary",size:"small",onClick:a=>o.viewDetails(e)},{default:i(()=>r[23]||(r[23]=[f(" 查看详情 ",-1)])),_:2,__:[23]},1032,["onClick"]),n(F,{link:"",type:"info",size:"small",onClick:a=>o.viewAnalytics(e)},{default:i(()=>r[24]||(r[24]=[f(" 分析报告 ",-1)])),_:2,__:[24]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[fe,o.loading]]):b("",!0),t("div",Be,[n(ce,{"current-page":o.currentPage,"onUpdate:currentPage":r[7]||(r[7]=e=>o.currentPage=e),"page-size":o.pageSize,"onUpdate:pageSize":r[8]||(r[8]=e=>o.pageSize=e),total:o.agents.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["current-page","page-size","total"])])]),_:1}),n(ge,{modelValue:o.showHelpDialog,"onUpdate:modelValue":r[10]||(r[10]=e=>o.showHelpDialog=e),title:"代理商绩效分析功能说明",width:"1000px",class:"help-dialog"},{default:i(()=>[t("div",Qe,[r[36]||(r[36]=t("div",{class:"help-section"},[t("h3",null,"📊 功能概述"),t("p",null,"代理商绩效分析是一个综合性的数据分析工具，通过多维度指标评估代理商的业务表现，帮助您识别优秀代理商、发现改进机会，制定更有效的激励策略。")],-1)),t("div",Ye,[r[29]||(r[29]=t("h3",null,"🎯 核心绩效指标",-1)),n(M,{gutter:20},{default:i(()=>[n(E,{span:12},{default:i(()=>[t("div",Ze,[t("div",Ke,[n(g,null,{default:i(()=>[n(o.Money)]),_:1})]),r[25]||(r[25]=t("div",{class:"metric-content"},[t("h4",null,"佣金收入"),t("p",null,"代理商通过推广获得的总佣金收入，反映其业务价值"),t("div",{class:"metric-formula"},"计算公式: 直推佣金 + 团队佣金 + 奖励佣金")],-1))])]),_:1}),n(E,{span:12},{default:i(()=>[t("div",Xe,[t("div",Je,[n(g,null,{default:i(()=>[n(o.User)]),_:1})]),r[26]||(r[26]=t("div",{class:"metric-content"},[t("h4",null,"推广用户数"),t("p",null,"代理商直接或间接推广的有效用户总数"),t("div",{class:"metric-formula"},"计算公式: 直推用户 + 下级代理推广用户")],-1))])]),_:1}),n(E,{span:12},{default:i(()=>[t("div",We,[t("div",ea,[n(g,null,{default:i(()=>[n(o.TrendCharts)]),_:1})]),r[27]||(r[27]=t("div",{class:"metric-content"},[t("h4",null,"转化率"),t("p",null,"访问推广链接的用户中实际付费的比例"),t("div",{class:"metric-formula"},"计算公式: 付费用户数 ÷ 访问用户数 × 100%")],-1))])]),_:1}),n(E,{span:12},{default:i(()=>[t("div",aa,[t("div",ta,[n(g,null,{default:i(()=>[n(o.Star)]),_:1})]),r[28]||(r[28]=t("div",{class:"metric-content"},[t("h4",null,"绩效评分"),t("p",null,"综合多个维度计算得出的代理商绩效综合评分"),t("div",{class:"metric-formula"},"权重分配: 佣金收入40% + 用户数30% + 转化率20% + 活跃度10%")],-1))])]),_:1})]),_:1})]),t("div",la,[r[30]||(r[30]=t("h3",null,"🏆 绩效评级标准",-1)),n(de,{data:o.performanceGrades,style:{width:"100%"}},{default:i(()=>[n(G,{prop:"grade",label:"评级",width:"100"},{default:i(({row:e})=>[n(L,{type:e.color},{default:i(()=>[f(Q(e.grade),1)]),_:2},1032,["type"])]),_:1}),n(G,{prop:"score_range",label:"评分区间",width:"120"}),n(G,{prop:"star_rating",label:"星级",width:"100"},{default:i(({row:e})=>[n($,{modelValue:e.stars,"onUpdate:modelValue":a=>e.stars=a,disabled:"","show-score":"","text-color":"#ff9900"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),n(G,{prop:"description",label:"评级说明"}),n(G,{prop:"benefits",label:"对应权益"})]),_:1},8,["data"])]),t("div",ra,[r[35]||(r[35]=t("h3",null,"📝 操作指南",-1)),n(me,{modelValue:o.activeGuides,"onUpdate:modelValue":r[9]||(r[9]=e=>o.activeGuides=e)},{default:i(()=>[n(pe,{title:"如何查看绩效趋势？",name:"trend"},{default:i(()=>[t("div",sa,[r[32]||(r[32]=t("ol",null,[t("li",null,'在"绩效趋势分析"图表中选择时间周期'),t("li",null,"观察曲线变化，识别增长或下降趋势"),t("li",null,"对比不同时期的表现，寻找规律"),t("li",null,"结合外部因素分析趋势变化原因")],-1)),n(ue,{type:"info",closable:!1},{default:i(()=>r[31]||(r[31]=[f(" 💡 提示：关注季节性变化和营销活动对绩效的影响 ",-1)])),_:1,__:[31]})])]),_:1}),n(pe,{title:"如何分析代理商表现？",name:"analysis"},{default:i(()=>[t("div",oa,[r[34]||(r[34]=t("ol",null,[t("li",null,"查看绩效排行榜，识别头部代理商"),t("li",null,"对比不同代理商的各项指标"),t("li",null,"分析高绩效代理商的成功经验"),t("li",null,"识别低绩效代理商的问题原因"),t("li",null,"制定针对性的改进措施")],-1)),n(ue,{type:"success",closable:!1},{default:i(()=>r[33]||(r[33]=[f(" ✅ 建议：定期与代理商沟通，提供针对性的指导和支持 ",-1)])),_:1,__:[33]})])]),_:1})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-4918d2e4"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/agent/AgentPerformance.vue"]]);export{na as default};
