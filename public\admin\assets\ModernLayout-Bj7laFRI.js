/* empty css             *//* empty css                  *//* empty css                        *//* empty css                  *//* empty css                         *//* empty css                  *//* empty css                 *//* empty css                           */import{l as e,m as a,q as t,C as n,G as i,A as s,F as l,Y as o,z as r,D as d,ah as c,r as m,c as u,w as p,a8 as h,ag as g,o as f,I as b,E as y,T as v,am as C}from"./vue-vendor-BcnDv-68.js";import{a1 as k,a4 as _,c5 as S,Z as M,c6 as w,s as F,ah as L,bV as x,c7 as D,c8 as A,aG as j,c9 as P,ca as B,au as T,cb as U,cc as z,aF as E,bi as I,aH as R,cd as O,b9 as N,a9 as H,bO as G,ce as q,c1 as Q,ag as V,bh as W,aI as Y,aJ as J}from"./element-plus-C2UshkXo.js";/* empty css                     */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import{_ as K}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const Z=[{title:"核心功能",group:!0},{path:"/admin/dashboard",title:"仪表板",icon:"TrendCharts",name:"Dashboard"},{path:"/admin/analytics",title:"数据分析",icon:"DataLine",name:"DashboardAnalytics"},{path:"/data-screen",title:"数据大屏",icon:"Monitor",name:"DataScreen",isNew:!0},{title:"用户管理",group:!0},{path:"/admin/users",title:"用户管理",icon:"User",name:"UserManagement",children:[{path:"/admin/users/list",title:"用户列表",icon:"List",name:"UserList"},{path:"/admin/users/analytics",title:"用户分析",icon:"TrendCharts",name:"UserAnalytics"},{path:"/admin/users/profile",title:"个人资料",icon:"Avatar",name:"UserProfile"}]},{title:"社群功能",group:!0},{path:"/admin/community",title:"社群管理",icon:"ChatDotRound",name:"CommunityManagement",children:[{path:"/admin/community/groups",title:"群组管理",icon:"ChatDotRound",name:"GroupManagement"},{path:"/admin/community/templates",title:"模板管理",icon:"Document",name:"TemplateManagement"}]},{title:"业务管理",group:!0},{path:"/admin/agents",title:"代理商管理",icon:"Avatar",name:"AgentManagement",roles:["admin"],children:[{path:"/admin/agents/list",title:"代理商列表",icon:"List",name:"AgentList",roles:["admin"]},{path:"/admin/agents/hierarchy",title:"层级结构",icon:"Grid",name:"AgentHierarchy",roles:["admin"]},{path:"/admin/agents/commission",title:"佣金管理",icon:"Money",name:"AgentCommission",roles:["admin"]},{path:"/admin/agents/performance",title:"业绩统计",icon:"TrendCharts",name:"AgentPerformance",roles:["admin"]}]},{path:"/admin/orders",title:"订单管理",icon:"ShoppingCart",name:"OrderManagement"},{title:"财务管理",group:!0},{path:"/admin/finance",title:"财务管理",icon:"Money",name:"FinanceManagement",roles:["admin"],children:[{path:"/admin/finance/dashboard",title:"财务概览",icon:"DataBoard",name:"FinanceDashboard",roles:["admin"]},{path:"/admin/finance/transactions",title:"交易记录",icon:"CreditCard",name:"TransactionList",roles:["admin"]},{path:"/admin/finance/commission",title:"佣金日志",icon:"Coin",name:"CommissionLog",roles:["admin"]},{path:"/admin/finance/withdraw",title:"提现管理",icon:"Upload",name:"WithdrawManage",roles:["admin"]}]},{title:"支付系统",group:!0},{path:"/admin/payment",title:"支付管理",icon:"CreditCard",name:"PaymentManagement",roles:["admin"],children:[{path:"/admin/payment/settings",title:"支付设置",icon:"Setting",name:"PaymentSettings",roles:["admin"]},{path:"/admin/payment/channels",title:"支付渠道",icon:"Connection",name:"PaymentChannelManagement",roles:["admin"]},{path:"/admin/payment/orders",title:"支付订单",icon:"Tickets",name:"PaymentOrders",roles:["admin"]},{path:"/admin/payment/logs",title:"支付日志",icon:"Document",name:"PaymentLogs",roles:["admin"]}]},{title:"推广营销",group:!0},{path:"/admin/promotion",title:"分销推广",icon:"Share",name:"PromotionManagement",roles:["admin"],children:[{path:"/admin/promotion/distributors",title:"分销商管理",icon:"User",name:"DistributorManagement",roles:["admin"]},{path:"/admin/promotion/links",title:"推广链接",icon:"Link",name:"PromotionLinks",roles:["admin"]}]},{title:"安全防护",group:!0},{path:"/admin/anti-block",title:"防红系统",icon:"Lock",name:"AntiBlockSystem",roles:["admin"],children:[{path:"/admin/anti-block/dashboard",title:"防红概览",icon:"DataBoard",name:"AntiBlockDashboard",roles:["admin"]},{path:"/admin/anti-block/domains",title:"域名管理",icon:"Connection",name:"DomainManagement",roles:["admin"]},{path:"/admin/anti-block/links",title:"短链管理",icon:"Link",name:"ShortLinkManagement",roles:["admin"]},{path:"/admin/anti-block/analytics",title:"防红分析",icon:"TrendCharts",name:"AntiBlockAnalytics",roles:["admin"]},{path:"/admin/anti-block/enhanced",title:"增强防护",icon:"Star",name:"AntiBlockEnhanced",roles:["admin"],isNew:!0}]},{title:"系统管理",group:!0},{path:"/admin/permissions",title:"权限管理",icon:"Lock",name:"PermissionSystem",roles:["admin"],children:[{path:"/admin/permissions/roles",title:"角色管理",icon:"UserFilled",name:"RoleManagement",roles:["admin"]},{path:"/admin/permissions/permissions",title:"权限配置",icon:"Key",name:"PermissionManagement",roles:["admin"]}]},{path:"/admin/system",title:"系统管理",icon:"Setting",name:"SystemManagement",roles:["admin"],children:[{path:"/admin/system/settings",title:"系统设置",icon:"Setting",name:"SystemSettings",roles:["admin"]},{path:"/admin/system/monitor",title:"系统监控",icon:"Monitor",name:"SystemMonitor",roles:["admin"],isHot:!0},{path:"/admin/system/logs",title:"操作日志",icon:"Document",name:"OperationLogs",roles:["admin"]},{path:"/admin/system/notifications",title:"通知管理",icon:"Bell",name:"NotificationManagement",roles:["admin"]},{path:"/admin/system/data-export",title:"数据导出",icon:"Download",name:"DataExport",roles:["admin"]},{path:"/admin/system/file-management",title:"文件管理",icon:"Folder",name:"FileManagement",roles:["admin"]},{path:"/admin/system/function-test",title:"功能测试",icon:"Tools",name:"FunctionTest",roles:["admin"]},{path:"/admin/system/user-guide",title:"使用指南",icon:"QuestionFilled",name:"UserGuide"}]}],X=(e,a=[])=>e.filter(e=>{if(e.group)return!0;if(e.roles&&e.roles.length>0){if(!e.roles.some(e=>a.includes(e)))return!1}return!e.children||(e.children=X(e.children,a),e.children.length>0)}),$=(e,a=[])=>(e.forEach(e=>{e.group||(a.push(e),e.children&&$(e.children,a))}),a),ee=(e,a)=>{const t=[],n=(e,a,i=[])=>{for(const s of e){if(s.group)continue;const e=[...i,s];if(s.path===a)return t.push(...e),!0;if(s.children&&n(s.children,a,e))return!0}return!1};return n(e,a),t},ae={__name:"OptimizedSidebar",props:{collapse:{type:Boolean,default:!1},userRoles:{type:Array,default:()=>["admin"]}},emits:["collapse-change","menu-select"],setup(e,{expose:a,emit:t}){a();const n=e,i=t,s=c(),l=m(""),o=m(n.collapse),r=u(()=>s.path),d=u(()=>X(Z,n.userRoles)),h=u(()=>{if(!l.value)return d.value;const e=l.value.toLowerCase();return $(d.value).filter(a=>a.title.toLowerCase().includes(e)||a.name&&a.name.toLowerCase().includes(e))});p(()=>n.collapse,e=>{o.value=e});const g={props:n,emit:i,route:s,searchQuery:l,isCollapsed:o,activeMenu:r,filteredMenuByRole:d,filteredMenu:h,toggleCollapse:()=>{o.value=!o.value,i("collapse-change",o.value)},handleSearch:e=>{},handleMenuSelect:(e,a)=>{i("menu-select",{index:e,indexPath:a})},getMenuItemClass:e=>({"is-new":e.isNew,"is-hot":e.isHot,"has-badge":e.badge&&e.badge>0,"is-active":s.path===e.path}),ref:m,computed:u,watch:p,get useRoute(){return c},get Search(){return E},get StarFilled(){return z},get Promotion(){return U},get UserFilled(){return T},get Expand(){return B},get Fold(){return P},get sidebarMenuConfig(){return Z},get filterMenuByRole(){return X},get flattenMenu(){return $}};return Object.defineProperty(g,"__isScriptSetup",{enumerable:!1,value:!0}),g}},te={class:"optimized-sidebar"},ne={class:"sidebar-header"},ie={key:0,class:"sidebar-search"},se={key:0,class:"menu-group-title"},le={class:"menu-title"},oe={class:"menu-title"},re={class:"menu-title"},de={key:1,class:"sidebar-footer"},ce={class:"user-info"};const me=K(ae,[["render",function(c,m,u,p,h,g){const f=k,b=_,y=L,v=x,C=w,P=D,B=S,T=A,U=j;return a(),e("div",te,[t("div",ne,[m[1]||(m[1]=t("div",{class:"logo-container"},[t("div",{class:"logo-placeholder"},"L"),t("span",{class:"brand-text"},"晨鑫流量变现系统")],-1)),i(f,{icon:p.isCollapsed?"Expand":"Fold",circle:"",size:"small",class:"collapse-btn",onClick:p.toggleCollapse},null,8,["icon"])]),p.isCollapsed?n("",!0):(a(),e("div",ie,[i(b,{modelValue:p.searchQuery,"onUpdate:modelValue":m[0]||(m[0]=e=>p.searchQuery=e),placeholder:"搜索菜单...","prefix-icon":p.Search,size:"small",clearable:"",onInput:p.handleSearch},null,8,["modelValue","prefix-icon"])])),i(T,{class:"sidebar-scrollbar"},{default:s(()=>[i(B,{"default-active":p.activeMenu,collapse:p.isCollapsed,"unique-opened":!0,"collapse-transition":!1,router:"",class:"sidebar-menu",onSelect:p.handleMenuSelect},{default:s(()=>[(a(!0),e(l,null,o(p.filteredMenu,(c,m)=>(a(),e(l,{key:m},[c.group&&!p.isCollapsed?(a(),e("div",se,M(c.title),1)):c.group?n("",!0):(a(),e(l,{key:1},[c.children&&c.children.length>0?(a(),r(P,{key:0,index:c.path,class:"sidebar-submenu"},{title:s(()=>[i(y,null,{default:s(()=>[(a(),r(d(c.icon)))]),_:2},1024),t("span",le,M(c.title),1),c.badge?(a(),r(v,{key:0,value:c.badge,type:c.badgeType||"danger",class:"menu-badge"},null,8,["value","type"])):n("",!0),c.isNew?(a(),r(y,{key:1,class:"new-indicator",title:"新功能"},{default:s(()=>[i(p.StarFilled)]),_:1})):n("",!0),c.isHot?(a(),r(y,{key:2,class:"hot-indicator",title:"热门"},{default:s(()=>[i(p.Promotion)]),_:1})):n("",!0)]),default:s(()=>[(a(!0),e(l,null,o(c.children,e=>(a(),r(C,{key:e.path,index:e.path,class:F(p.getMenuItemClass(e))},{title:s(()=>[t("span",oe,M(e.title),1),e.badge?(a(),r(v,{key:0,value:e.badge,type:e.badgeType||"danger",class:"menu-badge"},null,8,["value","type"])):n("",!0),e.isNew?(a(),r(y,{key:1,class:"new-indicator",title:"新功能"},{default:s(()=>[i(p.StarFilled)]),_:1})):n("",!0),e.isHot?(a(),r(y,{key:2,class:"hot-indicator",title:"热门"},{default:s(()=>[i(p.Promotion)]),_:1})):n("",!0)]),default:s(()=>[i(y,null,{default:s(()=>[(a(),r(d(e.icon)))]),_:2},1024)]),_:2},1032,["index","class"]))),128))]),_:2},1032,["index"])):(a(),r(C,{key:1,index:c.path,class:F(p.getMenuItemClass(c))},{title:s(()=>[t("span",re,M(c.title),1),c.badge?(a(),r(v,{key:0,value:c.badge,type:c.badgeType||"danger",class:"menu-badge"},null,8,["value","type"])):n("",!0),c.isNew?(a(),r(y,{key:1,class:"new-indicator",title:"新功能"},{default:s(()=>[i(p.StarFilled)]),_:1})):n("",!0),c.isHot?(a(),r(y,{key:2,class:"hot-indicator",title:"热门"},{default:s(()=>[i(p.Promotion)]),_:1})):n("",!0)]),default:s(()=>[i(y,null,{default:s(()=>[(a(),r(d(c.icon)))]),_:2},1024)]),_:2},1032,["index","class"]))],64))],64))),128))]),_:1},8,["default-active","collapse"])]),_:1}),p.isCollapsed?n("",!0):(a(),e("div",de,[t("div",ce,[i(U,{size:32,icon:p.UserFilled},null,8,["icon"]),m[2]||(m[2]=t("div",{class:"user-details"},[t("div",{class:"username"},"管理员"),t("div",{class:"user-role"},"超级管理员")],-1))])]))])}],["__scopeId","data-v-699f3db1"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/layout/OptimizedSidebar.vue"]]),ue={class:"modern-layout"},pe={class:"main-header"},he={class:"header-left"},ge={class:"breadcrumb-container"},fe={class:"header-right"},be={class:"user-avatar"},ye={key:0,class:"user-info"},ve={class:"user-name"},Ce={class:"user-role"},ke={class:"main-content"},_e={class:"content-wrapper"};const Se=K({__name:"ModernLayout",setup(e,{expose:a}){a();const t=c(),n=g(),i=m(!1),s=m(!1),l=m(!1),o=m(3),r=u(()=>({name:"管理员",role:"超级管理员",avatar:""})),d=u(()=>["admin"]),p=m(["Dashboard","ModernDashboard","UserList","GroupList","OrderManagement","FinanceDashboard","AntiBlockDashboard"]),h=u(()=>ee(Z,t.path)),y=()=>{s.value=!!document.fullscreenElement},v=()=>{const e=window.innerWidth;l.value=e<768,l.value&&(i.value=!0)};f(()=>{document.addEventListener("fullscreenchange",y),window.addEventListener("resize",v),v()}),b(()=>{document.removeEventListener("fullscreenchange",y),window.removeEventListener("resize",v)});const C={route:t,router:n,isCollapsed:i,isFullscreen:s,isMobile:l,notificationCount:o,userInfo:r,userRoles:d,cacheableComponents:p,breadcrumbList:h,toggleSidebar:()=>{i.value=!i.value},closeSidebar:()=>{l.value&&(i.value=!0)},handleCollapseChange:e=>{i.value=e},handleMenuSelect:e=>{l.value&&(i.value=!0)},toggleFullscreen:()=>{s.value?document.exitFullscreen?.():document.documentElement.requestFullscreen?.()},showNotifications:()=>{n.push("/admin/system/notifications")},goToProfile:()=>{n.push("/admin/users/profile")},goToSettings:()=>{n.push("/admin/system/settings")},logout:()=>{V.confirm("确定要退出登录吗？","确认退出",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{localStorage.removeItem("token"),sessionStorage.clear(),n.push("/login")}).catch(()=>{})},handleFullscreenChange:y,handleResize:v,ref:m,computed:u,onMounted:f,onUnmounted:b,get useRoute(){return c},get useRouter(){return g},get ElMessageBox(){return V},get Expand(){return B},get Fold(){return P},get FullScreen(){return Q},get Aim(){return q},get Bell(){return G},get User(){return H},get Setting(){return N},get SwitchButton(){return O},OptimizedSidebar:me,get getBreadcrumb(){return ee},get sidebarMenuConfig(){return Z}};return Object.defineProperty(C,"__isScriptSetup",{enumerable:!1,value:!0}),C}},[["render",function(c,m,u,p,g,f){const b=k,_=L,S=W,w=I,D=x,A=j,P=J,B=Y,T=R,U=h("router-view");return a(),e("div",ue,[t("div",{class:F(["sidebar-container",{"is-collapsed":p.isCollapsed}])},[i(p.OptimizedSidebar,{collapse:p.isCollapsed,"user-roles":p.userRoles,onCollapseChange:p.handleCollapseChange,onMenuSelect:p.handleMenuSelect},null,8,["collapse","user-roles"])],2),t("div",{class:F(["main-container",{"sidebar-collapsed":p.isCollapsed}])},[t("header",pe,[t("div",he,[i(b,{icon:p.isCollapsed?"Expand":"Fold",circle:"",size:"small",onClick:p.toggleSidebar,class:"collapse-btn"},null,8,["icon"]),t("div",ge,[i(w,{separator:"/",class:"app-breadcrumb"},{default:s(()=>[(a(!0),e(l,null,o(p.breadcrumbList,(e,t)=>(a(),r(S,{key:e.path,to:t===p.breadcrumbList.length-1?void 0:e.path},{default:s(()=>[e.icon?(a(),r(_,{key:0,class:"breadcrumb-icon"},{default:s(()=>[(a(),r(d(e.icon)))]),_:2},1024)):n("",!0),y(" "+M(e.title),1)]),_:2},1032,["to"]))),128))]),_:1})])]),t("div",fe,[i(b,{icon:p.isFullscreen?"Aim":"FullScreen",circle:"",size:"small",onClick:p.toggleFullscreen,class:"header-btn"},null,8,["icon"]),i(D,{value:p.notificationCount,hidden:0===p.notificationCount},{default:s(()=>[i(b,{icon:p.Bell,circle:"",size:"small",class:"header-btn",onClick:p.showNotifications},null,8,["icon"])]),_:1},8,["value","hidden"]),i(T,{class:"user-dropdown",trigger:"click"},{dropdown:s(()=>[i(B,null,{default:s(()=>[i(P,{onClick:p.goToProfile},{default:s(()=>[i(_,null,{default:s(()=>[i(p.User)]),_:1}),m[0]||(m[0]=y(" 个人资料 ",-1))]),_:1,__:[0]}),i(P,{onClick:p.goToSettings},{default:s(()=>[i(_,null,{default:s(()=>[i(p.Setting)]),_:1}),m[1]||(m[1]=y(" 系统设置 ",-1))]),_:1,__:[1]}),i(P,{divided:"",onClick:p.logout},{default:s(()=>[i(_,null,{default:s(()=>[i(p.SwitchButton)]),_:1}),m[2]||(m[2]=y(" 退出登录 ",-1))]),_:1,__:[2]})]),_:1})]),default:s(()=>[t("div",be,[i(A,{size:32,src:p.userInfo.avatar},{default:s(()=>[i(_,null,{default:s(()=>[i(p.User)]),_:1})]),_:1},8,["src"]),p.isCollapsed?n("",!0):(a(),e("div",ye,[t("div",ve,M(p.userInfo.name),1),t("div",Ce,M(p.userInfo.role),1)]))])]),_:1})])]),t("main",ke,[t("div",_e,[i(U,null,{default:s(({Component:e,route:t})=>[i(v,{name:"page-fade",mode:"out-in"},{default:s(()=>[(a(),r(C,{include:p.cacheableComponents},[(a(),r(d(e),{key:t.fullPath}))],1032,["include"]))]),_:2},1024)]),_:1})])]),m[3]||(m[3]=t("footer",{class:"main-footer"},[t("div",{class:"footer-content"},[t("span",null,"© 2024 晨鑫流量变现系统. All rights reserved."),t("span",null,"Version 1.0.0")])],-1))],2),p.isMobile&&!p.isCollapsed?(a(),e("div",{key:0,class:"mobile-overlay",onClick:p.closeSidebar})):n("",!0)])}],["__scopeId","data-v-b55ca2c2"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/layout/ModernLayout.vue"]]);export{Se as default};
