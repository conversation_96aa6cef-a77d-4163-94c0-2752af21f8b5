/* empty css             *//* empty css                   *//* empty css                             *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                        *//* empty css                         *//* empty css                    *//* empty css               */import{l as e,q as a,G as t,A as s,r as l,M as o,c,o as n,n as i,m as r,E as d,B as u,z as m,F as p,Y as _,C as h}from"./vue-vendor-BcnDv-68.js";import{a1 as f,U as v,W as g,X as b,a$ as k,aN as y,aE as w,b3 as D,bL as C,b5 as j,be as S,bM as x,bn as V,bN as z,ae as A,af as F,ag as T,ah as q,Y as U,Z as H,V as L,ai as M,aj as B,ak as E,bg as R,bf as P,aH as Q,aI as N,aJ as O,a7 as $,an as I,a5 as W,a6 as Y,a2 as G,a3 as J,a4 as X,aq as Z,ar as K,as as ee}from"./element-plus-C2UshkXo.js";import{i as ae,e as te}from"./echarts-D6CUuNS9.js";import{a as se}from"./index-D4AyIzGN.js";import{_ as le}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";const oe={getDomainHealth:()=>se.get("/anti-block/domain-health"),checkDomainHealth:e=>se.post("/anti-block/check-domain",{domain:e}),getBrowserStats:(e=7)=>se.get("/anti-block/browser-stats",{params:{days:e}}),validateGroupAccess:e=>se.post(`/anti-block/validate-access/${e}`),getAccessReport:(e,a="7days")=>se.get(`/anti-block/access-report/${e}`,{params:{period:a}})},ce={class:"anti-block-dashboard"},ne={class:"page-header"},ie={class:"header-content"},re={class:"header-actions"},de={class:"stats-section"},ue={class:"stats-card primary"},me={class:"stats-content"},pe={class:"stats-icon"},_e={class:"stats-info"},he={class:"stats-value"},fe={class:"stats-trend"},ve={class:"stats-card success"},ge={class:"stats-content"},be={class:"stats-icon"},ke={class:"stats-info"},ye={class:"stats-value"},we={class:"stats-trend positive"},De={class:"stats-card warning"},Ce={class:"stats-content"},je={class:"stats-icon"},Se={class:"stats-info"},xe={class:"stats-value"},Ve={class:"stats-card danger"},ze={class:"stats-content"},Ae={class:"stats-icon"},Fe={class:"stats-info"},Te={class:"stats-value"},qe={class:"card-header"},Ue={class:"header-actions"},He={class:"health-score"},Le={key:0},Me={key:1,class:"text-muted"},Be={class:"access-status"},Ee={class:"pagination-wrapper"},Re={class:"browser-stats"},Pe={class:"browser-info"},Qe={class:"browser-name"},Ne={class:"browser-count"},Oe={class:"trend-chart",ref:"trendChart",style:{height:"200px"}},$e={key:0,class:"domain-details"},Ie={key:0,class:"mt-4"},We={class:"check-item"},Ye={class:"check-item"},Ge={class:"check-item"},Je={class:"check-item"},Xe={class:"check-item"},Ze={class:"check-item"};const Ke=le({__name:"EnhancedDashboard",setup(e,{expose:a}){a();const t=l(!1),s=l([]),r=l({total:0,normal:0,abnormal:0,blocked:0,avg_health_score:0}),d=l([]),u=l(""),m=l(!1),p=l(!1),_=l(null),h=l(null),f=o({page:1,size:20,total:0}),v=o({domain:"",domain_type:"redirect",priority:5,remarks:""}),g=c(()=>u.value?s.value.filter(e=>{switch(u.value){case"normal":return 1===e.status;case"abnormal":return 2===e.status;case"blocked":return 3===e.status;default:return!0}}):s.value),q=async()=>{t.value=!0;try{const e=await oe.getDomainHealth();s.value=e.data.domains||[],r.value=e.data.stats||{},f.total=e.data.total||0}catch(e){b.error("获取域名列表失败")}finally{t.value=!1}},U=async()=>{try{const e=await oe.getBrowserStats(7);d.value=e.data||[]}catch(e){}},H=()=>{i(()=>{if(!h.value)return;ae(h.value).setOption({tooltip:{trigger:"axis"},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{data:[120,200,150,80,70,110,130],type:"line",smooth:!0,areaStyle:{opacity:.3}}]})})};n(()=>{q(),U(),H()});const L={loading:t,domainList:s,domainStats:r,browserStats:d,domainFilter:u,showAddDomain:m,showDomainDetails:p,selectedDomain:_,trendChart:h,pagination:f,domainForm:v,filteredDomains:g,fetchDomains:q,fetchBrowserStats:U,checkDomain:async e=>{try{await oe.checkDomainHealth(e.domain),b.success("域名检查完成"),q()}catch(a){b.error("域名检查失败")}},checkAllDomains:async()=>{try{const e=await oe.checkDomainHealth();b.success(`批量检查完成，检查了 ${e.data.checked} 个域名`),q()}catch(e){b.error("批量检查失败")}},addDomain:async()=>{if(v.domain)try{b.success("域名添加成功"),m.value=!1,q(),Object.keys(v).forEach(e=>{v[e]="domain_type"===e?"redirect":"priority"===e?5:""})}catch(e){b.error("域名添加失败")}else b.warning("请输入域名")},viewDetails:e=>{_.value=e,p.value=!0},handleDomainAction:async({action:e,domain:a})=>{switch(e){case"restore":try{b.success("域名已恢复"),q()}catch(t){b.error("域名恢复失败")}break;case"block":try{b.success("域名已封禁"),q()}catch(t){b.error("域名封禁失败")}break;case"delete":try{await T.confirm("确定要删除这个域名吗？","确认删除",{type:"warning"}),b.success("域名已删除"),q()}catch(t){"cancel"!==t&&b.error("域名删除失败")}}},getStatusType:e=>{switch(e){case 1:return"success";case 2:return"warning";case 3:return"danger";default:return"info"}},getHealthColor:e=>e>=80?"#67c23a":e>=60?"#e6a23c":"#f56c6c",formatTime:e=>e?new Date(e).toLocaleString():"-",initTrendChart:H,ref:l,reactive:o,computed:c,onMounted:n,nextTick:i,get ElMessage(){return b},get ElMessageBox(){return T},get Plus(){return F},get Refresh(){return A},get CircleCheck(){return z},get Warning(){return V},get CircleClose(){return x},get CaretTop(){return S},get ChatDotRound(){return j},get Message(){return C},get Lock(){return D},get ArrowDown(){return w},get Connection(){return y},get Timer(){return k},get echarts(){return te},get antiBlockApi(){return oe}};return Object.defineProperty(L,"__isScriptSetup",{enumerable:!1,value:!0}),L}},[["render",function(l,o,c,n,i,b){const k=q,y=f,w=U,D=v,C=Y,j=W,S=B,x=E,V=R,z=P,A=O,F=N,T=Q,ae=M,te=I,se=L,le=X,oe=J,Ke=Z,ea=G,aa=g,ta=ee,sa=K,la=$;return r(),e("div",ce,[a("div",ne,[a("div",ie,[o[13]||(o[13]=a("div",{class:"header-left"},[a("h1",{class:"page-title"},"防封系统管理"),a("p",{class:"page-subtitle"},"监控和管理域名健康状态、访问统计和防封策略")],-1)),a("div",re,[t(y,{type:"primary",onClick:n.checkAllDomains},{default:s(()=>[t(k,null,{default:s(()=>[t(n.Refresh)]),_:1}),o[11]||(o[11]=d(" 检查所有域名 ",-1))]),_:1,__:[11]}),t(y,{onClick:o[0]||(o[0]=e=>n.showAddDomain=!0)},{default:s(()=>[t(k,null,{default:s(()=>[t(n.Plus)]),_:1}),o[12]||(o[12]=d(" 添加域名 ",-1))]),_:1,__:[12]})])])]),a("div",de,[t(D,{gutter:24},{default:s(()=>[t(w,{span:6},{default:s(()=>[a("div",ue,[a("div",me,[a("div",pe,[t(k,null,{default:s(()=>[t(n.Connection)]),_:1})]),a("div",_e,[a("div",he,H(n.domainStats.total),1),o[14]||(o[14]=a("div",{class:"stats-label"},"总域名数",-1))])]),a("div",fe,[a("span",null,"健康域名: "+H(n.domainStats.normal),1)])])]),_:1}),t(w,{span:6},{default:s(()=>[a("div",ve,[a("div",ge,[a("div",be,[t(k,null,{default:s(()=>[t(n.CircleCheck)]),_:1})]),a("div",ke,[a("div",ye,H(n.domainStats.avg_health_score)+"%",1),o[15]||(o[15]=a("div",{class:"stats-label"},"平均健康分数",-1))])]),a("div",we,[t(k,null,{default:s(()=>[t(n.CaretTop)]),_:1}),o[16]||(o[16]=a("span",null,"+2.3%",-1))])])]),_:1}),t(w,{span:6},{default:s(()=>[a("div",De,[a("div",Ce,[a("div",je,[t(k,null,{default:s(()=>[t(n.Warning)]),_:1})]),a("div",Se,[a("div",xe,H(n.domainStats.abnormal),1),o[17]||(o[17]=a("div",{class:"stats-label"},"异常域名",-1))])]),o[18]||(o[18]=a("div",{class:"stats-trend"},[a("span",null,"需要关注")],-1))])]),_:1}),t(w,{span:6},{default:s(()=>[a("div",Ve,[a("div",ze,[a("div",Ae,[t(k,null,{default:s(()=>[t(n.CircleClose)]),_:1})]),a("div",Fe,[a("div",Te,H(n.domainStats.blocked),1),o[19]||(o[19]=a("div",{class:"stats-label"},"封禁域名",-1))])]),o[20]||(o[20]=a("div",{class:"stats-trend negative"},[a("span",null,"需要处理")],-1))])]),_:1})]),_:1})]),t(D,{gutter:24},{default:s(()=>[t(w,{span:16},{default:s(()=>[t(se,{title:"域名管理"},{header:s(()=>[a("div",qe,[o[21]||(o[21]=a("span",null,"域名管理",-1)),a("div",Ue,[t(j,{modelValue:n.domainFilter,"onUpdate:modelValue":o[1]||(o[1]=e=>n.domainFilter=e),placeholder:"筛选状态",style:{width:"120px"}},{default:s(()=>[t(C,{label:"全部",value:""}),t(C,{label:"正常",value:"normal"}),t(C,{label:"异常",value:"abnormal"}),t(C,{label:"封禁",value:"blocked"})]),_:1},8,["modelValue"])])])]),default:s(()=>[u((r(),m(ae,{data:n.filteredDomains},{default:s(()=>[t(S,{prop:"domain",label:"域名","min-width":"200"}),t(S,{prop:"status_name",label:"状态",width:"100"},{default:s(({row:e})=>[t(x,{type:n.getStatusType(e.status)},{default:s(()=>[d(H(e.status_name),1)]),_:2},1032,["type"])]),_:1}),t(S,{prop:"health_score",label:"健康分数",width:"120"},{default:s(({row:e})=>[t(V,{percentage:e.health_score,color:n.getHealthColor(e.health_score),"show-text":!1},null,8,["percentage","color"]),a("span",He,H(e.health_score)+"%",1)]),_:1}),t(S,{prop:"use_count",label:"使用次数",width:"100"}),t(S,{prop:"last_check_time",label:"最后检查",width:"150"},{default:s(({row:a})=>[a.last_check_time?(r(),e("span",Le,H(n.formatTime(a.last_check_time)),1)):(r(),e("span",Me,"未检查"))]),_:1}),t(S,{label:"访问检测",width:"120"},{default:s(({row:e})=>[a("div",Be,[t(z,{content:"微信访问",placement:"top"},{default:s(()=>[t(k,{color:e.check_results?.wechat_accessible?"#67c23a":"#f56c6c"},{default:s(()=>[t(n.ChatDotRound)]),_:2},1032,["color"])]),_:2},1024),t(z,{content:"QQ访问",placement:"top"},{default:s(()=>[t(k,{color:e.check_results?.qq_accessible?"#67c23a":"#f56c6c"},{default:s(()=>[t(n.Message)]),_:2},1032,["color"])]),_:2},1024),t(z,{content:"SSL证书",placement:"top"},{default:s(()=>[t(k,{color:e.check_results?.ssl_valid?"#67c23a":"#f56c6c"},{default:s(()=>[t(n.Lock)]),_:2},1032,["color"])]),_:2},1024)])]),_:1}),t(S,{label:"操作",width:"200",fixed:"right"},{default:s(({row:e})=>[t(y,{size:"small",onClick:a=>n.checkDomain(e)},{default:s(()=>o[22]||(o[22]=[d("检查",-1)])),_:2,__:[22]},1032,["onClick"]),t(y,{size:"small",type:"success",onClick:a=>n.viewDetails(e)},{default:s(()=>o[23]||(o[23]=[d("详情",-1)])),_:2,__:[23]},1032,["onClick"]),t(T,{onCommand:n.handleDomainAction},{dropdown:s(()=>[t(F,null,{default:s(()=>[t(A,{command:{action:"restore",domain:e}},{default:s(()=>o[25]||(o[25]=[d("恢复",-1)])),_:2,__:[25]},1032,["command"]),t(A,{command:{action:"block",domain:e}},{default:s(()=>o[26]||(o[26]=[d("封禁",-1)])),_:2,__:[26]},1032,["command"]),t(A,{command:{action:"delete",domain:e},divided:""},{default:s(()=>o[27]||(o[27]=[d("删除",-1)])),_:2,__:[27]},1032,["command"])]),_:2},1024)]),default:s(()=>[t(y,{size:"small"},{default:s(()=>[o[24]||(o[24]=d(" 更多",-1)),t(k,null,{default:s(()=>[t(n.ArrowDown)]),_:1})]),_:1,__:[24]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[la,n.loading]]),a("div",Ee,[t(te,{"current-page":n.pagination.page,"onUpdate:currentPage":o[2]||(o[2]=e=>n.pagination.page=e),"page-size":n.pagination.size,"onUpdate:pageSize":o[3]||(o[3]=e=>n.pagination.size=e),total:n.pagination.total,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next",onSizeChange:n.fetchDomains,onCurrentChange:n.fetchDomains},null,8,["current-page","page-size","total"])])]),_:1})]),_:1}),t(w,{span:8},{default:s(()=>[t(se,{title:"浏览器统计",class:"mb-4"},{default:s(()=>[a("div",Re,[(r(!0),e(p,null,_(n.browserStats,s=>(r(),e("div",{key:s.browser_type,class:"browser-item"},[a("div",Pe,[a("span",Qe,H(s.browser_name),1),a("span",Ne,H(s.count),1)]),t(V,{percentage:s.percentage,"show-text":!1,"stroke-width":6},null,8,["percentage"])]))),128))])]),_:1}),t(se,{title:"访问趋势"},{default:s(()=>[a("div",Oe,null,512)]),_:1})]),_:1})]),_:1}),t(aa,{modelValue:n.showAddDomain,"onUpdate:modelValue":o[9]||(o[9]=e=>n.showAddDomain=e),title:"添加域名",width:"500px"},{footer:s(()=>[t(y,{onClick:o[8]||(o[8]=e=>n.showAddDomain=!1)},{default:s(()=>o[28]||(o[28]=[d("取消",-1)])),_:1,__:[28]}),t(y,{type:"primary",onClick:n.addDomain},{default:s(()=>o[29]||(o[29]=[d("添加",-1)])),_:1,__:[29]})]),default:s(()=>[t(ea,{model:n.domainForm,"label-width":"100px"},{default:s(()=>[t(oe,{label:"域名",required:""},{default:s(()=>[t(le,{modelValue:n.domainForm.domain,"onUpdate:modelValue":o[4]||(o[4]=e=>n.domainForm.domain=e),placeholder:"请输入域名，如：example.com"},null,8,["modelValue"])]),_:1}),t(oe,{label:"域名类型"},{default:s(()=>[t(j,{modelValue:n.domainForm.domain_type,"onUpdate:modelValue":o[5]||(o[5]=e=>n.domainForm.domain_type=e)},{default:s(()=>[t(C,{label:"重定向域名",value:"redirect"}),t(C,{label:"落地页域名",value:"landing"}),t(C,{label:"API域名",value:"api"})]),_:1},8,["modelValue"])]),_:1}),t(oe,{label:"优先级"},{default:s(()=>[t(Ke,{modelValue:n.domainForm.priority,"onUpdate:modelValue":o[6]||(o[6]=e=>n.domainForm.priority=e),min:1,max:10},null,8,["modelValue"])]),_:1}),t(oe,{label:"备注"},{default:s(()=>[t(le,{modelValue:n.domainForm.remarks,"onUpdate:modelValue":o[7]||(o[7]=e=>n.domainForm.remarks=e),type:"textarea",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(aa,{modelValue:n.showDomainDetails,"onUpdate:modelValue":o[10]||(o[10]=e=>n.showDomainDetails=e),title:"域名详情",width:"70%"},{default:s(()=>[n.selectedDomain?(r(),e("div",$e,[t(sa,{column:2,border:""},{default:s(()=>[t(ta,{label:"域名"},{default:s(()=>[d(H(n.selectedDomain.domain),1)]),_:1}),t(ta,{label:"状态"},{default:s(()=>[t(x,{type:n.getStatusType(n.selectedDomain.status)},{default:s(()=>[d(H(n.selectedDomain.status_name),1)]),_:1},8,["type"])]),_:1}),t(ta,{label:"健康分数"},{default:s(()=>[d(H(n.selectedDomain.health_score)+"%",1)]),_:1}),t(ta,{label:"使用次数"},{default:s(()=>[d(H(n.selectedDomain.use_count),1)]),_:1}),t(ta,{label:"最后检查"},{default:s(()=>[d(H(n.formatTime(n.selectedDomain.last_check_time)),1)]),_:1}),t(ta,{label:"最后使用"},{default:s(()=>[d(H(n.formatTime(n.selectedDomain.last_use_time)),1)]),_:1})]),_:1}),n.selectedDomain.check_results?(r(),e("div",Ie,[o[36]||(o[36]=a("h4",null,"检查结果详情",-1)),t(D,{gutter:16},{default:s(()=>[t(w,{span:8},{default:s(()=>[a("div",We,[t(k,{color:n.selectedDomain.check_results.accessible?"#67c23a":"#f56c6c"},{default:s(()=>[t(n.Connection)]),_:1},8,["color"]),o[30]||(o[30]=a("span",null,"可访问性",-1)),t(x,{type:n.selectedDomain.check_results.accessible?"success":"danger",size:"small"},{default:s(()=>[d(H(n.selectedDomain.check_results.accessible?"正常":"异常"),1)]),_:1},8,["type"])])]),_:1}),t(w,{span:8},{default:s(()=>[a("div",Ye,[t(k,{color:n.selectedDomain.check_results.dns_resolved?"#67c23a":"#f56c6c"},{default:s(()=>[t(n.Connection)]),_:1},8,["color"]),o[31]||(o[31]=a("span",null,"DNS解析",-1)),t(x,{type:n.selectedDomain.check_results.dns_resolved?"success":"danger",size:"small"},{default:s(()=>[d(H(n.selectedDomain.check_results.dns_resolved?"正常":"异常"),1)]),_:1},8,["type"])])]),_:1}),t(w,{span:8},{default:s(()=>[a("div",Ge,[t(k,{color:n.selectedDomain.check_results.ssl_valid?"#67c23a":"#f56c6c"},{default:s(()=>[t(n.Lock)]),_:1},8,["color"]),o[32]||(o[32]=a("span",null,"SSL证书",-1)),t(x,{type:n.selectedDomain.check_results.ssl_valid?"success":"danger",size:"small"},{default:s(()=>[d(H(n.selectedDomain.check_results.ssl_valid?"有效":"无效"),1)]),_:1},8,["type"])])]),_:1})]),_:1}),t(D,{gutter:16,class:"mt-3"},{default:s(()=>[t(w,{span:8},{default:s(()=>[a("div",Je,[t(k,{color:n.selectedDomain.check_results.wechat_accessible?"#67c23a":"#f56c6c"},{default:s(()=>[t(n.ChatDotRound)]),_:1},8,["color"]),o[33]||(o[33]=a("span",null,"微信访问",-1)),t(x,{type:n.selectedDomain.check_results.wechat_accessible?"success":"danger",size:"small"},{default:s(()=>[d(H(n.selectedDomain.check_results.wechat_accessible?"正常":"受限"),1)]),_:1},8,["type"])])]),_:1}),t(w,{span:8},{default:s(()=>[a("div",Xe,[t(k,{color:n.selectedDomain.check_results.qq_accessible?"#67c23a":"#f56c6c"},{default:s(()=>[t(n.Message)]),_:1},8,["color"]),o[34]||(o[34]=a("span",null,"QQ访问",-1)),t(x,{type:n.selectedDomain.check_results.qq_accessible?"success":"danger",size:"small"},{default:s(()=>[d(H(n.selectedDomain.check_results.qq_accessible?"正常":"受限"),1)]),_:1},8,["type"])])]),_:1}),t(w,{span:8},{default:s(()=>[a("div",Ze,[t(k,null,{default:s(()=>[t(n.Timer)]),_:1}),o[35]||(o[35]=a("span",null,"响应时间",-1)),t(x,{size:"small"},{default:s(()=>[d(H(n.selectedDomain.check_results.response_time)+"ms",1)]),_:1})])]),_:1})]),_:1})])):h("",!0)])):h("",!0)]),_:1},8,["modelValue"])])}],["__scopeId","data-v-236336c9"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/anti-block/EnhancedDashboard.vue"]]);export{Ke as default};
