<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>数据看板</h1>
      <p>欢迎使用管理后台系统</p>
    </div>

    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="stat-info">
            <h3>用户总数</h3>
            <p class="stat-number">1,234</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <div class="stat-info">
            <h3>社群数量</h3>
            <p class="stat-number">56</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="stat-info">
            <h3>今日收入</h3>
            <p class="stat-number">¥8,888</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <h3>转化率</h3>
            <p class="stat-number">12.5%</p>
          </div>
        </div>
      </el-card>
    </div>

    <div class="dashboard-content">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-card title="最近活动">
            <div class="activity-list">
              <div class="activity-item" v-for="item in activities" :key="item.id">
                <div class="activity-time">{{ item.time }}</div>
                <div class="activity-desc">{{ item.description }}</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card title="系统状态">
            <div class="system-status">
              <div class="status-item">
                <span class="status-label">服务器状态</span>
                <el-tag type="success">正常</el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">数据库连接</span>
                <el-tag type="success">正常</el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">缓存服务</span>
                <el-tag type="success">正常</el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">消息队列</span>
                <el-tag type="warning">警告</el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="quick-actions">
      <h3>快捷操作</h3>
      <div class="action-buttons">
        <el-button type="primary" @click="goToGroups">
          <el-icon><Plus /></el-icon>
          创建社群
        </el-button>
        <el-button type="success" @click="goToUsers">
          <el-icon><User /></el-icon>
          用户管理
        </el-button>
        <el-button type="info" @click="goToSettings">
          <el-icon><Setting /></el-icon>
          系统设置
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  UserFilled, 
  ChatDotRound, 
  Money, 
  TrendCharts,
  Plus,
  User,
  Setting
} from '@element-plus/icons-vue'

const router = useRouter()

const activities = ref([
  { id: 1, time: '10:30', description: '用户张三加入了"前端开发交流群"' },
  { id: 2, time: '09:45', description: '管理员发布了新的群规则' },
  { id: 3, time: '09:20', description: '社群"产品经理交流群"达到500人' },
  { id: 4, time: '08:55', description: '系统完成了数据备份' },
  { id: 5, time: '08:30', description: '新用户李四完成了注册' }
])

const goToGroups = () => {
  router.push('/admin/community/groups')
}

const goToUsers = () => {
  router.push('/admin/users/list')
}

const goToSettings = () => {
  router.push('/admin/system/settings')
}
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #262626;
}

.dashboard-header p {
  margin: 0;
  color: #8c8c8c;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.stat-info h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #8c8c8c;
  font-weight: normal;
}

.stat-number {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #262626;
}

.dashboard-content {
  margin-bottom: 24px;
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  color: #8c8c8c;
  font-size: 12px;
  min-width: 40px;
}

.activity-desc {
  color: #262626;
  font-size: 14px;
}

.system-status {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.status-label {
  color: #262626;
  font-size: 14px;
}

.quick-actions h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #262626;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}
</style>
