/* empty css             *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                   *//* empty css                *//* empty css               *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                  */import{l as e,m as l,G as a,A as t,q as r,F as o,Y as s,z as i,E as u,r as d,c,M as n}from"./vue-vendor-BcnDv-68.js";import{U as m,Y as p,a5 as f,a6 as v,a4 as b,ah as _,ak as y,Z as g,a1 as V,V as h,a2 as w,a3 as C,ap as F,aq as j,W as T,aF as U,af as q,bs as k,X as x}from"./element-plus-C2UshkXo.js";import{_ as D}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const I={class:"content-template-library"},S={class:"card-header"},A={class:"card-title"},L={class:"header-actions"},O={class:"template-filters"},P={class:"template-list"},Q=["onClick"],z={class:"template-header"},K={class:"template-content"},E={class:"template-title"},M={class:"template-preview"},Y={class:"template-footer"},B={class:"usage-count"};const G=D({__name:"ContentTemplateLibrary",emits:["template-selected"],setup(e,{expose:l,emit:a}){l();const t=a,r=d(""),o=d(""),s=d(""),i=d([{id:1,title:"互联网技术交流群标题模板",category:"title",industry:"tech",content:"{city}程序员技术交流群 - 汇聚IT精英，分享前沿技术",usageCount:156}]),u=c(()=>i.value.filter(e=>{const l=!r.value||e.category===r.value,a=!o.value||e.industry===o.value,t=!s.value||e.title.includes(s.value)||e.content.includes(s.value);return l&&a&&t})),m=d(!1),p=n({title:"",description:"",category:"",industry:"",content:{title:"",subtitle:"",description:"",price:0,faq:[],reviews:[]}}),f={emit:t,filterCategory:r,filterIndustry:o,searchKeyword:s,templates:i,filteredTemplates:u,selectTemplate:e=>{t("template-selected",e),x.success(`已选择模板：${e.title}`)},createDialogVisible:m,createForm:p,showCreateDialog:()=>{Object.assign(p,{title:"",description:"",category:"",industry:"",content:{title:"",subtitle:"",description:"",price:0,faq:[],reviews:[]}}),m.value=!0},saveTemplate:async()=>{if(p.title.trim())if(p.category)try{const e={...p,id:Date.now(),created_at:(new Date).toISOString(),usage_count:0};i.value.unshift(e),x.success("模板创建成功"),m.value=!1}catch(e){x.error("创建模板失败，请重试")}else x.warning("请选择模板分类");else x.warning("请输入模板名称")},getCategoryTagType:e=>({title:"primary",description:"success",faq:"warning",reviews:"info"}[e]||""),getCategoryLabel:e=>({title:"标题",description:"描述",faq:"FAQ",reviews:"评论"}[e]||e),ref:d,computed:c,get ElMessage(){return x},get Collection(){return k},get Plus(){return q},get Search(){return U}};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}},[["render",function(d,c,n,U,q,k){const x=_,D=V,G=v,N=f,R=p,W=b,X=m,Z=y,$=h,H=C,J=F,ee=j,le=w,ae=T;return l(),e("div",I,[a($,{class:"library-card",shadow:"never"},{header:t(()=>[r("div",S,[r("span",A,[a(x,null,{default:t(()=>[a(U.Collection)]),_:1}),c[12]||(c[12]=u(" 内容模板库 ",-1))]),r("div",L,[a(D,{type:"primary",size:"small",onClick:U.showCreateDialog},{default:t(()=>[a(x,null,{default:t(()=>[a(U.Plus)]),_:1}),c[13]||(c[13]=u(" 新建模板 ",-1))]),_:1,__:[13]})])])]),default:t(()=>[r("div",O,[a(X,{gutter:20},{default:t(()=>[a(R,{span:8},{default:t(()=>[a(N,{modelValue:U.filterCategory,"onUpdate:modelValue":c[0]||(c[0]=e=>U.filterCategory=e),placeholder:"选择分类",clearable:""},{default:t(()=>[a(G,{label:"全部分类",value:""}),a(G,{label:"群组标题",value:"title"}),a(G,{label:"群组描述",value:"description"}),a(G,{label:"FAQ问答",value:"faq"}),a(G,{label:"用户评论",value:"reviews"})]),_:1},8,["modelValue"])]),_:1}),a(R,{span:8},{default:t(()=>[a(N,{modelValue:U.filterIndustry,"onUpdate:modelValue":c[1]||(c[1]=e=>U.filterIndustry=e),placeholder:"选择行业",clearable:""},{default:t(()=>[a(G,{label:"全部行业",value:""}),a(G,{label:"互联网/科技",value:"tech"}),a(G,{label:"金融/投资",value:"finance"}),a(G,{label:"教育/培训",value:"education"})]),_:1},8,["modelValue"])]),_:1}),a(R,{span:8},{default:t(()=>[a(W,{modelValue:U.searchKeyword,"onUpdate:modelValue":c[2]||(c[2]=e=>U.searchKeyword=e),placeholder:"搜索模板...",clearable:""},{prefix:t(()=>[a(x,null,{default:t(()=>[a(U.Search)]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),r("div",P,[a(X,{gutter:20},{default:t(()=>[(l(!0),e(o,null,s(U.filteredTemplates,e=>(l(),i(R,{span:8,key:e.id},{default:t(()=>[r("div",{class:"template-card",onClick:l=>U.selectTemplate(e)},[r("div",z,[a(Z,{type:U.getCategoryTagType(e.category),size:"small"},{default:t(()=>[u(g(U.getCategoryLabel(e.category)),1)]),_:2},1032,["type"])]),r("div",K,[r("h4",E,g(e.title),1),r("p",M,g(e.content.substring(0,100))+"...",1)]),r("div",Y,[r("span",B,"使用 "+g(e.usageCount)+" 次",1)])],8,Q)]),_:2},1024))),128))]),_:1})])]),_:1}),a(ae,{modelValue:U.createDialogVisible,"onUpdate:modelValue":c[11]||(c[11]=e=>U.createDialogVisible=e),title:"创建新模板",width:"600px","close-on-click-modal":!1},{footer:t(()=>[a(D,{onClick:c[10]||(c[10]=e=>U.createDialogVisible=!1)},{default:t(()=>c[15]||(c[15]=[u("取消",-1)])),_:1,__:[15]}),a(D,{type:"primary",onClick:U.saveTemplate},{default:t(()=>c[16]||(c[16]=[u("保存模板",-1)])),_:1,__:[16]})]),default:t(()=>[a(le,{model:U.createForm,"label-width":"100px"},{default:t(()=>[a(H,{label:"模板名称",required:""},{default:t(()=>[a(W,{modelValue:U.createForm.title,"onUpdate:modelValue":c[3]||(c[3]=e=>U.createForm.title=e),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1}),a(H,{label:"模板描述"},{default:t(()=>[a(W,{modelValue:U.createForm.description,"onUpdate:modelValue":c[4]||(c[4]=e=>U.createForm.description=e),type:"textarea",rows:3,placeholder:"请输入模板描述"},null,8,["modelValue"])]),_:1}),a(X,{gutter:20},{default:t(()=>[a(R,{span:12},{default:t(()=>[a(H,{label:"分类",required:""},{default:t(()=>[a(N,{modelValue:U.createForm.category,"onUpdate:modelValue":c[5]||(c[5]=e=>U.createForm.category=e),placeholder:"选择分类"},{default:t(()=>[a(G,{label:"群组标题",value:"title"}),a(G,{label:"群组描述",value:"description"}),a(G,{label:"FAQ问答",value:"faq"}),a(G,{label:"用户评论",value:"reviews"}),a(G,{label:"综合模板",value:"comprehensive"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(R,{span:12},{default:t(()=>[a(H,{label:"行业"},{default:t(()=>[a(N,{modelValue:U.createForm.industry,"onUpdate:modelValue":c[6]||(c[6]=e=>U.createForm.industry=e),placeholder:"选择行业"},{default:t(()=>[a(G,{label:"互联网/科技",value:"tech"}),a(G,{label:"金融/投资",value:"finance"}),a(G,{label:"教育/培训",value:"education"}),a(G,{label:"电商/零售",value:"ecommerce"}),a(G,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(J,null,{default:t(()=>c[14]||(c[14]=[u("模板内容",-1)])),_:1,__:[14]}),a(H,{label:"群组标题"},{default:t(()=>[a(W,{modelValue:U.createForm.content.title,"onUpdate:modelValue":c[7]||(c[7]=e=>U.createForm.content.title=e),placeholder:"输入群组标题模板"},null,8,["modelValue"])]),_:1}),a(H,{label:"群组描述"},{default:t(()=>[a(W,{modelValue:U.createForm.content.description,"onUpdate:modelValue":c[8]||(c[8]=e=>U.createForm.content.description=e),type:"textarea",rows:4,placeholder:"输入群组描述模板"},null,8,["modelValue"])]),_:1}),a(H,{label:"参考价格"},{default:t(()=>[a(ee,{modelValue:U.createForm.content.price,"onUpdate:modelValue":c[9]||(c[9]=e=>U.createForm.content.price=e),min:0,max:9999,step:.1,precision:2},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-b8bfb211"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/ContentTemplateLibrary.vue"]]);export{G as default};
