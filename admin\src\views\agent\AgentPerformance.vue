<template>
  <div class="modern-agent-performance performance-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><TrendCharts /></el-icon>
          </div>
          <div class="header-text">
            <h1>代理商绩效分析</h1>
            <p>深度分析代理商业绩表现，提供数据驱动的业务洞察</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleExport" class="action-btn secondary">
            <el-icon><Download /></el-icon>
            导出报表
          </el-button>
          <el-button @click="showHelpDialog = true" class="action-btn secondary">
            <el-icon><QuestionFilled /></el-icon>
            功能说明
          </el-button>
          <el-button type="primary" @click="refreshAllData" class="action-btn primary">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 时间筛选区域 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm" @submit.prevent="handleFilter">
        <el-form-item label="统计周期">
          <el-select v-model="filterForm.period" placeholder="选择统计周期" class="filter-select">
            <el-option label="今日" value="today" />
            <el-option label="近7天" value="week" />
            <el-option label="近30天" value="month" />
            <el-option label="近90天" value="quarter" />
            <el-option label="近1年" value="year" />
          </el-select>
        </el-form-item>
        <el-form-item label="代理商类型">
          <el-select v-model="filterForm.agentType" placeholder="全部类型" clearable class="filter-select">
            <el-option label="平台代理商" value="platform" />
            <el-option label="分站代理商" value="substation" />
          </el-select>
        </el-form-item>
        <el-form-item label="绩效排名">
          <el-select v-model="filterForm.ranking" placeholder="全部排名" clearable class="filter-select">
            <el-option label="前10名" value="top10" />
            <el-option label="前20名" value="top20" />
            <el-option label="前50名" value="top50" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter" class="search-btn">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetFilter" class="reset-btn">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-grid" v-loading="statsLoading">
        <div class="stat-card" v-for="stat in performanceStatCards" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表分析区域 -->
    <el-row :gutter="20" class="chart-section">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>绩效趋势分析</h3>
              <div class="chart-controls">
                <el-radio-group v-model="chartPeriod" size="small" @change="loadPerformanceTrend">
                  <el-radio-button label="week">近7天</el-radio-button>
                  <el-radio-button label="month">近30天</el-radio-button>
                  <el-radio-button label="quarter">近3个月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <LineChart
            :data="performanceTrendData"
            :options="chartOptions"
            height="350px"
          />
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>绩效分布</h3>
            </div>
          </template>
          <DoughnutChart
            :data="performanceDistributionData"
            :options="doughnutOptions"
            height="350px"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 绩效排行榜 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h3>代理商绩效排行榜</h3>
            <el-tag size="small" type="info">共 {{ agents.total || 0 }} 个代理商</el-tag>
          </div>
          <div class="header-right">
            <el-button-group>
              <el-button size="small" :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                <el-icon><List /></el-icon>
              </el-button>
              <el-button size="small" :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
                <el-icon><Grid /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>
      
      <el-table 
        v-if="viewMode === 'table'"
        :data="agents.data" 
        v-loading="loading"
        class="modern-table"
      >
        <el-table-column label="排名" width="80" align="center">
          <template #default="{ $index }">
            <div class="rank-badge" :class="getRankClass($index + 1)">
              {{ $index + 1 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="代理商信息" width="220">
          <template #default="{ row }">
            <div class="agent-info">
              <el-avatar :size="40" class="agent-avatar">
                <el-icon><Avatar /></el-icon>
              </el-avatar>
              <div class="agent-details">
                <div class="agent-name">{{ row.agent_name }}</div>
                <div class="agent-code">编码: {{ row.agent_code }}</div>
                <div class="agent-type">{{ getAgentTypeText(row.agent_type) }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="total_commission" label="总佣金" width="120" sortable>
          <template #default="{ row }">
            <span class="amount primary">¥{{ formatNumber(row.total_commission) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="total_users" label="推广用户" width="100" sortable>
          <template #default="{ row }">
            <span class="count">{{ row.total_users }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="conversion_rate" label="转化率" width="100" sortable>
          <template #default="{ row }">
            <div class="conversion-rate">
              <el-progress
                :percentage="row.conversion_rate"
                :stroke-width="6"
                :show-text="false"
                :color="getProgressColor(row.conversion_rate)"
              />
              <span class="rate-text">{{ row.conversion_rate }}%</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="monthly_growth" label="月增长率" width="120">
          <template #default="{ row }">
            <span class="growth-rate" :class="row.monthly_growth >= 0 ? 'positive' : 'negative'">
              {{ row.monthly_growth >= 0 ? '+' : '' }}{{ row.monthly_growth }}%
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="performance_score" label="绩效评分" width="120" sortable>
          <template #default="{ row }">
            <div class="performance-score">
              <el-rate
                v-model="row.star_rating"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              />
              <div class="score-text">{{ row.performance_score }}分</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="last_active" label="最后活跃" width="120">
          <template #default="{ row }">
            {{ formatDate(row.last_active) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="viewDetails(row)">
              查看详情
            </el-button>
            <el-button link type="info" size="small" @click="viewAnalytics(row)">
              分析报告
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="agents.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 功能说明对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="代理商绩效分析功能说明"
      width="1000px"
      class="help-dialog"
    >
      <div class="help-content">
        <!-- 功能概述 -->
        <div class="help-section">
          <h3>📊 功能概述</h3>
          <p>代理商绩效分析是一个综合性的数据分析工具，通过多维度指标评估代理商的业务表现，帮助您识别优秀代理商、发现改进机会，制定更有效的激励策略。</p>
        </div>

        <!-- 核心指标 -->
        <div class="help-section">
          <h3>🎯 核心绩效指标</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="metric-item">
                <div class="metric-icon">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="metric-content">
                  <h4>佣金收入</h4>
                  <p>代理商通过推广获得的总佣金收入，反映其业务价值</p>
                  <div class="metric-formula">计算公式: 直推佣金 + 团队佣金 + 奖励佣金</div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="metric-item">
                <div class="metric-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div class="metric-content">
                  <h4>推广用户数</h4>
                  <p>代理商直接或间接推广的有效用户总数</p>
                  <div class="metric-formula">计算公式: 直推用户 + 下级代理推广用户</div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="metric-item">
                <div class="metric-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="metric-content">
                  <h4>转化率</h4>
                  <p>访问推广链接的用户中实际付费的比例</p>
                  <div class="metric-formula">计算公式: 付费用户数 ÷ 访问用户数 × 100%</div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="metric-item">
                <div class="metric-icon">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="metric-content">
                  <h4>绩效评分</h4>
                  <p>综合多个维度计算得出的代理商绩效综合评分</p>
                  <div class="metric-formula">权重分配: 佣金收入40% + 用户数30% + 转化率20% + 活跃度10%</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 绩效评级 -->
        <div class="help-section">
          <h3>🏆 绩效评级标准</h3>
          <el-table :data="performanceGrades" style="width: 100%">
            <el-table-column prop="grade" label="评级" width="100">
              <template #default="{ row }">
                <el-tag :type="row.color">{{ row.grade }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="score_range" label="评分区间" width="120" />
            <el-table-column prop="star_rating" label="星级" width="100">
              <template #default="{ row }">
                <el-rate v-model="row.stars" disabled show-score text-color="#ff9900" />
              </template>
            </el-table-column>
            <el-table-column prop="description" label="评级说明" />
            <el-table-column prop="benefits" label="对应权益" />
          </el-table>
        </div>

        <!-- 操作指南 -->
        <div class="help-section">
          <h3>📝 操作指南</h3>
          <el-collapse v-model="activeGuides">
            <el-collapse-item title="如何查看绩效趋势？" name="trend">
              <div class="guide-content">
                <ol>
                  <li>在"绩效趋势分析"图表中选择时间周期</li>
                  <li>观察曲线变化，识别增长或下降趋势</li>
                  <li>对比不同时期的表现，寻找规律</li>
                  <li>结合外部因素分析趋势变化原因</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 提示：关注季节性变化和营销活动对绩效的影响
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何分析代理商表现？" name="analysis">
              <div class="guide-content">
                <ol>
                  <li>查看绩效排行榜，识别头部代理商</li>
                  <li>对比不同代理商的各项指标</li>
                  <li>分析高绩效代理商的成功经验</li>
                  <li>识别低绩效代理商的问题原因</li>
                  <li>制定针对性的改进措施</li>
                </ol>
                <el-alert type="success" :closable="false">
                  ✅ 建议：定期与代理商沟通，提供针对性的指导和支持
                </el-alert>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  TrendCharts, Download, QuestionFilled, Refresh, Search, RefreshLeft,
  List, Grid, Avatar, Money, User, Star, ArrowUp, ArrowDown
} from '@element-plus/icons-vue'
import LineChart from '@/components/Charts/LineChart.vue'
import DoughnutChart from '@/components/Charts/DoughnutChart.vue'
import { agentApi } from '@/api/agent'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const statsLoading = ref(true)
const showHelpDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref('table')
const chartPeriod = ref('month')

// 筛选表单
const filterForm = reactive({
  period: 'month',
  agentType: '',
  ranking: ''
})

// 代理商绩效统计卡片数据
const performanceStatCards = ref([
  {
    key: 'total_agents',
    label: '总代理商数',
    value: '0',
    icon: 'User',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+8%'
  },
  {
    key: 'top_performers',
    label: '优秀代理商',
    value: '0',
    icon: 'Star',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+12%'
  },
  {
    key: 'total_performance',
    label: '总绩效得分',
    value: '0',
    icon: 'TrendCharts',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15.3%'
  },
  {
    key: 'avg_conversion',
    label: '平均转化率',
    value: '0%',
    icon: 'Money',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+3.2%'
  }
])

// 代理商数据
const agents = ref({ data: [], total: 0 })

// 图表数据
const performanceTrendData = ref({
  labels: [],
  datasets: [{
    label: '绩效得分',
    data: [],
    borderColor: '#409EFF',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    tension: 0.4
  }]
})

const performanceDistributionData = ref({
  labels: ['优秀', '良好', '一般', '需改进'],
  datasets: [{
    data: [0, 0, 0, 0],
    backgroundColor: ['#67C23A', '#409EFF', '#E6A23C', '#F56C6C']
  }]
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
}

// 帮助对话框数据
const activeGuides = ref(['trend'])

const performanceGrades = ref([
  {
    grade: '优秀',
    color: 'success',
    score_range: '90-100',
    stars: 5,
    description: '各项指标表现卓越',
    benefits: '最高佣金比例、优先推广资源、专属客服'
  },
  {
    grade: '良好',
    color: 'primary',
    score_range: '80-89',
    stars: 4,
    description: '大部分指标表现良好',
    benefits: '较高佣金比例、推广支持、定期培训'
  },
  {
    grade: '一般',
    color: 'warning',
    score_range: '70-79',
    stars: 3,
    description: '基本达到要求',
    benefits: '标准佣金比例、基础推广工具'
  },
  {
    grade: '需改进',
    color: 'danger',
    score_range: '60-69',
    stars: 2,
    description: '多项指标需要改进',
    benefits: '基础佣金、改进计划指导'
  }
])

// 方法
const handleFilter = () => {
  currentPage.value = 1
  loadAgentPerformance()
}

const resetFilter = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = key === 'period' ? 'month' : ''
  })
  handleFilter()
}

const handleExport = async () => {
  try {
    ElMessage.success('绩效报表导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const refreshAllData = async () => {
  await Promise.all([
    loadStats(),
    loadAgentPerformance(),
    loadPerformanceTrend()
  ])
  ElMessage.success('数据刷新成功')
}

const loadStats = async () => {
  try {
    statsLoading.value = true
    console.log('加载绩效统计数据...')
    
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const mockStats = {
      total_agents: 156,
      top_performers: 23,
      total_performance: 8750,
      avg_conversion: 12.5
    }
    
    performanceStatCards.value[0].value = mockStats.total_agents.toString()
    performanceStatCards.value[1].value = mockStats.top_performers.toString()
    performanceStatCards.value[2].value = mockStats.total_performance.toString()
    performanceStatCards.value[3].value = mockStats.avg_conversion.toFixed(1) + '%'
    
    console.log('绩效统计数据加载完成')
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

const loadAgentPerformance = async () => {
  try {
    loading.value = true
    console.log('加载代理商绩效数据...')
    
    await new Promise(resolve => setTimeout(resolve, 500))
    
    agents.value = {
      data: [
        {
          id: 1,
          agent_name: '张三代理',
          agent_code: 'AG001',
          agent_type: 'platform',
          total_commission: 15678.90,
          total_users: 234,
          conversion_rate: 15.8,
          monthly_growth: 18.5,
          performance_score: 95,
          star_rating: 5,
          last_active: '2024-01-15'
        },
        {
          id: 2,
          agent_name: '李四企业',
          agent_code: 'AG002',
          agent_type: 'substation',
          total_commission: 12450.30,
          total_users: 189,
          conversion_rate: 12.3,
          monthly_growth: 12.8,
          performance_score: 88,
          star_rating: 4,
          last_active: '2024-01-14'
        },
        {
          id: 3,
          agent_name: '王五渠道',
          agent_code: 'AG003',
          agent_type: 'platform',
          total_commission: 8956.78,
          total_users: 156,
          conversion_rate: 9.5,
          monthly_growth: -2.3,
          performance_score: 76,
          star_rating: 3,
          last_active: '2024-01-13'
        }
      ],
      total: 3
    }
    
    console.log('代理商绩效数据加载完成')
  } catch (error) {
    console.error('加载绩效数据失败:', error)
    ElMessage.error('加载绩效数据失败')
  } finally {
    loading.value = false
  }
}

const loadPerformanceTrend = async () => {
  try {
    const mockData = {
      week: {
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        data: [75, 78, 82, 85, 87, 89, 92]
      },
      month: {
        labels: Array.from({length: 30}, (_, i) => `${i+1}日`),
        data: Array.from({length: 30}, () => Math.floor(Math.random() * 20) + 75)
      },
      quarter: {
        labels: ['1月', '2月', '3月'],
        data: [78, 85, 92]
      }
    }
    
    const data = mockData[chartPeriod.value]
    performanceTrendData.value = {
      labels: data.labels,
      datasets: [{
        label: '绩效得分',
        data: data.data,
        borderColor: '#409EFF',
        backgroundColor: 'rgba(64, 158, 255, 0.1)',
        tension: 0.4
      }]
    }
    
    // 更新分布数据
    performanceDistributionData.value = {
      labels: ['优秀', '良好', '一般', '需改进'],
      datasets: [{
        data: [23, 45, 67, 21],
        backgroundColor: ['#67C23A', '#409EFF', '#E6A23C', '#F56C6C']
      }]
    }
  } catch (error) {
    ElMessage.error('加载趋势数据失败')
  }
}

const viewDetails = (agent) => {
  router.push(`/admin/agents/detail/${agent.id}`)
}

const viewAnalytics = (agent) => {
  router.push(`/admin/agents/analytics/${agent.id}`)
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadAgentPerformance()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadAgentPerformance()
}

// 工具方法
const formatNumber = (number) => {
  return number.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getAgentTypeText = (type) => {
  const texts = {
    'platform': '平台代理',
    'substation': '分站代理'
  }
  return texts[type] || '未知'
}

const getRankClass = (rank) => {
  if (rank <= 3) return 'rank-top'
  if (rank <= 10) return 'rank-good'
  return 'rank-normal'
}

const getProgressColor = (rate) => {
  if (rate >= 15) return '#67C23A'
  if (rate >= 10) return '#409EFF'
  if (rate >= 5) return '#E6A23C'
  return '#F56C6C'
}

// 生命周期
onMounted(() => {
  loadStats()
  loadAgentPerformance()
  loadPerformanceTrend()
})
</script>

<style lang="scss" scoped>
.performance-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部样式
  .page-header {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          height: 40px;
          padding: 0 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }

  // 筛选卡片样式
  .filter-card {
    margin-bottom: 24px;
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    
    .filter-select {
      width: 150px;
    }
    
    .search-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      }
    }
    
    .reset-btn {
      background: #f5f7fa;
      border-color: #dcdfe6;
      color: #606266;
      border-radius: 8px;
      
      &:hover {
        background: #ecf5ff;
        border-color: #409eff;
        color: #409eff;
      }
    }
  }

  // 统计卡片区域
  .stats-section {
    margin-bottom: 24px;
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 图表和内容区域
  .chart-section {
    margin-bottom: 24px;
    
    .el-card {
      border-radius: 12px;
      border: none;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
      overflow: hidden;
      
      :deep(.el-card__header) {
        background: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        padding: 20px 24px;
      }
      
      :deep(.el-card__body) {
        padding: 24px;
      }
    }
  }

  // 表格卡片样式
  .table-card {
    margin-bottom: 24px;
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    
    :deep(.el-card__header) {
      background: #f9fafb;
      border-bottom: 1px solid #e5e7eb;
      padding: 20px 24px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
          
          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
          
          .el-tag {
            background: #ecf5ff;
            color: #409eff;
            border: none;
          }
        }
      }
    }
    
    :deep(.el-card__body) {
      padding: 0;
    }
    
    .modern-table {
      :deep(.el-table__header) {
        background: #fafbfc;
        
        th {
          background: #fafbfc !important;
          border-bottom: 1px solid #e4e7ed;
          font-weight: 600;
          color: #606266;
          font-size: 13px;
          padding: 16px 12px;
        }
      }
      
      :deep(.el-table__body) {
        tr {
          transition: all 0.3s ease;
          
          &:hover {
            background: #f9fafb !important;
          }
          
          td {
            border-bottom: 1px solid #f0f2f5;
            padding: 16px 12px;
          }
        }
      }
      
      .agent-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .agent-avatar {
          border: 2px solid #e5e7eb;
          transition: all 0.3s ease;
          
          &:hover {
            transform: scale(1.05);
          }
        }
        
        .agent-details {
          .agent-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 2px;
          }
          
          .agent-code {
            font-size: 12px;
            color: #909399;
            margin-bottom: 1px;
          }
          
          .agent-type {
            font-size: 11px;
            color: #c0c4cc;
          }
        }
      }
      
      .rank-badge {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 12px;
        
        &.rank-top {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
          color: white;
        }
        
        &.rank-good {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          color: white;
        }
        
        &.rank-normal {
          background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
          color: white;
        }
      }
      
      .conversion-rate {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .rate-text {
          font-size: 12px;
          font-weight: 600;
          color: #303133;
        }
      }
      
      .performance-score {
        text-align: center;
        
        .score-text {
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
    
    .pagination {
      margin-top: 20px;
      text-align: center;
    }
  }

  // 金额和数值样式
  .amount {
    font-weight: 700;
    
    &.primary {
      color: #409eff;
    }
  }
  
  .count {
    color: #303133;
    font-weight: 600;
  }
  
  .growth-rate {
    font-weight: 600;
    
    &.positive {
      color: #67c23a;
    }
    
    &.negative {
      color: #f56c6c;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

// 帮助对话框样式
.help-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.help-content {
  .help-section {
    margin-bottom: 30px;
    
    h3 {
      color: #303133;
      margin-bottom: 15px;
      font-size: 18px;
      border-bottom: 2px solid #667eea;
      padding-bottom: 8px;
    }
    
    p {
      color: #606266;
      line-height: 1.6;
      margin-bottom: 15px;
    }
  }
  
  .metric-item {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 12px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    background: white;
    
    &:hover {
      border-color: #667eea;
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
      transform: translateY(-2px);
    }
    
    .metric-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      flex-shrink: 0;
      
      .el-icon {
        color: white;
        font-size: 20px;
      }
    }
    
    .metric-content {
      flex: 1;
      
      h4 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
      
      p {
        margin: 0 0 8px 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }
      
      .metric-formula {
        font-size: 12px;
        color: #909399;
        background: rgba(102, 126, 234, 0.1);
        padding: 4px 8px;
        border-radius: 4px;
        font-family: monospace;
      }
    }
  }
  
  .guide-content {
    padding: 20px;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-radius: 12px;
    
    ol, ul {
      margin: 0 0 16px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #606266;
        line-height: 1.5;
      }
    }
    
    :deep(.el-alert) {
      margin-top: 16px;
      border-radius: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .performance-container {
    .stats-grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .performance-container {
    padding: 16px;
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .stats-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
    
    .stat-card {
      padding: 20px;
    }
    
    .chart-section {
      .el-row {
        flex-direction: column;
      }
      
      .el-col {
        width: 100%;
        margin-bottom: 20px;
      }
    }
    
    .help-content .metric-item {
      flex-direction: column;
      text-align: center;
      
      .metric-icon {
        margin: 0 0 15px 0;
      }
    }
    
    .filter-card .el-form {
      .el-form-item {
        width: 100%;
        margin-right: 0;
        margin-bottom: 16px;
      }
      
      .filter-select {
        width: 100%;
      }
    }
  }
}
</style>