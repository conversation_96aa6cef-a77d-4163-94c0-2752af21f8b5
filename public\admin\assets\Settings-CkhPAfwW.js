/* empty css             *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                   *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                     *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                          *//* empty css               *//* empty css                 *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                     *//* empty css                  *//* empty css                *//* empty css                     */import{l as e,m as l,G as a,A as t,q as i,r as o,c as n,w as s,B as d,E as u,z as r,C as m,M as c,o as p,F as _,Y as f,H as g}from"./vue-vendor-BcnDv-68.js";import{ah as y,ao as h,W as b,aa as v,cl as V,af as w,X as k,a4 as C,a0 as x,a5 as S,a6 as U,a1 as T,a7 as F,aj as D,Z as R,ak as z,ai as P,an as j,V as M,aF as E,a2 as I,a3 as q,al as A,bj as L,aq as N,ax as G,az as O,ag as B,ap as $,aK as H,aH as W,aI as Q,aJ as K,U as Z,Y as J,b6 as Y,b7 as X,aQ as ee,ac as le,ci as ae,aw as te,av as ie,b4 as oe,bb as ne,cm as se,aE as de,at as ue,cn as re,aC as me,co as ce,b5 as pe,cg as _e,b2 as fe,aN as ge,a9 as ye,cp as he,bL as be,b3 as ve,bO as Ve,aS as we,b9 as ke,c5 as Ce,c6 as xe,c7 as Se,aW as Ue,aU as Te,aV as Fe,aZ as De,bP as Re,_ as ze,aL as Pe}from"./element-plus-C2UshkXo.js";/* empty css                  *//* empty css                    */import{_ as je,u as Me}from"./index-eUTsTR3J.js";import{a as Ee,t as Ie,u as qe}from"./system-Co5QGav0.js";/* empty css                   *//* empty css                      *//* empty css                       */import{f as Ae}from"./format-3eU4VJ9V.js";/* empty css                         */import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const Le={class:"image-upload"},Ne=["src"],Ge={class:"el-upload-list__item-actions"},Oe=["onClick"],Be=["onClick"],$e=["src"];const He=je({__name:"ImageUpload",props:{modelValue:{type:[String,Array],default:""},limit:{type:Number,default:1},accept:{type:String,default:"image/*"},maxSize:{type:Number,default:5}},emits:["update:modelValue"],setup(e,{expose:l,emit:a}){l();const t=e,i=a,d=Me(),u=o([]),r=o(!1),m=o(""),c=n(()=>"/api/v1/admin/upload/image"),p=n(()=>({Authorization:`Bearer ${d.token}`}));s(()=>t.modelValue,e=>{e?Array.isArray(e)?u.value=e.map((e,l)=>({name:`image-${l}`,url:e,uid:l})):u.value=[{name:"image",url:e,uid:1}]:u.value=[]},{immediate:!0});const _=()=>{const e=u.value.map(e=>e.url).filter(Boolean);1===t.limit?i("update:modelValue",e[0]||""):i("update:modelValue",e)},f={props:t,emit:i,userStore:d,fileList:u,dialogVisible:r,dialogImageUrl:m,uploadUrl:c,uploadHeaders:p,beforeUpload:e=>{const l=e.type.startsWith("image/"),a=e.size/1024/1024<t.maxSize;return l?!!a||(k.error(`图片大小不能超过 ${t.maxSize}MB!`),!1):(k.error("只能上传图片文件!"),!1)},handleSuccess:(e,l)=>{200===e.code?(l.url=e.data.url,_(),k.success("上传成功")):k.error(e.message||"上传失败")},handleError:e=>{k.error("上传失败")},handleRemove:e=>{const l=u.value.findIndex(l=>l.uid===e.uid);l>-1&&(u.value.splice(l,1),_())},handlePictureCardPreview:e=>{m.value=e.url,r.value=!0},updateModelValue:_,ref:o,computed:n,watch:s,get ElMessage(){return k},get Plus(){return w},get ZoomIn(){return V},get Delete(){return v},get useUserStore(){return Me}};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}},[["render",function(o,n,s,d,u,r){const m=y,c=h,p=b;return l(),e("div",Le,[a(c,{"file-list":d.fileList,"onUpdate:fileList":n[0]||(n[0]=e=>d.fileList=e),action:d.uploadUrl,headers:d.uploadHeaders,"before-upload":d.beforeUpload,"on-success":d.handleSuccess,"on-error":d.handleError,"on-remove":d.handleRemove,limit:s.limit,accept:s.accept,"list-type":"picture-card","auto-upload":!0},{file:t(({file:e})=>[i("div",null,[i("img",{class:"el-upload-list__item-thumbnail",src:e.url,alt:""},null,8,Ne),i("span",Ge,[i("span",{class:"el-upload-list__item-preview",onClick:l=>d.handlePictureCardPreview(e)},[a(m,null,{default:t(()=>[a(d.ZoomIn)]),_:1})],8,Oe),i("span",{class:"el-upload-list__item-delete",onClick:l=>d.handleRemove(e)},[a(m,null,{default:t(()=>[a(d.Delete)]),_:1})],8,Be)])])]),default:t(()=>[a(m,null,{default:t(()=>[a(d.Plus)]),_:1})]),_:1},8,["file-list","action","headers","limit","accept"]),a(p,{modelValue:d.dialogVisible,"onUpdate:modelValue":n[1]||(n[1]=e=>d.dialogVisible=e),title:"图片预览"},{default:t(()=>[i("img",{"w-full":"",src:d.dialogImageUrl,alt:"Preview Image"},null,8,$e)]),_:1},8,["modelValue"])])}],["__scopeId","data-v-36a93c58"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/Upload/ImageUpload.vue"]]),We={class:"audit-logs-container"},Qe={class:"filter-container"},Ke={class:"pagination-container"},Ze={key:0},Je={key:0},Ye={class:"code-block"};const Xe=je({__name:"AuditLogs",setup(e,{expose:l}){l();const a=o(!1),t=o([]),i=c({user:"",dateRange:[],actionType:""}),n=c({page:1,limit:10,total:0}),s=o(!1),d=o(null),u=Array.from({length:50}).map((e,l)=>{const a=[{id:1,name:"Admin"},{id:2,name:"Operator"}],t=["create","update","delete","login"][l%4];return{id:l+1,timestamp:new Date(Date.now()-3600*l*1e3).toISOString(),user:a[l%2],ip_address:`192.168.1.${l+1}`,action_type:t,description:`${a[l%2].name} ${t}了文章 '文章标题${l+1}'`,user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) ...",data_changes:"login"!==t?{before:{status:"draft"},after:{status:"published"}}:null}}),r=()=>{a.value=!0,setTimeout(()=>{n.total=u.length;const e=(n.page-1)*n.limit,l=e+n.limit;t.value=u.slice(e,l),a.value=!1},500)};p(()=>{r()});const m={loading:a,logs:t,filters:i,pagination:n,detailDialogVisible:s,selectedLog:d,mockLogs:u,fetchLogs:r,getActionTypeTag:e=>({create:"success",update:"primary",delete:"danger",login:"info"}[e]||"default"),viewLogDetail:e=>{d.value=e,s.value=!0},ref:o,reactive:c,onMounted:p,get formatDate(){return Ae},get Search(){return E}};return Object.defineProperty(m,"__isScriptSetup",{enumerable:!1,value:!0}),m}},[["render",function(o,n,s,c,p,_){const f=C,g=x,y=U,h=S,v=T,V=D,w=z,k=P,E=j,I=M,q=b,A=F;return l(),e("div",We,[a(I,null,{header:t(()=>n[6]||(n[6]=[i("h3",null,"系统日志",-1)])),default:t(()=>[i("div",Qe,[a(f,{modelValue:c.filters.user,"onUpdate:modelValue":n[0]||(n[0]=e=>c.filters.user=e),placeholder:"操作人",style:{width:"180px"},clearable:""},null,8,["modelValue"]),a(g,{modelValue:c.filters.dateRange,"onUpdate:modelValue":n[1]||(n[1]=e=>c.filters.dateRange=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"380px","margin-left":"10px"}},null,8,["modelValue"]),a(h,{modelValue:c.filters.actionType,"onUpdate:modelValue":n[2]||(n[2]=e=>c.filters.actionType=e),placeholder:"操作类型",style:{width:"150px","margin-left":"10px"},clearable:""},{default:t(()=>[a(y,{label:"创建",value:"create"}),a(y,{label:"更新",value:"update"}),a(y,{label:"删除",value:"delete"}),a(y,{label:"登录",value:"login"})]),_:1},8,["modelValue"]),a(v,{type:"primary",icon:"Search",onClick:c.fetchLogs,style:{"margin-left":"10px"}},{default:t(()=>n[7]||(n[7]=[u("搜索",-1)])),_:1,__:[7]})]),d((l(),r(k,{data:c.logs,style:{width:"100%","margin-top":"20px"}},{default:t(()=>[a(V,{prop:"timestamp",label:"操作时间",width:"180"},{default:t(({row:e})=>[u(R(c.formatDate(e.timestamp)),1)]),_:1}),a(V,{prop:"user.name",label:"操作人",width:"120"}),a(V,{prop:"ip_address",label:"IP地址",width:"150"}),a(V,{label:"操作类型",width:"100"},{default:t(({row:e})=>[a(w,{type:c.getActionTypeTag(e.action_type)},{default:t(()=>[u(R(e.action_type.toUpperCase()),1)]),_:2},1032,["type"])]),_:1}),a(V,{prop:"description",label:"操作描述"}),a(V,{label:"操作",width:"100"},{default:t(({row:e})=>[a(v,{link:"",type:"primary",size:"small",onClick:l=>c.viewLogDetail(e)},{default:t(()=>n[8]||(n[8]=[u("详情",-1)])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[A,c.loading]]),i("div",Ke,[a(E,{"current-page":c.pagination.page,"onUpdate:currentPage":n[3]||(n[3]=e=>c.pagination.page=e),"page-size":c.pagination.limit,"onUpdate:pageSize":n[4]||(n[4]=e=>c.pagination.limit=e),total:c.pagination.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:c.fetchLogs,onCurrentChange:c.fetchLogs},null,8,["current-page","page-size","total"])])]),_:1}),a(q,{modelValue:c.detailDialogVisible,"onUpdate:modelValue":n[5]||(n[5]=e=>c.detailDialogVisible=e),title:"日志详情",width:"600px"},{default:t(()=>[c.selectedLog?(l(),e("div",Ze,[i("p",null,[n[9]||(n[9]=i("strong",null,"操作描述:",-1)),u(" "+R(c.selectedLog.description),1)]),i("p",null,[n[10]||(n[10]=i("strong",null,"操作人:",-1)),u(" "+R(c.selectedLog.user.name)+" (ID: "+R(c.selectedLog.user.id)+")",1)]),i("p",null,[n[11]||(n[11]=i("strong",null,"时间:",-1)),u(" "+R(c.formatDate(c.selectedLog.timestamp)),1)]),i("p",null,[n[12]||(n[12]=i("strong",null,"IP 地址:",-1)),u(" "+R(c.selectedLog.ip_address),1)]),i("p",null,[n[13]||(n[13]=i("strong",null,"User Agent:",-1)),u(" "+R(c.selectedLog.user_agent),1)]),c.selectedLog.data_changes?(l(),e("div",Je,[n[14]||(n[14]=i("h4",null,"数据变更:",-1)),i("pre",Ye,R(JSON.stringify(c.selectedLog.data_changes,null,2)),1)])):m("",!0)])):m("",!0)]),_:1},8,["modelValue"])])}],["__scopeId","data-v-b8f80d3c"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/components/AuditLogs.vue"]]),el={class:"backup-settings-container"},ll={class:"card-header"};const al=je({__name:"BackupSettings",setup(e,{expose:l}){l();const a=o(!1),t=o(!1),i=o([]),n=c({enabled:!0,frequency:"daily",time:new Date(2023,1,1,2,0,0),retention:7}),s=()=>{a.value=!0,setTimeout(()=>{i.value=[{id:1,timestamp:"2024-06-01T02:00:00Z",filename:"backup-202406010200.zip",size:"128 MB",type:"db"},{id:2,timestamp:"2024-05-31T02:00:00Z",filename:"backup-202405310200.zip",size:"127 MB",type:"db"},{id:3,timestamp:"2024-05-30T15:30:00Z",filename:"manual-backup-202405301530.zip",size:"2.5 GB",type:"files"}],a.value=!1},500)};p(()=>{s()});const d={loading:a,creatingBackup:t,backupList:i,backupPolicy:n,fetchBackups:s,saveBackupPolicy:()=>{k.success("自动备份策略已保存")},createManualBackup:()=>{t.value=!0,k.info("正在创建手动备份，请稍候..."),setTimeout(()=>{t.value=!1,k.success("手动备份创建成功"),s()},3e3)},handleRestore:e=>{B.confirm(`确定要从备份文件 "${e.filename}" 恢复吗？此操作不可逆，将覆盖现有数据！`,"严重警告",{type:"warning",confirmButtonText:"我确定要恢复"}).then(()=>{k.info("正在执行恢复操作...")}).catch(()=>{})},handleDownload:e=>{k.success(`开始下载备份文件: ${e.filename}`)},handleDelete:e=>{B.confirm(`确定要删除备份文件 "${e.filename}" 吗？`,"确认删除",{type:"warning"}).then(()=>{i.value=i.value.filter(l=>l.id!==e.id),k.success("备份文件删除成功")}).catch(()=>{})},ref:o,reactive:c,onMounted:p,get ElMessage(){return k},get ElMessageBox(){return B},get formatDate(){return Ae},get Plus(){return w},get RefreshLeft(){return O},get Download(){return G},get Delete(){return v}};return Object.defineProperty(d,"__isScriptSetup",{enumerable:!1,value:!0}),d}},[["render",function(o,n,s,c,p,f){const g=A,y=q,h=U,b=S,v=L,V=N,w=T,k=I,C=M,x=D,j=z,E=P,G=F;return l(),e("div",el,[a(C,null,{header:t(()=>n[4]||(n[4]=[i("h3",null,"备份与恢复",-1)])),default:t(()=>[a(C,{shadow:"never",style:{"margin-bottom":"20px"}},{header:t(()=>n[5]||(n[5]=[i("h4",null,"自动备份策略",-1)])),default:t(()=>[a(k,{model:c.backupPolicy,"label-width":"120px"},{default:t(()=>[a(y,{label:"启用自动备份"},{default:t(()=>[a(g,{modelValue:c.backupPolicy.enabled,"onUpdate:modelValue":n[0]||(n[0]=e=>c.backupPolicy.enabled=e)},null,8,["modelValue"])]),_:1}),c.backupPolicy.enabled?(l(),e(_,{key:0},[a(y,{label:"备份频率"},{default:t(()=>[a(b,{modelValue:c.backupPolicy.frequency,"onUpdate:modelValue":n[1]||(n[1]=e=>c.backupPolicy.frequency=e),placeholder:"请选择频率"},{default:t(()=>[a(h,{label:"每天",value:"daily"}),a(h,{label:"每周",value:"weekly"})]),_:1},8,["modelValue"])]),_:1}),a(y,{label:"备份时间"},{default:t(()=>[a(v,{modelValue:c.backupPolicy.time,"onUpdate:modelValue":n[2]||(n[2]=e=>c.backupPolicy.time=e),format:"HH:mm",placeholder:"选择备份时间"},null,8,["modelValue"])]),_:1}),a(y,{label:"保留备份数量"},{default:t(()=>[a(V,{modelValue:c.backupPolicy.retention,"onUpdate:modelValue":n[3]||(n[3]=e=>c.backupPolicy.retention=e),min:1,max:30},null,8,["modelValue"]),n[6]||(n[6]=i("span",null," 个",-1))]),_:1,__:[6]})],64)):m("",!0),a(y,null,{default:t(()=>[a(w,{type:"primary",onClick:c.saveBackupPolicy},{default:t(()=>n[7]||(n[7]=[u("保存策略",-1)])),_:1,__:[7]})]),_:1})]),_:1},8,["model"])]),_:1}),a(C,{shadow:"never"},{header:t(()=>[i("div",ll,[n[9]||(n[9]=i("h4",null,"备份历史",-1)),a(w,{type:"primary",icon:"Plus",onClick:c.createManualBackup,loading:c.creatingBackup},{default:t(()=>n[8]||(n[8]=[u(" 创建手动备份 ",-1)])),_:1,__:[8]},8,["loading"])])]),default:t(()=>[d((l(),r(E,{data:c.backupList,style:{width:"100%"}},{default:t(()=>[a(x,{prop:"timestamp",label:"备份时间",width:"180"},{default:t(({row:e})=>[u(R(c.formatDate(e.timestamp)),1)]),_:1}),a(x,{prop:"filename",label:"文件名"}),a(x,{prop:"size",label:"文件大小",width:"120"}),a(x,{label:"类型",width:"100"},{default:t(({row:e})=>[a(j,{type:"db"===e.type?"success":"primary"},{default:t(()=>[u(R("db"===e.type?"数据库":"文件"),1)]),_:2},1032,["type"])]),_:1}),a(x,{label:"操作",width:"200",fixed:"right"},{default:t(({row:e})=>[a(w,{link:"",type:"primary",icon:"RefreshLeft",size:"small",onClick:l=>c.handleRestore(e)},{default:t(()=>n[10]||(n[10]=[u("恢复",-1)])),_:2,__:[10]},1032,["onClick"]),a(w,{link:"",type:"success",icon:"Download",size:"small",onClick:l=>c.handleDownload(e)},{default:t(()=>n[11]||(n[11]=[u("下载",-1)])),_:2,__:[11]},1032,["onClick"]),a(w,{link:"",type:"danger",icon:"Delete",size:"small",onClick:l=>c.handleDelete(e)},{default:t(()=>n[12]||(n[12]=[u("删除",-1)])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[G,c.loading]])]),_:1})]),_:1})])}],["__scopeId","data-v-52f4ae37"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/components/BackupSettings.vue"]]),tl={class:"community-settings-container"};const il=je({__name:"CommunitySettings",setup(e,{expose:l}){l();const a=o(!1),t=o(!1),i=c({default_welcome_message:"欢迎 {memberName} 加入 {groupName}！",default_rules:["rule_2"],moderation_enabled:!0,sensitive_words:"广告\n推广\n加V\n私聊",paid_groups_enabled:!0,events_enabled:!0,checkin_enabled:!1}),n=()=>{a.value=!0,setTimeout(()=>{a.value=!1},500)};p(()=>{n()});const s={loading:a,saving:t,settings:i,fetchSettings:n,saveSettings:()=>{t.value=!0,setTimeout(()=>{k.success("社群模块设置已保存"),t.value=!1},1e3)},ref:o,reactive:c,onMounted:p,get ElMessage(){return k}};return Object.defineProperty(s,"__isScriptSetup",{enumerable:!1,value:!0}),s}},[["render",function(o,n,s,d,r,m){const c=$,p=C,_=q,f=U,g=S,y=A,h=T,b=I,v=M;return l(),e("div",tl,[a(v,null,{header:t(()=>n[7]||(n[7]=[i("h3",null,"社群模块设置",-1)])),default:t(()=>[a(b,{model:d.settings,"label-width":"150px"},{default:t(()=>[a(c,{"content-position":"left"},{default:t(()=>n[8]||(n[8]=[u("默认设置",-1)])),_:1,__:[8]}),a(_,{label:"新成员默认欢迎语"},{default:t(()=>[a(p,{type:"textarea",rows:3,modelValue:d.settings.default_welcome_message,"onUpdate:modelValue":n[0]||(n[0]=e=>d.settings.default_welcome_message=e),placeholder:"可用 {memberName} 指代新成员昵称，{groupName} 指代群组名称"},null,8,["modelValue"])]),_:1}),a(_,{label:"新群组默认规则"},{default:t(()=>[a(g,{modelValue:d.settings.default_rules,"onUpdate:modelValue":n[1]||(n[1]=e=>d.settings.default_rules=e),multiple:"",placeholder:"选择默认应用的自动化规则",style:{width:"100%"}},{default:t(()=>[a(f,{label:"回复“入群”关键词",value:"rule_1"}),a(f,{label:"禁止发送广告链接",value:"rule_2"})]),_:1},8,["modelValue"])]),_:1}),a(c,{"content-position":"left"},{default:t(()=>n[9]||(n[9]=[u("内容审核",-1)])),_:1,__:[9]}),a(_,{label:"新内容先审后发"},{default:t(()=>[a(y,{modelValue:d.settings.moderation_enabled,"onUpdate:modelValue":n[2]||(n[2]=e=>d.settings.moderation_enabled=e)},null,8,["modelValue"]),n[10]||(n[10]=i("div",{class:"form-item-help"},"开启后，所有用户发布的新帖子和评论都需要经过管理员审核才能显示。",-1))]),_:1,__:[10]}),a(_,{label:"敏感词词库"},{default:t(()=>[a(p,{type:"textarea",rows:5,modelValue:d.settings.sensitive_words,"onUpdate:modelValue":n[3]||(n[3]=e=>d.settings.sensitive_words=e),placeholder:"每行一个敏感词"},null,8,["modelValue"]),n[11]||(n[11]=i("div",{class:"form-item-help"},"当用户发布的内容包含这些词汇时，将自动进入审核队列。",-1))]),_:1,__:[11]}),a(c,{"content-position":"left"},{default:t(()=>n[12]||(n[12]=[u("功能开关",-1)])),_:1,__:[12]}),a(_,{label:"允许创建付费群组"},{default:t(()=>[a(y,{modelValue:d.settings.paid_groups_enabled,"onUpdate:modelValue":n[4]||(n[4]=e=>d.settings.paid_groups_enabled=e)},null,8,["modelValue"])]),_:1}),a(_,{label:"开启活动功能"},{default:t(()=>[a(y,{modelValue:d.settings.events_enabled,"onUpdate:modelValue":n[5]||(n[5]=e=>d.settings.events_enabled=e)},null,8,["modelValue"])]),_:1}),a(_,{label:"开启打卡功能"},{default:t(()=>[a(y,{modelValue:d.settings.checkin_enabled,"onUpdate:modelValue":n[6]||(n[6]=e=>d.settings.checkin_enabled=e)},null,8,["modelValue"])]),_:1}),a(_,null,{default:t(()=>[a(h,{type:"primary",onClick:d.saveSettings,loading:d.saving},{default:t(()=>n[13]||(n[13]=[u("保存设置",-1)])),_:1,__:[13]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])}],["__scopeId","data-v-0070bf25"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/components/CommunitySettings.vue"]]),ol={class:"notification-template-manager"},nl={class:"template-header"},sl={class:"header-actions"},dl={class:"list-header"},ul={class:"view-controls"},rl={key:0,class:"template-grid"},ml={class:"card-header"},cl={class:"template-type"},pl={class:"template-actions"},_l={class:"card-content"},fl={class:"template-title"},gl={class:"template-description"},yl={class:"template-channels"},hl={class:"template-content-preview"},bl={class:"card-footer"},vl={class:"template-stats"},Vl={class:"stat-item"},wl={class:"stat-item"},kl={class:"template-status"},Cl={class:"pagination-container"},xl={class:"dialog-footer"},Sl={class:"template-preview-container"},Ul={class:"preview-header"},Tl={class:"preview-channels"},Fl={class:"preview-content"},Dl={class:"content-display"},Rl={key:0,class:"preview-variables"},zl={class:"variables-display"};const Pl=je({__name:"NotificationTemplateManager",setup(e,{expose:l}){l();const a=o("grid"),t=o(1),i=o(10),s=o(!1),d=c({type:"",channel:"",status:"",keyword:""}),u=o([{id:1,name:"用户注册欢迎通知",description:"新用户注册成功后发送的欢迎通知",type:"user",channels:["email","system"],content:"亲爱的{{username}}，欢迎加入我们的平台！您的账户已成功创建，开始您的精彩之旅吧！",variables:"{{username}}=用户名",usage_count:156,active:!0,created_at:"2024-01-01 10:00:00",updated_at:"2024-01-15 14:30:00"},{id:2,name:"订单确认通知",description:"用户下单成功后的确认通知",type:"user",channels:["email","sms","system"],content:"您的订单{{order_no}}已成功提交，订单金额{{amount}}元，我们将尽快为您处理。",variables:"{{order_no}}=订单号，{{amount}}=订单金额",usage_count:234,active:!0,created_at:"2024-01-02 09:15:00",updated_at:"2024-01-16 11:20:00"},{id:3,name:"提现成功通知",description:"用户提现申请成功处理后的通知",type:"user",channels:["email","sms"],content:"您的提现申请已成功处理，金额{{amount}}元已转入您的账户，请注意查收。",variables:"{{amount}}=提现金额，{{account}}=收款账户",usage_count:89,active:!0,created_at:"2024-01-03 16:00:00",updated_at:"2024-01-17 09:45:00"},{id:4,name:"系统维护通知",description:"系统维护前的用户通知",type:"system",channels:["email","system"],content:"系统将于{{maintenance_time}}进行维护，预计持续{{duration}}，期间可能影响正常使用，请您谅解。",variables:"{{maintenance_time}}=维护时间，{{duration}}=维护时长",usage_count:12,active:!1,created_at:"2024-01-04 08:30:00",updated_at:"2024-01-18 13:15:00"},{id:5,name:"营销推广通知",description:"产品促销活动通知",type:"marketing",channels:["email","wechat"],content:"限时优惠！{{product_name}}现在享受{{discount}}折扣，活动截止{{end_time}}，立即抢购！",variables:"{{product_name}}=产品名称，{{discount}}=折扣，{{end_time}}=结束时间",usage_count:67,active:!0,created_at:"2024-01-05 12:00:00",updated_at:"2024-01-19 10:30:00"}]),r=c({visible:!1,isEdit:!1}),m=c({name:"",description:"",type:"user",channels:[],content:"",variables:"",active:!0}),_=c({visible:!1}),f=o(null),g=n(()=>{let e=u.value;if(d.type&&(e=e.filter(e=>e.type===d.type)),d.channel&&(e=e.filter(e=>e.channels.includes(d.channel))),d.status){const l="active"===d.status;e=e.filter(e=>e.active===l)}if(d.keyword){const l=d.keyword.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(l)||e.content.toLowerCase().includes(l))}return e}),y=n(()=>{const e=(t.value-1)*i.value,l=e+i.value;return g.value.slice(e,l)}),h=()=>{Object.assign(m,{name:"",description:"",type:"user",channels:[],content:"",variables:"",active:!0})},b=e=>{Object.assign(m,e),r.isEdit=!0,r.visible=!0},v=e=>{Object.assign(m,{...e,name:e.name+" (副本)",id:void 0}),r.isEdit=!1,r.visible=!0},V=e=>{f.value=e,_.visible=!0},C=e=>{B.confirm(`确定要删除模板"${e.name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const l=u.value.findIndex(l=>l.id===e.id);l>-1&&(u.value.splice(l,1),k.success("模板删除成功"))})};p(()=>{});const x={viewMode:a,currentPage:t,pageSize:i,saving:s,filterForm:d,templates:u,templateDialog:r,templateForm:m,templateRules:{name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],type:[{required:!0,message:"请选择模板类型",trigger:"change"}],channels:[{required:!0,message:"请选择支持渠道",trigger:"change"}],content:[{required:!0,message:"请输入模板内容",trigger:"blur"}]},previewDialog:_,previewTemplate:f,filteredTemplates:g,paginatedTemplates:y,getTypeColor:e=>({user:"primary",system:"info",marketing:"warning",security:"danger"}[e]||"info"),getTypeName:e=>({user:"用户通知",system:"系统通知",marketing:"营销通知",security:"安全通知"}[e]||e),getChannelName:e=>({email:"邮件",sms:"短信",wechat:"微信",system:"系统"}[e]||e),formatDate:e=>e?.slice(0,16)||"",applyFilters:()=>{t.value=1},resetFilters:()=>{Object.assign(d,{type:"",channel:"",status:"",keyword:""}),t.value=1},showCreateDialog:()=>{h(),r.isEdit=!1,r.visible=!0},resetTemplateForm:h,handleTemplateAction:({action:e,template:l})=>{switch(e){case"edit":b(l);break;case"copy":v(l);break;case"preview":V(l);break;case"delete":C(l)}},editTemplate:b,copyTemplate:v,previewTemplateDialog:V,deleteTemplate:C,saveTemplate:async()=>{s.value=!0;try{if(await new Promise(e=>setTimeout(e,1e3)),r.isEdit){const e=u.value.findIndex(e=>e.id===m.id);e>-1&&(u.value[e]={...m,updated_at:(new Date).toISOString().slice(0,19).replace("T"," ")}),k.success("模板修改成功")}else{const e={...m,id:Date.now(),usage_count:0,created_at:(new Date).toISOString().slice(0,19).replace("T"," "),updated_at:(new Date).toISOString().slice(0,19).replace("T"," ")};u.value.unshift(e),k.success("模板创建成功")}r.visible=!1}catch(e){k.error("保存模板失败")}finally{s.value=!1}},toggleTemplateStatus:e=>{const l=e.active?"启用":"禁用";k.success(`模板"${e.name}"已${l}`)},importTemplates:()=>{k.info("模板导入功能开发中...")},exportTemplates:()=>{const e=JSON.stringify(u.value,null,2),l=new Blob([e],{type:"application/json"}),a=URL.createObjectURL(l),t=document.createElement("a");t.href=a,t.download=`notification-templates-${(new Date).toISOString().slice(0,10)}.json`,t.click(),URL.revokeObjectURL(a),k.success("模板已导出")},ref:o,reactive:c,computed:n,onMounted:p,get ElMessage(){return k},get ElMessageBox(){return B},get Plus(){return w},get Upload(){return oe},get Download(){return G},get Grid(){return ie},get List(){return te},get MoreFilled(){return ae},get View(){return le},get Clock(){return ee}};return Object.defineProperty(x,"__isScriptSetup",{enumerable:!1,value:!0}),x}},[["render",function(o,n,s,d,c,p){const g=y,h=T,v=U,V=S,w=q,k=C,x=I,F=M,E=H,L=z,N=K,G=Q,O=W,B=A,$=D,ee=P,le=j,ae=J,te=Z,ie=X,oe=Y,ne=b;return l(),e("div",ol,[i("div",nl,[n[21]||(n[21]=i("div",{class:"header-left"},[i("h3",null,"通知模板管理"),i("p",null,"管理系统中的所有通知模板，支持多渠道和变量配置")],-1)),i("div",sl,[a(h,{type:"primary",onClick:d.showCreateDialog},{default:t(()=>[a(g,null,{default:t(()=>[a(d.Plus)]),_:1}),n[18]||(n[18]=u(" 新建模板 ",-1))]),_:1,__:[18]}),a(h,{type:"info",onClick:d.importTemplates},{default:t(()=>[a(g,null,{default:t(()=>[a(d.Upload)]),_:1}),n[19]||(n[19]=u(" 导入模板 ",-1))]),_:1,__:[19]}),a(h,{onClick:d.exportTemplates},{default:t(()=>[a(g,null,{default:t(()=>[a(d.Download)]),_:1}),n[20]||(n[20]=u(" 导出模板 ",-1))]),_:1,__:[20]})])]),a(F,{class:"filter-card"},{default:t(()=>[a(x,{model:d.filterForm,inline:""},{default:t(()=>[a(w,{label:"模板类型"},{default:t(()=>[a(V,{modelValue:d.filterForm.type,"onUpdate:modelValue":n[0]||(n[0]=e=>d.filterForm.type=e),placeholder:"选择类型",clearable:"",style:{width:"150px"}},{default:t(()=>[a(v,{label:"用户通知",value:"user"}),a(v,{label:"系统通知",value:"system"}),a(v,{label:"营销通知",value:"marketing"}),a(v,{label:"安全通知",value:"security"})]),_:1},8,["modelValue"])]),_:1}),a(w,{label:"通知渠道"},{default:t(()=>[a(V,{modelValue:d.filterForm.channel,"onUpdate:modelValue":n[1]||(n[1]=e=>d.filterForm.channel=e),placeholder:"选择渠道",clearable:"",style:{width:"150px"}},{default:t(()=>[a(v,{label:"邮件",value:"email"}),a(v,{label:"短信",value:"sms"}),a(v,{label:"微信",value:"wechat"}),a(v,{label:"系统",value:"system"})]),_:1},8,["modelValue"])]),_:1}),a(w,{label:"状态"},{default:t(()=>[a(V,{modelValue:d.filterForm.status,"onUpdate:modelValue":n[2]||(n[2]=e=>d.filterForm.status=e),placeholder:"选择状态",clearable:"",style:{width:"120px"}},{default:t(()=>[a(v,{label:"启用",value:"active"}),a(v,{label:"禁用",value:"inactive"})]),_:1},8,["modelValue"])]),_:1}),a(w,{label:"关键词"},{default:t(()=>[a(k,{modelValue:d.filterForm.keyword,"onUpdate:modelValue":n[3]||(n[3]=e=>d.filterForm.keyword=e),placeholder:"搜索模板名称或内容",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(w,null,{default:t(()=>[a(h,{type:"primary",onClick:d.applyFilters},{default:t(()=>n[22]||(n[22]=[u("搜索",-1)])),_:1,__:[22]}),a(h,{onClick:d.resetFilters},{default:t(()=>n[23]||(n[23]=[u("重置",-1)])),_:1,__:[23]})]),_:1})]),_:1},8,["model"])]),_:1}),a(F,{class:"template-list-card"},{default:t(()=>[i("div",dl,[i("span",null,"共找到 "+R(d.filteredTemplates.length)+" 个模板",1),i("div",ul,[a(E,null,{default:t(()=>[a(h,{type:"grid"===d.viewMode?"primary":"",onClick:n[4]||(n[4]=e=>d.viewMode="grid")},{default:t(()=>[a(g,null,{default:t(()=>[a(d.Grid)]),_:1})]),_:1},8,["type"]),a(h,{type:"table"===d.viewMode?"primary":"",onClick:n[5]||(n[5]=e=>d.viewMode="table")},{default:t(()=>[a(g,null,{default:t(()=>[a(d.List)]),_:1})]),_:1},8,["type"])]),_:1})])]),"grid"===d.viewMode?(l(),e("div",rl,[(l(!0),e(_,null,f(d.paginatedTemplates,o=>(l(),e("div",{key:o.id,class:"template-card"},[i("div",ml,[i("div",cl,[a(L,{type:d.getTypeColor(o.type),size:"small"},{default:t(()=>[u(R(d.getTypeName(o.type)),1)]),_:2},1032,["type"])]),i("div",pl,[a(O,{onCommand:d.handleTemplateAction},{dropdown:t(()=>[a(G,null,{default:t(()=>[a(N,{command:{action:"edit",template:o}},{default:t(()=>[...n[24]||(n[24]=[u("编辑",-1)])]),_:2,__:[24]},1032,["command"]),a(N,{command:{action:"copy",template:o}},{default:t(()=>[...n[25]||(n[25]=[u("复制",-1)])]),_:2,__:[25]},1032,["command"]),a(N,{command:{action:"preview",template:o}},{default:t(()=>[...n[26]||(n[26]=[u("预览",-1)])]),_:2,__:[26]},1032,["command"]),a(N,{command:{action:"delete",template:o},divided:""},{default:t(()=>[...n[27]||(n[27]=[u("删除",-1)])]),_:2,__:[27]},1032,["command"])]),_:2},1024)]),default:t(()=>[a(h,{type:"text",size:"small"},{default:t(()=>[a(g,null,{default:t(()=>[a(d.MoreFilled)]),_:1})]),_:1})]),_:2},1024)])]),i("div",_l,[i("h4",fl,R(o.name),1),i("p",gl,R(o.description),1),i("div",yl,[(l(!0),e(_,null,f(o.channels,e=>(l(),r(L,{key:e,size:"small",class:"channel-tag"},{default:t(()=>[u(R(d.getChannelName(e)),1)]),_:2},1024))),128))]),i("div",hl,R(o.content),1)]),i("div",bl,[i("div",vl,[i("span",Vl,[a(g,null,{default:t(()=>[a(d.View)]),_:1}),u(" "+R(o.usage_count||0),1)]),i("span",wl,[a(g,null,{default:t(()=>[a(d.Clock)]),_:1}),u(" "+R(d.formatDate(o.updated_at)),1)])]),i("div",kl,[a(B,{modelValue:o.active,"onUpdate:modelValue":e=>o.active=e,onChange:e=>d.toggleTemplateStatus(o),size:"small"},null,8,["modelValue","onUpdate:modelValue","onChange"])])])]))),128))])):(l(),r(ee,{key:1,data:d.paginatedTemplates,style:{width:"100%"}},{default:t(()=>[a($,{prop:"name",label:"模板名称",width:"200"}),a($,{prop:"type",label:"类型",width:"100"},{default:t(e=>[a(L,{type:d.getTypeColor(e.row.type),size:"small"},{default:t(()=>[u(R(d.getTypeName(e.row.type)),1)]),_:2},1032,["type"])]),_:1}),a($,{prop:"channels",label:"支持渠道",width:"150"},{default:t(a=>[(l(!0),e(_,null,f(a.row.channels,e=>(l(),r(L,{key:e,size:"small",style:{"margin-right":"4px"}},{default:t(()=>[u(R(d.getChannelName(e)),1)]),_:2},1024))),128))]),_:1}),a($,{prop:"content",label:"模板内容","show-overflow-tooltip":""}),a($,{prop:"usage_count",label:"使用次数",width:"100"}),a($,{prop:"active",label:"状态",width:"80"},{default:t(e=>[a(B,{modelValue:e.row.active,"onUpdate:modelValue":l=>e.row.active=l,onChange:l=>d.toggleTemplateStatus(e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a($,{prop:"updated_at",label:"更新时间",width:"160"},{default:t(e=>[u(R(d.formatDate(e.row.updated_at)),1)]),_:1}),a($,{label:"操作",width:"150"},{default:t(e=>[a(h,{type:"text",size:"small",onClick:l=>d.editTemplate(e.row)},{default:t(()=>n[28]||(n[28]=[u("编辑",-1)])),_:2,__:[28]},1032,["onClick"]),a(h,{type:"text",size:"small",onClick:l=>d.previewTemplate(e.row)},{default:t(()=>n[29]||(n[29]=[u("预览",-1)])),_:2,__:[29]},1032,["onClick"]),a(h,{type:"text",size:"small",onClick:l=>d.deleteTemplate(e.row)},{default:t(()=>n[30]||(n[30]=[u("删除",-1)])),_:2,__:[30]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),i("div",Cl,[a(le,{"current-page":d.currentPage,"onUpdate:currentPage":n[6]||(n[6]=e=>d.currentPage=e),"page-size":d.pageSize,"onUpdate:pageSize":n[7]||(n[7]=e=>d.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:d.filteredTemplates.length},null,8,["current-page","page-size","total"])])]),_:1}),a(ne,{title:d.templateDialog.isEdit?"编辑模板":"创建模板",modelValue:d.templateDialog.visible,"onUpdate:modelValue":n[16]||(n[16]=e=>d.templateDialog.visible=e),width:"800px","close-on-click-modal":!1},{footer:t(()=>[i("div",xl,[a(h,{onClick:n[15]||(n[15]=e=>d.templateDialog.visible=!1)},{default:t(()=>n[35]||(n[35]=[u("取消",-1)])),_:1,__:[35]}),a(h,{type:"primary",onClick:d.saveTemplate,loading:d.saving},{default:t(()=>[u(R(d.templateDialog.isEdit?"保存修改":"创建模板"),1)]),_:1},8,["loading"])])]),default:t(()=>[a(x,{model:d.templateForm,rules:d.templateRules,ref:"templateFormRef","label-width":"100px"},{default:t(()=>[a(te,{gutter:20},{default:t(()=>[a(ae,{span:12},{default:t(()=>[a(w,{label:"模板名称",prop:"name"},{default:t(()=>[a(k,{modelValue:d.templateForm.name,"onUpdate:modelValue":n[8]||(n[8]=e=>d.templateForm.name=e),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1})]),_:1}),a(ae,{span:12},{default:t(()=>[a(w,{label:"模板类型",prop:"type"},{default:t(()=>[a(V,{modelValue:d.templateForm.type,"onUpdate:modelValue":n[9]||(n[9]=e=>d.templateForm.type=e),placeholder:"选择模板类型",style:{width:"100%"}},{default:t(()=>[a(v,{label:"用户通知",value:"user"}),a(v,{label:"系统通知",value:"system"}),a(v,{label:"营销通知",value:"marketing"}),a(v,{label:"安全通知",value:"security"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(w,{label:"模板描述",prop:"description"},{default:t(()=>[a(k,{modelValue:d.templateForm.description,"onUpdate:modelValue":n[10]||(n[10]=e=>d.templateForm.description=e),type:"textarea",rows:2,placeholder:"简要描述模板用途"},null,8,["modelValue"])]),_:1}),a(w,{label:"支持渠道",prop:"channels"},{default:t(()=>[a(oe,{modelValue:d.templateForm.channels,"onUpdate:modelValue":n[11]||(n[11]=e=>d.templateForm.channels=e)},{default:t(()=>[a(ie,{label:"email"},{default:t(()=>n[31]||(n[31]=[u("邮件",-1)])),_:1,__:[31]}),a(ie,{label:"sms"},{default:t(()=>n[32]||(n[32]=[u("短信",-1)])),_:1,__:[32]}),a(ie,{label:"wechat"},{default:t(()=>n[33]||(n[33]=[u("微信",-1)])),_:1,__:[33]}),a(ie,{label:"system"},{default:t(()=>n[34]||(n[34]=[u("系统",-1)])),_:1,__:[34]})]),_:1},8,["modelValue"])]),_:1}),a(w,{label:"模板内容",prop:"content"},{default:t(()=>[a(k,{modelValue:d.templateForm.content,"onUpdate:modelValue":n[12]||(n[12]=e=>d.templateForm.content=e),type:"textarea",rows:8,placeholder:"请输入模板内容，支持变量：{{username}}, {{amount}}, {{time}}等"},null,8,["modelValue"])]),_:1}),a(w,{label:"变量说明"},{default:t(()=>[a(k,{modelValue:d.templateForm.variables,"onUpdate:modelValue":n[13]||(n[13]=e=>d.templateForm.variables=e),type:"textarea",rows:3,placeholder:"变量说明，例如：{{username}}=用户名，{{amount}}=金额"},null,8,["modelValue"])]),_:1}),a(w,{label:"启用状态"},{default:t(()=>[a(B,{modelValue:d.templateForm.active,"onUpdate:modelValue":n[14]||(n[14]=e=>d.templateForm.active=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"]),a(ne,{title:"模板预览",modelValue:d.previewDialog.visible,"onUpdate:modelValue":n[17]||(n[17]=e=>d.previewDialog.visible=e),width:"600px"},{default:t(()=>[i("div",Sl,[i("div",Ul,[i("h4",null,R(d.previewTemplate?.name),1),a(L,{type:d.getTypeColor(d.previewTemplate?.type),size:"small"},{default:t(()=>[u(R(d.getTypeName(d.previewTemplate?.type)),1)]),_:1},8,["type"])]),i("div",Tl,[n[36]||(n[36]=i("span",null,"支持渠道：",-1)),(l(!0),e(_,null,f(d.previewTemplate?.channels,e=>(l(),r(L,{key:e,size:"small",style:{"margin-left":"8px"}},{default:t(()=>[u(R(d.getChannelName(e)),1)]),_:2},1024))),128))]),i("div",Fl,[n[37]||(n[37]=i("h5",null,"模板内容：",-1)),i("div",Dl,R(d.previewTemplate?.content),1)]),d.previewTemplate?.variables?(l(),e("div",Rl,[n[38]||(n[38]=i("h5",null,"变量说明：",-1)),i("div",zl,R(d.previewTemplate?.variables),1)])):m("",!0)])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-3c2b4863"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/components/NotificationTemplateManager.vue"]]),jl={class:"app-container"},Ml={style:{"font-size":"14px",color:"#666",margin:"8px 0 0 0"}},El={class:"card-header"},Il={class:"header-actions"},ql={class:"channel-config-section"},Al={class:"channel-list"},Ll={class:"channel-item"},Nl={class:"channel-info"},Gl={class:"channel-item"},Ol={class:"channel-info"},Bl={class:"channel-item"},$l={class:"channel-info"},Hl={class:"channel-item"},Wl={class:"channel-info"},Ql={class:"channel-config-section"},Kl={class:"channel-status-list"},Zl={class:"status-item"},Jl={class:"service-header"},Yl={class:"status-item"},Xl={class:"service-header"},ea={class:"status-item"},la={class:"service-header"},aa={class:"status-item"},ta={class:"service-header"},ia={class:"events-config"},oa={class:"events-header"},na={class:"global-settings-section"},sa={key:0,style:{"margin-top":"8px"}},da={key:0,style:{"margin-top":"8px"}},ua={key:0,style:{"margin-top":"8px"}},ra={class:"global-settings-section"},ma={class:"notification-actions",style:{"margin-top":"20px","text-align":"center"}},ca={class:"rules-config-container"},pa={class:"rule-section"},_a={key:0,style:{"margin-top":"10px"}},fa={style:{"margin-top":"5px",display:"inline-block"}},ga={class:"rule-section"},ya={key:0,style:{"margin-top":"10px"}},ha={class:"channel-priority-list"},ba={class:"rule-section"},va={key:0,style:{"margin-top":"10px"}},Va={style:{"margin-top":"8px"}},wa={key:0,style:{"margin-top":"10px"}},ka={key:0,style:{"margin-top":"10px"}},Ca={class:"rule-section"},xa={key:0,style:{"margin-top":"10px"}},Sa={key:0,style:{"margin-top":"10px"}},Ua={class:"dialog-footer"},Ta={class:"dialog-footer"},Fa={class:"dialog-footer"},Da={class:"dialog-footer"},Ra={class:"email-templates"},za={class:"template-info"},Pa={class:"template-name"},ja={class:"template-desc"},Ma={key:0,class:"template-config"},Ea={class:"config-item"},Ia={class:"dialog-footer"},qa={class:"sms-template-manager"},Aa={class:"template-actions",style:{"margin-bottom":"16px"}},La={class:"dialog-footer"};const Na=je({__name:"Settings",setup(e,{expose:l}){l();const a=o("basic"),t=o(),i=o(),s=o(),d=o(),u=o({site_name:"",site_description:"",site_keywords:"",site_logo:"",site_favicon:"",contact_email:"",contact_phone:"",icp_number:""}),r=o({wechat_enabled:!1,wechat_app_id:"",wechat_mch_id:"",wechat_key:"",alipay_enabled:!1,alipay_app_id:"",alipay_private_key:"",alipay_public_key:""}),m=o({email_enabled:!0,sms_enabled:!1,wechat_enabled:!1,system_enabled:!0,user_register:["system"],new_order:["email","system"],withdrawal_request:["email","system"]}),_=o("channels"),f=o(!1),g=o(!1),y=o(!1),h=c({visible:!1}),b=c({visible:!1}),v=o("frequency"),V=o(!1),C=c({same_type_interval:30,daily_user_limit:20,peak_hour_limit:!0,peak_start:"09:00",peak_end:"18:00",peak_reduction_rate:50}),x=c({user_preference_priority:!0,fallback_enabled:!0,priority_order:["system","email","sms","wechat"],urgent_multi_channel:!0}),S=c({do_not_disturb:!0,dnd_start:"22:00",dnd_end:"08:00",dnd_except_urgent:!0,optimal_time:!0,optimal_hours:["09","14","19"],holiday_handling:!0,holiday_strategy:"delay"}),U=c({personalization:!0,multi_language:!0,supported_languages:["zh-CN","zh-TW"],length_optimization:!0,sms_max_length:70}),T=o(!1),F=o(!1),D=o(!1),R=o(!1),z=o(!1),P=o(!1),j=o(!1),M=o(!1),E=o(!1),I=o(!1),q=o(!1),A=o(""),L=o([{key:"qq",name:"QQ邮箱",desc:"适用于QQ邮箱、QQ企业邮箱",config:{host:"smtp.qq.com",port:587,encryption:"tls"}},{key:"163",name:"网易163邮箱",desc:"适用于163邮箱、126邮箱、yeah.net",config:{host:"smtp.163.com",port:587,encryption:"tls"}},{key:"gmail",name:"Gmail",desc:"适用于Gmail、Google Workspace",config:{host:"smtp.gmail.com",port:587,encryption:"tls"}},{key:"outlook",name:"Outlook/Hotmail",desc:"适用于Outlook.com、Hotmail、Live邮箱",config:{host:"smtp-mail.outlook.com",port:587,encryption:"tls"}},{key:"aliyun",name:"阿里云邮件推送",desc:"适用于阿里云邮件推送服务",config:{host:"smtpdm.aliyun.com",port:465,encryption:"ssl"}},{key:"tencent",name:"腾讯云邮件推送",desc:"适用于腾讯云SES服务",config:{host:"smtp.qcloudmail.com",port:587,encryption:"tls"}},{key:"custom",name:"自定义配置",desc:"手动输入SMTP服务器信息",config:{host:"",port:587,encryption:"tls"}}]),N=o([{id:1,name:"登录验证码",type:"verification",template_id:"SMS_123456",content:"您的登录验证码是：${code}，5分钟内有效，请勿泄露。",variables:"code",active:!0},{id:2,name:"订单通知",type:"notification",template_id:"SMS_789012",content:"您的订单${order_no}已确认，金额${amount}元。",variables:"order_no,amount",active:!0},{id:3,name:"提现通知",type:"notification",template_id:"SMS_345678",content:"您的提现申请已处理，金额${amount}元已到账。",variables:"amount",active:!1}]),G=c({host:"",port:587,encryption:"tls",username:"",password:"",from_name:"系统通知",test_email:""}),O=c({provider:"",access_key:"",access_secret:"",secret_id:"",secret_key:"",sdk_app_id:"",sign_name:"",test_phone:""}),$=c({type:"",corp_id:"",corp_secret:"",agent_id:null,app_id:"",app_secret:"",test_user:""}),H=o({email:!1,sms:!1,wechat:!1,system:!0}),W=n(()=>H.value.email||H.value.sms||H.value.wechat),Q=n(()=>{let e=1;return H.value.email&&e++,H.value.sms&&e++,H.value.wechat&&e++,e}),K=n(()=>J.value.filter(e=>e.enabled).length),Z=n(()=>{let e=0;return C.peak_hour_limit&&e++,x.fallback_enabled&&e++,S.do_not_disturb&&e++,S.optimal_time&&e++,U.personalization&&e++,U.multi_language&&e++,e}),J=o([{id:1,name:"用户注册",description:"新用户注册时发送欢迎通知",channels:["system"],enabled:!0},{id:2,name:"订单创建",description:"用户创建新订单时通知管理员",channels:["email","system"],enabled:!0},{id:3,name:"提现申请",description:"用户提交提现申请时通知",channels:["email","sms","system"],enabled:!0},{id:4,name:"系统维护",description:"系统维护前通知所有用户",channels:["system","email"],enabled:!1}]),Y=o({rate_limit_enabled:!0,max_per_hour:50,night_mode_enabled:!0,night_start:"22:00",night_end:"08:00",retry_enabled:!0,max_retries:3,enable_logging:!0,queue_enabled:!0,template_cache:!0}),X=o({login_captcha:!0,register_captcha:!0,password_strength:!0,login_attempts:5,session_timeout:120,ip_whitelist:""}),ee=async()=>{try{const{data:e}=await Ee();u.value={...u.value,...e.basic},r.value={...r.value,...e.payment},m.value={...m.value,...e.notification},X.value={...X.value,...e.security}}catch(e){k.error("获取系统设置失败")}},le=e=>({email:"邮件通知",sms:"短信通知",wechat:"微信通知",system:"系统通知"}[e]||e),ae=async e=>new Promise((l,a)=>{setTimeout(()=>{"email"!==e||H.value.email?("sms"!==e||H.value.sms,l()):l()},500)});p(()=>{ee()});const te={activeMenu:a,basicFormRef:t,paymentFormRef:i,notificationFormRef:s,securityFormRef:d,basicForm:u,paymentForm:r,notificationForm:m,notificationActiveTab:_,notificationSaving:f,showAddEventDialog:g,showRulesConfigDialog:y,templateManagerDialog:h,rulesConfigDialog:b,rulesActiveTab:v,rulesSaving:V,frequencyRules:C,channelRules:x,timingRules:S,contentRules:U,showEmailConfigDialog:T,showSmsConfigDialog:F,showWechatConfigDialog:D,showSmsTemplateDialog:R,showEmailTemplateDialog:z,emailSaving:P,emailTesting:j,smsSaving:M,smsTesting:E,wechatSaving:I,wechatTesting:q,selectedEmailTemplate:A,emailTemplates:L,smsTemplates:N,emailConfig:G,emailRules:{host:[{required:!0,message:"请输入SMTP服务器地址",trigger:"blur"}],port:[{required:!0,message:"请输入端口号",trigger:"blur"}],username:[{required:!0,message:"请输入发件人邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],password:[{required:!0,message:"请输入邮箱密码或授权码",trigger:"blur"}]},smsConfig:O,smsRules:{provider:[{required:!0,message:"请选择短信服务商",trigger:"change"}],access_key:[{required:!0,message:"请输入Access Key",trigger:"blur"}],access_secret:[{required:!0,message:"请输入Access Secret",trigger:"blur"}],sign_name:[{required:!0,message:"请输入短信签名",trigger:"blur"}]},wechatConfig:$,wechatRules:{type:[{required:!0,message:"请选择微信通知类型",trigger:"change"}]},notificationChannelStatus:H,hasAnyServiceConfigured:W,configuredServicesCount:Q,activeEventsCount:K,activeRulesCount:Z,notificationEvents:J,notificationGlobalSettings:Y,securityForm:X,basicRules:{site_name:[{required:!0,message:"请输入网站名称",trigger:"blur"}],contact_email:[{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}]},fetchSettings:ee,handleMenuSelect:e=>{a.value=e},saveBasicSettings:async()=>{try{await t.value.validate(),await qe("basic",u.value),k.success("基础设置保存成功")}catch(e){k.error("保存基础设置失败")}},resetBasicForm:()=>{t.value.resetFields()},savePaymentSettings:async()=>{try{await qe("payment",r.value),k.success("支付设置保存成功")}catch(e){k.error("保存支付设置失败")}},testPayment:async()=>{try{await Ie(r.value),k.success("支付配置测试通过")}catch(e){k.error("支付配置测试失败")}},saveNotificationSettings:async()=>{f.value=!0;try{const e={...m.value,channel_status:H.value,events:J.value,global_settings:Y.value};await qe("notification",e),k.success("通知设置保存成功")}catch(e){k.error("保存通知设置失败")}finally{f.value=!1}},resetNotificationSettings:()=>{Object.assign(m.value,{email_enabled:!0,sms_enabled:!1,wechat_enabled:!1,system_enabled:!0}),Object.assign(Y.value,{rate_limit_enabled:!0,max_per_hour:50,night_mode_enabled:!0,night_start:"22:00",night_end:"08:00",retry_enabled:!0,max_retries:3,enable_logging:!0,queue_enabled:!0,template_cache:!0}),k.success("通知设置已重置")},exportNotificationConfig:()=>{const e={basic:m.value,channels:H.value,events:J.value,global:Y.value,export_time:(new Date).toISOString()},l=JSON.stringify(e,null,2),a=new Blob([l],{type:"application/json"}),t=URL.createObjectURL(a),i=document.createElement("a");i.href=t,i.download=`notification-config-${(new Date).toISOString().slice(0,10)}.json`,i.click(),URL.revokeObjectURL(t),k.success("通知配置已导出")},handleChannelChange:async(e,l)=>{try{l?(await ae(e),H.value[e]=!0,k.success(`${le(e)}已启用`)):(H.value[e]=!1,k.info(`${le(e)}已禁用`))}catch(a){m.value[`${e}_enabled`]=!1,k.error(`${le(e)}配置有误，请检查设置`)}},getChannelName:le,checkChannelConfiguration:ae,testEmailNotification:async()=>{try{k.success("邮件测试发送成功，请检查收件箱")}catch(e){k.error("邮件测试发送失败")}},testSmsNotification:async()=>{try{k.success("短信测试发送成功")}catch(e){k.error("短信测试发送失败")}},testWechatNotification:async()=>{try{k.success("微信通知测试发送成功")}catch(e){k.error("微信通知测试发送失败")}},updateEventChannels:(e,l)=>{const a=J.value.find(l=>l.id===e);a&&(a.channels=l,k.success("事件通知渠道已更新"))},updateEventStatus:(e,l)=>{const a=J.value.find(l=>l.id===e);if(a){a.enabled=l;const e=l?"启用":"禁用";k.success(`事件 ${a.name} 已${e}`)}},editNotificationEvent:e=>{k.info(`编辑事件: ${e.name}`)},deleteNotificationEvent:e=>{const l=J.value.findIndex(l=>l.id===e);if(l>-1){const e=J.value[l];J.value.splice(l,1),k.success(`事件 ${e.name} 已删除`)}},openAdvancedNotificationSettings:()=>{k.info("打开高级通知配置界面")},openTemplateManager:()=>{h.visible=!0},moveChannelUp:e=>{if(e>0){const l=x.priority_order[e];x.priority_order[e]=x.priority_order[e-1],x.priority_order[e-1]=l}},moveChannelDown:e=>{if(e<x.priority_order.length-1){const l=x.priority_order[e];x.priority_order[e]=x.priority_order[e+1],x.priority_order[e+1]=l}},saveRulesConfig:async()=>{V.value=!0;try{await new Promise(e=>setTimeout(e,1500)),k.success("智能规则配置已保存"),b.visible=!1}catch(e){k.error("保存规则配置失败")}finally{V.value=!1}},saveEmailConfig:async()=>{P.value=!0;try{await new Promise(e=>setTimeout(e,1500)),H.value.email=!0,k.success("邮件服务配置已保存"),T.value=!1}catch(e){k.error("保存邮件配置失败")}finally{P.value=!1}},testEmailConfig:async()=>{if(G.test_email){j.value=!0;try{await new Promise(e=>setTimeout(e,2e3)),k.success(`测试邮件已发送到 ${G.test_email}，请检查收件箱`)}catch(e){k.error("邮件发送测试失败，请检查配置")}finally{j.value=!1}}else k.warning("请先填写测试邮箱")},handleSmsProviderChange:e=>{Object.assign(O,{provider:e,access_key:"",access_secret:"",secret_id:"",secret_key:"",sdk_app_id:"",sign_name:"",test_phone:O.test_phone})},saveSmsConfig:async()=>{M.value=!0;try{await new Promise(e=>setTimeout(e,1500)),H.value.sms=!0,k.success("短信服务配置已保存"),F.value=!1}catch(e){k.error("保存短信配置失败")}finally{M.value=!1}},testSmsConfig:async()=>{if(O.test_phone){E.value=!0;try{await new Promise(e=>setTimeout(e,2e3)),k.success(`测试短信已发送到 ${O.test_phone}`)}catch(e){k.error("短信发送测试失败，请检查配置")}finally{E.value=!1}}else k.warning("请先填写测试手机号")},handleWechatTypeChange:e=>{Object.assign($,{type:e,corp_id:"",corp_secret:"",agent_id:null,app_id:"",app_secret:"",test_user:$.test_user})},saveWechatConfig:async()=>{I.value=!0;try{await new Promise(e=>setTimeout(e,1500)),H.value.wechat=!0,k.success("微信服务配置已保存"),D.value=!1}catch(e){k.error("保存微信配置失败")}finally{I.value=!1}},testWechatConfig:async()=>{if($.test_user){q.value=!0;try{await new Promise(e=>setTimeout(e,2e3)),k.success(`测试通知已发送到用户 ${$.test_user}`)}catch(e){k.error("微信通知测试失败，请检查配置")}finally{q.value=!1}}else k.warning("请先填写测试用户")},applyEmailTemplate:e=>{const l=L.value.find(l=>l.key===e);l&&"custom"!==l.key&&(Object.assign(G,l.config),k.success(`已应用${l.name}模板配置`))},confirmEmailTemplate:()=>{const e=L.value.find(e=>e.key===A.value);e&&("custom"===e.key?k.info("请手动填写SMTP服务器信息"):(Object.assign(G,e.config),k.success(`已应用${e.name}模板，请填写邮箱和密码信息`)),z.value=!1)},getSmsTemplateTypeColor:e=>({verification:"primary",notification:"success",marketing:"warning",alert:"danger"}[e]||"info"),getSmsTemplateTypeName:e=>({verification:"验证码",notification:"通知",marketing:"营销",alert:"告警"}[e]||e),addSmsTemplate:()=>{k.info("添加短信模板功能开发中...")},editSmsTemplate:e=>{k.info(`编辑模板：${e.name}`)},testSmsTemplate:async e=>{if(O.test_phone){k.info(`使用模板 ${e.name} 发送测试短信到 ${O.test_phone}`);try{await new Promise(e=>setTimeout(e,2e3)),k.success("测试短信发送成功")}catch(l){k.error("测试短信发送失败")}}else k.warning("请先在短信配置中填写测试手机号")},deleteSmsTemplate:e=>{B.confirm(`确定要删除短信模板"${e.name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const l=N.value.findIndex(l=>l.id===e.id);l>-1&&(N.value.splice(l,1),k.success("短信模板删除成功"))})},saveSecuritySettings:async()=>{try{await qe("security",X.value),k.success("安全设置保存成功")}catch(e){k.error("保存安全设置失败")}},ref:o,reactive:c,onMounted:p,computed:n,get ElMessage(){return k},get ElMessageBox(){return B},get Setting(){return ke},get CreditCard(){return we},get Bell(){return Ve},get Lock(){return ve},get Message(){return be},get FolderOpened(){return he},get User(){return ye},get Connection(){return ge},get Grid(){return ie},get Document(){return fe},get DataLine(){return _e},get ChatDotRound(){return pe},get ChatSquare(){return ce},get Plus(){return w},get Check(){return me},get Rank(){return re},get ArrowUp(){return ue},get ArrowDown(){return de},get Position(){return se},get CircleCheckFilled(){return ne},ImageUpload:He,get getSystemSettings(){return Ee},get updateSystemSettings(){return qe},get testPaymentConfig(){return Ie},AuditLogs:Xe,BackupSettings:al,CommunitySettings:il,NotificationTemplateManager:Pl};return Object.defineProperty(te,"__isScriptSetup",{enumerable:!1,value:!0}),te}},[["render",function(o,n,s,c,p,h){const v=y,V=xe,w=Se,k=Ce,x=M,F=J,j=C,E=q,G=T,O=I,B=Ue,W=$,Q=A,K=De,ee=z,le=Z,ae=Fe,te=D,ie=X,oe=Y,ne=P,se=Re,de=N,ue=L,re=Te,me=b,ce=H,pe=Pe,_e=ze,fe=U,ge=S;return l(),e("div",jl,[a(le,{gutter:20},{default:t(()=>[a(F,{span:6},{default:t(()=>[a(x,null,{header:t(()=>n[109]||(n[109]=[i("h3",null,"系统设置",-1)])),default:t(()=>[a(k,{modelValue:c.activeMenu,"onUpdate:modelValue":n[0]||(n[0]=e=>c.activeMenu=e),class:"settings-menu",onSelect:c.handleMenuSelect},{default:t(()=>[a(V,{index:"basic"},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Setting)]),_:1}),n[110]||(n[110]=i("span",null,"基础设置",-1))]),_:1,__:[110]}),a(V,{index:"security"},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Lock)]),_:1}),n[111]||(n[111]=i("span",null,"安全设置",-1))]),_:1,__:[111]}),a(w,{index:"integrations"},{title:t(()=>[a(v,null,{default:t(()=>[a(c.Connection)]),_:1}),n[112]||(n[112]=i("span",null,"集成设置",-1))]),default:t(()=>[a(V,{index:"payment"},{default:t(()=>n[113]||(n[113]=[u("支付快速配置",-1)])),_:1,__:[113]}),a(V,{index:"notification"},{default:t(()=>n[114]||(n[114]=[u("通知设置",-1)])),_:1,__:[114]}),a(V,{index:"storage"},{default:t(()=>n[115]||(n[115]=[u("存储设置",-1)])),_:1,__:[115]})]),_:1}),a(w,{index:"modules"},{title:t(()=>[a(v,null,{default:t(()=>[a(c.Grid)]),_:1}),n[116]||(n[116]=i("span",null,"模块设置",-1))]),default:t(()=>[a(V,{index:"community_settings"},{default:t(()=>n[117]||(n[117]=[u("社群设置",-1)])),_:1,__:[117]})]),_:1}),a(V,{index:"audit_logs"},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Document)]),_:1}),n[118]||(n[118]=i("span",null,"系统日志",-1))]),_:1,__:[118]}),a(V,{index:"backup"},{default:t(()=>[a(v,null,{default:t(()=>[a(c.DataLine)]),_:1}),n[119]||(n[119]=i("span",null,"备份与恢复",-1))]),_:1,__:[119]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(F,{span:18},{default:t(()=>[d(a(x,null,{header:t(()=>n[120]||(n[120]=[i("h3",null,"基础设置",-1)])),default:t(()=>[a(O,{ref:"basicFormRef",model:c.basicForm,rules:c.basicRules,"label-width":"120px"},{default:t(()=>[a(E,{label:"网站名称",prop:"site_name"},{default:t(()=>[a(j,{modelValue:c.basicForm.site_name,"onUpdate:modelValue":n[1]||(n[1]=e=>c.basicForm.site_name=e),placeholder:"请输入网站名称"},null,8,["modelValue"])]),_:1}),a(E,{label:"网站描述",prop:"site_description"},{default:t(()=>[a(j,{modelValue:c.basicForm.site_description,"onUpdate:modelValue":n[2]||(n[2]=e=>c.basicForm.site_description=e),type:"textarea",rows:3,placeholder:"请输入网站描述"},null,8,["modelValue"])]),_:1}),a(E,{label:"网站关键词",prop:"site_keywords"},{default:t(()=>[a(j,{modelValue:c.basicForm.site_keywords,"onUpdate:modelValue":n[3]||(n[3]=e=>c.basicForm.site_keywords=e),placeholder:"请输入网站关键词，用逗号分隔"},null,8,["modelValue"])]),_:1}),a(E,{label:"网站Logo",prop:"site_logo"},{default:t(()=>[a(c.ImageUpload,{modelValue:c.basicForm.site_logo,"onUpdate:modelValue":n[4]||(n[4]=e=>c.basicForm.site_logo=e),limit:1},null,8,["modelValue"])]),_:1}),a(E,{label:"网站图标",prop:"site_favicon"},{default:t(()=>[a(c.ImageUpload,{modelValue:c.basicForm.site_favicon,"onUpdate:modelValue":n[5]||(n[5]=e=>c.basicForm.site_favicon=e),limit:1},null,8,["modelValue"])]),_:1}),a(E,{label:"联系邮箱",prop:"contact_email"},{default:t(()=>[a(j,{modelValue:c.basicForm.contact_email,"onUpdate:modelValue":n[6]||(n[6]=e=>c.basicForm.contact_email=e),placeholder:"请输入联系邮箱"},null,8,["modelValue"])]),_:1}),a(E,{label:"联系电话",prop:"contact_phone"},{default:t(()=>[a(j,{modelValue:c.basicForm.contact_phone,"onUpdate:modelValue":n[7]||(n[7]=e=>c.basicForm.contact_phone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),a(E,{label:"备案号",prop:"icp_number"},{default:t(()=>[a(j,{modelValue:c.basicForm.icp_number,"onUpdate:modelValue":n[8]||(n[8]=e=>c.basicForm.icp_number=e),placeholder:"请输入备案号"},null,8,["modelValue"])]),_:1}),a(E,null,{default:t(()=>[a(G,{type:"primary",onClick:c.saveBasicSettings},{default:t(()=>n[121]||(n[121]=[u("保存设置",-1)])),_:1,__:[121]}),a(G,{onClick:c.resetBasicForm},{default:t(()=>n[122]||(n[122]=[u("重置",-1)])),_:1,__:[122]})]),_:1})]),_:1},8,["model"])]),_:1},512),[[g,"basic"===c.activeMenu]]),d(a(x,null,{header:t(()=>[n[125]||(n[125]=i("h3",null,"支付快速配置",-1)),i("p",Ml,[n[124]||(n[124]=u("快速启用和配置基本支付功能，详细配置请前往 ",-1)),a(B,{type:"primary",href:"/payment",target:"_blank"},{default:t(()=>n[123]||(n[123]=[u("支付管理",-1)])),_:1,__:[123]})])]),default:t(()=>[a(O,{ref:"paymentFormRef",model:c.paymentForm,"label-width":"120px"},{default:t(()=>[a(W,{"content-position":"left"},{default:t(()=>n[126]||(n[126]=[u("微信支付",-1)])),_:1,__:[126]}),a(E,{label:"启用微信支付"},{default:t(()=>[a(Q,{modelValue:c.paymentForm.wechat_enabled,"onUpdate:modelValue":n[9]||(n[9]=e=>c.paymentForm.wechat_enabled=e)},null,8,["modelValue"])]),_:1}),c.paymentForm.wechat_enabled?(l(),e(_,{key:0},[a(E,{label:"应用ID",prop:"wechat_app_id"},{default:t(()=>[a(j,{modelValue:c.paymentForm.wechat_app_id,"onUpdate:modelValue":n[10]||(n[10]=e=>c.paymentForm.wechat_app_id=e),placeholder:"请输入微信应用ID"},null,8,["modelValue"])]),_:1}),a(E,{label:"商户号",prop:"wechat_mch_id"},{default:t(()=>[a(j,{modelValue:c.paymentForm.wechat_mch_id,"onUpdate:modelValue":n[11]||(n[11]=e=>c.paymentForm.wechat_mch_id=e),placeholder:"请输入微信商户号"},null,8,["modelValue"])]),_:1}),a(E,{label:"API密钥",prop:"wechat_key"},{default:t(()=>[a(j,{modelValue:c.paymentForm.wechat_key,"onUpdate:modelValue":n[12]||(n[12]=e=>c.paymentForm.wechat_key=e),type:"password",placeholder:"请输入微信API密钥","show-password":""},null,8,["modelValue"])]),_:1})],64)):m("",!0),a(W,{"content-position":"left"},{default:t(()=>n[127]||(n[127]=[u("支付宝支付",-1)])),_:1,__:[127]}),a(E,{label:"启用支付宝"},{default:t(()=>[a(Q,{modelValue:c.paymentForm.alipay_enabled,"onUpdate:modelValue":n[13]||(n[13]=e=>c.paymentForm.alipay_enabled=e)},null,8,["modelValue"])]),_:1}),c.paymentForm.alipay_enabled?(l(),e(_,{key:1},[a(E,{label:"应用ID",prop:"alipay_app_id"},{default:t(()=>[a(j,{modelValue:c.paymentForm.alipay_app_id,"onUpdate:modelValue":n[14]||(n[14]=e=>c.paymentForm.alipay_app_id=e),placeholder:"请输入支付宝应用ID"},null,8,["modelValue"])]),_:1}),a(E,{label:"应用私钥",prop:"alipay_private_key"},{default:t(()=>[a(j,{modelValue:c.paymentForm.alipay_private_key,"onUpdate:modelValue":n[15]||(n[15]=e=>c.paymentForm.alipay_private_key=e),type:"textarea",rows:4,placeholder:"请输入支付宝应用私钥"},null,8,["modelValue"])]),_:1}),a(E,{label:"支付宝公钥",prop:"alipay_public_key"},{default:t(()=>[a(j,{modelValue:c.paymentForm.alipay_public_key,"onUpdate:modelValue":n[16]||(n[16]=e=>c.paymentForm.alipay_public_key=e),type:"textarea",rows:4,placeholder:"请输入支付宝公钥"},null,8,["modelValue"])]),_:1})],64)):m("",!0),a(E,null,{default:t(()=>[a(G,{type:"primary",onClick:c.savePaymentSettings},{default:t(()=>n[128]||(n[128]=[u("保存设置",-1)])),_:1,__:[128]}),a(G,{onClick:c.testPayment},{default:t(()=>n[129]||(n[129]=[u("测试支付",-1)])),_:1,__:[129]})]),_:1})]),_:1},8,["model"])]),_:1},512),[[g,"payment"===c.activeMenu]]),d(a(x,null,{header:t(()=>[i("div",El,[n[132]||(n[132]=i("h3",null,"通知配置中心",-1)),i("div",Il,[a(G,{type:"info",size:"small",onClick:c.openTemplateManager},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Document)]),_:1}),n[130]||(n[130]=u(" 模板管理 ",-1))]),_:1,__:[130]}),a(G,{type:"primary",size:"small",onClick:c.openAdvancedNotificationSettings},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Setting)]),_:1}),n[131]||(n[131]=u(" 高级配置 ",-1))]),_:1,__:[131]})])])]),default:t(()=>[a(re,{modelValue:c.notificationActiveTab,"onUpdate:modelValue":n[40]||(n[40]=e=>c.notificationActiveTab=e),type:"card"},{default:t(()=>[a(ae,{label:"通知渠道",name:"channels"},{default:t(()=>[c.hasAnyServiceConfigured?m("",!0):(l(),r(K,{key:0,title:"配置提醒",type:"warning",closable:!1,style:{"margin-bottom":"20px"}},{default:t(()=>n[133]||(n[133]=[u(' 检测到您还未配置任何通知服务，请先点击右侧"配置"按钮完成基础服务配置，才能正常发送通知。 ',-1)])),_:1})),c.hasAnyServiceConfigured?(l(),r(K,{key:1,title:"✨ 配置完成",type:"success",closable:!1,style:{"margin-bottom":"20px"}},{default:t(()=>n[134]||(n[134]=[u(" 通知服务配置完成！您现在可以正常发送通知了。建议测试各个渠道的连通性，确保服务正常运行。 ",-1)])),_:1})):m("",!0),a(le,{gutter:20},{default:t(()=>[a(F,{span:12},{default:t(()=>[i("div",ql,[n[139]||(n[139]=i("h4",null,"通知渠道开关",-1)),i("div",Al,[i("div",Ll,[i("div",Nl,[a(v,null,{default:t(()=>[a(c.Message)]),_:1}),n[135]||(n[135]=i("div",{class:"channel-details"},[i("span",{class:"channel-name"},"邮件通知"),i("span",{class:"channel-desc"},"通过SMTP发送邮件通知")],-1))]),a(Q,{modelValue:c.notificationForm.email_enabled,"onUpdate:modelValue":n[17]||(n[17]=e=>c.notificationForm.email_enabled=e),onChange:n[18]||(n[18]=e=>c.handleChannelChange("email",e))},null,8,["modelValue"])]),i("div",Gl,[i("div",Ol,[a(v,null,{default:t(()=>[a(c.ChatDotRound)]),_:1}),n[136]||(n[136]=i("div",{class:"channel-details"},[i("span",{class:"channel-name"},"短信通知"),i("span",{class:"channel-desc"},"通过短信平台发送通知")],-1))]),a(Q,{modelValue:c.notificationForm.sms_enabled,"onUpdate:modelValue":n[19]||(n[19]=e=>c.notificationForm.sms_enabled=e),onChange:n[20]||(n[20]=e=>c.handleChannelChange("sms",e))},null,8,["modelValue"])]),i("div",Bl,[i("div",$l,[a(v,null,{default:t(()=>[a(c.ChatSquare)]),_:1}),n[137]||(n[137]=i("div",{class:"channel-details"},[i("span",{class:"channel-name"},"微信通知"),i("span",{class:"channel-desc"},"通过企业微信发送通知")],-1))]),a(Q,{modelValue:c.notificationForm.wechat_enabled,"onUpdate:modelValue":n[21]||(n[21]=e=>c.notificationForm.wechat_enabled=e),onChange:n[22]||(n[22]=e=>c.handleChannelChange("wechat",e))},null,8,["modelValue"])]),i("div",Hl,[i("div",Wl,[a(v,null,{default:t(()=>[a(c.Bell)]),_:1}),n[138]||(n[138]=i("div",{class:"channel-details"},[i("span",{class:"channel-name"},"系统通知"),i("span",{class:"channel-desc"},"站内系统消息通知")],-1))]),a(Q,{modelValue:c.notificationForm.system_enabled,"onUpdate:modelValue":n[23]||(n[23]=e=>c.notificationForm.system_enabled=e),onChange:n[24]||(n[24]=e=>c.handleChannelChange("system",e))},null,8,["modelValue"])])])])]),_:1}),a(F,{span:12},{default:t(()=>[i("div",Ql,[n[152]||(n[152]=i("h4",null,"服务配置 & 状态",-1)),i("div",Kl,[c.notificationForm.email_enabled||c.notificationForm.sms_enabled||c.notificationForm.wechat_enabled?m("",!0):(l(),r(K,{key:0,title:"警告",type:"warning",description:"所有外部通知渠道均已禁用，用户将无法收到重要通知","show-icon":"",closable:!1})),i("div",Zl,[i("div",Jl,[n[142]||(n[142]=i("span",null,"邮件服务：",-1)),a(ee,{type:c.notificationChannelStatus.email?"success":"danger",size:"small"},{default:t(()=>[u(R(c.notificationChannelStatus.email?"已配置":"未配置"),1)]),_:1},8,["type"]),a(G,{type:"text",size:"small",onClick:n[25]||(n[25]=e=>c.showEmailConfigDialog=!0)},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Setting)]),_:1}),n[140]||(n[140]=u(" 配置 ",-1))]),_:1,__:[140]}),c.notificationForm.email_enabled?(l(),r(G,{key:0,type:"text",size:"small",onClick:c.testEmailNotification},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Position)]),_:1}),n[141]||(n[141]=u(" 测试 ",-1))]),_:1,__:[141]})):m("",!0)])]),i("div",Yl,[i("div",Xl,[n[145]||(n[145]=i("span",null,"短信服务：",-1)),a(ee,{type:c.notificationChannelStatus.sms?"success":"danger",size:"small"},{default:t(()=>[u(R(c.notificationChannelStatus.sms?"已配置":"未配置"),1)]),_:1},8,["type"]),a(G,{type:"text",size:"small",onClick:n[26]||(n[26]=e=>c.showSmsConfigDialog=!0)},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Setting)]),_:1}),n[143]||(n[143]=u(" 配置 ",-1))]),_:1,__:[143]}),c.notificationForm.sms_enabled?(l(),r(G,{key:0,type:"text",size:"small",onClick:c.testSmsNotification},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Position)]),_:1}),n[144]||(n[144]=u(" 测试 ",-1))]),_:1,__:[144]})):m("",!0)])]),i("div",ea,[i("div",la,[n[148]||(n[148]=i("span",null,"微信服务：",-1)),a(ee,{type:c.notificationChannelStatus.wechat?"success":"danger",size:"small"},{default:t(()=>[u(R(c.notificationChannelStatus.wechat?"已配置":"未配置"),1)]),_:1},8,["type"]),a(G,{type:"text",size:"small",onClick:n[27]||(n[27]=e=>c.showWechatConfigDialog=!0)},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Setting)]),_:1}),n[146]||(n[146]=u(" 配置 ",-1))]),_:1,__:[146]}),c.notificationForm.wechat_enabled?(l(),r(G,{key:0,type:"text",size:"small",onClick:c.testWechatNotification},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Position)]),_:1}),n[147]||(n[147]=u(" 测试 ",-1))]),_:1,__:[147]})):m("",!0)])]),i("div",aa,[i("div",ta,[n[150]||(n[150]=i("span",null,"系统服务：",-1)),a(ee,{type:"success",size:"small"},{default:t(()=>n[149]||(n[149]=[u("正常",-1)])),_:1,__:[149]}),n[151]||(n[151]=i("span",{style:{"margin-left":"8px",color:"#909399","font-size":"12px"}},"无需配置",-1))])])])])]),_:1})]),_:1})]),_:1}),a(ae,{label:"事件规则",name:"events"},{default:t(()=>[i("div",ia,[i("div",oa,[n[155]||(n[155]=i("h4",null,"通知事件配置",-1)),a(G,{type:"primary",size:"small",onClick:n[28]||(n[28]=e=>c.showAddEventDialog=!0)},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Plus)]),_:1}),n[153]||(n[153]=u(" 添加事件 ",-1))]),_:1,__:[153]}),a(G,{type:"info",size:"small",onClick:n[29]||(n[29]=e=>c.showRulesConfigDialog=!0)},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Setting)]),_:1}),n[154]||(n[154]=u(" 智能规则 ",-1))]),_:1,__:[154]})]),a(ne,{data:c.notificationEvents,style:{width:"100%"}},{default:t(()=>[a(te,{prop:"name",label:"事件名称",width:"180"}),a(te,{prop:"description",label:"描述"}),a(te,{label:"通知渠道",width:"200"},{default:t(e=>[a(oe,{modelValue:e.row.channels,"onUpdate:modelValue":l=>e.row.channels=l,onChange:l=>c.updateEventChannels(e.row.id,l)},{default:t(()=>[a(ie,{label:"email",disabled:!c.notificationForm.email_enabled},{default:t(()=>n[156]||(n[156]=[u("邮件",-1)])),_:1,__:[156]},8,["disabled"]),a(ie,{label:"sms",disabled:!c.notificationForm.sms_enabled},{default:t(()=>n[157]||(n[157]=[u("短信",-1)])),_:1,__:[157]},8,["disabled"]),a(ie,{label:"wechat",disabled:!c.notificationForm.wechat_enabled},{default:t(()=>n[158]||(n[158]=[u("微信",-1)])),_:1,__:[158]},8,["disabled"]),a(ie,{label:"system"},{default:t(()=>n[159]||(n[159]=[u("系统",-1)])),_:1,__:[159]})]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(te,{prop:"enabled",label:"启用",width:"80"},{default:t(e=>[a(Q,{modelValue:e.row.enabled,"onUpdate:modelValue":l=>e.row.enabled=l,onChange:l=>c.updateEventStatus(e.row.id,l)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(te,{label:"操作",width:"120"},{default:t(e=>[a(G,{size:"small",onClick:l=>c.editNotificationEvent(e.row)},{default:t(()=>n[160]||(n[160]=[u("编辑",-1)])),_:2,__:[160]},1032,["onClick"]),a(G,{size:"small",type:"danger",onClick:l=>c.deleteNotificationEvent(e.row.id)},{default:t(()=>n[161]||(n[161]=[u("删除",-1)])),_:2,__:[161]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),_:1}),a(ae,{label:"全局设置",name:"global"},{default:t(()=>[a(x,{class:"config-summary",style:{"margin-bottom":"20px"}},{header:t(()=>n[162]||(n[162]=[i("div",{class:"card-header"},[i("h4",{style:{margin:"0"}},"📊 当前配置摘要")],-1)])),default:t(()=>[a(le,{gutter:20},{default:t(()=>[a(F,{span:6},{default:t(()=>[a(se,{title:"已配置服务",value:c.configuredServicesCount,suffix:"个"},{prefix:t(()=>[a(v,{style:{"vertical-align":"-0.125em"}},{default:t(()=>[a(c.Setting)]),_:1})]),_:1},8,["value"])]),_:1}),a(F,{span:6},{default:t(()=>[a(se,{title:"活跃通知事件",value:c.activeEventsCount,suffix:"个"},{prefix:t(()=>[a(v,{style:{"vertical-align":"-0.125em"}},{default:t(()=>[a(c.Bell)]),_:1})]),_:1},8,["value"])]),_:1}),a(F,{span:6},{default:t(()=>[a(se,{title:"智能规则",value:c.activeRulesCount,suffix:"条"},{prefix:t(()=>[a(v,{style:{"vertical-align":"-0.125em"}},{default:t(()=>[a(c.Check)]),_:1})]),_:1},8,["value"])]),_:1}),a(F,{span:6},{default:t(()=>[a(se,{title:"系统状态",value:"运行正常"},{prefix:t(()=>[a(v,{style:{"vertical-align":"-0.125em",color:"#67c23a"}},{default:t(()=>[a(c.CircleCheckFilled)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),a(le,{gutter:20},{default:t(()=>[a(F,{span:12},{default:t(()=>[i("div",na,[n[168]||(n[168]=i("h4",null,"发送控制",-1)),a(O,{model:c.notificationGlobalSettings,"label-width":"120px"},{default:t(()=>[a(E,{label:"频率限制"},{default:t(()=>[a(Q,{modelValue:c.notificationGlobalSettings.rate_limit_enabled,"onUpdate:modelValue":n[30]||(n[30]=e=>c.notificationGlobalSettings.rate_limit_enabled=e)},null,8,["modelValue"]),c.notificationGlobalSettings.rate_limit_enabled?(l(),e("div",sa,[n[163]||(n[163]=u(" 每用户每小时最多 ",-1)),a(de,{modelValue:c.notificationGlobalSettings.max_per_hour,"onUpdate:modelValue":n[31]||(n[31]=e=>c.notificationGlobalSettings.max_per_hour=e),min:1,max:100,size:"small",style:{width:"80px",margin:"0 5px"}},null,8,["modelValue"]),n[164]||(n[164]=u(" 条通知 ",-1))])):m("",!0)]),_:1}),a(E,{label:"夜间免打扰"},{default:t(()=>[a(Q,{modelValue:c.notificationGlobalSettings.night_mode_enabled,"onUpdate:modelValue":n[32]||(n[32]=e=>c.notificationGlobalSettings.night_mode_enabled=e)},null,8,["modelValue"]),c.notificationGlobalSettings.night_mode_enabled?(l(),e("div",da,[a(ue,{modelValue:c.notificationGlobalSettings.night_start,"onUpdate:modelValue":n[33]||(n[33]=e=>c.notificationGlobalSettings.night_start=e),format:"HH:mm",placeholder:"开始时间",size:"small",style:{width:"100px","margin-right":"8px"}},null,8,["modelValue"]),n[165]||(n[165]=u(" 至 ",-1)),a(ue,{modelValue:c.notificationGlobalSettings.night_end,"onUpdate:modelValue":n[34]||(n[34]=e=>c.notificationGlobalSettings.night_end=e),format:"HH:mm",placeholder:"结束时间",size:"small",style:{width:"100px","margin-left":"8px"}},null,8,["modelValue"])])):m("",!0)]),_:1}),a(E,{label:"失败重试"},{default:t(()=>[a(Q,{modelValue:c.notificationGlobalSettings.retry_enabled,"onUpdate:modelValue":n[35]||(n[35]=e=>c.notificationGlobalSettings.retry_enabled=e)},null,8,["modelValue"]),c.notificationGlobalSettings.retry_enabled?(l(),e("div",ua,[n[166]||(n[166]=u(" 失败后最多重试 ",-1)),a(de,{modelValue:c.notificationGlobalSettings.max_retries,"onUpdate:modelValue":n[36]||(n[36]=e=>c.notificationGlobalSettings.max_retries=e),min:1,max:5,size:"small",style:{width:"60px",margin:"0 5px"}},null,8,["modelValue"]),n[167]||(n[167]=u(" 次 ",-1))])):m("",!0)]),_:1})]),_:1},8,["model"])])]),_:1}),a(F,{span:12},{default:t(()=>[i("div",ra,[n[172]||(n[172]=i("h4",null,"其他设置",-1)),a(O,{model:c.notificationGlobalSettings,"label-width":"120px"},{default:t(()=>[a(E,{label:"通知日志"},{default:t(()=>[a(Q,{modelValue:c.notificationGlobalSettings.enable_logging,"onUpdate:modelValue":n[37]||(n[37]=e=>c.notificationGlobalSettings.enable_logging=e)},null,8,["modelValue"]),n[169]||(n[169]=i("div",{style:{"margin-top":"4px","font-size":"12px",color:"#909399"}}," 记录所有通知发送状态和结果 ",-1))]),_:1,__:[169]}),a(E,{label:"队列处理"},{default:t(()=>[a(Q,{modelValue:c.notificationGlobalSettings.queue_enabled,"onUpdate:modelValue":n[38]||(n[38]=e=>c.notificationGlobalSettings.queue_enabled=e)},null,8,["modelValue"]),n[170]||(n[170]=i("div",{style:{"margin-top":"4px","font-size":"12px",color:"#909399"}}," 使用队列异步处理通知发送 ",-1))]),_:1,__:[170]}),a(E,{label:"模板缓存"},{default:t(()=>[a(Q,{modelValue:c.notificationGlobalSettings.template_cache,"onUpdate:modelValue":n[39]||(n[39]=e=>c.notificationGlobalSettings.template_cache=e)},null,8,["modelValue"]),n[171]||(n[171]=i("div",{style:{"margin-top":"4px","font-size":"12px",color:"#909399"}}," 缓存通知模板提高发送速度 ",-1))]),_:1,__:[171]})]),_:1},8,["model"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"]),i("div",ma,[a(G,{type:"primary",onClick:c.saveNotificationSettings,loading:c.notificationSaving},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Check)]),_:1}),n[173]||(n[173]=u(" 保存所有设置 ",-1))]),_:1,__:[173]},8,["loading"]),a(G,{onClick:c.resetNotificationSettings},{default:t(()=>n[174]||(n[174]=[u("重置设置",-1)])),_:1,__:[174]}),a(G,{type:"info",onClick:c.exportNotificationConfig},{default:t(()=>n[175]||(n[175]=[u("导出配置",-1)])),_:1,__:[175]})])]),_:1},512),[[g,"notification"===c.activeMenu]]),d(a(x,null,{header:t(()=>n[176]||(n[176]=[i("h3",null,"安全设置",-1)])),default:t(()=>[a(O,{ref:"securityFormRef",model:c.securityForm,"label-width":"120px"},{default:t(()=>[a(E,{label:"登录验证码"},{default:t(()=>[a(Q,{modelValue:c.securityForm.login_captcha,"onUpdate:modelValue":n[41]||(n[41]=e=>c.securityForm.login_captcha=e)},null,8,["modelValue"])]),_:1}),a(E,{label:"注册验证码"},{default:t(()=>[a(Q,{modelValue:c.securityForm.register_captcha,"onUpdate:modelValue":n[42]||(n[42]=e=>c.securityForm.register_captcha=e)},null,8,["modelValue"])]),_:1}),a(E,{label:"密码强度检查"},{default:t(()=>[a(Q,{modelValue:c.securityForm.password_strength,"onUpdate:modelValue":n[43]||(n[43]=e=>c.securityForm.password_strength=e)},null,8,["modelValue"])]),_:1}),a(E,{label:"登录失败限制"},{default:t(()=>[a(de,{modelValue:c.securityForm.login_attempts,"onUpdate:modelValue":n[44]||(n[44]=e=>c.securityForm.login_attempts=e),min:3,max:10,placeholder:"登录失败次数限制"},null,8,["modelValue"]),n[177]||(n[177]=i("span",{style:{"margin-left":"10px"}},"次后锁定账户",-1))]),_:1,__:[177]}),a(E,{label:"会话超时时间"},{default:t(()=>[a(de,{modelValue:c.securityForm.session_timeout,"onUpdate:modelValue":n[45]||(n[45]=e=>c.securityForm.session_timeout=e),min:30,max:1440,placeholder:"会话超时时间"},null,8,["modelValue"]),n[178]||(n[178]=i("span",{style:{"margin-left":"10px"}},"分钟",-1))]),_:1,__:[178]}),a(E,{label:"IP白名单"},{default:t(()=>[a(j,{modelValue:c.securityForm.ip_whitelist,"onUpdate:modelValue":n[46]||(n[46]=e=>c.securityForm.ip_whitelist=e),type:"textarea",rows:3,placeholder:"请输入IP白名单，每行一个IP或IP段"},null,8,["modelValue"])]),_:1}),a(E,null,{default:t(()=>[a(G,{type:"primary",onClick:c.saveSecuritySettings},{default:t(()=>n[179]||(n[179]=[u("保存设置",-1)])),_:1,__:[179]})]),_:1})]),_:1},8,["model"])]),_:1},512),[[g,"security"===c.activeMenu]]),"audit_logs"===c.activeMenu?(l(),r(c.AuditLogs,{key:0})):m("",!0),"backup"===c.activeMenu?(l(),r(c.BackupSettings,{key:1})):m("",!0),"community_settings"===c.activeMenu?(l(),r(c.CommunitySettings,{key:2})):m("",!0),a(me,{title:"通知模板管理",modelValue:c.templateManagerDialog.visible,"onUpdate:modelValue":n[47]||(n[47]=e=>c.templateManagerDialog.visible=e),width:"90%","close-on-click-modal":!1,top:"5vh"},{default:t(()=>[a(c.NotificationTemplateManager)]),_:1},8,["modelValue"]),a(me,{title:"智能通知规则配置",modelValue:c.rulesConfigDialog.visible,"onUpdate:modelValue":n[72]||(n[72]=e=>c.rulesConfigDialog.visible=e),width:"800px","close-on-click-modal":!1},{footer:t(()=>[i("div",Ua,[a(G,{onClick:n[71]||(n[71]=e=>c.rulesConfigDialog.visible=!1)},{default:t(()=>n[216]||(n[216]=[u("取消",-1)])),_:1,__:[216]}),a(G,{type:"primary",onClick:c.saveRulesConfig,loading:c.rulesSaving},{default:t(()=>n[217]||(n[217]=[u(" 保存规则配置 ",-1)])),_:1,__:[217]},8,["loading"])])]),default:t(()=>[i("div",ca,[a(K,{title:"智能规则帮助",type:"info",closable:!1,style:{"margin-bottom":"20px"}},{default:t(()=>n[180]||(n[180]=[u(" 智能规则可以根据条件自动选择通知渠道、发送时间和频率，提升通知的精准性和用户体验。 ",-1)])),_:1}),a(re,{modelValue:c.rulesActiveTab,"onUpdate:modelValue":n[70]||(n[70]=e=>c.rulesActiveTab=e),type:"card"},{default:t(()=>[a(ae,{label:"频率控制",name:"frequency"},{default:t(()=>[i("div",pa,[n[190]||(n[190]=i("h4",null,"发送频率规则",-1)),a(O,{model:c.frequencyRules,"label-width":"120px"},{default:t(()=>[a(E,{label:"同类通知间隔"},{default:t(()=>[a(de,{modelValue:c.frequencyRules.same_type_interval,"onUpdate:modelValue":n[48]||(n[48]=e=>c.frequencyRules.same_type_interval=e),min:1,max:1440,style:{width:"120px"}},null,8,["modelValue"]),n[181]||(n[181]=u(" 分钟 ",-1)),n[182]||(n[182]=i("span",{style:{"margin-left":"10px",color:"#909399","font-size":"12px"}}," 相同类型通知的最小发送间隔 ",-1))]),_:1,__:[181,182]}),a(E,{label:"用户每日限额"},{default:t(()=>[a(de,{modelValue:c.frequencyRules.daily_user_limit,"onUpdate:modelValue":n[49]||(n[49]=e=>c.frequencyRules.daily_user_limit=e),min:1,max:100,style:{width:"120px"}},null,8,["modelValue"]),n[183]||(n[183]=u(" 条 ",-1)),n[184]||(n[184]=i("span",{style:{"margin-left":"10px",color:"#909399","font-size":"12px"}}," 每个用户每天最多接收的通知数量 ",-1))]),_:1,__:[183,184]}),a(E,{label:"高峰期限制"},{default:t(()=>[a(Q,{modelValue:c.frequencyRules.peak_hour_limit,"onUpdate:modelValue":n[50]||(n[50]=e=>c.frequencyRules.peak_hour_limit=e)},null,8,["modelValue"]),c.frequencyRules.peak_hour_limit?(l(),e("div",_a,[n[187]||(n[187]=u(" 高峰期时间： ",-1)),a(ue,{modelValue:c.frequencyRules.peak_start,"onUpdate:modelValue":n[51]||(n[51]=e=>c.frequencyRules.peak_start=e),format:"HH:mm",style:{width:"100px",margin:"0 8px"}},null,8,["modelValue"]),n[188]||(n[188]=u(" 至 ",-1)),a(ue,{modelValue:c.frequencyRules.peak_end,"onUpdate:modelValue":n[52]||(n[52]=e=>c.frequencyRules.peak_end=e),format:"HH:mm",style:{width:"100px",margin:"0 8px"}},null,8,["modelValue"]),n[189]||(n[189]=i("br",null,null,-1)),i("span",fa,[n[185]||(n[185]=u(" 高峰期降低发送频率至 ",-1)),a(de,{modelValue:c.frequencyRules.peak_reduction_rate,"onUpdate:modelValue":n[53]||(n[53]=e=>c.frequencyRules.peak_reduction_rate=e),min:10,max:90,step:10,style:{width:"80px",margin:"0 5px"}},null,8,["modelValue"]),n[186]||(n[186]=u("% ",-1))])])):m("",!0)]),_:1})]),_:1},8,["model"])])]),_:1}),a(ae,{label:"渠道选择",name:"channel"},{default:t(()=>[i("div",ga,[n[194]||(n[194]=i("h4",null,"智能渠道选择规则",-1)),a(O,{model:c.channelRules,"label-width":"120px"},{default:t(()=>[a(E,{label:"用户偏好优先"},{default:t(()=>[a(Q,{modelValue:c.channelRules.user_preference_priority,"onUpdate:modelValue":n[54]||(n[54]=e=>c.channelRules.user_preference_priority=e)},null,8,["modelValue"]),n[191]||(n[191]=i("div",{style:{"margin-top":"5px",color:"#909399","font-size":"12px"}}," 优先使用用户设置的首选通知渠道 ",-1))]),_:1,__:[191]}),a(E,{label:"渠道降级策略"},{default:t(()=>[a(Q,{modelValue:c.channelRules.fallback_enabled,"onUpdate:modelValue":n[55]||(n[55]=e=>c.channelRules.fallback_enabled=e)},null,8,["modelValue"]),c.channelRules.fallback_enabled?(l(),e("div",ya,[n[192]||(n[192]=i("div",null,"渠道优先级（拖拽排序）：",-1)),i("div",ha,[(l(!0),e(_,null,f(c.channelRules.priority_order,(o,n)=>(l(),e("div",{key:o,class:"priority-item"},[a(v,null,{default:t(()=>[a(c.Rank)]),_:1}),i("span",null,R(c.getChannelName(o)),1),a(ce,{size:"small"},{default:t(()=>[a(G,{disabled:0===n,onClick:e=>c.moveChannelUp(n),size:"small"},{default:t(()=>[a(v,null,{default:t(()=>[a(c.ArrowUp)]),_:1})]),_:2},1032,["disabled","onClick"]),a(G,{disabled:n===c.channelRules.priority_order.length-1,onClick:e=>c.moveChannelDown(n),size:"small"},{default:t(()=>[a(v,null,{default:t(()=>[a(c.ArrowDown)]),_:1})]),_:2},1032,["disabled","onClick"])]),_:2},1024)]))),128))])])):m("",!0)]),_:1}),a(E,{label:"紧急消息处理"},{default:t(()=>[a(Q,{modelValue:c.channelRules.urgent_multi_channel,"onUpdate:modelValue":n[56]||(n[56]=e=>c.channelRules.urgent_multi_channel=e)},null,8,["modelValue"]),n[193]||(n[193]=i("div",{style:{"margin-top":"5px",color:"#909399","font-size":"12px"}}," 紧急消息同时通过多个渠道发送 ",-1))]),_:1,__:[193]})]),_:1},8,["model"])])]),_:1}),a(ae,{label:"时间规则",name:"timing"},{default:t(()=>[i("div",ba,[n[206]||(n[206]=i("h4",null,"发送时间智能规则",-1)),a(O,{model:c.timingRules,"label-width":"120px"},{default:t(()=>[a(E,{label:"免打扰模式"},{default:t(()=>[a(Q,{modelValue:c.timingRules.do_not_disturb,"onUpdate:modelValue":n[57]||(n[57]=e=>c.timingRules.do_not_disturb=e)},null,8,["modelValue"]),c.timingRules.do_not_disturb?(l(),e("div",va,[n[196]||(n[196]=u(" 免打扰时间： ",-1)),a(ue,{modelValue:c.timingRules.dnd_start,"onUpdate:modelValue":n[58]||(n[58]=e=>c.timingRules.dnd_start=e),format:"HH:mm",style:{width:"100px",margin:"0 8px"}},null,8,["modelValue"]),n[197]||(n[197]=u(" 至 ",-1)),a(ue,{modelValue:c.timingRules.dnd_end,"onUpdate:modelValue":n[59]||(n[59]=e=>c.timingRules.dnd_end=e),format:"HH:mm",style:{width:"100px",margin:"0 8px"}},null,8,["modelValue"]),i("div",Va,[a(ie,{modelValue:c.timingRules.dnd_except_urgent,"onUpdate:modelValue":n[60]||(n[60]=e=>c.timingRules.dnd_except_urgent=e)},{default:t(()=>n[195]||(n[195]=[u(" 紧急消息例外 ",-1)])),_:1,__:[195]},8,["modelValue"])])])):m("",!0)]),_:1}),a(E,{label:"最佳发送时间"},{default:t(()=>[a(Q,{modelValue:c.timingRules.optimal_time,"onUpdate:modelValue":n[61]||(n[61]=e=>c.timingRules.optimal_time=e)},null,8,["modelValue"]),c.timingRules.optimal_time?(l(),e("div",wa,[a(oe,{modelValue:c.timingRules.optimal_hours,"onUpdate:modelValue":n[62]||(n[62]=e=>c.timingRules.optimal_hours=e)},{default:t(()=>[a(ie,{label:"09"},{default:t(()=>n[198]||(n[198]=[u("9:00-10:00",-1)])),_:1,__:[198]}),a(ie,{label:"14"},{default:t(()=>n[199]||(n[199]=[u("14:00-15:00",-1)])),_:1,__:[199]}),a(ie,{label:"19"},{default:t(()=>n[200]||(n[200]=[u("19:00-20:00",-1)])),_:1,__:[200]}),a(ie,{label:"21"},{default:t(()=>n[201]||(n[201]=[u("21:00-22:00",-1)])),_:1,__:[201]})]),_:1},8,["modelValue"]),n[202]||(n[202]=i("div",{style:{"margin-top":"8px",color:"#909399","font-size":"12px"}}," 非最佳时间的消息将延迟到最近的最佳时间发送 ",-1))])):m("",!0)]),_:1}),a(E,{label:"节假日处理"},{default:t(()=>[a(Q,{modelValue:c.timingRules.holiday_handling,"onUpdate:modelValue":n[63]||(n[63]=e=>c.timingRules.holiday_handling=e)},null,8,["modelValue"]),c.timingRules.holiday_handling?(l(),e("div",ka,[a(_e,{modelValue:c.timingRules.holiday_strategy,"onUpdate:modelValue":n[64]||(n[64]=e=>c.timingRules.holiday_strategy=e)},{default:t(()=>[a(pe,{label:"delay"},{default:t(()=>n[203]||(n[203]=[u("延迟到工作日",-1)])),_:1,__:[203]}),a(pe,{label:"reduce"},{default:t(()=>n[204]||(n[204]=[u("减少发送频率",-1)])),_:1,__:[204]}),a(pe,{label:"normal"},{default:t(()=>n[205]||(n[205]=[u("正常发送",-1)])),_:1,__:[205]})]),_:1},8,["modelValue"])])):m("",!0)]),_:1})]),_:1},8,["model"])])]),_:1}),a(ae,{label:"内容优化",name:"content"},{default:t(()=>[i("div",Ca,[n[215]||(n[215]=i("h4",null,"内容智能优化规则",-1)),a(O,{model:c.contentRules,"label-width":"120px"},{default:t(()=>[a(E,{label:"个性化内容"},{default:t(()=>[a(Q,{modelValue:c.contentRules.personalization,"onUpdate:modelValue":n[65]||(n[65]=e=>c.contentRules.personalization=e)},null,8,["modelValue"]),n[207]||(n[207]=i("div",{style:{"margin-top":"5px",color:"#909399","font-size":"12px"}}," 根据用户偏好和行为自动调整通知内容 ",-1))]),_:1,__:[207]}),a(E,{label:"多语言支持"},{default:t(()=>[a(Q,{modelValue:c.contentRules.multi_language,"onUpdate:modelValue":n[66]||(n[66]=e=>c.contentRules.multi_language=e)},null,8,["modelValue"]),c.contentRules.multi_language?(l(),e("div",xa,[a(oe,{modelValue:c.contentRules.supported_languages,"onUpdate:modelValue":n[67]||(n[67]=e=>c.contentRules.supported_languages=e)},{default:t(()=>[a(ie,{label:"zh-CN"},{default:t(()=>n[208]||(n[208]=[u("简体中文",-1)])),_:1,__:[208]}),a(ie,{label:"zh-TW"},{default:t(()=>n[209]||(n[209]=[u("繁体中文",-1)])),_:1,__:[209]}),a(ie,{label:"en"},{default:t(()=>n[210]||(n[210]=[u("English",-1)])),_:1,__:[210]}),a(ie,{label:"ja"},{default:t(()=>n[211]||(n[211]=[u("日本語",-1)])),_:1,__:[211]})]),_:1},8,["modelValue"])])):m("",!0)]),_:1}),a(E,{label:"内容长度优化"},{default:t(()=>[a(Q,{modelValue:c.contentRules.length_optimization,"onUpdate:modelValue":n[68]||(n[68]=e=>c.contentRules.length_optimization=e)},null,8,["modelValue"]),c.contentRules.length_optimization?(l(),e("div",Sa,[n[212]||(n[212]=u(" 短信渠道自动截取前 ",-1)),a(de,{modelValue:c.contentRules.sms_max_length,"onUpdate:modelValue":n[69]||(n[69]=e=>c.contentRules.sms_max_length=e),min:50,max:200,style:{width:"80px",margin:"0 5px"}},null,8,["modelValue"]),n[213]||(n[213]=u(" 字符 ",-1)),n[214]||(n[214]=i("div",{style:{"margin-top":"5px",color:"#909399","font-size":"12px"}},' 超长内容自动生成摘要或添加"查看详情"链接 ',-1))])):m("",!0)]),_:1})]),_:1},8,["model"])])]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["modelValue"]),a(me,{title:"邮件服务配置",modelValue:c.showEmailConfigDialog,"onUpdate:modelValue":n[82]||(n[82]=e=>c.showEmailConfigDialog=e),width:"600px","close-on-click-modal":!1},{footer:t(()=>[i("div",Ta,[a(G,{onClick:n[81]||(n[81]=e=>c.showEmailConfigDialog=!1)},{default:t(()=>n[223]||(n[223]=[u("取消",-1)])),_:1,__:[223]}),a(G,{type:"info",onClick:c.testEmailConfig,loading:c.emailTesting},{default:t(()=>n[224]||(n[224]=[u("测试连接",-1)])),_:1,__:[224]},8,["loading"]),a(G,{type:"primary",onClick:c.saveEmailConfig,loading:c.emailSaving},{default:t(()=>n[225]||(n[225]=[u("保存配置",-1)])),_:1,__:[225]},8,["loading"])])]),default:t(()=>[a(O,{model:c.emailConfig,rules:c.emailRules,ref:"emailFormRef","label-width":"120px"},{default:t(()=>[a(K,{title:"配置说明",type:"info",closable:!1,style:{"margin-bottom":"20px"}},{default:t(()=>[n[219]||(n[219]=u(" 请配置SMTP邮件服务器信息，用于发送系统通知邮件。支持主流邮件服务商如QQ邮箱、163邮箱、Gmail等。 ",-1)),n[220]||(n[220]=i("br",null,null,-1)),a(G,{type:"text",onClick:n[73]||(n[73]=e=>c.showEmailTemplateDialog=!0),style:{"margin-top":"5px"}},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Document)]),_:1}),n[218]||(n[218]=u(" 使用常用邮件模板快速配置 ",-1))]),_:1,__:[218]})]),_:1}),a(E,{label:"SMTP服务器",prop:"host"},{default:t(()=>[a(j,{modelValue:c.emailConfig.host,"onUpdate:modelValue":n[74]||(n[74]=e=>c.emailConfig.host=e),placeholder:"例如: smtp.qq.com"},null,8,["modelValue"])]),_:1}),a(E,{label:"端口号",prop:"port"},{default:t(()=>[a(de,{modelValue:c.emailConfig.port,"onUpdate:modelValue":n[75]||(n[75]=e=>c.emailConfig.port=e),min:1,max:65535,style:{width:"100%"}},null,8,["modelValue"]),n[221]||(n[221]=i("div",{style:{"margin-top":"5px",color:"#909399","font-size":"12px"}}," 常用端口：25 (非加密)、465 (SSL)、587 (TLS) ",-1))]),_:1,__:[221]}),a(E,{label:"加密方式",prop:"encryption"},{default:t(()=>[a(ge,{modelValue:c.emailConfig.encryption,"onUpdate:modelValue":n[76]||(n[76]=e=>c.emailConfig.encryption=e),placeholder:"选择加密方式",style:{width:"100%"}},{default:t(()=>[a(fe,{label:"无加密",value:""}),a(fe,{label:"SSL",value:"ssl"}),a(fe,{label:"TLS",value:"tls"})]),_:1},8,["modelValue"])]),_:1}),a(E,{label:"发件人邮箱",prop:"username"},{default:t(()=>[a(j,{modelValue:c.emailConfig.username,"onUpdate:modelValue":n[77]||(n[77]=e=>c.emailConfig.username=e),placeholder:"例如: <EMAIL>"},null,8,["modelValue"])]),_:1}),a(E,{label:"邮箱密码",prop:"password"},{default:t(()=>[a(j,{modelValue:c.emailConfig.password,"onUpdate:modelValue":n[78]||(n[78]=e=>c.emailConfig.password=e),type:"password",placeholder:"邮箱密码或授权码","show-password":""},null,8,["modelValue"]),n[222]||(n[222]=i("div",{style:{"margin-top":"5px",color:"#909399","font-size":"12px"}}," QQ邮箱和163邮箱请使用授权码，Gmail需开启应用专用密码 ",-1))]),_:1,__:[222]}),a(E,{label:"发件人名称"},{default:t(()=>[a(j,{modelValue:c.emailConfig.from_name,"onUpdate:modelValue":n[79]||(n[79]=e=>c.emailConfig.from_name=e),placeholder:"例如: 系统通知"},null,8,["modelValue"])]),_:1}),a(E,{label:"测试邮箱"},{default:t(()=>[a(j,{modelValue:c.emailConfig.test_email,"onUpdate:modelValue":n[80]||(n[80]=e=>c.emailConfig.test_email=e),placeholder:"用于测试的收件邮箱"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(me,{title:"短信服务配置",modelValue:c.showSmsConfigDialog,"onUpdate:modelValue":n[94]||(n[94]=e=>c.showSmsConfigDialog=e),width:"600px","close-on-click-modal":!1},{footer:t(()=>[i("div",Fa,[a(G,{onClick:n[93]||(n[93]=e=>c.showSmsConfigDialog=!1)},{default:t(()=>n[229]||(n[229]=[u("取消",-1)])),_:1,__:[229]}),a(G,{type:"info",onClick:c.testSmsConfig,loading:c.smsTesting},{default:t(()=>n[230]||(n[230]=[u("测试发送",-1)])),_:1,__:[230]},8,["loading"]),a(G,{type:"primary",onClick:c.saveSmsConfig,loading:c.smsSaving},{default:t(()=>n[231]||(n[231]=[u("保存配置",-1)])),_:1,__:[231]},8,["loading"])])]),default:t(()=>[a(O,{model:c.smsConfig,rules:c.smsRules,ref:"smsFormRef","label-width":"120px"},{default:t(()=>[a(K,{title:"服务商选择",type:"info",closable:!1,style:{"margin-bottom":"20px"}},{default:t(()=>n[226]||(n[226]=[u(" 支持阿里云短信、腾讯云短信、华为云短信等主流服务商。请先到对应平台申请短信服务并获取API密钥。 ",-1)])),_:1}),a(E,{label:"服务商",prop:"provider"},{default:t(()=>[a(ge,{modelValue:c.smsConfig.provider,"onUpdate:modelValue":n[83]||(n[83]=e=>c.smsConfig.provider=e),placeholder:"选择短信服务商",style:{width:"100%"},onChange:c.handleSmsProviderChange},{default:t(()=>[a(fe,{label:"阿里云短信",value:"aliyun"}),a(fe,{label:"腾讯云短信",value:"tencent"}),a(fe,{label:"华为云短信",value:"huawei"}),a(fe,{label:"七牛云短信",value:"qiniu"})]),_:1},8,["modelValue"])]),_:1}),"aliyun"===c.smsConfig.provider?(l(),e(_,{key:0},[a(E,{label:"Access Key ID",prop:"access_key"},{default:t(()=>[a(j,{modelValue:c.smsConfig.access_key,"onUpdate:modelValue":n[84]||(n[84]=e=>c.smsConfig.access_key=e),placeholder:"阿里云Access Key ID"},null,8,["modelValue"])]),_:1}),a(E,{label:"Access Key Secret",prop:"access_secret"},{default:t(()=>[a(j,{modelValue:c.smsConfig.access_secret,"onUpdate:modelValue":n[85]||(n[85]=e=>c.smsConfig.access_secret=e),type:"password",placeholder:"阿里云Access Key Secret","show-password":""},null,8,["modelValue"])]),_:1}),a(E,{label:"短信签名",prop:"sign_name"},{default:t(()=>[a(j,{modelValue:c.smsConfig.sign_name,"onUpdate:modelValue":n[86]||(n[86]=e=>c.smsConfig.sign_name=e),placeholder:"例如: 您的网站名"},null,8,["modelValue"])]),_:1})],64)):"tencent"===c.smsConfig.provider?(l(),e(_,{key:1},[a(E,{label:"Secret ID",prop:"secret_id"},{default:t(()=>[a(j,{modelValue:c.smsConfig.secret_id,"onUpdate:modelValue":n[87]||(n[87]=e=>c.smsConfig.secret_id=e),placeholder:"腾讯云Secret ID"},null,8,["modelValue"])]),_:1}),a(E,{label:"Secret Key",prop:"secret_key"},{default:t(()=>[a(j,{modelValue:c.smsConfig.secret_key,"onUpdate:modelValue":n[88]||(n[88]=e=>c.smsConfig.secret_key=e),type:"password",placeholder:"腾讯云Secret Key","show-password":""},null,8,["modelValue"])]),_:1}),a(E,{label:"SDK App ID",prop:"sdk_app_id"},{default:t(()=>[a(j,{modelValue:c.smsConfig.sdk_app_id,"onUpdate:modelValue":n[89]||(n[89]=e=>c.smsConfig.sdk_app_id=e),placeholder:"短信应用SDK App ID"},null,8,["modelValue"])]),_:1}),a(E,{label:"短信签名",prop:"sign_name"},{default:t(()=>[a(j,{modelValue:c.smsConfig.sign_name,"onUpdate:modelValue":n[90]||(n[90]=e=>c.smsConfig.sign_name=e),placeholder:"例如: 您的网站名"},null,8,["modelValue"])]),_:1})],64)):m("",!0),a(E,{label:"测试手机号"},{default:t(()=>[a(j,{modelValue:c.smsConfig.test_phone,"onUpdate:modelValue":n[91]||(n[91]=e=>c.smsConfig.test_phone=e),placeholder:"用于测试的手机号"},null,8,["modelValue"])]),_:1}),a(E,{label:"模板配置"},{default:t(()=>[a(G,{type:"text",onClick:n[92]||(n[92]=e=>c.showSmsTemplateDialog=!0)},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Document)]),_:1}),n[227]||(n[227]=u(" 管理短信模板 ",-1))]),_:1,__:[227]}),n[228]||(n[228]=i("div",{style:{"margin-top":"5px",color:"#909399","font-size":"12px"}}," 配置常用的短信模板，如验证码、通知等 ",-1))]),_:1,__:[228]})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(me,{title:"微信通知服务配置",modelValue:c.showWechatConfigDialog,"onUpdate:modelValue":n[103]||(n[103]=e=>c.showWechatConfigDialog=e),width:"600px","close-on-click-modal":!1},{footer:t(()=>[i("div",Da,[a(G,{onClick:n[102]||(n[102]=e=>c.showWechatConfigDialog=!1)},{default:t(()=>n[233]||(n[233]=[u("取消",-1)])),_:1,__:[233]}),a(G,{type:"info",onClick:c.testWechatConfig,loading:c.wechatTesting},{default:t(()=>n[234]||(n[234]=[u("测试发送",-1)])),_:1,__:[234]},8,["loading"]),a(G,{type:"primary",onClick:c.saveWechatConfig,loading:c.wechatSaving},{default:t(()=>n[235]||(n[235]=[u("保存配置",-1)])),_:1,__:[235]},8,["loading"])])]),default:t(()=>[a(O,{model:c.wechatConfig,rules:c.wechatRules,ref:"wechatFormRef","label-width":"120px"},{default:t(()=>[a(K,{title:"配置说明",type:"info",closable:!1,style:{"margin-bottom":"20px"}},{default:t(()=>n[232]||(n[232]=[u(" 支持企业微信应用消息、微信公众号模板消息等。请确保已创建相应应用并获取必要的API权限。 ",-1)])),_:1}),a(E,{label:"通知类型",prop:"type"},{default:t(()=>[a(ge,{modelValue:c.wechatConfig.type,"onUpdate:modelValue":n[95]||(n[95]=e=>c.wechatConfig.type=e),placeholder:"选择微信通知类型",style:{width:"100%"},onChange:c.handleWechatTypeChange},{default:t(()=>[a(fe,{label:"企业微信应用消息",value:"work_wechat"}),a(fe,{label:"微信公众号模板消息",value:"mp_template"}),a(fe,{label:"微信小程序订阅消息",value:"miniprogram_subscribe"})]),_:1},8,["modelValue"])]),_:1}),"work_wechat"===c.wechatConfig.type?(l(),e(_,{key:0},[a(E,{label:"企业ID",prop:"corp_id"},{default:t(()=>[a(j,{modelValue:c.wechatConfig.corp_id,"onUpdate:modelValue":n[96]||(n[96]=e=>c.wechatConfig.corp_id=e),placeholder:"企业微信CorpId"},null,8,["modelValue"])]),_:1}),a(E,{label:"应用Secret",prop:"corp_secret"},{default:t(()=>[a(j,{modelValue:c.wechatConfig.corp_secret,"onUpdate:modelValue":n[97]||(n[97]=e=>c.wechatConfig.corp_secret=e),type:"password",placeholder:"企业微信应用Secret","show-password":""},null,8,["modelValue"])]),_:1}),a(E,{label:"应用AgentId",prop:"agent_id"},{default:t(()=>[a(de,{modelValue:c.wechatConfig.agent_id,"onUpdate:modelValue":n[98]||(n[98]=e=>c.wechatConfig.agent_id=e),min:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1})],64)):"mp_template"===c.wechatConfig.type?(l(),e(_,{key:1},[a(E,{label:"公众号AppId",prop:"app_id"},{default:t(()=>[a(j,{modelValue:c.wechatConfig.app_id,"onUpdate:modelValue":n[99]||(n[99]=e=>c.wechatConfig.app_id=e),placeholder:"微信公众号AppId"},null,8,["modelValue"])]),_:1}),a(E,{label:"公众号AppSecret",prop:"app_secret"},{default:t(()=>[a(j,{modelValue:c.wechatConfig.app_secret,"onUpdate:modelValue":n[100]||(n[100]=e=>c.wechatConfig.app_secret=e),type:"password",placeholder:"微信公众号AppSecret","show-password":""},null,8,["modelValue"])]),_:1})],64)):m("",!0),a(E,{label:"测试用户"},{default:t(()=>[a(j,{modelValue:c.wechatConfig.test_user,"onUpdate:modelValue":n[101]||(n[101]=e=>c.wechatConfig.test_user=e),placeholder:"测试用户OpenId或企业微信UserId"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(me,{title:"选择邮件服务模板",modelValue:c.showEmailTemplateDialog,"onUpdate:modelValue":n[106]||(n[106]=e=>c.showEmailTemplateDialog=e),width:"900px","close-on-click-modal":!1,top:"5vh"},{footer:t(()=>[i("div",Ia,[a(G,{onClick:n[105]||(n[105]=e=>c.showEmailTemplateDialog=!1)},{default:t(()=>n[237]||(n[237]=[u("取消",-1)])),_:1,__:[237]}),a(G,{type:"primary",onClick:c.confirmEmailTemplate,disabled:!c.selectedEmailTemplate},{default:t(()=>n[238]||(n[238]=[u(" 应用模板 ",-1)])),_:1,__:[238]},8,["disabled"])])]),default:t(()=>[i("div",Ra,[a(K,{title:"快速配置提示",type:"info",closable:!1,style:{"margin-bottom":"20px"}},{default:t(()=>n[236]||(n[236]=[u(" 选择邮件服务商模板可快速配置SMTP服务器信息，选择后仍需填写邮箱账号和密码。 ",-1)])),_:1}),a(_e,{modelValue:c.selectedEmailTemplate,"onUpdate:modelValue":n[104]||(n[104]=e=>c.selectedEmailTemplate=e),class:"template-radio-group"},{default:t(()=>[(l(!0),e(_,null,f(c.emailTemplates,o=>(l(),e("div",{class:"template-option",key:o.key},[a(pe,{label:o.key,class:"template-radio"},{default:t(()=>[i("div",za,[i("div",Pa,R(o.name),1),i("div",ja,R(o.desc),1),"custom"!==o.key?(l(),e("div",Ma,[i("span",Ea,R(o.config.host)+":"+R(o.config.port),1),a(ee,{size:"small",type:"info"},{default:t(()=>[u(R(o.config.encryption.toUpperCase()),1)]),_:2},1024)])):m("",!0)])]),_:2},1032,["label"])]))),128))]),_:1},8,["modelValue"])])]),_:1},8,["modelValue"]),a(me,{title:"短信模板管理",modelValue:c.showSmsTemplateDialog,"onUpdate:modelValue":n[108]||(n[108]=e=>c.showSmsTemplateDialog=e),width:"700px","close-on-click-modal":!1},{footer:t(()=>[i("div",La,[a(G,{onClick:n[107]||(n[107]=e=>c.showSmsTemplateDialog=!1)},{default:t(()=>n[244]||(n[244]=[u("关闭",-1)])),_:1,__:[244]})])]),default:t(()=>[i("div",qa,[a(K,{title:"模板说明",type:"info",closable:!1,style:{"margin-bottom":"20px"}},{default:t(()=>n[239]||(n[239]=[u(" 短信模板需要在对应服务商平台预先申请和审核通过。这里配置模板ID和变量，用于系统调用发送。 ",-1)])),_:1}),i("div",Aa,[a(G,{type:"primary",size:"small",onClick:c.addSmsTemplate},{default:t(()=>[a(v,null,{default:t(()=>[a(c.Plus)]),_:1}),n[240]||(n[240]=u(" 添加模板 ",-1))]),_:1,__:[240]})]),a(ne,{data:c.smsTemplates,style:{width:"100%"}},{default:t(()=>[a(te,{prop:"name",label:"模板名称",width:"150"}),a(te,{prop:"type",label:"类型",width:"100"},{default:t(e=>[a(ee,{size:"small",type:c.getSmsTemplateTypeColor(e.row.type)},{default:t(()=>[u(R(c.getSmsTemplateTypeName(e.row.type)),1)]),_:2},1032,["type"])]),_:1}),a(te,{prop:"template_id",label:"模板ID",width:"120"}),a(te,{prop:"content",label:"模板内容","show-overflow-tooltip":""}),a(te,{prop:"variables",label:"变量",width:"120","show-overflow-tooltip":""}),a(te,{prop:"status",label:"状态",width:"80"},{default:t(e=>[a(Q,{modelValue:e.row.active,"onUpdate:modelValue":l=>e.row.active=l,size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(te,{label:"操作",width:"150"},{default:t(e=>[a(G,{type:"text",size:"small",onClick:l=>c.editSmsTemplate(e.row)},{default:t(()=>n[241]||(n[241]=[u("编辑",-1)])),_:2,__:[241]},1032,["onClick"]),a(G,{type:"text",size:"small",onClick:l=>c.testSmsTemplate(e.row)},{default:t(()=>n[242]||(n[242]=[u("测试",-1)])),_:2,__:[242]},1032,["onClick"]),a(G,{type:"text",size:"small",onClick:l=>c.deleteSmsTemplate(e.row)},{default:t(()=>n[243]||(n[243]=[u("删除",-1)])),_:2,__:[243]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),_:1},8,["modelValue"])]),_:1})]),_:1})])}],["__scopeId","data-v-069257db"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/Settings.vue"]]);export{Na as default};
