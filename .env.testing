APP_NAME="晨鑫流量变现系统 - 测试环境"
APP_ENV=testing
APP_DEBUG=true
APP_URL=http://testing.linkhub.example.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=linkhub_test
DB_USERNAME=test_user
DB_PASSWORD=test_password

BROADCAST_DRIVER=log
CACHE_DRIVER=array
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=array
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="晨鑫流量变现系统 Test"

# JWT配置
JWT_SECRET=testing_jwt_secret
JWT_TTL=60
JWT_REFRESH_TTL=20160

# 防红系统配置
ANTI_BLOCK_ENABLED=true
ANTI_BLOCK_CHECK_INTERVAL=60
ANTI_BLOCK_FALLBACK_DOMAIN=testing.linkhub.example.com

# 支付配置 - 测试环境使用测试账号
PAYMENT_ENABLED=true
PAYMENT_TEST_MODE=true
PAYMENT_WECHAT_APPID=test_appid
PAYMENT_WECHAT_MCHID=test_mchid
PAYMENT_WECHAT_KEY=test_key
PAYMENT_ALIPAY_APPID=test_appid
PAYMENT_ALIPAY_KEY=test_key

# 短信配置 - 测试环境不发送真实短信
SMS_PROVIDER=array
SMS_TEST_MODE=true

# 测试环境特有配置
TESTING_SEED_DATA=true
TESTING_DISABLE_EVENTS=true
TESTING_DISABLE_NOTIFICATIONS=true