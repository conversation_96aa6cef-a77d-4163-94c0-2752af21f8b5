<template>
  <div class="login-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
      <div class="gradient-overlay"></div>
    </div>

    <!-- 主要内容 -->
    <div class="login-container">
      <div class="login-card">
        <!-- 头部 -->
        <div class="login-header">
          <div class="logo-section">
            <div class="logo-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 10V3L4 14h7v7l9-11h-7z" fill="currentColor"/>
              </svg>
            </div>
            <div class="logo-text">
              <h1>晨鑫流量变现系统</h1>
              <p>智能社群营销与多级分销平台</p>
            </div>
          </div>

          <div class="welcome-section">
            <h2>管理员登录</h2>
            <p>欢迎回来，请登录您的管理员账户</p>
            <div class="status-indicator">
              <div class="status-dot"></div>
              <span>系统运行正常</span>
            </div>
          </div>
        </div>
        
        <!-- 登录表单 -->
        <form class="login-form" @submit.prevent="handleLogin">
          <!-- 全局错误提示 -->
          <div v-if="globalError" class="error-alert">
            <div class="error-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="15" y1="9" x2="9" y2="15"></line>
                <line x1="9" y1="9" x2="15" y2="15"></line>
              </svg>
            </div>
            <span>{{ globalError }}</span>
          </div>

          <div class="form-fields">
            <!-- 用户名输入 -->
            <div class="form-group">
              <label for="username" class="form-label">用户名或邮箱</label>
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
                <input
                  id="username"
                  ref="usernameRef"
                  v-model="loginForm.username"
                  type="text"
                  required
                  class="modern-input"
                  :class="{ 'input-error': errors.username }"
                  placeholder="请输入用户名或邮箱"
                  autofocus
                  @input="clearFieldError('username')"
                  @keyup.enter="handleLogin"
                />
              </div>
              <p v-if="errors.username" class="field-error">
                {{ errors.username }}
              </p>
            </div>

            <!-- 密码输入 -->
            <div class="form-group">
              <label for="password" class="form-label">密码</label>
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <circle cx="12" cy="16" r="1"></circle>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                  </svg>
                </div>
                <input
                  id="password"
                  ref="passwordRef"
                  v-model="loginForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  required
                  class="modern-input"
                  :class="{ 'input-error': errors.password }"
                  placeholder="请输入密码"
                  @input="clearFieldError('password')"
                  @keyup.enter="handleLogin"
                />
                <button
                  type="button"
                  @click="showPassword = !showPassword"
                  class="password-toggle"
                >
                  <svg v-if="!showPassword" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                    <line x1="1" y1="1" x2="23" y2="23"></line>
                  </svg>
                </button>
              </div>
              <p v-if="errors.password" class="field-error">
                {{ errors.password }}
              </p>
            </div>
          </div>

          <!-- 表单选项 -->
          <div class="form-options">
            <div class="remember-me">
              <input
                id="remember"
                v-model="rememberMe"
                type="checkbox"
                class="modern-checkbox"
              />
              <label for="remember" class="checkbox-label">记住我</label>
            </div>
            <a href="#" class="forgot-password" @click.prevent="handleForgotPassword">
              忘记密码？
            </a>
          </div>

          <!-- 登录按钮 -->
          <button
            type="submit"
            :disabled="loading"
            class="modern-login-button"
          >
            <div v-if="loading" class="loading-spinner">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 12a9 9 0 11-6.219-8.56"/>
              </svg>
            </div>
            <span>{{ loading ? '登录中...' : '立即登录管理后台' }}</span>
          </button>

          <!-- 开发环境快速登录（仅开发模式显示） -->
          <div v-if="isDev" class="dev-tools">
            <button
              type="button"
              @click="handlePreviewLogin"
              class="preview-button"
            >
              🚀 开发预览模式
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { User, Lock, View, Hide, Right, Loading, InfoFilled, QuestionFilled, CircleCheckFilled, TrendCharts, Share } from "@element-plus/icons-vue"
import { securityLogger, sessionManager, logLogin, logSession } from '@/utils/security'
import { getToken } from '@/utils/auth'

const router = useRouter()
const userStore = useUserStore()


// 响应式数据
const loginFormRef = ref()
const usernameRef = ref()
const passwordRef = ref()
const loading = ref(false)
const showPassword = ref(false)
const capsTooltip = ref(false)
const rememberMe = ref(false)

// 响应式检测
const isCompactMode = ref(false)
const isDev = ref(process.env.NODE_ENV === 'development')

// 检测屏幕尺寸
const checkScreenSize = () => {
  const height = window.innerHeight
  const width = window.innerWidth

  // 在小屏幕或低分辨率下启用紧凑模式
  isCompactMode.value = height < 800 || width < 480
}

// 监听窗口大小变化
const handleResize = () => {
  checkScreenSize()
}

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }

        const trimmedValue = value.trim()

        // 如果包含@符号，验证邮箱格式
        if (trimmedValue.includes('@')) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(trimmedValue)) {
            callback(new Error('邮箱格式不正确'))
            return
          }
        } else {
          // 验证用户名格式（只允许字母、数字、下划线、中文）
          const usernameRegex = /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/
          if (!usernameRegex.test(trimmedValue)) {
            callback(new Error('用户名只能包含字母、数字、下划线或中文'))
            return
          }
        }

        callback()
      },
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 128, message: '密码长度在 6 到 128 个字符', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }

        // 检查密码复杂度（至少包含字母和数字）
        const hasLetter = /[a-zA-Z]/.test(value)
        const hasNumber = /[0-9]/.test(value)

        if (!hasLetter || !hasNumber) {
          callback(new Error('密码必须包含字母和数字'))
          return
        }

        // 检查是否包含不安全字符
        const unsafeChars = /[<>'"&]/
        if (unsafeChars.test(value)) {
          callback(new Error('密码包含不安全字符'))
          return
        }

        callback()
      },
      trigger: 'blur'
    }
  ]
})

// 计算属性
const passwordType = computed(() => {
  return showPassword.value ? 'text' : 'password'
})

// 方法
const checkCapslock = (e) => {
  const { key } = e
  capsTooltip.value = key && key.length === 1 && (key >= 'A' && key <= 'Z')
}

const handlePreviewLogin = async () => {
  loading.value = true
  ElMessage.info('正在进入预览模式...')

  try {
    console.log('🔄 开始进入预览模式...')

    // 设置预览模式
    userStore.enterPreviewMode()
    console.log('✅ 预览模式状态设置完成')

    // 验证 token 是否设置成功
    const token = getToken()
    console.log('🔑 当前 token:', token)

    if (!token) {
      throw new Error('Token 设置失败')
    }

    // 延迟一小段时间确保状态更新
    setTimeout(async () => {
      console.log('🚀 准备跳转到 dashboard...')
      ElMessage.success('欢迎来到预览模式！')

      try {
        await router.push('/admin/dashboard')
        console.log('✅ 路由跳转成功')
      } catch (routerError) {
        console.error('❌ 路由跳转失败:', routerError)
        ElMessage.error('页面跳转失败，请手动刷新页面')
      }

      loading.value = false
    }, 500)
  } catch (error) {
    console.error('❌ 进入预览模式失败:', error)
    ElMessage.error('无法进入预览模式，请稍后重试。')
    loading.value = false
  }
}


const handleLogin = async () => {
  try {
    // 表单验证
    await loginFormRef.value.validate()

    loading.value = true

    // 调用登录接口
    const loginResult = await userStore.login(loginForm)

    // 获取用户角色信息
    const userRole = loginResult.data?.user?.role || userStore.userInfo?.role
    const userName = loginResult.data?.user?.nickname || loginResult.data?.user?.username || '用户'

    // 根据角色显示不同的欢迎消息
    const roleMessages = {
      admin: '欢迎回来，超级管理员！',
      substation: '欢迎回来，分站管理员！',
      agent: '欢迎回来，代理商！',
      distributor: '欢迎回来，分销员！',
      group_owner: '欢迎回来，群主！',
      user: '欢迎回来！'
    }

    ElMessage.success({
      message: `${roleMessages[userRole] || '登录成功！'} ${userName}`,
      type: 'success',
      duration: 3000
    })

    // 智能重定向：根据用户角色和URL参数决定跳转路径
    await handleSmartRedirect(userRole)

  } catch (error) {
    console.error('登录失败:', error)

    // 增强错误处理
    const errorMessages = {
      'Network Error': '网络连接失败，请检查网络设置',
      'timeout': '请求超时，请稍后重试',
      'Unauthorized': '用户名或密码错误',
      'Forbidden': '账户已被禁用，请联系管理员'
    }

    const errorMessage = errorMessages[error.code] ||
                        errorMessages[error.message] ||
                        error.message ||
                        '登录失败，请检查用户名和密码'

    ElMessage.error({
      message: errorMessage,
      type: 'error',
      duration: 5000
    })

    // 记录登录失败日志
    logLogin(loginForm.username, false, errorMessage)

  } finally {
    loading.value = false
  }
}

// 智能重定向函数
const handleSmartRedirect = async (userRole) => {
  try {
    // 动态导入导航配置
    const { getUserDefaultRoute } = await import('@/config/navigation')

    // 获取URL参数中的redirect路径
    const redirectParam = router.currentRoute.value.query.redirect

    // 如果有redirect参数，验证用户是否有权限访问该路径
    if (redirectParam) {
      const { checkRoutePermission } = await import('@/utils/permission')
      const mockRoute = { path: redirectParam, meta: {} }

      if (checkRoutePermission(mockRoute, userRole)) {
        // 有权限访问，跳转到指定路径
        router.push(redirectParam)
        return
      } else {
        // 无权限访问，显示提示并跳转到默认路径
        ElMessage.warning({
          message: '您没有权限访问请求的页面，已跳转到默认页面',
          duration: 4000
        })
      }
    }

    // 获取用户角色的默认路径
    const defaultRoute = getUserDefaultRoute(userRole)

    // 创建用户会话
    const session = sessionManager.createSession({
      id: userStore.userInfo?.id,
      username: loginForm.username,
      role: userRole
    })

    // 记录成功登录和会话创建日志
    logLogin(loginForm.username, true, `重定向到: ${defaultRoute}`)
    logSession(loginForm.username, 'login', session.id, `角色: ${userRole}, 重定向: ${defaultRoute}`)

    // 跳转到默认路径
    router.push(defaultRoute)

  } catch (error) {
    console.error('重定向失败:', error)
    // 如果重定向失败，跳转到通用仪表板
    router.push('/admin/dashboard')
  }
}



// 生命周期
onMounted(() => {
  // 初始化屏幕尺寸检测
  checkScreenSize()
  window.addEventListener('resize', handleResize)

  // 如果用户名为空，自动聚焦到用户名输入框
  if (loginForm.username === '') {
    nextTick(() => {
      usernameRef.value.focus()
    })
  } else {
    // 否则聚焦到密码输入框
    nextTick(() => {
      passwordRef.value.focus()
    })
  }
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 添加忘记密码处理函数
const handleForgotPassword = () => {
  ElMessage.info('请联系系统管理员重置密码')
}

// 添加错误处理
const errors = ref({
  username: '',
  password: ''
})

const globalError = ref('')

const clearFieldError = (field) => {
  if (errors.value[field]) {
    errors.value[field] = ''
  }
  if (globalError.value) {
    globalError.value = ''
  }
}
</script>

<style scoped>
/* 开发工具样式 */
.dev-tools {
  margin-top: 16px;
  padding: 16px;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 12px;
  border: 1px dashed #d1d5db;
}

.preview-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
}

.preview-button:hover {
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 登录页面样式 */
.login-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 70%;
  animation-delay: 4s;
}

.shape-4 {
  width: 120px;
  height: 120px;
  top: 70%;
  left: 20%;
  animation-delay: 1s;
}

.shape-5 {
  width: 60px;
  height: 60px;
  top: 30%;
  left: 50%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 登录容器 */
.login-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 480px;
}

/* 登录卡片 */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 24px;
  z-index: -1;
}

/* 头部样式 */
.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.logo-icon svg {
  width: 24px;
  height: 24px;
  color: white;
}

.logo-text h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.logo-text p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.welcome-section h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.welcome-section p {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 12px 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: #059669;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 表单样式 */
.login-form {
  margin-top: 24px;
}

.error-alert {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  margin-bottom: 20px;
  color: #dc2626;
  font-size: 0.875rem;
}

.error-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  flex-shrink: 0;
}

.error-icon svg {
  width: 100%;
  height: 100%;
}

.form-fields {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  line-height: 1.4;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  height: 52px;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #9ca3af;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-icon svg {
  width: 100%;
  height: 100%;
}

.modern-input {
  width: 100%;
  height: 52px;
  padding: 0 12px 0 44px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  background: white;
  transition: all 0.2s ease;
  color: #1f2937;
  line-height: 1.4;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.modern-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-input.input-error {
  border-color: #ef4444;
}

.modern-input.input-error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.password-toggle:hover {
  color: #6b7280;
}

.password-toggle svg {
  width: 100%;
  height: 100%;
}

.field-error {
  margin-top: 6px;
  font-size: 0.875rem;
  color: #dc2626;
}

/* 表单选项 */
.form-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.remember-me {
  display: flex;
  align-items: center;
}

.modern-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modern-checkbox:checked {
  background: #3b82f6;
  border-color: #3b82f6;
}

.checkbox-label {
  margin-left: 8px;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
}

.forgot-password {
  font-size: 0.875rem;
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s ease;
}

.forgot-password:hover {
  color: #2563eb;
}

/* 登录按钮 */
.modern-login-button {
  width: 100%;
  height: 52px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.modern-login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.modern-login-button:active {
  transform: translateY(0);
}

.modern-login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

.loading-spinner svg {
  width: 100%;
  height: 100%;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .login-page {
    padding: 16px;
  }

  .login-card {
    padding: 32px 24px;
  }

  .logo-section {
    flex-direction: column;
    text-align: center;
  }

  .logo-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
}

@media (max-height: 700px) {
  .login-card {
    padding: 24px;
  }

  .login-header {
    margin-bottom: 24px;
  }
}
</style>