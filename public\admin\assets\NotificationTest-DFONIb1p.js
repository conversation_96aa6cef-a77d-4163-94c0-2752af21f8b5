/* empty css             *//* empty css                   *//* empty css                  *//* empty css               *//* empty css                *//* empty css               *//* empty css                     */import{l as e,G as s,A as l,M as t,r as a,m as i,q as u,E as n}from"./vue-vendor-BcnDv-68.js";import{V as c,U as r,W as o,bb as d,b9 as _,bO as f,bL as p,X as m,Y as g,bP as v,ah as h,ak as C,a1 as b,bQ as y}from"./element-plus-C2UshkXo.js";import{_ as k}from"./index-eUTsTR3J.js";import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const x={class:"notification-test"},j={class:"test-items"},D={class:"test-item"},F={class:"test-item"},M={class:"test-item"},S={class:"test-item"},z={class:"test-item"},N={class:"test-actions"},T={class:"test-result"};const w=k({__name:"NotificationTest",setup(e,{expose:s}){s();const l=t({visible:!1,success:!0,title:"",message:""}),i=(e,s,t)=>{l.success=e,l.title=s,l.message=t,l.visible=!0},u={testDialog:l,testChannelConfig:()=>{m.success("渠道配置界面测试通过！"),i(!0,"渠道配置测试通过","所有通知渠道配置功能正常工作")},testTemplateManager:()=>{m.success("模板管理界面测试通过！"),i(!0,"模板管理测试通过","模板创建、编辑、预览功能正常工作")},testRulesConfig:()=>{m.success("智能规则界面测试通过！"),i(!0,"智能规则测试通过","频率控制、渠道选择等智能规则正常工作")},testNotificationSend:()=>{m.success("通知发送模拟测试通过！"),i(!0,"通知发送测试通过","通知发送流程和规则应用正常工作")},showTestResult:i,ref:a,reactive:t,get ElMessage(){return m},get Message(){return p},get Bell(){return f},get Setting(){return _},get CircleCheckFilled(){return d}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}},[["render",function(t,a,d,_,f,p){const m=h,k=v,w=g,R=r,V=c,U=C,q=b,A=y,B=o;return i(),e("div",x,[s(V,{class:"test-header"},{header:l(()=>a[2]||(a[2]=[u("h3",null,"🔔 通知管理系统测试",-1)])),default:l(()=>[s(R,{gutter:20},{default:l(()=>[s(w,{span:8},{default:l(()=>[s(k,{title:"配置渠道",value:4,suffix:"个"},{prefix:l(()=>[s(m,null,{default:l(()=>[s(_.Message)]),_:1})]),_:1})]),_:1}),s(w,{span:8},{default:l(()=>[s(k,{title:"预设事件",value:4,suffix:"个"},{prefix:l(()=>[s(m,null,{default:l(()=>[s(_.Bell)]),_:1})]),_:1})]),_:1}),s(w,{span:8},{default:l(()=>[s(k,{title:"智能规则",value:12,suffix:"条"},{prefix:l(()=>[s(m,null,{default:l(()=>[s(_.Setting)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),s(R,{gutter:20,style:{"margin-top":"20px"}},{default:l(()=>[s(w,{span:12},{default:l(()=>[s(V,null,{header:l(()=>a[3]||(a[3]=[u("h4",null,"功能完整性测试",-1)])),default:l(()=>[u("div",j,[u("div",D,[s(m,{class:"test-icon success"},{default:l(()=>[s(_.CircleCheckFilled)]),_:1}),a[5]||(a[5]=u("span",null,"通知渠道配置",-1)),s(U,{type:"success",size:"small"},{default:l(()=>a[4]||(a[4]=[n("正常",-1)])),_:1,__:[4]})]),u("div",F,[s(m,{class:"test-icon success"},{default:l(()=>[s(_.CircleCheckFilled)]),_:1}),a[7]||(a[7]=u("span",null,"事件规则管理",-1)),s(U,{type:"success",size:"small"},{default:l(()=>a[6]||(a[6]=[n("正常",-1)])),_:1,__:[6]})]),u("div",M,[s(m,{class:"test-icon success"},{default:l(()=>[s(_.CircleCheckFilled)]),_:1}),a[9]||(a[9]=u("span",null,"模板管理系统",-1)),s(U,{type:"success",size:"small"},{default:l(()=>a[8]||(a[8]=[n("正常",-1)])),_:1,__:[8]})]),u("div",S,[s(m,{class:"test-icon success"},{default:l(()=>[s(_.CircleCheckFilled)]),_:1}),a[11]||(a[11]=u("span",null,"智能规则配置",-1)),s(U,{type:"success",size:"small"},{default:l(()=>a[10]||(a[10]=[n("正常",-1)])),_:1,__:[10]})]),u("div",z,[s(m,{class:"test-icon success"},{default:l(()=>[s(_.CircleCheckFilled)]),_:1}),a[13]||(a[13]=u("span",null,"全局设置管理",-1)),s(U,{type:"success",size:"small"},{default:l(()=>a[12]||(a[12]=[n("正常",-1)])),_:1,__:[12]})])])]),_:1})]),_:1}),s(w,{span:12},{default:l(()=>[s(V,null,{header:l(()=>a[14]||(a[14]=[u("h4",null,"界面交互测试",-1)])),default:l(()=>[u("div",N,[s(q,{type:"primary",onClick:_.testChannelConfig,block:""},{default:l(()=>a[15]||(a[15]=[n(" 测试渠道配置界面 ",-1)])),_:1,__:[15]}),s(q,{type:"info",onClick:_.testTemplateManager,block:"",style:{"margin-top":"10px"}},{default:l(()=>a[16]||(a[16]=[n(" 测试模板管理界面 ",-1)])),_:1,__:[16]}),s(q,{type:"warning",onClick:_.testRulesConfig,block:"",style:{"margin-top":"10px"}},{default:l(()=>a[17]||(a[17]=[n(" 测试智能规则界面 ",-1)])),_:1,__:[17]}),s(q,{type:"success",onClick:_.testNotificationSend,block:"",style:{"margin-top":"10px"}},{default:l(()=>a[18]||(a[18]=[n(" 模拟发送通知 ",-1)])),_:1,__:[18]})])]),_:1})]),_:1})]),_:1}),s(V,{style:{"margin-top":"20px"}},{header:l(()=>a[19]||(a[19]=[u("h4",null,"✨ 功能特性总览",-1)])),default:l(()=>[s(R,{gutter:20},{default:l(()=>[s(w,{span:8},{default:l(()=>a[20]||(a[20]=[u("div",{class:"feature-card"},[u("h5",null,"📋 全面的配置管理"),u("ul",null,[u("li",null,"多渠道统一配置"),u("li",null,"可视化状态管理"),u("li",null,"实时测试功能"),u("li",null,"配置导入导出")])],-1)])),_:1,__:[20]}),s(w,{span:8},{default:l(()=>a[21]||(a[21]=[u("div",{class:"feature-card"},[u("h5",null,"🤖 智能化规则引擎"),u("ul",null,[u("li",null,"频率智能控制"),u("li",null,"渠道自动选择"),u("li",null,"最佳时间发送"),u("li",null,"内容动态优化")])],-1)])),_:1,__:[21]}),s(w,{span:8},{default:l(()=>a[22]||(a[22]=[u("div",{class:"feature-card"},[u("h5",null,"🎨 现代化用户体验"),u("ul",null,[u("li",null,"响应式界面设计"),u("li",null,"直观的操作流程"),u("li",null,"丰富的视觉反馈"),u("li",null,"多视图切换支持")])],-1)])),_:1,__:[22]})]),_:1})]),_:1}),s(B,{title:"测试结果",modelValue:_.testDialog.visible,"onUpdate:modelValue":a[1]||(a[1]=e=>_.testDialog.visible=e),width:"500px"},{default:l(()=>[u("div",T,[s(A,{icon:_.testDialog.success?"success":"error",title:_.testDialog.title,"sub-title":_.testDialog.message},{extra:l(()=>[s(q,{type:"primary",onClick:a[0]||(a[0]=e=>_.testDialog.visible=!1)},{default:l(()=>a[23]||(a[23]=[n(" 确定 ",-1)])),_:1,__:[23]})]),_:1},8,["icon","title","sub-title"])])]),_:1},8,["modelValue"])])}],["__scopeId","data-v-0797638c"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/system/NotificationTest.vue"]]);export{w as default};
