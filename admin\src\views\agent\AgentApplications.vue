<template>
  <div class="modern-agent-applications">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><UserFilled /></el-icon>
          </div>
          <div class="header-text">
            <h1>代理商申请管理</h1>
            <p>审核和管理代理商申请，包括申请列表、审核流程和批量操作</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click=\"handleExportReport\" class=\"action-btn secondary\">
            <el-icon><Download /></el-icon>
            导出报表
          </el-button>
          <el-button @click=\"showHelpDialog = true\" class=\"action-btn secondary\">
            <el-icon><QuestionFilled /></el-icon>
            功能说明
          </el-button>
          <el-button type=\"primary\" @click=\"refreshData\" class=\"action-btn primary\" :loading=\"loading\">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-container" v-loading="statsLoading">
        <div class="stat-card" v-for="stat in applicationStatCards" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="searchForm" inline @submit.prevent="handleSearch">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、姓名或手机号"
            clearable
            @keyup.enter="handleSearch"
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="申请状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable class="filter-select">
            <el-option label="全部" value="" />
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="代理商类型">
          <el-select v-model="searchForm.agent_type" placeholder="全部类型" clearable class="filter-select">
            <el-option label="全部" value="" />
            <el-option label="个人代理" value="individual" />
            <el-option label="企业代理" value="enterprise" />
            <el-option label="渠道代理" value="channel" />
          </el-select>
        </el-form-item>
        <el-form-item label="代理商等级">
          <el-select v-model="searchForm.agent_level" placeholder="全部等级" clearable class="filter-select">
            <el-option label="全部" value="" />
            <el-option label="平台代理商" value="platform" />
            <el-option label="分站代理商" value="substation" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" class="search-btn">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset" class="reset-btn">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量操作 -->
    <div class="batch-actions" v-if="selectedApplications.length > 0">
      <el-card class="batch-card">
        <div class="batch-content">
          <el-alert
            :title="`已选择 ${selectedApplications.length} 个申请`"
            type="info"
            show-icon
            :closable="false"
          />
          <div class="batch-buttons">
            <el-button type="success" @click="batchApprove" class="batch-btn">
              <el-icon><Check /></el-icon>
              批量通过
            </el-button>
            <el-button type="danger" @click="batchReject" class="batch-btn">
              <el-icon><Close /></el-icon>
              批量拒绝
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 申请列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h3>申请列表</h3>
            <el-tag size="small" type="info">共 {{ applications.total || 0 }} 条记录</el-tag>
          </div>
          <div class="header-right">
            <el-button-group>
              <el-button size="small" @click="loadApplications">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>
      
      <el-table
        :data="applications.data"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        class="modern-table"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="user.username" label="用户名" width="120">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="32" :src="row.user?.avatar">
                {{ row.user?.name?.charAt(0) }}
              </el-avatar>
              <div class="user-details">
                <div class="username">{{ row.user?.username }}</div>
                <div class="user-name">{{ row.user?.name }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="user.phone" label="联系方式" width="140">
          <template #default="{ row }">
            <div class="contact-info">
              <div class="phone">{{ row.user?.phone }}</div>
              <div class="email">{{ row.user?.email || '-' }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="agent_type_text" label="代理商类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getAgentTypeColor(row.agent_type)" size="small">
              {{ row.agent_type_text }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="agent_level_text" label="代理商等级" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getAgentLevelColor(row.agent_level)" size="small">
              {{ row.agent_level_text }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="expected_commission_rate" label="期望佣金" width="100" align="center">
          <template #default="{ row }">
            <span class="commission-rate">{{ row.expected_commission_rate }}%</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status_text" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ row.status_text }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="申请时间" width="140">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" link @click="viewApplication(row)">
              详情
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              size="small"
              type="success"
              link
              @click="approveApplication(row)"
            >
              通过
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              size="small"
              type="danger"
              link
              @click="rejectApplication(row)"
            >
              拒绝
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="applications.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 申请详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="申请详情" width="800px">
      <div v-if="currentApplication" class="application-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请人">
            {{ currentApplication.user?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ currentApplication.user?.username }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ currentApplication.user?.phone }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ currentApplication.user?.email }}
          </el-descriptions-item>
          <el-descriptions-item label="代理商类型">
            <el-tag :type="getAgentTypeColor(currentApplication.agent_type)">
              {{ currentApplication.agent_type_text }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="代理商等级">
            <el-tag :type="getAgentLevelColor(currentApplication.agent_level)">
              {{ currentApplication.agent_level_text }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="期望佣金比例">
            {{ currentApplication.expected_commission_rate }}%
          </el-descriptions-item>
          <el-descriptions-item label="申请状态">
            <el-tag :type="getStatusColor(currentApplication.status)">
              {{ currentApplication.status_text }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>企业信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="公司名称">
              {{ currentApplication.business_info?.company_name || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="营业执照">
              {{ currentApplication.business_info?.business_license || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="经营范围" :span="2">
              {{ currentApplication.business_info?.business_scope || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h4>联系信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="联系人">
              {{ currentApplication.contact_info?.contact_person }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ currentApplication.contact_info?.contact_phone }}
            </el-descriptions-item>
            <el-descriptions-item label="联系邮箱">
              {{ currentApplication.contact_info?.contact_email || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系地址">
              {{ currentApplication.contact_info?.contact_address || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h4>申请理由</h4>
          <p>{{ currentApplication.application_reason }}</p>
        </div>

        <div v-if="currentApplication.review_comment" class="detail-section">
          <h4>审核意见</h4>
          <p>{{ currentApplication.review_comment }}</p>
          <p class="review-info">
            审核人：{{ currentApplication.reviewer?.name }}
            审核时间：{{ formatDate(currentApplication.reviewed_at) }}
          </p>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          v-if="currentApplication?.status === 'pending'"
          type="success"
          @click="approveApplication(currentApplication)"
        >
          通过申请
        </el-button>
        <el-button
          v-if="currentApplication?.status === 'pending'"
          type="danger"
          @click="rejectApplication(currentApplication)"
        >
          拒绝申请
        </el-button>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog v-model="reviewDialogVisible" :title="reviewTitle" width="500px">
      <el-form :model="reviewForm" label-width="80px">
        <el-form-item label="审核意见">
          <el-input
            v-model="reviewForm.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="reviewDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReview" :loading="reviewLoading">
          确认
        </el-button>
      </template>
    </el-dialog>

    <!-- 功能说明对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="代理商申请管理功能说明"
      width="1000px"
      class="help-dialog"
    >
      <div class="help-content">
        <!-- 功能概述 -->
        <div class="help-section">
          <h3>👥 功能概述</h3>
          <p>代理商申请管理是平台分销体系的入口管理模块，负责处理和审核用户的代理商申请，确保代理商质量和平台规范。</p>
        </div>

        <!-- 核心功能 -->
        <div class="help-section">
          <h3>🚀 核心功能模块</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>申请审核</h4>
                  <p>审核代理商申请，包括个人信息、企业资质、申请理由等</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>批量操作</h4>
                  <p>支持批量通过或拒绝申请，提高审核效率</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><DataAnalysis /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>数据统计</h4>
                  <p>实时统计申请数量、审核状态、通过率等关键指标</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 操作指南 -->
        <div class="help-section">
          <h3>📝 操作指南</h3>
          <el-collapse v-model="activeGuides">
            <el-collapse-item title="如何审核代理商申请？" name="review-application">
              <div class="guide-content">
                <ol>
                  <li>在申请列表中查看待审核的申请</li>
                  <li>点击"详情"查看申请人的完整信息</li>
                  <li>评估申请人的资质和申请理由</li>
                  <li>点击"通过"或"拒绝"，填写审核意见</li>
                  <li>确认提交审核结果</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 提示：建议仔细核实申请人信息，确保代理商质量
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何使用批量操作？" name="batch-operation">
              <div class="guide-content">
                <ol>
                  <li>在申请列表中勾选需要批量处理的申请</li>
                  <li>页面上方会显示批量操作栏</li>
                  <li>选择"批量通过"或"批量拒绝"</li>
                  <li>确认操作后系统自动处理所有选中的申请</li>
                </ol>
                <el-alert type="warning" :closable="false">
                  ⚠️ 注意：批量操作不可撤销，请谨慎使用
                </el-alert>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, Refresh, Check, Close, Document, Clock, Download, QuestionFilled,
  UserFilled, RefreshLeft, DataAnalysis, ArrowUp
} from '@element-plus/icons-vue'
import { agentApplicationApi } from '@/api/agent'

// 响应式数据
const loading = ref(false)
const statsLoading = ref(true)
const currentPage = ref(1)
const pageSize = ref(20)
const detailDialogVisible = ref(false)
const reviewDialogVisible = ref(false)
const reviewLoading = ref(false)
const showHelpDialog = ref(false)

const applicationStats = ref({})
const applications = ref({ data: [], total: 0 })
const selectedApplications = ref([])
const currentApplication = ref(null)
const currentReviewAction = ref('')

// 帮助对话框相关数据
const activeGuides = ref(['review-application'])

// 统计卡片数据 - 与其他页面保持一致的设计
const applicationStatCards = ref([
  {
    key: 'total',
    label: '总申请数',
    value: '0',
    icon: 'Document',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15个'
  },
  {
    key: 'pending',
    label: '待审核',
    value: '0',
    icon: 'Clock',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '今日+8个'
  },
  {
    key: 'approved',
    label: '已通过',
    value: '0',
    icon: 'Check',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '通过率85%'
  },
  {
    key: 'rejected',
    label: '已拒绝',
    value: '0',
    icon: 'Close',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '拒绝率15%'
  }
])

const searchForm = reactive({
  keyword: '',
  status: '',
  agent_type: '',
  agent_level: ''
})

const reviewForm = reactive({
  comment: ''
})

// 计算属性
const reviewTitle = computed(() => {
  return currentReviewAction.value === 'approve' ? '通过申请' : '拒绝申请'
})

// 方法
const refreshData = async () => {
  await Promise.all([
    loadApplicationStats(),
    loadApplications()
  ])
  ElMessage.success('数据刷新成功')
}

const handleExportReport = async () => {
  try {
    ElMessage.success('申请报表导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const loadApplicationStats = async () => {
  try {
    statsLoading.value = true
    console.log('加载申请统计数据...')
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟统计数据
    const mockStats = {
      total: 156,
      pending: 23,
      approved: 108,
      rejected: 25
    }
    
    applicationStats.value = mockStats
    
    // 更新统计卡片数据
    applicationStatCards.value[0].value = mockStats.total.toString()
    applicationStatCards.value[1].value = mockStats.pending.toString()
    applicationStatCards.value[2].value = mockStats.approved.toString()
    applicationStatCards.value[3].value = mockStats.rejected.toString()
    
    console.log('申请统计数据加载完成')
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

const loadApplications = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm
    }
    const response = await agentApplicationApi.getList(params)
    applications.value = response.data
  } catch (error) {
    ElMessage.error('加载申请列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadApplications()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadApplications()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadApplications()
}

const handleSelectionChange = (selection) => {
  // 确保selection是数组
  const validSelection = Array.isArray(selection) ? selection : []
  selectedApplications.value = validSelection
}

const viewApplication = (application) => {
  currentApplication.value = application
  detailDialogVisible.value = true
}

const approveApplication = (application) => {
  currentApplication.value = application
  currentReviewAction.value = 'approve'
  reviewForm.comment = ''
  reviewDialogVisible.value = true
}

const rejectApplication = (application) => {
  currentApplication.value = application
  currentReviewAction.value = 'reject'
  reviewForm.comment = ''
  reviewDialogVisible.value = true
}

const confirmReview = async () => {
  try {
    reviewLoading.value = true
    await agentApplicationApi.review(currentApplication.value.id, {
      action: currentReviewAction.value,
      comment: reviewForm.comment
    })
    
    ElMessage.success(currentReviewAction.value === 'approve' ? '申请已通过' : '申请已拒绝')
    reviewDialogVisible.value = false
    detailDialogVisible.value = false
    loadApplications()
    loadApplicationStats()
  } catch (error) {
    ElMessage.error('审核失败')
  } finally {
    reviewLoading.value = false
  }
}

const batchApprove = async () => {
  try {
    await ElMessageBox.confirm('确定要批量通过选中的申请吗？', '确认操作', {
      type: 'warning'
    })
    
    const applicationIds = selectedApplications.value.map(app => app.id)
    await agentApplicationApi.batchReview({
      application_ids: applicationIds,
      action: 'approve',
      comment: '批量通过'
    })
    
    ElMessage.success('批量通过成功')
    loadApplications()
    loadApplicationStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量通过失败')
    }
  }
}

const batchReject = async () => {
  try {
    await ElMessageBox.confirm('确定要批量拒绝选中的申请吗？', '确认操作', {
      type: 'warning'
    })
    
    const applicationIds = selectedApplications.value.map(app => app.id)
    await agentApplicationApi.batchReview({
      application_ids: applicationIds,
      action: 'reject',
      comment: '批量拒绝'
    })
    
    ElMessage.success('批量拒绝成功')
    loadApplications()
    loadApplicationStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量拒绝失败')
    }
  }
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

const getStatusColor = (status) => {
  const colors = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'cancelled': 'info'
  }
  return colors[status] || 'info'
}

const getAgentTypeColor = (type) => {
  const colors = {
    'individual': 'primary',
    'enterprise': 'success',
    'channel': 'warning'
  }
  return colors[type] || 'info'
}

const getAgentLevelColor = (level) => {
  const colors = {
    'platform': 'primary',
    'substation': 'success'
  }
  return colors[level] || 'info'
}

// 生命周期
onMounted(() => {
  loadApplicationStats()
  loadApplications()
})
</script>

<style lang="scss" scoped>
.modern-agent-applications {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  // 页面头部样式
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          height: 40px;
          padding: 0 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }

  // 统计卡片区域
  .stats-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 筛选卡片样式
  .filter-card {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .search-input {
      width: 250px;
    }
    
    .filter-select {
      width: 150px;
    }
    
    .search-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      }
    }
    
    .reset-btn {
      background: #f5f7fa;
      border-color: #dcdfe6;
      color: #606266;
      border-radius: 8px;
      
      &:hover {
        background: #ecf5ff;
        border-color: #409eff;
        color: #409eff;
      }
    }
  }

  // 批量操作样式
  .batch-actions {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .batch-card {
      border-radius: 16px;
      border: none;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      
      .batch-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        
        .batch-buttons {
          display: flex;
          gap: 12px;
          
          .batch-btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &:hover {
              transform: translateY(-2px);
            }
          }
        }
      }
    }
  }

  // 表格卡片样式
  .table-card {
    max-width: 1400px;
    margin: 0 auto 40px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
          
          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
          
          .el-tag {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: none;
          }
        }
      }
    }
    
    :deep(.el-card__body) {
      padding: 0;
    }
    
    :deep(.el-table) {
      .el-table__header th {
        background: #fafbfc !important;
        border-bottom: 1px solid #e4e7ed;
        font-weight: 600;
        color: #606266;
        font-size: 13px;
        padding: 16px 12px;
      }
      
      .el-table__body tr {
        transition: all 0.3s ease;
        
        &:hover {
          background: #f8f9ff !important;
        }
        
        td {
          border-bottom: 1px solid #f0f2f5;
          padding: 16px 12px;
        }
      }
    }
    
    .pagination {
      padding: 32px 16px;
      text-align: center;
    }
  }

  // 表格内容样式
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .user-details {
      .username {
        font-weight: 600;
        color: #303133;
        font-size: 14px;
        margin-bottom: 2px;
      }
      
      .user-name {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .contact-info {
    .phone {
      font-weight: 600;
      color: #303133;
      margin-bottom: 2px;
    }
    
    .email {
      font-size: 12px;
      color: #909399;
    }
  }

  .commission-rate {
    color: #f56c6c;
    font-weight: 600;
  }

  // 申请详情样式
  .application-detail {
    max-height: 600px;
    overflow-y: auto;
  }

  .detail-section {
    margin-top: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 16px;
    }
  }

  .review-info {
    margin-top: 10px;
    color: #909399;
    font-size: 12px;
  }
}

/* 帮助对话框样式 */
.help-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.help-content {
  .help-section {
    margin-bottom: 30px;
    
    h3 {
      color: #303133;
      margin-bottom: 15px;
      font-size: 18px;
      border-bottom: 2px solid #667eea;
      padding-bottom: 8px;
    }
    
    p {
      color: #606266;
      line-height: 1.6;
      margin-bottom: 15px;
    }
  }
  
  .feature-item {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 12px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    background: white;
    
    &:hover {
      border-color: #667eea;
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
      transform: translateY(-2px);
    }
    
    .feature-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      flex-shrink: 0;
      
      .el-icon {
        color: white;
        font-size: 20px;
      }
    }
    
    .feature-content {
      flex: 1;
      
      h4 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
  
  .guide-content {
    padding: 20px;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-radius: 12px;
    
    ol, ul {
      margin: 0 0 16px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #606266;
        line-height: 1.5;
      }
    }
    
    :deep(.el-alert) {
      margin-top: 16px;
      border-radius: 8px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .modern-agent-applications {
    .stats-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .modern-agent-applications {
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .stats-container {
      grid-template-columns: 1fr;
    }
    
    .filter-card {
      .el-form {
        .el-form-item {
          width: 100%;
          margin-bottom: 16px;
          
          .search-input,
          .filter-select {
            width: 100%;
          }
        }
      }
    }
    
    .batch-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }
    
    .help-content .feature-item {
      flex-direction: column;
      text-align: center;
      
      .feature-icon {
        margin: 0 0 15px 0;
      }
    }
  }
}
</style>