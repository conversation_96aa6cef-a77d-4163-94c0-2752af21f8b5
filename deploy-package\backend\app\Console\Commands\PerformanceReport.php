<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MonitorServiceMerged;
use Illuminate\Support\Facades\Storage;

/**
 * 性能报告生成命令
 * 生成详细的系统性能报告
 */
class PerformanceReport extends Command
{
    protected $signature = 'performance:report {--format=json : 输出格式 (json|html|text)} {--save : 保存报告到文件}';
    protected $description = '生成系统性能报告';

    protected MonitorServiceMerged $performanceService;

    public function __construct(MonitorServiceMerged $performanceService)
    {
        parent::__construct();
        $this->performanceService = $performanceService;
    }

    public function handle()
    {
        $this->info('🔍 正在收集性能数据...');
        
        $report = $this->performanceService->generateReport();
        $format = $this->option('format');
        
        switch ($format) {
            case 'html':
                $output = $this->generateHtmlReport($report);
                break;
            case 'text':
                $output = $this->generateTextReport($report);
                break;
            default:
                $output = json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }

        if ($this->option('save')) {
            $filename = 'performance-reports/' . now()->format('Y-m-d_H-i-s') . '.' . $format;
            Storage::disk('local')->put($filename, $output);
            $this->info("📄 报告已保存到: storage/app/{$filename}");
        } else {
            $this->line($output);
        }

        // 显示关键指标摘要
        $this->displaySummary($report);
    }

    /**
     * 生成HTML格式报告
     */
    private function generateHtmlReport(array $report): string
    {
        $html = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>晨鑫流量变现系统 性能报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }
        .metric-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-critical { color: #dc3545; }
        .recommendations { background: #e9ecef; padding: 15px; border-radius: 6px; margin-top: 20px; }
        .recommendation { margin-bottom: 15px; padding: 10px; background: white; border-radius: 4px; }
        .priority-high { border-left: 4px solid #dc3545; }
        .priority-medium { border-left: 4px solid #ffc107; }
        .priority-low { border-left: 4px solid #28a745; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 晨鑫流量变现系统 性能报告</h1>
            <p>生成时间: ' . $report['generated_at'] . '</p>
            <p class="status-' . $report['analysis']['status'] . '">
                系统状态: ' . ($report['analysis']['status'] === 'healthy' ? '健康' : '需要关注') . ' 
                (评分: ' . $report['analysis']['score'] . '/100)
            </p>
        </div>';

        // 系统指标
        $html .= '<div class="metric-grid">';
        
        // 内存使用
        $memory = $report['metrics']['system']['memory'];
        $html .= '<div class="metric-card">
            <div class="metric-title">💾 内存使用</div>
            <div class="metric-value">' . $memory['usage_percentage'] . '%</div>
            <div>' . $memory['current_mb'] . 'MB / ' . $memory['limit_mb'] . 'MB</div>
        </div>';

        // 磁盘使用
        $disk = $report['metrics']['system']['disk'];
        $html .= '<div class="metric-card">
            <div class="metric-title">💿 磁盘使用</div>
            <div class="metric-value">' . $disk['usage_percentage'] . '%</div>
            <div>' . $disk['used_gb'] . 'GB / ' . $disk['total_gb'] . 'GB</div>
        </div>';

        // 缓存命中率
        if (isset($report['metrics']['cache']['redis']['stats']['hit_rate'])) {
            $hitRate = $report['metrics']['cache']['redis']['stats']['hit_rate'];
            $html .= '<div class="metric-card">
                <div class="metric-title">⚡ 缓存命中率</div>
                <div class="metric-value">' . $hitRate . '%</div>
            </div>';
        }

        // 数据库连接
        if (isset($report['metrics']['database']['connections'])) {
            $db = $report['metrics']['database']['connections'];
            $html .= '<div class="metric-card">
                <div class="metric-title">🗄️ 数据库连接</div>
                <div class="metric-value">' . $db['usage_percentage'] . '%</div>
                <div>' . $db['current'] . ' / ' . $db['max'] . '</div>
            </div>';
        }

        $html .= '</div>';

        // 优化建议
        if (!empty($report['recommendations'])) {
            $html .= '<div class="recommendations">
                <h2>🔧 优化建议</h2>';
            
            foreach ($report['recommendations'] as $rec) {
                $html .= '<div class="recommendation priority-' . $rec['priority'] . '">
                    <h3>' . $rec['title'] . '</h3>
                    <p>' . $rec['description'] . '</p>
                    <ul>';
                foreach ($rec['actions'] as $action) {
                    $html .= '<li>' . $action . '</li>';
                }
                $html .= '</ul></div>';
            }
            
            $html .= '</div>';
        }

        $html .= '</div></body></html>';
        
        return $html;
    }

    /**
     * 生成文本格式报告
     */
    private function generateTextReport(array $report): string
    {
        $text = "晨鑫流量变现系统 性能报告\n";
        $text .= str_repeat("=", 50) . "\n";
        $text .= "生成时间: " . $report['generated_at'] . "\n";
        $text .= "系统状态: " . ($report['analysis']['status'] === 'healthy' ? '健康' : '需要关注') . "\n";
        $text .= "性能评分: " . $report['analysis']['score'] . "/100\n\n";

        // 系统指标
        $text .= "系统指标\n";
        $text .= str_repeat("-", 20) . "\n";
        
        $memory = $report['metrics']['system']['memory'];
        $text .= sprintf("内存使用: %s%% (%sMB / %sMB)\n", 
            $memory['usage_percentage'], 
            $memory['current_mb'], 
            $memory['limit_mb']
        );

        $disk = $report['metrics']['system']['disk'];
        $text .= sprintf("磁盘使用: %s%% (%sGB / %sGB)\n", 
            $disk['usage_percentage'], 
            $disk['used_gb'], 
            $disk['total_gb']
        );

        if (isset($report['metrics']['cache']['redis']['stats']['hit_rate'])) {
            $text .= sprintf("缓存命中率: %s%%\n", 
                $report['metrics']['cache']['redis']['stats']['hit_rate']
            );
        }

        if (isset($report['metrics']['database']['connections'])) {
            $db = $report['metrics']['database']['connections'];
            $text .= sprintf("数据库连接: %s%% (%s / %s)\n", 
                $db['usage_percentage'], 
                $db['current'], 
                $db['max']
            );
        }

        // 问题和建议
        if (!empty($report['analysis']['issues'])) {
            $text .= "\n发现的问题\n";
            $text .= str_repeat("-", 20) . "\n";
            foreach ($report['analysis']['issues'] as $issue) {
                $text .= "• " . $issue . "\n";
            }
        }

        if (!empty($report['analysis']['suggestions'])) {
            $text .= "\n优化建议\n";
            $text .= str_repeat("-", 20) . "\n";
            foreach ($report['analysis']['suggestions'] as $suggestion) {
                $text .= "• " . $suggestion . "\n";
            }
        }

        return $text;
    }

    /**
     * 显示关键指标摘要
     */
    private function displaySummary(array $report): void
    {
        $this->newLine();
        $this->info('📊 性能指标摘要');
        $this->line(str_repeat('-', 50));

        // 系统状态
        $status = $report['analysis']['status'];
        $statusColor = $status === 'healthy' ? 'info' : 'warn';
        $statusText = $status === 'healthy' ? '健康' : '需要关注';
        $this->$statusColor("系统状态: {$statusText} (评分: {$report['analysis']['score']}/100)");

        // 关键指标
        $memory = $report['metrics']['system']['memory'];
        $memoryColor = $memory['usage_percentage'] > 80 ? 'warn' : 'info';
        $this->$memoryColor("内存使用: {$memory['usage_percentage']}% ({$memory['current_mb']}MB / {$memory['limit_mb']}MB)");

        $disk = $report['metrics']['system']['disk'];
        $diskColor = $disk['usage_percentage'] > 90 ? 'error' : ($disk['usage_percentage'] > 80 ? 'warn' : 'info');
        $this->$diskColor("磁盘使用: {$disk['usage_percentage']}% ({$disk['used_gb']}GB / {$disk['total_gb']}GB)");

        if (isset($report['metrics']['cache']['redis']['stats']['hit_rate'])) {
            $hitRate = $report['metrics']['cache']['redis']['stats']['hit_rate'];
            $cacheColor = $hitRate < 80 ? 'warn' : 'info';
            $this->$cacheColor("缓存命中率: {$hitRate}%");
        }

        // 问题提醒
        if (!empty($report['analysis']['issues'])) {
            $this->newLine();
            $this->warn('⚠️  发现的问题:');
            foreach ($report['analysis']['issues'] as $issue) {
                $this->line("  • {$issue}");
            }
        }

        // 建议
        if (!empty($report['analysis']['suggestions'])) {
            $this->newLine();
            $this->info('💡 优化建议:');
            foreach ($report['analysis']['suggestions'] as $suggestion) {
                $this->line("  • {$suggestion}");
            }
        }

        $this->newLine();
        $this->info('✅ 性能报告生成完成！');
    }
}