/* empty css             *//* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{z as e,A as a,r as t,M as s,o as n,m as i,q as l,G as o,B as r,E as d}from"./vue-vendor-BcnDv-68.js";import{P as c}from"./PageLayout-DKvOdnm6.js";import{_ as p}from"./index-eUTsTR3J.js";import{X as u,ag as m,V as g,ai as f,aj as h,ak as _,Z as y,a1 as j,a7 as b,an as v}from"./element-plus-C2UshkXo.js";/* empty css                           */import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const w={class:"content-wrapper"},z={class:"card-body"},C={class:"pagination-wrapper"};const k=p({__name:"PermissionManagement",setup(e,{expose:a}){a();const i=t(!1),l=t([]),o=s({current:1,size:20,total:0}),r=async()=>{i.value=!0;try{await new Promise(e=>setTimeout(e,300)),l.value=[{id:1,name:"用户管理权限",status:"active",created_at:"2024-01-01 12:00:00",description:"管理用户信息、角色分配等功能",module:"user"},{id:2,name:"内容管理权限",status:"active",created_at:"2024-01-02 12:00:00",description:"管理内容发布、审核等功能",module:"content"},{id:3,name:"财务管理权限",status:"inactive",created_at:"2024-01-03 12:00:00",description:"管理财务数据、支付配置等功能",module:"finance"},{id:4,name:"系统设置权限",status:"active",created_at:"2024-01-04 12:00:00",description:"管理系统配置、参数设置等功能",module:"system"}],o.total=4}catch(e){u.error("加载数据失败")}finally{i.value=!1}};n(()=>{r()});const d={loading:i,tableData:l,pagination:o,handleRefresh:()=>{r()},handleAdd:()=>{u.info("新增功能待实现")},handleEdit:e=>{u.info(`编辑功能待实现: ${e.name}`)},handleDelete:async e=>{try{await m.confirm(`确定要删除 "${e.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),u.success("删除成功"),r()}catch{u.info("已取消删除")}},handleSizeChange:e=>{o.size=e,r()},handleCurrentChange:e=>{o.current=e,r()},getModuleName:e=>({user:"用户管理",content:"内容管理",finance:"财务管理",system:"系统设置"}[e]||e),getModuleTagType:e=>({user:"primary",content:"success",finance:"warning",system:"danger"}[e]||"info"),loadData:r,ref:t,reactive:s,onMounted:n,get ElMessage(){return u},get ElMessageBox(){return m},PageLayout:c};return Object.defineProperty(d,"__isScriptSetup",{enumerable:!1,value:!0}),d}},[["render",function(t,s,n,c,p,u){const m=j,k=h,x=_,M=f,P=v,D=g,T=b;return i(),e(c.PageLayout,{title:"权限管理",subtitle:"管理系统权限和角色分配",loading:c.loading},{actions:a(()=>[o(m,{class:"modern-btn secondary",onClick:c.handleRefresh},{default:a(()=>s[2]||(s[2]=[l("i",{class:"el-icon-refresh"},null,-1),d(" 刷新数据 ",-1)])),_:1,__:[2]}),o(m,{class:"modern-btn primary",onClick:c.handleAdd},{default:a(()=>s[3]||(s[3]=[l("i",{class:"el-icon-plus"},null,-1),d(" 新增 ",-1)])),_:1,__:[3]})]),default:a(()=>[l("div",w,[o(D,{class:"modern-card"},{default:a(()=>[s[6]||(s[6]=l("div",{class:"card-header"},[l("h3",null,"权限管理"),l("p",{class:"text-muted"},"管理系统权限和角色分配")],-1)),l("div",z,[r((i(),e(M,{data:c.tableData,style:{width:"100%"},class:"modern-table"},{default:a(()=>[o(k,{prop:"id",label:"ID",width:"80"}),o(k,{prop:"name",label:"权限名称","min-width":"150"}),o(k,{prop:"description",label:"权限描述","min-width":"200","show-overflow-tooltip":""}),o(k,{prop:"module",label:"所属模块",width:"120"},{default:a(({row:e})=>[o(x,{size:"small",type:c.getModuleTagType(e.module)},{default:a(()=>[d(y(c.getModuleName(e.module)),1)]),_:2},1032,["type"])]),_:1}),o(k,{prop:"status",label:"状态",width:"100"},{default:a(({row:e})=>[o(x,{type:"active"===e.status?"success":"info",size:"small"},{default:a(()=>[d(y("active"===e.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),o(k,{prop:"created_at",label:"创建时间",width:"180"}),o(k,{label:"操作",width:"200",fixed:"right"},{default:a(({row:e})=>[o(m,{type:"primary",size:"small",onClick:a=>c.handleEdit(e)},{default:a(()=>s[4]||(s[4]=[d(" 编辑 ",-1)])),_:2,__:[4]},1032,["onClick"]),o(m,{type:"danger",size:"small",onClick:a=>c.handleDelete(e)},{default:a(()=>s[5]||(s[5]=[d(" 删除 ",-1)])),_:2,__:[5]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[T,c.loading]]),l("div",C,[o(P,{"current-page":c.pagination.current,"onUpdate:currentPage":s[0]||(s[0]=e=>c.pagination.current=e),"page-size":c.pagination.size,"onUpdate:pageSize":s[1]||(s[1]=e=>c.pagination.size=e),total:c.pagination.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:c.handleSizeChange,onCurrentChange:c.handleCurrentChange},null,8,["current-page","page-size","total"])])])]),_:1,__:[6]})])]),_:1},8,["loading"])}],["__scopeId","data-v-b8c92c79"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/permission/PermissionManagement.vue"]]);export{k as default};
