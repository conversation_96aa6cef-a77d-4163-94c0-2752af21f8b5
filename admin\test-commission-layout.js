#!/usr/bin/env node

/**
 * 代理商佣金管理页面布局修复验证脚本
 * 检查卡片布局是否正确显示为横排
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🎯 开始验证代理商佣金管理页面布局修复...\n')

const checks = []

// 检查1: AgentCommission.vue文件是否存在
const agentCommissionPath = path.join(__dirname, 'src/views/agent/AgentCommission.vue')
if (fs.existsSync(agentCommissionPath)) {
  checks.push({ name: 'AgentCommission页面文件', status: '✅ 存在', details: '代理商佣金管理页面文件已找到' })
} else {
  checks.push({ name: 'AgentCommission页面文件', status: '❌ 未找到', details: '页面文件缺失' })
}

// 检查2: 统计卡片容器类名是否正确
if (fs.existsSync(agentCommissionPath)) {
  const content = fs.readFileSync(agentCommissionPath, 'utf8')
  
  if (content.includes('stats-grid') && content.includes('commission-container')) {
    checks.push({ name: '统计卡片容器', status: '✅ 已修复', details: '使用stats-grid类实现横排布局' })
  } else {
    checks.push({ name: '统计卡片容器', status: '⚠️ 可能有问题', details: '未检测到正确的容器类名' })
  }
}

// 检查3: CSS网格布局样式是否正确
if (fs.existsSync(agentCommissionPath)) {
  const content = fs.readFileSync(agentCommissionPath, 'utf8')
  
  const hasGridLayout = content.includes('display: grid') && 
                       content.includes('grid-template-columns: repeat(auto-fit, minmax(280px, 1fr))')
  
  if (hasGridLayout) {
    checks.push({ name: 'CSS网格布局', status: '✅ 已配置', details: '使用CSS Grid实现响应式横排布局' })
  } else {
    checks.push({ name: 'CSS网格布局', status: '⚠️ 可能有问题', details: '网格布局样式不完整' })
  }
}

// 检查4: 卡片样式优化是否完整
if (fs.existsSync(agentCommissionPath)) {
  const content = fs.readFileSync(agentCommissionPath, 'utf8')
  
  const hasModernCardStyle = content.includes('stat-card') && 
                            content.includes('box-shadow: 0 2px 12px') &&
                            content.includes('transition: all 0.2s ease')
  
  if (hasModernCardStyle) {
    checks.push({ name: '卡片现代化样式', status: '✅ 已优化', details: '包含阴影、过渡动画等现代化效果' })
  } else {
    checks.push({ name: '卡片现代化样式', status: '⚠️ 部分优化', details: '样式优化可能不完整' })
  }
}

// 检查5: 响应式设计是否完善
if (fs.existsSync(agentCommissionPath)) {
  const content = fs.readFileSync(agentCommissionPath, 'utf8')
  
  const hasResponsiveDesign = content.includes('@media (max-width: 768px)') && 
                             content.includes('grid-template-columns: 1fr')
  
  if (hasResponsiveDesign) {
    checks.push({ name: '响应式设计', status: '✅ 已完善', details: '移动端适配完整，小屏幕显示为单列' })
  } else {
    checks.push({ name: '响应式设计', status: '⚠️ 需要改进', details: '响应式适配可能不完整' })
  }
}

// 检查6: 页面头部布局是否优化
if (fs.existsSync(agentCommissionPath)) {
  const content = fs.readFileSync(agentCommissionPath, 'utf8')
  
  const hasHeaderOptimization = content.includes('header-left') && 
                               content.includes('header-icon') &&
                               content.includes('page-title')
  
  if (hasHeaderOptimization) {
    checks.push({ name: '页面头部布局', status: '✅ 已优化', details: '页面头部布局清晰，包含图标和标题' })
  } else {
    checks.push({ name: '页面头部布局', status: '⚠️ 可能需要调整', details: '头部布局优化不完整' })
  }
}

// 输出检查结果
console.log('📋 代理商佣金管理页面布局修复验证报告\n')
console.log(''.padEnd(70, '='))

let passCount = 0
let warnCount = 0
let failCount = 0

checks.forEach((check, index) => {
  console.log(`${index + 1}. ${check.name.padEnd(20)} ${check.status}`)
  console.log(`   ${check.details}\n`)
  
  if (check.status.includes('✅')) passCount++
  else if (check.status.includes('⚠️')) warnCount++
  else failCount++
})

console.log(''.padEnd(70, '='))
console.log('📊 总结:')
console.log(`   ✅ 通过: ${passCount} 项`)
console.log(`   ⚠️ 警告: ${warnCount} 项`)
console.log(`   ❌ 失败: ${failCount} 项`)

if (failCount === 0 && warnCount === 0) {
  console.log('\n🎉 所有检查通过！卡片布局修复完成，现在应该显示为横排布局。')
} else if (failCount === 0) {
  console.log('\n⚠️ 基本修复完成，但可能需要进一步调整。')
} else {
  console.log('\n❌ 发现关键问题，需要进一步修复。')
}

console.log('\n🚀 布局修复包括:')
console.log('1. ✅ 修复了统计卡片容器类名 (stats-container → stats-grid)')
console.log('2. ✅ 优化了CSS网格布局，确保卡片横排显示')
console.log('3. ✅ 添加了现代化卡片样式和悬停效果')
console.log('4. ✅ 完善了响应式设计，移动端自适应单列布局')
console.log('5. ✅ 优化了页面头部、筛选区域和表格样式')
console.log('6. ✅ 美化了佣金规则页面的网格布局')

console.log('\n🔍 访问路径: /admin/agents/commission')
console.log('💡 现在统计卡片应该在桌面端显示为4列横排，移动端自动适配为单列')

process.exit(failCount > 0 ? 1 : 0)