// ============================================================================
// 晨鑫流量变现系统 现代导航系统样式库
// 基于UserList.vue成功设计风格制定
// ============================================================================

// === 导入基础变量 ===
@import './variables.scss';

// === 现代导航主题变量 ===
$modern-nav-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$modern-nav-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$modern-nav-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$modern-nav-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
$modern-nav-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

// 背景渐变
$modern-nav-bg: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$modern-nav-card-bg: rgba(255, 255, 255, 0.95);
$modern-nav-glass: rgba(255, 255, 255, 0.9);

// 阴影系统
$modern-nav-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$modern-nav-shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.08);
$modern-nav-shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.12);
$modern-nav-shadow-floating: 0 8px 32px rgba(102, 126, 234, 0.3);

// === 核心Mixins ===
@mixin modern-nav-card {
  background: $modern-nav-card-bg;
  border-radius: 16px;
  box-shadow: $modern-nav-shadow-medium;
  border: 1px solid #e4e7ed;
  backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    box-shadow: $modern-nav-shadow-heavy;
    transform: translateY(-2px);
  }
}

@mixin modern-nav-gradient-button($gradient: $modern-nav-primary) {
  background: $gradient;
  border: none;
  color: white;
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  }
  
  &:active {
    transform: translateY(0);
  }
}

@mixin modern-nav-glass-effect {
  background: $modern-nav-glass;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: $modern-nav-shadow-light;
}

@mixin modern-nav-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $modern-nav-shadow-heavy;
  }
}

@mixin modern-nav-text-gradient($gradient: $modern-nav-primary) {
  background: $gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// === 统计卡片样式 ===
.modern-stat-card {
  @include modern-nav-card;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  animation: stat-card-appear 0.6s ease-out;
  
  .stat-icon {
    width: 56px;
    height: 56px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.05) rotate(5deg);
    }
  }
  
  .stat-content {
    flex: 1;
    
    .stat-value {
      font-size: 28px;
      font-weight: 700;
      color: #303133;
      line-height: 1.2;
      margin-bottom: 4px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #909399;
      font-weight: 500;
    }
  }
  
  .stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
    
    &.up {
      color: #67c23a;
    }
    
    &.down {
      color: #f56c6c;
    }
  }
}

// === 过滤器卡片样式 ===
.modern-filter-card {
  @include modern-nav-card;
  padding: 24px;
  margin-bottom: 24px;
  
  .filter-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    
    .filter-left {
      display: flex;
      gap: 16px;
      flex: 1;
      
      .search-input {
        width: 300px;
        
        :deep(.el-input__wrapper) {
          border-radius: 8px;
          border: 1px solid #dcdfe6;
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #c0c4cc;
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
          }
        }
      }
      
      .filter-select {
        width: 150px;
        
        :deep(.el-input__wrapper) {
          border-radius: 8px;
        }
      }
    }
    
    .filter-right {
      display: flex;
      gap: 12px;
      
      .search-btn {
        @include modern-nav-gradient-button;
      }
      
      .reset-btn {
        background: #f5f7fa;
        border-color: #dcdfe6;
        color: #606266;
        border-radius: 8px;
        padding: 0 20px;
        height: 36px;
        
        &:hover {
          background: #ecf5ff;
          border-color: #409eff;
          color: #409eff;
        }
      }
    }
  }
}

// === 表格卡片样式 ===
.modern-table-card {
  @include modern-nav-card;
  overflow: hidden;
  
  .table-header {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-bottom: 1px solid #e4e7ed;
    padding: 20px 24px;
    
    .table-title {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      
      .el-tag {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        border: none;
      }
    }
    
    .table-actions {
      .el-button {
        border-radius: 8px;
        font-weight: 500;
      }
    }
  }
  
  .modern-table {
    :deep(.el-table__header) {
      background: #fafbfc;
      
      th {
        background: #fafbfc !important;
        border-bottom: 1px solid #e4e7ed;
        font-weight: 600;
        color: #606266;
        font-size: 13px;
        padding: 16px 12px;
      }
    }
    
    :deep(.el-table__body) {
      tr {
        transition: all 0.3s ease;
        
        &:hover {
          background: #f8f9ff !important;
        }
        
        td {
          border-bottom: 1px solid #f0f2f5;
          padding: 16px 12px;
        }
      }
    }
  }
  
  .pagination-wrapper {
    padding: 24px;
    display: flex;
    justify-content: center;
    background: #fafbfc;
    border-top: 1px solid #e4e7ed;
    
    .modern-pagination {
      :deep(.el-pagination__total) {
        color: #606266;
        font-weight: 500;
      }
      
      :deep(.btn-prev),
      :deep(.btn-next) {
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        background: white;
        
        &:hover {
          background: #667eea;
          border-color: #667eea;
          color: white;
        }
      }
      
      :deep(.el-pager) {
        li {
          border-radius: 6px;
          margin: 0 2px;
          border: 1px solid transparent;
          transition: all 0.3s ease;
          
          &:hover {
            background: #f0f2ff;
            border-color: #667eea;
            color: #667eea;
          }
          
          &.is-active {
            background: $modern-nav-primary;
            border-color: #667eea;
            color: white;
          }
        }
      }
    }
  }
}

// === 页面头部样式 ===
.modern-page-header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 24px 0;
  margin-bottom: 24px;
  box-shadow: $modern-nav-shadow-light;
  
  .header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .header-icon {
        width: 48px;
        height: 48px;
        background: $modern-nav-primary;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }
      
      .header-text {
        h1 {
          margin: 0;
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          line-height: 1.2;
        }
        
        p {
          margin: 4px 0 0 0;
          font-size: 14px;
          color: #909399;
          line-height: 1.4;
        }
      }
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
      
      .action-btn {
        height: 40px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &.secondary {
          background: #f5f7fa;
          border-color: #dcdfe6;
          color: #606266;
          
          &:hover {
            background: #ecf5ff;
            border-color: #409eff;
            color: #409eff;
          }
        }
        
        &.primary {
          @include modern-nav-gradient-button;
        }
      }
    }
  }
}

// === 动画关键帧 ===
@keyframes stat-card-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modern-nav-fade-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modern-nav-slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes modern-nav-bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes modern-nav-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

// === 响应式工具类 ===
.modern-nav-responsive {
  @media (max-width: 1200px) {
    .modern-stat-card {
      .stat-content .stat-value {
        font-size: 24px;
      }
    }
  }
  
  @media (max-width: 768px) {
    .modern-page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .modern-filter-card .filter-content {
      flex-direction: column;
      gap: 16px;
      
      .filter-left {
        flex-direction: column;
        width: 100%;
        
        .search-input,
        .filter-select {
          width: 100%;
        }
      }
      
      .filter-right {
        width: 100%;
        justify-content: center;
      }
    }
    
    .modern-stat-card {
      .stat-content .stat-value {
        font-size: 20px;
      }
    }
  }
}

// === 主题变量映射 ===
.modern-nav-theme {
  // 主色
  --modern-nav-primary: #{$modern-nav-primary};
  --modern-nav-secondary: #{$modern-nav-secondary};
  --modern-nav-success: #{$modern-nav-success};
  --modern-nav-warning: #{$modern-nav-warning};
  --modern-nav-danger: #{$modern-nav-danger};
  
  // 背景
  --modern-nav-bg: #{$modern-nav-bg};
  --modern-nav-card-bg: #{$modern-nav-card-bg};
  --modern-nav-glass: #{$modern-nav-glass};
  
  // 阴影
  --modern-nav-shadow-light: #{$modern-nav-shadow-light};
  --modern-nav-shadow-medium: #{$modern-nav-shadow-medium};
  --modern-nav-shadow-heavy: #{$modern-nav-shadow-heavy};
  --modern-nav-shadow-floating: #{$modern-nav-shadow-floating};
  
  // 文字
  --modern-nav-text-primary: #303133;
  --modern-nav-text-secondary: #606266;
  --modern-nav-text-tertiary: #909399;
  --modern-nav-text-light: rgba(255, 255, 255, 0.9);
  
  // 边框
  --modern-nav-border: #e4e7ed;
  --modern-nav-border-light: #f0f2ff;
}

// === 工具类 ===
.modern-nav-fade-in {
  animation: modern-nav-fade-in 0.6s ease-out;
}

.modern-nav-slide-in {
  animation: modern-nav-slide-in 0.4s ease-out;
}

.modern-nav-bounce-in {
  animation: modern-nav-bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.modern-nav-hover-lift {
  @include modern-nav-hover-lift;
}

.modern-nav-glass {
  @include modern-nav-glass-effect;
}

.modern-nav-text-gradient {
  @include modern-nav-text-gradient;
}

// === 辅助类 ===
.modern-nav-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-nav-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modern-nav-column {
  display: flex;
  flex-direction: column;
}

.modern-nav-gap-sm { gap: 8px; }
.modern-nav-gap-md { gap: 16px; }
.modern-nav-gap-lg { gap: 24px; }

.modern-nav-radius-sm { border-radius: 8px; }
.modern-nav-radius-md { border-radius: 12px; }
.modern-nav-radius-lg { border-radius: 16px; }

.modern-nav-shadow-sm { box-shadow: $modern-nav-shadow-light; }
.modern-nav-shadow-md { box-shadow: $modern-nav-shadow-medium; }
.modern-nav-shadow-lg { box-shadow: $modern-nav-shadow-heavy; }

// === 特殊效果 ===
.modern-nav-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: 200px 0; }
}

.modern-nav-glow {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
  transition: box-shadow 0.3s ease;
  
  &:hover {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
  }
}

// === 高对比度和可访问性支持 ===
@media (prefers-contrast: high) {
  .modern-nav-theme {
    --modern-nav-text-primary: #000;
    --modern-nav-border: #000;
    
    .modern-stat-card,
    .modern-filter-card,
    .modern-table-card {
      border: 2px solid #000;
    }
  }
}

@media (prefers-reduced-motion: reduce) {
  .modern-nav-theme {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// === 打印样式 ===
@media print {
  .modern-nav-theme {
    .modern-stat-card,
    .modern-filter-card,
    .modern-table-card {
      box-shadow: none;
      border: 1px solid #000;
      background: white;
    }
  }
}