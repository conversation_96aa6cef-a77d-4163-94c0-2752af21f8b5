<template>
  <div class="home-page">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content animate-fade-in-up">
          <h1 class="hero-title">
            智能社群营销
            <span class="text-gradient">新时代</span>
          </h1>
          <p class="hero-subtitle animate-fade-in-up delay-200">
            晨鑫流量变现系统是新一代智能社群营销与多级分销平台，
            助力您的流量变现，打造专业的社群商业生态。
          </p>
          
          <div class="hero-actions animate-fade-in-up delay-400">
            <NuxtLink to="/register" class="modern-btn modern-btn-primary hero-btn">
              <Icon name="rocket" />
              立即开始
            </NuxtLink>
            <NuxtLink to="/demo" class="modern-btn modern-btn-secondary hero-btn">
              <Icon name="play" />
              观看演示
            </NuxtLink>
          </div>
          
          <div class="hero-stats animate-fade-in-up delay-600">
            <div class="stat-item">
              <div class="stat-number">10K+</div>
              <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">50K+</div>
              <div class="stat-label">成功订单</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">99.9%</div>
              <div class="stat-label">系统稳定性</div>
            </div>
          </div>
        </div>
        
        <div class="hero-visual animate-fade-in-right delay-300">
          <div class="visual-card main-card">
            <div class="card-header">
              <div class="card-title">实时数据面板</div>
              <div class="card-status online">在线</div>
            </div>
            <div class="card-content">
              <div class="metric-row">
                <div class="metric">
                  <div class="metric-label">今日收入</div>
                  <div class="metric-value">¥12,580</div>
                  <div class="metric-trend up">+15.2%</div>
                </div>
                <div class="metric">
                  <div class="metric-label">活跃群组</div>
                  <div class="metric-value">156</div>
                  <div class="metric-trend up">+8.3%</div>
                </div>
              </div>
              <div class="chart-placeholder">
                <div class="chart-bar" style="height: 60%"></div>
                <div class="chart-bar" style="height: 80%"></div>
                <div class="chart-bar" style="height: 45%"></div>
                <div class="chart-bar" style="height: 90%"></div>
                <div class="chart-bar" style="height: 70%"></div>
              </div>
            </div>
          </div>
          
          <div class="visual-card floating-card-1">
            <Icon name="shield" />
            <span>智能防红</span>
          </div>
          
          <div class="visual-card floating-card-2">
            <Icon name="users" />
            <span>多级分销</span>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 特色功能 -->
    <section class="features-section">
      <div class="section-container">
        <div class="section-header animate-fade-in-up">
          <h2 class="section-title">核心功能特色</h2>
          <p class="section-subtitle">
            强大的功能组合，助力您的业务快速增长
          </p>
        </div>
        
        <div class="features-grid">
          <div 
            v-for="(feature, index) in features" 
            :key="feature.id"
            class="feature-card animate-fade-in-up"
            :class="`delay-${(index + 1) * 100}`"
          >
            <div class="feature-icon">
              <Icon :name="feature.icon" />
            </div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
            <div class="feature-tags">
              <span 
                v-for="tag in feature.tags" 
                :key="tag" 
                class="feature-tag"
              >
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 数据统计 -->
    <section class="stats-section">
      <div class="section-container">
        <div class="stats-grid">
          <div 
            v-for="(stat, index) in stats" 
            :key="stat.id"
            class="stat-card animate-fade-in-up"
            :class="`delay-${index * 100}`"
          >
            <div class="stat-icon">
              <Icon :name="stat.icon" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stat.number }}</div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-description">{{ stat.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 用户评价 -->
    <section class="testimonials-section">
      <div class="section-container">
        <div class="section-header animate-fade-in-up">
          <h2 class="section-title">用户好评如潮</h2>
          <p class="section-subtitle">
            来自真实用户的使用体验分享
          </p>
        </div>
        
        <div class="testimonials-grid">
          <div 
            v-for="(testimonial, index) in testimonials" 
            :key="testimonial.id"
            class="testimonial-card animate-fade-in-up"
            :class="`delay-${index * 200}`"
          >
            <div class="testimonial-content">
              <div class="quote-icon">
                <Icon name="quote" />
              </div>
              <p class="testimonial-text">{{ testimonial.content }}</p>
            </div>
            <div class="testimonial-author">
              <img :src="testimonial.avatar" :alt="testimonial.name" />
              <div class="author-info">
                <div class="author-name">{{ testimonial.name }}</div>
                <div class="author-title">{{ testimonial.title }}</div>
              </div>
              <div class="testimonial-rating">
                <Icon 
                  v-for="i in 5" 
                  :key="i" 
                  name="star" 
                  :class="{ filled: i <= testimonial.rating }"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="cta-container">
        <div class="cta-content animate-fade-in-up">
          <h2 class="cta-title">准备开始您的成功之旅？</h2>
          <p class="cta-subtitle">
            加入数万名用户的行列，体验智能社群营销的强大力量
          </p>
          <div class="cta-actions">
            <NuxtLink to="/register" class="modern-btn modern-btn-primary cta-btn">
              免费注册
            </NuxtLink>
            <NuxtLink to="/contact" class="modern-btn modern-btn-secondary cta-btn">
              联系销售
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 页面元数据
useHead({
  title: '晨鑫流量变现系统 - 智能社群营销平台',
  meta: [
    { name: 'description', content: '新一代智能社群营销与多级分销平台，助力流量变现，打造专业社群商业生态' }
  ]
});

// 功能特色数据
const features = ref([
  {
    id: 1,
    icon: 'shield',
    title: '智能防红系统',
    description: '自动检测域名状态，智能切换可用域名，确保链接永不失效，保障业务连续性。',
    tags: ['自动检测', '智能切换', '高可用']
  },
  {
    id: 2,
    icon: 'users',
    title: '多级分销体系',
    description: '支持无限级分销，自动计算佣金，实时结算分成，构建强大的分销网络。',
    tags: ['无限级', '自动结算', '实时统计']
  },
  {
    id: 3,
    icon: 'chart',
    title: '数据分析中心',
    description: '详细的数据统计和分析，帮助您了解业务状况，做出明智的决策。',
    tags: ['实时数据', '可视化', '智能分析']
  },
  {
    id: 4,
    icon: 'mobile',
    title: '移动端优化',
    description: '完美适配移动设备，随时随地管理您的业务，把握每一个商机。',
    tags: ['响应式', '移动优先', '跨平台']
  },
  {
    id: 5,
    icon: 'lock',
    title: '安全可靠',
    description: '企业级安全保障，数据加密传输，多重安全防护，保护您的业务安全。',
    tags: ['数据加密', '安全防护', '隐私保护']
  },
  {
    id: 6,
    icon: 'support',
    title: '专业支持',
    description: '7x24小时技术支持，专业团队为您提供全方位的服务保障。',
    tags: ['24小时', '专业团队', '快速响应']
  }
]);

// 统计数据
const stats = ref([
  {
    id: 1,
    icon: 'users',
    number: '50,000+',
    label: '注册用户',
    description: '遍布全国的活跃用户群体'
  },
  {
    id: 2,
    icon: 'chart',
    number: '1,000,000+',
    label: '成功订单',
    description: '累计处理的订单数量'
  },
  {
    id: 3,
    icon: 'money',
    number: '¥5,000万+',
    label: '交易金额',
    description: '平台累计交易总额'
  },
  {
    id: 4,
    icon: 'clock',
    number: '99.9%',
    label: '系统稳定性',
    description: '7x24小时稳定运行'
  }
]);

// 用户评价
const testimonials = ref([
  {
    id: 1,
    content: '使用晨鑫流量变现系统后，我的业务效率提升了300%，智能防红系统让我再也不用担心链接失效的问题。',
    name: '张经理',
    title: '电商运营总监',
    avatar: '/avatars/user1.jpg',
    rating: 5
  },
  {
    id: 2,
    content: '多级分销系统非常强大，帮助我快速建立了庞大的分销网络，收入翻了好几倍。',
    name: '李总',
    title: '创业者',
    avatar: '/avatars/user2.jpg',
    rating: 5
  },
  {
    id: 3,
    content: '数据分析功能很实用，让我能够清楚地了解业务状况，做出更好的决策。',
    name: '王女士',
    title: '市场营销专家',
    avatar: '/avatars/user3.jpg',
    rating: 5
  }
]);
</script>

<style scoped>
.home-page {
  min-height: 100vh;
}

/* 英雄区域 */
.hero-section {
  padding: 6rem 0 4rem;
  position: relative;
  overflow: hidden;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-content {
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  color: white;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2.5rem;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
}

.hero-btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.hero-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

/* 英雄视觉效果 */
.hero-visual {
  position: relative;
  height: 500px;
}

.visual-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  transition: all var(--transition-normal);
}

.main-card {
  width: 320px;
  height: 240px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-title {
  font-weight: 600;
  color: var(--gray-800);
}

.card-status {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.card-status.online {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.metric-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.metric {
  flex: 1;
}

.metric-label {
  font-size: 0.75rem;
  color: var(--gray-600);
  margin-bottom: 0.25rem;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: 0.25rem;
}

.metric-trend {
  font-size: 0.75rem;
  font-weight: 500;
}

.metric-trend.up {
  color: var(--success-color);
}

.chart-placeholder {
  display: flex;
  align-items: end;
  gap: 0.5rem;
  height: 60px;
}

.chart-bar {
  flex: 1;
  background: var(--bg-gradient-primary);
  border-radius: 2px;
  animation: chartGrow 1s ease-out;
}

@keyframes chartGrow {
  from { height: 0; }
}

.floating-card-1 {
  width: 120px;
  height: 60px;
  top: 20%;
  right: 10%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  animation: float 3s ease-in-out infinite;
}

.floating-card-2 {
  width: 120px;
  height: 60px;
  bottom: 20%;
  left: 10%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  animation: float 3s ease-in-out infinite 1.5s;
}

/* 功能特色区域 */
.features-section {
  padding: 6rem 0;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: var(--bg-gradient-primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: white;
  font-size: 1.5rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.feature-tag {
  padding: 0.25rem 0.75rem;
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

/* 统计区域 */
.stats-section {
  padding: 4rem 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: 2rem;
  text-align: center;
  transition: all var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: white;
  font-size: 1.25rem;
}

.stat-content .stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.stat-content .stat-label {
  font-size: 1.125rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.5rem;
}

.stat-content .stat-description {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

/* 用户评价区域 */
.testimonials-section {
  padding: 6rem 0;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.testimonial-content {
  margin-bottom: 1.5rem;
  position: relative;
}

.quote-icon {
  position: absolute;
  top: -10px;
  left: -10px;
  width: 30px;
  height: 30px;
  background: var(--bg-gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
}

.testimonial-text {
  color: var(--gray-700);
  line-height: 1.6;
  font-style: italic;
  padding-left: 1rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.testimonial-author img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info {
  flex: 1;
}

.author-name {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 0.25rem;
}

.author-title {
  font-size: 0.875rem;
  color: var(--gray-600);
}

.testimonial-rating {
  display: flex;
  gap: 0.25rem;
  color: var(--gray-300);
}

.testimonial-rating .filled {
  color: #fbbf24;
}

/* CTA区域 */
.cta-section {
  padding: 6rem 0;
  background: var(--bg-gradient-primary);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/patterns/dots.svg') repeat;
  opacity: 0.1;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1.5rem;
  text-align: center;
  position: relative;
  z-index: 2;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
}

.cta-subtitle {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.cta-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.cta-btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-stats {
    justify-content: center;
  }
  
  .hero-visual {
    height: 300px;
  }
  
  .main-card {
    width: 280px;
    height: 200px;
  }
  
  .floating-card-1,
  .floating-card-2 {
    display: none;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .testimonials-grid {
    grid-template-columns: 1fr;
  }
  
  .cta-title {
    font-size: 2rem;
  }
  
  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-card,
  .testimonial-card {
    padding: 1.5rem;
  }
}
</style>