const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Login-zWuNZhsZ.js","assets/vue-vendor-BcnDv-68.js","assets/element-plus-C2UshkXo.js","assets/utils-SdQ7DxjY.js","assets/echarts-D6CUuNS9.js","assets/Login-DWDk5P_-.css","assets/ModernLayout-Bj7laFRI.js","assets/el-tooltip-l0sNRNKZ.js","assets/ModernLayout-B0_tV39L.css","assets/base-pYMXRPpM.css","assets/el-button-CDqfIFiK.css","assets/el-button-group-ePhtJS9H.css","assets/el-popper-Ba7_ER_z.css","assets/el-dropdown-item-h8USmNax.css","assets/el-avatar-BmRr_O8d.css","assets/el-badge-BWN_0xb6.css","assets/el-breadcrumb-item-DNDT2TZX.css","assets/el-menu-item-DeEUhTwP.css","assets/el-input-Cz--kClu.css","assets/ModernDashboard-D0fysfIU.js","assets/navigation-DbqezFjv.js","assets/ModernDashboard-3QC7BX8-.css","assets/el-overlay-CcMgTIy5.css","assets/el-progress-Dw9yTa91.css","assets/el-tag-DljBBxJR.css","assets/el-select-CvzM3W2w.css","assets/el-card-fwQOLwdi.css","assets/el-drawer-DuQr35wS.css","assets/el-alert-G57rL0jl.css","assets/el-col-Ds2mGN2S.css","assets/el-radio-group-BzMpJalG.css","assets/el-radio-button-CSkroacn.css","assets/Analytics-BGCbPrsr.js","assets/mapLoader-Bhh47pun.js","assets/Analytics-DvHXDE2A.css","assets/el-loading-DLSpKYce.css","assets/el-table-column-CKoPG0Y8.css","assets/el-checkbox-DIj50LEB.css","assets/UserList-BVxi7zRt.js","assets/UserList-BZlCcJXc.css","assets/el-pagination-BNQcHhjS.css","assets/el-form-item-DCFsf57O.css","assets/el-radio-BuDgLcOG.css","assets/UserAnalytics-q_Gb08qQ.js","assets/UserAnalytics-CKYgj_a8.css","assets/el-date-picker-Db-ufUiu.css","assets/Profile-xO7mppEr.js","assets/Profile-CQdtj8zL.css","assets/el-divider-BUtF_RGI.css","assets/el-switch-B5lTGWdM.css","assets/el-upload-q8uObtwj.css","assets/GroupList--IHYG03d.js","assets/community-CUcF7leP.js","assets/chunk-KZPPZA2C-C8HwxGb3.js","assets/format-3eU4VJ9V.js","assets/anti-block-CJ1NNk3N.js","assets/index-D4AyIzGN.js","assets/GroupList-BPTC0QOL.css","assets/el-input-number-DUUPPWGj.css","assets/el-timeline-item-BvbJTz1y.css","assets/TemplateManagement-DWbbhB3d.js","assets/TemplateManagement-CdogPOdC.css","assets/el-descriptions-item-o9ObloqJ.css","assets/el-text-3XkjT9nK.css","assets/AgentList-MEP9TWru.js","assets/StatCard-CLi1H67J.js","assets/StatCard-Bmn-oCkK.css","assets/agent-CxsSQ9z5.js","assets/AgentList-VRT6-da3.css","assets/AgentHierarchy-35UQK5OK.js","assets/AgentHierarchy-D6JVpGAC.css","assets/el-tab-pane-DTGC0oAx.css","assets/el-tree-C2sTlbKd.css","assets/AgentCommission-mC_GLKny.js","assets/AgentCommission-D29KwZhP.css","assets/el-collapse-item-BqS7tZDP.css","assets/el-link-B58a4a3I.css","assets/AgentPerformance-CJB6TS6g.js","assets/LineChart-tPEDyfYE.js","assets/LineChart-Dhlb-Gbi.css","assets/AgentPerformance-bABT0cDp.css","assets/el-rate-CTwMIh-J.css","assets/FinanceDashboard-B2zIRit8.js","assets/finance-CGlhYFvA.js","assets/FinanceDashboard-OHLUE5V-.css","assets/TransactionList-BjjGagcH.js","assets/index-B0-VWyD5.js","assets/index-B6h2nhp4.css","assets/TransactionList-zCWJb7WO.css","assets/CommissionLog-BZhJKCDH.js","assets/export-C8s0bFWZ.js","assets/CommissionLog-A_CAI-zn.css","assets/WithdrawManage-rH3pHA11.js","assets/WithdrawManage-qyJ7mHcW.css","assets/PaymentSettings-CDSSD3-P.js","assets/PageLayout-DKvOdnm6.js","assets/PageLayout-00fxvEoL.css","assets/payment-CejeSQd2.js","assets/PaymentSettings-CjcomfVS.css","assets/el-checkbox-group-D_6SYB2i.css","assets/PaymentChannelManagement-By3wnWCu.js","assets/PaymentChannelManagement-DJrO5TJH.css","assets/PaymentOrders-vx0LUNGp.js","assets/PaymentOrders-9oc6YtVj.css","assets/PaymentLogs-qgwwcSMt.js","assets/PaymentLogs-BYdRhrKp.css","assets/OrderList-BUADqphy.js","assets/order-DI2E_fWj.js","assets/OrderList-BhsKeZW_.css","assets/DistributorList-C-VnCZwb.js","assets/DistributorList-DEPN35mX.css","assets/LinkManagement-BDOAwA3l.js","assets/LinkManagement-B7xKJTRo.css","assets/Dashboard-DYNmwGSy.js","assets/Dashboard-DiWRcbvR.css","assets/DomainList-fqLt-fk3.js","assets/DomainList-CQg-Uwpd.css","assets/ShortLinkList-j3BHHMvK.js","assets/ShortLinkList-DHcwUu9Q.css","assets/Analytics-Cn7P8lRo.js","assets/Analytics-BbctNh0q.css","assets/EnhancedDashboard-CQGp-Mzg.js","assets/EnhancedDashboard-BLZdQjaN.css","assets/RoleManagement-CrKCOlgF.js","assets/RoleManagement-DQUl-b_V.css","assets/PermissionManagement-DVrQl3FG.js","assets/PermissionManagement-C5-P6vKX.css","assets/Settings-CkhPAfwW.js","assets/system-Co5QGav0.js","assets/Settings-DPRr5-O2.css","assets/el-time-picker-B4D4rMOz.css","assets/el-statistic-Bt0nnCWz.css","assets/Monitor-BeUGGs6I.js","assets/Monitor-LPctsDhL.css","assets/OperationLogs-DA0lTvMh.js","assets/OperationLogs-CsNsfwsH.css","assets/Notifications-D3Q4abvm.js","assets/Notifications-QfWE5Mkg.css","assets/DataExport-DYd0SMBT.js","assets/DataExport--chMyI2f.css","assets/FileManagement-vjE7D4gD.js","assets/FileManagement-Bpi1dZDP.css","assets/FunctionTest-CrP4ViBD.js","assets/FunctionTest-B9b8F9CV.css","assets/NotificationTest-DFONIb1p.js","assets/NotificationTest-DRgOBv4B.css","assets/UserGuide-JLs9djEx.js","assets/UserGuide-D-Uoj1tE.css","assets/NavigationPerformanceTest-34r79UU5.js","assets/NavigationPerformanceTest-OSXy3Khr.css","assets/ErrorPage-5JCWG06-.js","assets/ErrorPage-wvIK7dNr.css","assets/DataScreen-tsBSLhGl.js","assets/DataScreen-CsudltdZ.css","assets/DataScreenFullscreen-CFgCB-qm.js","assets/DataScreenFullscreen-DmC939XF.css","assets/ScreenTestSuite-DsLU9l3D.js","assets/ScreenTestSuite-CcdigOTp.css"])))=>i.map(i=>d[i]);
import{a8 as e,l as t,m as n,G as a,ao as o,ap as r,an as s,r as i,c as m,ae as c,aq as l}from"./vue-vendor-BcnDv-68.js";import{b as u,a as d}from"./utils-SdQ7DxjY.js";import{X as p,ag as h,cA as _,cB as g}from"./element-plus-C2UshkXo.js";import{b as f,a as C,d as E,s as y,t as v,A,u as w,o as T,n as P,m as D,w as b,c as L,v as S,j as O,p as k,x as j,f as R,h as I,q as M,g as V,z as q,y as N,k as x,l as B,a2 as U,O as G,P as $,J as F,Y as z,Z as H,_ as J,a4 as Q,F as Z,K as W,C as K,B as X,V as Y,X as ee,W as te,U as ne,T as ae,S as oe,I as re,D as se,E as ie,G as me,R as ce,Q as le,M as ue,N as de,a3 as pe,$ as he,a0 as _e,a1 as ge}from"./echarts-D6CUuNS9.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const fe=(e,t)=>{const n=e.__vccOpts||e;for(const[a,o]of t)n[a]=o;return n},Ce={id:"app"};const Ee=fe({__name:"App",setup(e,{expose:t}){t();const n={};return Object.defineProperty(n,"__isScriptSetup",{enumerable:!1,value:!0}),n}},[["render",function(o,r,s,i,m,c){const l=e("router-view");return n(),t("div",Ce,[a(l)])}],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/App.vue"]]),ye={},ve=function(e,t,n){let a=Promise.resolve();if(t&&t.length>0){document.getElementsByTagName("link");const e=document.querySelector("meta[property=csp-nonce]"),n=e?.nonce||e?.getAttribute("nonce");a=Promise.allSettled(t.map(e=>{if((e=function(e){return"/admin/"+e}(e))in ye)return;ye[e]=!0;const t=e.endsWith(".css"),a=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${a}`))return;const o=document.createElement("link");return o.rel=t?"stylesheet":"modulepreload",t||(o.as="script"),o.crossOrigin="",o.href=e,n&&o.setAttribute("nonce",n),document.head.appendChild(o),t?new Promise((t,n)=>{o.addEventListener("load",t),o.addEventListener("error",()=>n(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}function o(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return a.then(t=>{for(const e of t||[])"rejected"===e.status&&o(e.reason);return e().catch(o)})},Ae="Admin-Token";function we(){return u.get(Ae)}function Te(e){return u.set(Ae,e,{expires:7})}function Pe(){u.remove(Ae),u.remove("Admin-Refresh-Token")}const De=[...[{path:"/",redirect:"/login"},{path:"/login",name:"Login",component:()=>ve(()=>import("./Login-zWuNZhsZ.js"),__vite__mapDeps([0,1,2,3,4,5])),meta:{title:"登录",hideInMenu:!0}},{path:"/admin",component:()=>ve(()=>import("./ModernLayout-Bj7laFRI.js"),__vite__mapDeps([6,1,2,7,3,4,8,9,10,11,12,13,14,15,16,17,18])),redirect:"/admin/dashboard",meta:{requiresAuth:!0},children:[{path:"dashboard",name:"Dashboard",component:()=>ve(()=>import("./ModernDashboard-D0fysfIU.js"),__vite__mapDeps([19,1,2,3,20,7,4,21,9,22,23,15,11,14,24,25,12,10,26,27,28,29,13,18,30,31])),meta:{title:"仪表板",icon:"TrendCharts",requiresAuth:!0,group:"核心功能"}},{path:"analytics",name:"DashboardAnalytics",component:()=>ve(()=>import("./Analytics-BGCbPrsr.js"),__vite__mapDeps([32,7,1,2,4,33,3,34,9,35,36,37,12,24,10,25,30,31])),meta:{title:"数据分析",icon:"DataLine",requiresAuth:!0,group:"核心功能"}},{path:"users",name:"UserManagement",redirect:"/admin/users/list",meta:{title:"用户管理",icon:"User",requiresAuth:!0,group:"用户管理"},children:[{path:"list",name:"UserList",component:()=>ve(()=>import("./UserList-BVxi7zRt.js"),__vite__mapDeps([38,7,1,2,3,4,39,9,35,40,24,25,12,18,36,37,14,26,10,22,41,30,42])),meta:{title:"用户列表",icon:"List",requiresAuth:!0}},{path:"analytics",name:"UserAnalytics",component:()=>ve(()=>import("./UserAnalytics-q_Gb08qQ.js"),__vite__mapDeps([43,1,2,4,3,44,9,22,41,24,25,12,18,10,45,30,31,29,26])),meta:{title:"用户分析",icon:"TrendCharts",requiresAuth:!0}},{path:"profile",name:"UserProfile",component:()=>ve(()=>import("./Profile-xO7mppEr.js"),__vite__mapDeps([46,7,1,2,3,4,47,9,22,36,37,12,24,48,49,29,28,26,41,10,45,18,30,42,15,50,23])),meta:{title:"个人资料",icon:"Avatar",requiresAuth:!0}}]},{path:"community",name:"CommunityManagement",redirect:"/admin/community/groups",meta:{title:"社群管理",icon:"ChatDotRound",requiresAuth:!0,group:"社群功能"},children:[{path:"groups",name:"GroupManagement",component:()=>ve(()=>import("./GroupList--IHYG03d.js"),__vite__mapDeps([51,7,1,2,52,53,54,55,56,3,4,57,9,35,40,24,25,12,18,29,36,37,10,11,13,23,14,26,22,41,49,30,42,58,28,27,59,48,31])),meta:{title:"群组管理",icon:"ChatDotRound",requiresAuth:!0}},{path:"templates",name:"TemplateManagement",component:()=>ve(()=>import("./TemplateManagement-DWbbhB3d.js"),__vite__mapDeps([60,7,1,2,54,52,53,3,4,61,9,35,62,22,18,58,48,50,23,40,24,25,12,29,36,37,63,49,30,31,26,41,10])),meta:{title:"模板管理",icon:"Document",requiresAuth:!0}}]},{path:"agents",name:"AgentManagement",redirect:"/admin/agents/list",meta:{title:"代理商管理",icon:"Avatar",requiresAuth:!0,roles:["admin"],group:"业务管理"},children:[{path:"list",name:"AgentList",component:()=>ve(()=>import("./AgentList-MEP9TWru.js"),__vite__mapDeps([64,7,1,2,65,66,9,67,3,4,68,35,22,45,18,12,10,58,30,42,40,24,25,36,37,11,13,14,26,41])),meta:{title:"代理商列表",icon:"List",requiresAuth:!0,roles:["admin"]}},{path:"hierarchy",name:"AgentHierarchy",component:()=>ve(()=>import("./AgentHierarchy-35UQK5OK.js"),__vite__mapDeps([69,7,1,2,3,4,70,9,35,71,22,41,18,12,24,37,42,25,26,40,36,15,72,63,10,11,13,14])),meta:{title:"层级结构",icon:"Grid",requiresAuth:!0,roles:["admin"]}},{path:"commission",name:"AgentCommission",component:()=>ve(()=>import("./AgentCommission-mC_GLKny.js"),__vite__mapDeps([73,7,1,2,3,4,74,9,35,22,75,28,29,71,40,24,25,12,18,36,37,14,76,26,41,45,10])),meta:{title:"佣金管理",icon:"Money",requiresAuth:!0,roles:["admin"]}},{path:"performance",name:"AgentPerformance",component:()=>ve(()=>import("./AgentPerformance-CJB6TS6g.js"),__vite__mapDeps([77,7,1,2,78,79,67,3,4,80,9,35,22,75,28,40,24,25,12,18,36,37,81,23,14,11,29,30,31,26,41,10])),meta:{title:"业绩统计",icon:"TrendCharts",requiresAuth:!0,roles:["admin"]}}]},{path:"finance",name:"FinanceManagement",redirect:"/admin/finance/dashboard",meta:{title:"财务管理",icon:"Money",requiresAuth:!0,roles:["admin"],group:"财务管理"},children:[{path:"dashboard",name:"FinanceDashboard",component:()=>ve(()=>import("./FinanceDashboard-B2zIRit8.js"),__vite__mapDeps([82,7,1,2,78,79,4,83,53,54,3,84,9,22,75,28,24,36,37,12,29,26,11,10])),meta:{title:"财务概览",icon:"DataBoard",requiresAuth:!0,roles:["admin"]}},{path:"transactions",name:"TransactionList",component:()=>ve(()=>import("./TransactionList-BjjGagcH.js"),__vite__mapDeps([85,7,1,2,83,53,86,87,9,40,24,25,12,18,3,4,88,35,22,62,36,37,29,26,41,10,45])),meta:{title:"交易记录",icon:"CreditCard",requiresAuth:!0,roles:["admin"]}},{path:"commission",name:"CommissionLog",component:()=>ve(()=>import("./CommissionLog-BZhJKCDH.js"),__vite__mapDeps([89,7,1,2,83,53,90,56,3,54,86,87,9,40,24,25,12,18,4,91,35,22,58,36,37,14,11,28,26,41,45,10])),meta:{title:"佣金日志",icon:"Coin",requiresAuth:!0,roles:["admin"]}},{path:"withdraw",name:"WithdrawManage",component:()=>ve(()=>import("./WithdrawManage-rH3pHA11.js"),__vite__mapDeps([92,7,1,2,90,56,3,54,4,93,9,35,28,22,62,40,24,25,12,18,36,37,41,10,45,58,29,26])),meta:{title:"提现管理",icon:"Upload",requiresAuth:!0,roles:["admin"]}}]},{path:"payment",name:"PaymentManagement",redirect:"/admin/payment/settings",meta:{title:"支付管理",icon:"CreditCard",requiresAuth:!0,roles:["admin"],group:"支付系统"},children:[{path:"settings",name:"PaymentSettings",component:()=>ve(()=>import("./PaymentSettings-CDSSD3-P.js"),__vite__mapDeps([94,1,2,95,96,9,10,16,97,3,4,98,29,24,26,71,18,58,99,37,50,23,41,25,12,49])),meta:{title:"支付设置",icon:"Setting",requiresAuth:!0,roles:["admin"]}},{path:"channels",name:"PaymentChannelManagement",component:()=>ve(()=>import("./PaymentChannelManagement-By3wnWCu.js"),__vite__mapDeps([100,7,1,2,97,3,4,101,9,35,71,22,41,45,18,12,10,99,37,24,25,36,49,29,26])),meta:{title:"支付渠道",icon:"Connection",requiresAuth:!0,roles:["admin"]}},{path:"orders",name:"PaymentOrders",component:()=>ve(()=>import("./PaymentOrders-vx0LUNGp.js"),__vite__mapDeps([102,7,1,2,95,96,9,10,16,97,3,4,103,35,40,24,25,12,18,36,37,11,13,26,45,29,22,62,41,58])),meta:{title:"支付订单",icon:"Tickets",requiresAuth:!0,roles:["admin"]}},{path:"logs",name:"PaymentLogs",component:()=>ve(()=>import("./PaymentLogs-qgwwcSMt.js"),__vite__mapDeps([104,7,1,2,95,96,9,10,16,3,4,105,22,28,18,62,40,24,25,12,36,37,41,45,29,26])),meta:{title:"支付日志",icon:"Document",requiresAuth:!0,roles:["admin"]}}]},{path:"orders",name:"OrderManagement",component:()=>ve(()=>import("./OrderList-BUADqphy.js"),__vite__mapDeps([106,7,1,2,65,66,9,107,56,3,54,4,108,35,40,24,25,12,18,36,37,10,11,13,14,26,41,45,22,48,62,28,30,42,58,59])),meta:{title:"订单管理",icon:"ShoppingCart",requiresAuth:!0,group:"业务管理"}},{path:"promotion",name:"PromotionManagement",redirect:"/admin/promotion/distributors",meta:{title:"分销推广",icon:"Share",requiresAuth:!0,roles:["admin"],group:"推广营销"},children:[{path:"distributors",name:"DistributorManagement",component:()=>ve(()=>import("./DistributorList-C-VnCZwb.js"),__vite__mapDeps([109,7,1,2,56,3,86,87,9,40,24,25,12,18,4,110,35,22,36,37,49,14,11,28,26,41,10])),meta:{title:"分销商管理",icon:"User",requiresAuth:!0,roles:["admin"]}},{path:"links",name:"PromotionLinks",component:()=>ve(()=>import("./LinkManagement-BDOAwA3l.js"),__vite__mapDeps([111,7,1,2,65,66,9,54,3,4,112,35,26,40,24,25,12,18,36,37,10,11,13,76,29,22,41,30,42,58,45,27,31,28,99])),meta:{title:"推广链接",icon:"Link",requiresAuth:!0,roles:["admin"]}}]},{path:"anti-block",name:"AntiBlockSystem",redirect:"/admin/anti-block/dashboard",meta:{title:"防红系统",icon:"Lock",requiresAuth:!0,roles:["admin"],group:"安全防护"},children:[{path:"dashboard",name:"AntiBlockDashboard",component:()=>ve(()=>import("./Dashboard-DYNmwGSy.js"),__vite__mapDeps([113,7,1,2,55,56,3,4,114,9,28,22,41,18,58,12,24,25,36,37,23,26,29,10])),meta:{title:"防红概览",icon:"DataBoard",requiresAuth:!0,roles:["admin"]}},{path:"domains",name:"DomainManagement",component:()=>ve(()=>import("./DomainList-fqLt-fk3.js"),__vite__mapDeps([115,7,1,2,55,56,3,86,87,9,40,24,25,12,18,4,116,35,22,58,30,42,36,37,29,26,41,10])),meta:{title:"域名管理",icon:"Connection",requiresAuth:!0,roles:["admin"]}},{path:"links",name:"ShortLinkManagement",component:()=>ve(()=>import("./ShortLinkList-j3BHHMvK.js"),__vite__mapDeps([117,7,1,2,55,56,3,4,118,9,35,28,71,22,40,24,25,12,18,36,37,41,45,10,26])),meta:{title:"短链管理",icon:"Link",requiresAuth:!0,roles:["admin"]}},{path:"analytics",name:"AntiBlockAnalytics",component:()=>ve(()=>import("./Analytics-Cn7P8lRo.js"),__vite__mapDeps([119,7,55,56,3,2,1,4,120,9,35,41,18,58,49,48,22,36,37,12,24,23,26,30,31,29,10,45])),meta:{title:"防红分析",icon:"TrendCharts",requiresAuth:!0,roles:["admin"]}},{path:"enhanced",name:"AntiBlockEnhanced",component:()=>ve(()=>import("./EnhancedDashboard-CQGp-Mzg.js"),__vite__mapDeps([121,7,1,2,4,56,3,122,9,35,62,22,41,18,58,26,40,24,25,12,36,37,10,11,13,23,29])),meta:{title:"增强防护",icon:"Star",requiresAuth:!0,roles:["admin"]}}]},{path:"permissions",name:"PermissionSystem",redirect:"/admin/permissions/roles",meta:{title:"权限管理",icon:"Lock",requiresAuth:!0,roles:["admin"],group:"系统管理"},children:[{path:"roles",name:"RoleManagement",component:()=>ve(()=>import("./RoleManagement-CrKCOlgF.js"),__vite__mapDeps([123,7,1,2,3,4,124,9,35,40,24,25,12,18,36,37,10,11,13,26,22,41,30,42,72,63])),meta:{title:"角色管理",icon:"UserFilled",requiresAuth:!0,roles:["admin"]}},{path:"permissions",name:"PermissionManagement",component:()=>ve(()=>import("./PermissionManagement-DVrQl3FG.js"),__vite__mapDeps([125,7,1,2,95,96,9,10,16,3,4,126,35,26,40,24,25,12,18,36,37])),meta:{title:"权限配置",icon:"Key",requiresAuth:!0,roles:["admin"]}}]},{path:"system",name:"SystemManagement",redirect:"/admin/system/settings",meta:{title:"系统管理",icon:"Setting",requiresAuth:!0,roles:["admin"],group:"系统管理"},children:[{path:"settings",name:"SystemSettings",component:()=>ve(()=>import("./Settings-CkhPAfwW.js"),__vite__mapDeps([127,7,1,2,128,54,3,4,129,9,24,25,12,30,42,11,22,71,130,18,58,131,36,37,99,29,28,49,48,76,41,10,26,17,50,23,35,40,45,13])),meta:{title:"系统设置",icon:"Setting",requiresAuth:!0,roles:["admin"]}},{path:"monitor",name:"SystemMonitor",component:()=>ve(()=>import("./Monitor-BeUGGs6I.js"),__vite__mapDeps([132,1,2,4,128,3,133,9,40,24,25,12,18,10,29,23,26])),meta:{title:"系统监控",icon:"Monitor",requiresAuth:!0,roles:["admin"]}},{path:"logs",name:"OperationLogs",component:()=>ve(()=>import("./OperationLogs-DA0lTvMh.js"),__vite__mapDeps([134,7,1,2,4,3,135,9,35,18,58,30,42,22,28,62,40,24,25,12,36,37,41,10,45,29,26,71])),meta:{title:"操作日志",icon:"Document",requiresAuth:!0,roles:["admin"]}},{path:"notifications",name:"NotificationManagement",component:()=>ve(()=>import("./Notifications-D3Q4abvm.js"),__vite__mapDeps([136,7,1,2,4,3,137,9,22,41,45,18,12,10,99,37,30,42,40,24,25,36,29,26])),meta:{title:"通知管理",icon:"Bell",requiresAuth:!0,roles:["admin"]}},{path:"data-export",name:"DataExport",component:()=>ve(()=>import("./DataExport-DYd0SMBT.js"),__vite__mapDeps([138,7,1,2,90,56,3,4,139,9,22,49,130,18,12,40,24,25,36,37,23,41,99,58,45,10,30,42,26,29])),meta:{title:"数据导出",icon:"Download",requiresAuth:!0,roles:["admin"]}},{path:"file-management",name:"FileManagement",component:()=>ve(()=>import("./FileManagement-vjE7D4gD.js"),__vite__mapDeps([140,7,1,2,95,96,9,10,16,3,4,141,35,26,40,24,25,12,18,36,37])),meta:{title:"文件管理",icon:"Folder",requiresAuth:!0,roles:["admin"]}},{path:"function-test",name:"FunctionTest",component:()=>ve(()=>import("./FunctionTest-CrP4ViBD.js"),__vite__mapDeps([142,1,2,90,56,3,107,4,143,9,29,24,25,12,26,10,28])),meta:{title:"功能测试",icon:"Tools",requiresAuth:!0,roles:["admin"]}},{path:"notification-test",name:"NotificationTest",component:()=>ve(()=>import("./NotificationTest-DFONIb1p.js"),__vite__mapDeps([144,1,2,3,4,145,9,22,10,24,26,29,131])),meta:{title:"通知管理测试",icon:"Bell",requiresAuth:!0,roles:["admin"]}},{path:"user-guide",name:"UserGuide",component:()=>ve(()=>import("./UserGuide-JLs9djEx.js"),__vite__mapDeps([146,1,2,3,4,147,9,10,75,28,24,26,29])),meta:{title:"使用指南",icon:"QuestionFilled",requiresAuth:!0}}]}]},{path:"/dashboard",redirect:"/admin/dashboard"},{path:"/community",redirect:"/admin/community/groups"},{path:"/community/groups",redirect:"/admin/community/groups"},{path:"/community/templates",redirect:"/admin/community/templates"},{path:"/users",redirect:"/admin/users/list"},{path:"/users/analytics",redirect:"/admin/users/analytics"},{path:"/agent",redirect:"/admin/agents/list"},{path:"/finance",redirect:"/admin/finance/dashboard"},{path:"/payment",redirect:"/admin/payment/settings"},{path:"/orders",redirect:"/admin/orders"},{path:"/distribution",redirect:"/admin/promotion/distributors"},{path:"/promotion",redirect:"/admin/promotion/links"},{path:"/anti-block",redirect:"/admin/anti-block/dashboard"},{path:"/permission",redirect:"/admin/permissions/roles"},{path:"/system",redirect:"/admin/system/settings"},{path:"/navigation-performance-test",name:"NavigationPerformanceTest",component:()=>ve(()=>import("./NavigationPerformanceTest-34r79UU5.js"),__vite__mapDeps([148,7,1,2,3,4,149,9,26,29,36,37,12,24,10])),meta:{title:"导航性能测试",hideInMenu:!0}},{path:"/:pathMatch(.*)*",name:"NotFound",component:()=>ve(()=>import("./ErrorPage-5JCWG06-.js"),__vite__mapDeps([150,1,2,3,4,151,9,10,75])),meta:{title:"页面未找到",hideInMenu:!0}}],...[{path:"/data-screen",name:"DataScreen",component:()=>ve(()=>import("./DataScreen-tsBSLhGl.js"),__vite__mapDeps([152,1,2,4,33,56,3,153,9])),meta:{title:"数据大屏",icon:"Monitor",requiresAuth:!1,fullscreen:!0}},{path:"/data-screen/ultra",name:"UltraDataScreen",component:()=>ve(()=>import("./DataScreen-tsBSLhGl.js"),__vite__mapDeps([152,1,2,4,33,56,3,153,9])),meta:{title:"Ultra 数据大屏",fullscreen:!0,requiresAuth:!0}},{path:"/data-screen/enhanced",name:"EnhancedDataScreen",component:()=>ve(()=>import("./DataScreen-tsBSLhGl.js"),__vite__mapDeps([152,1,2,4,33,56,3,153,9])),meta:{title:"增强数据大屏",fullscreen:!0,requiresAuth:!0}},{path:"/data-screen/classic",name:"ClassicDataScreen",component:()=>ve(()=>import("./DataScreen-tsBSLhGl.js"),__vite__mapDeps([152,1,2,4,33,56,3,153,9])),meta:{title:"经典数据大屏",fullscreen:!0,requiresAuth:!1}},{path:"/data-screen/simple",name:"SimpleDataScreen",component:()=>ve(()=>import("./DataScreen-tsBSLhGl.js"),__vite__mapDeps([152,1,2,4,33,56,3,153,9])),meta:{title:"简约数据大屏",fullscreen:!0,requiresAuth:!0}},{path:"/data-screen/fullscreen",name:"DataScreenFullscreen",component:()=>ve(()=>import("./DataScreenFullscreen-CFgCB-qm.js"),__vite__mapDeps([154,1,2,3,4,155,9,29,24,25,12,10])),meta:{title:"全屏数据大屏",fullscreen:!0,requiresAuth:!0}},{path:"/data-screen/optimized",name:"OptimizedDataScreen",component:()=>ve(()=>import("./DataScreen-tsBSLhGl.js"),__vite__mapDeps([152,1,2,4,33,56,3,153,9])),meta:{title:"优化数据大屏",fullscreen:!0,requiresAuth:!0}},{path:"/data-screen/test-suite",name:"ScreenTestSuite",component:()=>ve(()=>import("./ScreenTestSuite-DsLU9l3D.js"),__vite__mapDeps([156,1,2,3,4,157])),meta:{title:"显示测试套件",requiresAuth:!0,roles:["admin"]}}]],be=o({history:r(),routes:De,scrollBehavior:(e,t,n)=>n||{top:0}}),Le=new Set,Se=async e=>{if(!Le.has(e))try{const t=De.find(t=>t.path===e);t?.component&&(await t.component(),Le.add(e))}catch(t){}},Oe=["/admin/dashboard","/admin/community/groups","/admin/users/list","/admin/finance/dashboard"];setTimeout(()=>{Oe.forEach(e=>Se(e))},2e3),be.beforeEach((e,t,n)=>{try{if(e.meta.title&&(document.title=`${e.meta.title} - 晨鑫流量变现系统`),e.path.includes("/admin/")){const t=e.path.split("/");if(t.length>=3){const e=`/${t[1]}/${t[2]}`;setTimeout(()=>Se(e),100)}}n()}catch(a){n()}}),be.onError(e=>{e.message&&e.message.includes("axisPointer")});const ke=d.create({baseURL:"/api/v1",timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json"}});ke.interceptors.request.use(e=>{const t=we();return t&&(e.headers.Authorization=`Bearer ${t}`),"get"===e.method&&(e.params={...e.params,_t:Date.now()}),e},e=>Promise.reject(e)),ke.interceptors.response.use(e=>{const t=e.data;return 200!==t.code&&0!==t.code?(p({message:t.message||"请求失败",type:"error",duration:5e3}),50008!==t.code&&50012!==t.code&&50014!==t.code||h.confirm("你已被登出，可以取消继续留在该页面，或者重新登录","确定登出",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then(()=>{Pe(),location.reload()}),Promise.reject(new Error(t.message||"请求失败"))):t},e=>{let t="请求失败";if(e.response){const{status:n,data:a}=e.response;switch(n){case 400:t=a.message||"请求参数错误";break;case 401:t="未授权，请重新登录",Pe(),be.push("/login");break;case 403:t="拒绝访问";break;case 404:t="请求地址出错";break;case 408:t="请求超时";break;case 500:t="服务器内部错误";break;case 501:t="服务未实现";break;case 502:t="网关错误";break;case 503:t="服务不可用";break;case 504:t="网关超时";break;case 505:t="HTTP版本不受支持";break;default:t=a.message||`连接错误${n}`}}else t=e.request?"网络连接异常，请检查网络":e.message||"请求配置错误";return p({message:t,type:"error",duration:5e3}),Promise.reject(e)});const je=()=>ke({url:"/admin/auth/user",method:"get"}),Re=e=>ke({url:"/admin/auth/profile",method:"put",data:e}),Ie=e=>ke({url:"/admin/auth/password",method:"put",data:e}),Me=()=>ke({url:"/admin/auth/security-settings",method:"get"}),Ve=e=>ke({url:"/admin/auth/security-settings",method:"put",data:e}),qe=e=>ke({url:"/admin/auth/login-logs",method:"get",params:e}),Ne=()=>ke({url:"/admin/auth/sessions",method:"get"}),xe=e=>ke({url:`/admin/auth/sessions/${e}`,method:"delete"}),Be=()=>ke({url:"/admin/auth/sessions/all",method:"delete"}),Ue=e=>{const t=/[A-Z]/.test(e),n=/[a-z]/.test(e),a=/\d/.test(e),o=/[!@#$%^&*(),.?":{}|<>]/.test(e);let r=0,s=[];e.length>=8?r+=1:s.push("密码长度至少8位"),t?r+=1:s.push("包含大写字母"),n?r+=1:s.push("包含小写字母"),a?r+=1:s.push("包含数字"),o?r+=1:s.push("包含特殊字符");let i="weak";return r>=4?i="strong":r>=3&&(i="medium"),{score:r,strength:i,feedback:s,isValid:r>=3}},Ge=s("user",()=>{const e=i(we()),t=i(null),n=()=>{const n="preview-mode-token-"+Date.now();Te(n),e.value=n,t.value={id:1,username:"admin",nickname:"超级管理员 (预览)",email:"<EMAIL>",avatar:"/default-avatar.png",role:"admin",roles:["admin"],permissions:["*"]}},a=m(()=>t.value?.roles||[]),o=m(()=>t.value?.nickname||""),r=m(()=>t.value?.avatar||""),s=m(()=>t.value?.role||""),c=m(()=>"admin"===s.value),l=m(()=>"substation"===s.value),u=m(()=>"agent"===s.value),d=m(()=>"distributor"===s.value),p=m(()=>"group_owner"===s.value),h=m(()=>"user"===s.value),_=m(()=>{try{const e=localStorage.getItem("sessionInfo");return e?JSON.parse(e):null}catch{return null}}),g=m(()=>{if(!e.value||!_.value)return!1;const t=new Date(_.value.loginTime);return new Date-t<864e5}),f=()=>{window.autoLogoutTimer&&clearTimeout(window.autoLogoutTimer),window.autoLogoutTimer=setTimeout(()=>{C()},864e5)},C=async()=>{try{await ke({url:"/admin/auth/logout",method:"post"})}catch(e){}finally{E(),t.value=null,localStorage.removeItem("sessionInfo"),window.autoLogoutTimer&&(clearTimeout(window.autoLogoutTimer),window.autoLogoutTimer=null)}},E=()=>{Pe(),e.value=""};return{token:e,userInfo:t,roles:a,nickname:o,avatar:r,userRole:s,sessionInfo:_,isSessionValid:g,isAdmin:c,isSubstation:l,isAgent:u,isDistributor:d,isGroupOwner:p,isUser:h,login:async n=>{try{const a=await(e=>ke({url:"/admin/auth/login",method:"post",data:e}))(n),o=a.data;if(o.success){Te(o.data.token),e.value=o.data.token,t.value=o.data.user;const n={loginTime:(new Date).toISOString(),userAgent:navigator.userAgent,role:o.data.user.role,userId:o.data.user.id,username:o.data.user.username};return localStorage.setItem("sessionInfo",JSON.stringify(n)),f(),o}throw new Error(o.message||"登录失败")}catch(a){if(a.response){const e=a.response.status,t=a.response.data?.message||a.message;switch(e){case 401:throw new Error("用户名或密码错误");case 403:throw new Error("账户已被禁用或权限不足");case 429:throw new Error("登录尝试过于频繁，请稍后再试");case 500:throw new Error("服务器内部错误，请稍后重试");default:throw new Error(t||"登录失败")}}throw a}},getUserInfo:async()=>{if(t.value)return{success:!0,data:{user:t.value}};if(e.value&&e.value.includes("preview-mode-token"))return{success:!0,data:{user:t.value}};try{const e=(await je()).data;if(e.success)return t.value=e.data.user,e;throw new Error(e.message||"获取用户信息失败")}catch(a){return n(),{success:!0,data:{user:t.value}}}},logout:C,resetToken:E,setToken:t=>{Te(t),e.value=t},setUserInfo:e=>{t.value=e},setupAutoLogout:f,hasPermission:e=>{if(!t.value)return!1;if(c.value)return!0;return({substation:["user_management","agent_management","order_management","group_management","finance_view"],agent:["team_management","commission_view","performance_view","application_management"],distributor:["customer_management","group_management","promotion_management","commission_view"],group_owner:["group_management","content_management","template_management"],user:["profile_management","order_view"]}[s.value]||[]).includes(e)},hasRouteAccess:e=>{if(!t.value)return!1;if(c.value)return!0;try{const{checkMenuPermission:t}=require("@/config/navigation");return t({path:e},s.value)}catch{return!1}},getUserDefaultRoute:()=>{try{const{getUserDefaultRoute:e}=require("@/config/navigation");return e(s.value)}catch{return"/dashboard"}},enterPreviewMode:n}});class $e{static handleHttpError(e){const t=Ge();if(e.response){const{status:n,data:a}=e.response;switch(n){case 400:p.error(a.message||"请求参数错误");break;case 401:p.error("登录已过期，请重新登录"),t.logout(),be.push("/login");break;case 403:p.error("没有权限访问该资源");break;case 404:p.error("请求的资源不存在");break;case 422:this.handleValidationError(a);break;case 429:p.error("请求过于频繁，请稍后再试");break;case 500:p.error("服务器内部错误，请联系管理员");break;case 502:p.error("网关错误，请稍后重试");break;case 503:p.error("服务暂时不可用，请稍后重试");break;default:p.error(a.message||`请求失败 (${n})`)}}else e.request?p.error("网络连接失败，请检查网络设置"):p.error("请求配置错误")}static handleValidationError(e){if(e.errors){const t=Object.values(e.errors).flat(),n=t.length>0?t[0]:"表单验证失败";p.error(n)}else p.error(e.message||"表单验证失败")}static handleBusinessError(e){const t=e.message||"操作失败";p.error(t)}static handleRuntimeError(e){if(!(e?.message?.includes("Cannot read properties of null")||e?.message?.includes("reading 'type'")||e?.message?.includes("__jstcache")||e?.message?.includes("QBMiniVideo")||e?.message?.includes("jstProcess")||e?.message?.includes("sessionStorage")||e?.message?.includes("Access is denied for this document")||e?.stack?.includes("unmountComponent")||e?.stack?.includes("QBMiniVideo")||e?.stack?.includes("jstProcess"))){const t=e?.message||e?.toString()||"未知错误";p.error(`运行时错误: ${t}`)}}static handleUnhandledRejection(e){const t=e.reason?.message||"",n=e.reason?.stack||"";if(t.includes("拒绝了我们的连接请求")||t.includes("Network Error")||t.includes("ERR_CONNECTION_REFUSED")||t.includes("data2 is not iterable")||t.includes("is not iterable")||t.includes("Cannot read properties of null")||t.includes("Cannot convert object to primitive value")||t.includes("__jstcache")||t.includes("QBMiniVideo")||t.includes("jstProcess")||t.includes("sessionStorage")||t.includes("Access is denied for this document")||n.includes("unmountComponent")||n.includes("QBMiniVideo")||n.includes("jstProcess"))e.preventDefault();else{{const n=t||e.reason?.toString()||"未知错误";p.error(`未处理的Promise错误: ${n}`)}e.preventDefault()}}static showConfirm(e,t="提示"){return h.confirm(e,t,{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})}static showAlert(e,t="提示"){return h.alert(e,t,{confirmButtonText:"确定"})}}const Fe={"GET /api/v1/users":{code:200,message:"获取成功",data:{list:[{id:1,username:"admin",realName:"系统管理员",email:"<EMAIL>",phone:"13800138000",role:"admin",status:"active",avatar:"",created_at:"2024-01-01 10:00:00",last_login_at:"2024-08-06 15:30:00"},{id:2,username:"zhangsan",realName:"张三",email:"<EMAIL>",phone:"13800138001",role:"agent",status:"active",avatar:"",created_at:"2024-02-15 14:20:00",last_login_at:"2024-08-06 12:15:00"}],total:2,page:1,size:20}},"POST /api/v1/users":{code:200,message:"用户创建成功",data:{id:Date.now(),username:"new_user",realName:"新用户",email:"<EMAIL>",phone:"13800138999",role:"user",status:"active",created_at:(new Date).toISOString()}},"PUT /api/v1/users/:id":{code:200,message:"用户更新成功",data:null},"DELETE /api/v1/users/:id":{code:200,message:"用户删除成功",data:null},"GET /api/v1/roles":{code:200,message:"获取成功",data:{list:[{id:1,name:"admin",display_name:"超级管理员",description:"拥有系统所有权限，可以管理所有功能模块",permissions_count:45,users_count:3,is_system:!0,status:"active",created_at:"2024-01-01 10:00:00"},{id:2,name:"agent",display_name:"代理商",description:"管理下级分销员，拥有团队管理权限",permissions_count:25,users_count:23,is_system:!0,status:"active",created_at:"2024-02-01 09:30:00"}],total:2,page:1,size:20}},"POST /api/v1/roles":{code:200,message:"角色创建成功",data:{id:Date.now(),name:"new_role",display_name:"新角色",description:"新创建的角色",is_system:!1,status:"active",created_at:(new Date).toISOString()}},"PUT /api/v1/roles/:id":{code:200,message:"角色更新成功",data:null},"DELETE /api/v1/roles/:id":{code:200,message:"角色删除成功",data:null},"GET /api/v1/roles/:id/permissions":{code:200,message:"获取成功",data:{permissions:["dashboard.view","user.list","user.create","community.list","finance.dashboard"]}},"PUT /api/v1/roles/:id/permissions":{code:200,message:"权限配置保存成功",data:null},"POST /api/v1/auth/login":{code:200,message:"登录成功",data:{token:"mock_token_"+Date.now(),user:{id:1,username:"admin",email:"<EMAIL>",name:"系统管理员",role:"admin",avatar:"",permissions:["*"]}}},"GET /api/v1/auth/user":{code:200,message:"获取成功",data:{id:1,username:"admin",email:"<EMAIL>",name:"系统管理员",role:"admin",avatar:"",permissions:["*"]}},"POST /api/v1/auth/logout":{code:200,message:"退出成功",data:null},"GET /api/v1/health":{code:200,message:"系统运行正常",data:{status:"ok",timestamp:Date.now(),version:"1.0.0"}}};function ze(){const e=window.fetch;window.fetch=async function(t,n={}){const a=n.method||"GET";let o=`${a.toUpperCase()} ${t}`;if(t.startsWith("/api/")&&(o=`${a.toUpperCase()} ${t}`),Fe[o]){await new Promise(e=>setTimeout(e,100+200*Math.random()));const e=Fe[o];return new Response(JSON.stringify(e),{status:200,headers:{"Content-Type":"application/json"}})}try{return await e(t,n)}catch(r){return new Response(JSON.stringify({code:200,message:"操作成功",data:null}),{status:200,headers:{"Content-Type":"application/json"}})}}}async function He(){{const e=await async function(){try{return!0}catch(e){return!1}}();e||ze()}}function Je(e,t,n,a,o){const r=a||e?.message||"",s=o||e?.stack||"",i=t||"";return["QBMiniVideo","jstProcess","__jstcache","crbug/1173575","non-JS module files deprecated","sessionStorage","localStorage","Access is denied for this document","Cannot read properties of null","拒绝了我们的连接请求","Network Error","ERR_CONNECTION_REFUSED","ECONNREFUSED","Failed to fetch","data2 is not iterable","is not iterable","Cannot convert object to primitive value","unmountComponent","closest is not a function","querySelector is not a function","addEventListener is not a function","axisPointer","CartesianAxisPointer exists","Component already exists","extension already exists"].some(e=>r.includes(e)||s.includes(e)||i.includes(e))}const Qe={install(e){e.config.errorHandler=(e,t,n)=>{if(!(e?.message?.includes("Cannot read properties of null")||e?.message?.includes("reading 'type'")||e?.stack?.includes("unmountComponent")||e?.stack?.includes("unmount")||n?.includes("unmount")||e?.message?.includes("拒绝了我们的连接请求")||e?.message?.includes("Network Error")||e?.message?.includes("ERR_CONNECTION_REFUSED")||e?.message?.includes("data2 is not iterable")||e?.message?.includes("is not iterable"))){const t=e?.message||e?.toString()||"未知错误";p.error(`组件错误: ${t}`)}},e.config.warnHandler=(e,t,n)=>{try{"string"==typeof e||String(e),"string"==typeof n||n&&String(n)}catch(a){}},e.provide("$handleError",(e,t="")=>{e?.message?.includes("Cannot read properties of null")||e?.stack?.includes("unmountComponent")||p.error(`${t}: ${e.message}`)}),e.provide("$safeAsync",async(e,t="")=>{try{return await e()}catch(n){if(n?.message?.includes("Cannot read properties of null")||n?.stack?.includes("unmountComponent"))return null;throw p.error(`${t}: ${n.message}`),n}})}};const Ze=new class{constructor(){this.preloadCache=new Map,this.loadingStates=new Map,this.observers=new Set}async preloadRoute(e){if(this.preloadCache.has(e)||this.loadingStates.has(e))return this.preloadCache.get(e);this.loadingStates.set(e,!0);try{const t=await((e,t,n)=>{const a=e[t];return a?"function"==typeof a?a():Promise.resolve(a):new Promise((e,a)=>{("function"==typeof queueMicrotask?queueMicrotask:setTimeout)(a.bind(null,new Error("Unknown variable dynamic import: "+t+(t.split("/").length!==n?". Note that variables only represent file names one level deep.":""))))})})(Object.assign({}),`../views${e}.vue`,2);return this.preloadCache.set(e,t),t}catch(t){return null}finally{this.loadingStates.delete(e)}}async preloadCriticalRoutes(){const e=["/dashboard/ModernDashboard","/community/GroupList","/user/UserList","/finance/FinanceDashboard","/anti-block/Dashboard"].map(e=>this.preloadRoute(e));await Promise.all(e)}setupHoverPreload(){ve(async()=>{const{safeClosest:e}=await import("./dom-safe-RtthwF4q.js");return{safeClosest:e}},[]).then(({safeClosest:e})=>{document.addEventListener("mouseenter",t=>{const n=e(t.target,"[data-route-path]");if(n&&n.dataset&&n.dataset.routePath){const e=n.dataset.routePath;setTimeout(()=>this.preloadRoute(e),200)}},{passive:!0,capture:!0})}).catch(e=>{})}setupVisibilityOptimization(){document.addEventListener("visibilitychange",()=>{document.hidden?this.pausePreloading=!0:this.pausePreloading=!1})}setupNetworkAwarePreload(){if("connection"in navigator){const e=navigator.connection,t=()=>{const t=e.effectiveType;"4g"===t?this.preloadAggressively=!0:"3g"===t?this.preloadAggressively=!1:this.preloadEnabled=!1};e.addEventListener("change",t),t()}}init(){setTimeout(()=>{this.preloadCriticalRoutes(),this.setupHoverPreload(),this.setupVisibilityOptimization(),this.setupNetworkAwarePreload()},2e3)}getStats(){return{preloaded:this.preloadCache.size,loading:this.loadingStates.size,routes:Array.from(this.preloadCache.keys())}}},We={navigationStart:0,startNavigation(e,t){this.navigationStart=performance.now()},endNavigation(e){const t=performance.now()-this.navigationStart;window.gtag&&window.gtag("event","page_load_time",{event_category:"Navigation",event_label:e,value:Math.round(t)})}};let Ke=!1,Xe=null;async function Ye(){return Ke?Promise.resolve():Xe||(Xe=async function(){try{const{use:t}=await ve(async()=>{const{use:e}=await import("./echarts-D6CUuNS9.js").then(e=>e.a5);return{use:e}},__vite__mapDeps([4,1,2])),{CanvasRenderer:n}=await ve(async()=>{const{CanvasRenderer:e}=await Promise.resolve().then(()=>at);return{CanvasRenderer:e}},void 0),{LineChart:a,BarChart:o,PieChart:r,MapChart:s,HeatmapChart:i,FunnelChart:m,ScatterChart:c,RadarChart:l,GaugeChart:u}=await ve(async()=>{const{LineChart:e,BarChart:t,PieChart:n,MapChart:a,HeatmapChart:o,FunnelChart:r,ScatterChart:s,RadarChart:i,GaugeChart:m}=await Promise.resolve().then(()=>ot);return{LineChart:e,BarChart:t,PieChart:n,MapChart:a,HeatmapChart:o,FunnelChart:r,ScatterChart:s,RadarChart:i,GaugeChart:m}},void 0),{TitleComponent:d,TooltipComponent:p,LegendComponent:h,GridComponent:_,CalendarComponent:g,VisualMapComponent:f,DataZoomComponent:C,MarkLineComponent:E,MarkPointComponent:y,DatasetComponent:v,TransformComponent:A,BrushComponent:w,TimelineComponent:T,ToolboxComponent:P}=await ve(async()=>{const{TitleComponent:e,TooltipComponent:t,LegendComponent:n,GridComponent:a,CalendarComponent:o,VisualMapComponent:r,DataZoomComponent:s,MarkLineComponent:i,MarkPointComponent:m,DatasetComponent:c,TransformComponent:l,BrushComponent:u,TimelineComponent:d,ToolboxComponent:p}=await Promise.resolve().then(()=>rt);return{TitleComponent:e,TooltipComponent:t,LegendComponent:n,GridComponent:a,CalendarComponent:o,VisualMapComponent:r,DataZoomComponent:s,MarkLineComponent:i,MarkPointComponent:m,DatasetComponent:c,TransformComponent:l,BrushComponent:u,TimelineComponent:d,ToolboxComponent:p}},void 0);try{t([n,a,o,r,s,i,m,c,l,u,d,p,h,_,g,f,C,E,y,v,A,w,T,P])}catch(e){if(!e.message||!e.message.includes("exists"))throw e}Ke=!0}catch(e){throw Ke=!1,Xe=null,e}}(),Xe)}async function et(e,t=null,n={}){await Ye();const a=await ve(()=>import("./echarts-D6CUuNS9.js").then(e=>e.e),__vite__mapDeps([4,1,2]));try{const o=a.getInstanceByDom(e);return o&&o.dispose(),a.default?a.default.init(e,t,n):a.init(e,t,n)}catch(o){throw o}}async function tt(e){try{if(!e)return;if(1===e.nodeType){const t=await ve(()=>import("./echarts-D6CUuNS9.js").then(e=>e.e),__vite__mapDeps([4,1,2])),n=(t.default||t).getInstanceByDom(e);n&&n.dispose()}else e.dispose&&e.dispose()}catch(t){}}Ye().then(()=>{}).catch(e=>{});const nt=c(Ee);window.addEventListener("error",e=>{$e.handleRuntimeError(e.error)}),window.addEventListener("unhandledrejection",e=>{$e.handleUnhandledRejection(e)}),async function(){try{!function(){try{const e="__storage_test__";window.sessionStorage.setItem(e,"test"),window.sessionStorage.removeItem(e)}catch(e){const t={_data:{},setItem(e,t){this._data[e]=String(t)},getItem(e){return this._data[e]||null},removeItem(e){delete this._data[e]},clear(){this._data={}},get length(){return Object.keys(this._data).length},key(e){return Object.keys(this._data)[e]||null}};Object.defineProperty(window,"sessionStorage",{value:t,writable:!1})}}(),function(){try{const e="__storage_test__";window.localStorage.setItem(e,"test"),window.localStorage.removeItem(e)}catch(e){const t={_data:{},setItem(e,t){this._data[e]=String(t)},getItem(e){return this._data[e]||null},removeItem(e){delete this._data[e]},clear(){this._data={}},get length(){return Object.keys(this._data).length},key(e){return Object.keys(this._data)[e]||null}};Object.defineProperty(window,"localStorage",{value:t,writable:!1})}}(),function(){const e=console.warn;console.warn=function(...t){let n="";try{n=t.map(e=>"string"==typeof e?e:"object"==typeof e&&null!==e?JSON.stringify(e):String(e)).join(" ")}catch(a){return void e.apply(console,t)}n.includes("crbug/1173575")||n.includes("non-JS module files deprecated")||n.includes("__jstcache")||e.apply(console,t)};const t=console.error;console.error=function(...e){let n="";try{n=e.map(e=>"string"==typeof e?e:"object"==typeof e&&null!==e?JSON.stringify(e):String(e)).join(" ")}catch(a){return void t.apply(console,e)}Je(null,null,0,n,"")||t.apply(console,e)}}(),function(){{window.__PREVIEW_MODE__=!0,localStorage.setItem("preview-mode","true");const e=document.createElement("style");e.textContent='\n      body::before {\n        content: "预览模式";\n        position: fixed;\n        top: 10px;\n        right: 10px;\n        background: #ff6b6b;\n        color: white;\n        padding: 4px 8px;\n        border-radius: 4px;\n        font-size: 12px;\n        z-index: 9999;\n        pointer-events: none;\n      }\n    ',document.head.appendChild(e)}}(),function(){function e(){navigator.onLine}window.addEventListener("online",e),window.addEventListener("offline",e),e()}(),window.addEventListener("error",e=>{Je(e.error,e.filename,e.lineno)&&e.preventDefault()}),window.addEventListener("unhandledrejection",e=>{const t=e.reason?.message||"",n=e.reason?.stack||"";Je(e.reason,null,0,t,n)&&e.preventDefault()});!0&&ze(),await He()}catch(e){}}();for(const[st,it]of Object.entries(_))nt.component(st,it);nt.use(Qe),nt.use(g),nt.use(l()),nt.use(be),nt.mount("#app"),setTimeout(()=>{Ze.init(),be.beforeEach((e,t,n)=>{We.startNavigation(t.path,e.path),n()}),be.afterEach(e=>{We.endNavigation(e.name)})},1e3);const at=Object.freeze(Object.defineProperty({__proto__:null,CanvasRenderer:f,SVGRenderer:C},Symbol.toStringTag,{value:"Module"})),ot=Object.freeze(Object.defineProperty({__proto__:null,BarChart:E,BoxplotChart:y,CandlestickChart:v,CustomChart:A,EffectScatterChart:w,FunnelChart:T,GaugeChart:P,GraphChart:D,HeatmapChart:b,LineChart:L,LinesChart:S,MapChart:O,ParallelChart:k,PictorialBarChart:j,PieChart:R,RadarChart:I,SankeyChart:M,ScatterChart:V,SunburstChart:q,ThemeRiverChart:N,TreeChart:x,TreemapChart:B},Symbol.toStringTag,{value:"Module"})),rt=Object.freeze(Object.defineProperty({__proto__:null,AriaComponent:U,AxisPointerComponent:G,BrushComponent:$,CalendarComponent:F,DataZoomComponent:z,DataZoomInsideComponent:H,DataZoomSliderComponent:J,DatasetComponent:Q,GeoComponent:Z,GraphicComponent:W,GridComponent:K,GridSimpleComponent:X,LegendComponent:Y,LegendPlainComponent:ee,LegendScrollComponent:te,MarkAreaComponent:ne,MarkLineComponent:ae,MarkPointComponent:oe,ParallelComponent:re,PolarComponent:se,RadarComponent:ie,SingleAxisComponent:me,TimelineComponent:ce,TitleComponent:le,ToolboxComponent:ue,TooltipComponent:de,TransformComponent:pe,VisualMapComponent:he,VisualMapContinuousComponent:_e,VisualMapPiecewiseComponent:ge},Symbol.toStringTag,{value:"Module"}));export{fe as _,ve as a,je as b,et as c,tt as d,qe as e,xe as f,we as g,Ne as h,Ve as i,Me as j,Ie as k,Re as l,Ze as n,be as r,ke as s,Be as t,Ge as u,Ue as v};
