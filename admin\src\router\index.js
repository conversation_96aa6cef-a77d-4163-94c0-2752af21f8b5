// 主路由配置 - 集成所有功能模块
import { createRouter, createWebHashHistory } from 'vue-router'
import { dataScreenRoutes } from './dataScreen.js'
import { getToken } from '@/utils/auth'

// 主要路由配置
const routes = [
  // 重定向根路径到登录页
  {
    path: '/',
    redirect: '/login'
  },
  // 主布局路由
  {
    path: '/admin',
    component: () => import('@/components/layout/ModernLayout.vue'),
    redirect: '/admin/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/ModernDashboard.vue'),
        meta: {
          title: '控制台',
          icon: 'TrendCharts',
          requiresAuth: true
        }
      },
      {
        path: 'analytics',
        name: 'DashboardAnalytics',
        component: () => import('@/views/dashboard/Analytics.vue'),
        meta: {
          title: '数据分析',
          icon: 'DataLine',
          requiresAuth: true
        }
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('@/views/user/UserList.vue'),
        meta: {
          title: '用户管理',
          icon: 'User',
          requiresAuth: true
        }
      },
      {
        path: 'user-analytics',
        name: 'UserAnalytics',
        component: () => import('@/views/user/UserAnalytics.vue'),
        meta: {
          title: '用户分析',
          icon: 'TrendCharts',
          requiresAuth: true
        }
      },
      {
        path: 'permissions',
        name: 'PermissionManagement',
        component: () => import('@/views/permission/PermissionManagement.vue'),
        meta: {
          title: '权限管理',
          icon: 'Lock',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'roles',
        name: 'RoleManagement',
        component: () => import('@/views/permission/RoleManagement.vue'),
        meta: {
          title: '角色管理',
          icon: 'UserFilled',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'groups',
        name: 'GroupManagement',
        component: () => import('@/views/community/GroupList.vue'),
        meta: {
          title: '群组管理',
          icon: 'ChatDotRound',
          requiresAuth: true
        }
      },
      {
        path: 'links',
        name: 'LinkManagement',
        component: () => import('@/views/anti-block/ShortLinkList.vue'),
        meta: {
          title: '链接管理',
          icon: 'Link',
          requiresAuth: true
        }
      },
      {
        path: 'settings',
        name: 'SystemSettings',
        component: () => import('@/views/system/Settings.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting',
          requiresAuth: true
        }
      },
      {
        path: 'operation-logs',
        name: 'OperationLogs',
        component: () => import('@/views/system/OperationLogs.vue'),
        meta: {
          title: '操作日志',
          icon: 'Document',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'system-monitor',
        name: 'SystemMonitor',
        component: () => import('@/views/system/Monitor.vue'),
        meta: {
          title: '系统监控',
          icon: 'Monitor',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'notifications',
        name: 'NotificationManagement',
        component: () => import('@/views/system/Notifications.vue'),
        meta: {
          title: '通知管理',
          icon: 'Bell',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'data-export',
        name: 'DataExport',
        component: () => import('@/views/system/DataExport.vue'),
        meta: {
          title: '数据导出',
          icon: 'Document',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'file-management',
        name: 'FileManagement',
        component: () => import('@/views/system/FileManagement.vue'),
        meta: {
          title: '文件管理',
          icon: 'Folder',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'templates',
        name: 'TemplateManagement',
        component: () => import('@/views/community/TemplateManagement.vue'),
        meta: {
          title: '模板管理',
          icon: 'Document',
          requiresAuth: true
        }
      },
      {
        path: 'agents',
        name: 'AgentList',
        component: () => import('@/views/agent/AgentList.vue'),
        meta: {
          title: '代理商管理',
          icon: 'Avatar',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'agent-hierarchy',
        name: 'AgentHierarchy',
        component: () => import('@/views/agent/AgentHierarchy.vue'),
        meta: {
          title: '代理商层级',
          icon: 'Grid',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'agent-commission',
        name: 'AgentCommission',
        component: () => import('@/views/agent/AgentCommission.vue'),
        meta: {
          title: '佣金管理',
          icon: 'Money',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'agent-performance',
        name: 'AgentPerformance',
        component: () => import('@/views/agent/AgentPerformance.vue'),
        meta: {
          title: '业绩统计',
          icon: 'TrendCharts',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'finance',
        name: 'FinanceDashboard',
        component: () => import('@/views/finance/FinanceDashboard.vue'),
        meta: {
          title: '财务管理',
          icon: 'Money',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'transactions',
        name: 'TransactionList',
        component: () => import('@/views/finance/TransactionList.vue'),
        meta: {
          title: '交易记录',
          icon: 'CreditCard',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'commission-logs',
        name: 'CommissionLog',
        component: () => import('@/views/finance/CommissionLog.vue'),
        meta: {
          title: '佣金日志',
          icon: 'Money',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'withdraw-manage',
        name: 'WithdrawManage',
        component: () => import('@/views/finance/WithdrawManage.vue'),
        meta: {
          title: '提现管理',
          icon: 'CreditCard',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'payment-settings',
        name: 'PaymentSettings',
        component: () => import('@/views/payment/PaymentSettings.vue'),
        meta: {
          title: '支付设置',
          icon: 'Setting',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'payment-channels',
        name: 'PaymentChannelManagement',
        component: () => import('@/views/payment/PaymentChannelManagement.vue'),
        meta: {
          title: '支付渠道',
          icon: 'CreditCard',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'payment-orders',
        name: 'PaymentOrders',
        component: () => import('@/views/payment/PaymentOrders.vue'),
        meta: {
          title: '支付订单',
          icon: 'Tickets',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'payment-logs',
        name: 'PaymentLogs',
        component: () => import('@/views/payment/PaymentLogs.vue'),
        meta: {
          title: '支付日志',
          icon: 'Document',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'orders',
        name: 'OrderManagement',
        component: () => import('@/views/orders/OrderList.vue'),
        meta: {
          title: '订单管理',
          icon: 'ShoppingCart',
          requiresAuth: true
        }
      },
      {
        path: 'distributors',
        name: 'DistributorManagement',
        component: () => import('@/views/distribution/DistributorList.vue'),
        meta: {
          title: '分销管理',
          icon: 'Share',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'promotion',
        name: 'PromotionManagement',
        component: () => import('@/views/promotion/LinkManagement.vue'),
        meta: {
          title: '推广管理',
          icon: 'Share',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'anti-block',
        name: 'AntiBlockDashboard',
        component: () => import('@/views/anti-block/Dashboard.vue'),
        meta: {
          title: '防红系统',
          icon: 'Lock',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'domains',
        name: 'DomainManagement',
        component: () => import('@/views/anti-block/DomainList.vue'),
        meta: {
          title: '域名管理',
          icon: 'Connection',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'short-links',
        name: 'ShortLinkManagement',
        component: () => import('@/views/anti-block/ShortLinkList.vue'),
        meta: {
          title: '短链管理',
          icon: 'Link',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'anti-block-analytics',
        name: 'AntiBlockAnalytics',
        component: () => import('@/views/anti-block/Analytics.vue'),
        meta: {
          title: '防红分析',
          icon: 'TrendCharts',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'anti-block-enhanced',
        name: 'AntiBlockEnhanced',
        component: () => import('@/views/anti-block/EnhancedDashboard.vue'),
        meta: {
          title: '增强防红',
          icon: 'Star',
          requiresAuth: true,
          roles: ['admin']
        }
      }
    ]
  },
  // 兼容旧路由
  {
    path: '/dashboard',
    redirect: '/admin/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      hideInMenu: true
    }
  },
  {
    path: '/test',
    name: 'TestRoute',
    component: () => import('@/views/TestRoute.vue'),
    meta: {
      title: '路由测试',
      hideInMenu: true
    }
  },
  {
    path: '/test-dashboard',
    name: 'TestDashboard',
    component: () => import('@/views/dashboard/ModernDashboard.vue'),
    meta: {
      title: '测试控制台',
      hideInMenu: true
    }
  },
  {
    path: '/scroll-test',
    name: 'ScrollTest',
    component: () => import('@/views/ScrollTest.vue'),
    meta: {
      title: '滚动测试',
      hideInMenu: true
    }
  },
  {
    path: '/system-status',
    name: 'SystemStatus',
    component: () => import('@/views/SystemStatus.vue'),
    meta: {
      title: '系统状态',
      hideInMenu: true
    }
  },
  {
    path: '/navigation-test',
    name: 'NavigationTest',
    component: () => import('@/views/NavigationTest.vue'),
    meta: {
      title: '导航测试',
      hideInMenu: true
    }
  },
  {
    path: '/permission-test',
    name: 'PermissionTest',
    component: () => import('@/views/PermissionTest.vue'),
    meta: {
      title: '权限测试',
      hideInMenu: true
    }
  },

  // 兼容旧路由 - 重定向到新的嵌套路由
  {
    path: '/community',
    redirect: '/admin/groups'
  },
  {
    path: '/community/groups',
    redirect: '/admin/groups'
  },
  {
    path: '/community/templates',
    redirect: '/admin/templates'
  },
  {
    path: '/users',
    redirect: '/admin/users'
  },
  {
    path: '/users/analytics',
    redirect: '/admin/analytics'
  },

  // 添加用户 - 暂时注释，等待组件创建
  // {
  //   path: '/user/add',
  //   name: 'UserAdd',
  //   component: () => import('@/views/user/UserAdd.vue'),
  //   meta: {
  //     title: '添加用户',
  //     icon: 'Plus',
  //     requiresAuth: true,
  //     roles: ['admin']
  //   }
  // },

  // 兼容旧路由 - 代理商管理重定向
  {
    path: '/agent',
    redirect: '/admin/agents'
  },
  {
    path: '/agent/list',
    redirect: '/admin/agents'
  },
  {
    path: '/agent/hierarchy',
    redirect: '/admin/agent-hierarchy'
  },
  {
    path: '/agent/commission',
    redirect: '/admin/agent-commission'
  },
  {
    path: '/agent/performance',
    redirect: '/admin/agent-performance'
  },

  // 兼容旧路由 - 订单管理重定向
  {
    path: '/orders',
    redirect: '/admin/orders'
  },
  
  // 兼容旧路由 - 分销和推广管理重定向
  {
    path: '/distribution',
    redirect: '/admin/distributors'
  },
  {
    path: '/distribution/distributors',
    redirect: '/admin/distributors'
  },
  {
    path: '/distribution/detail/:id?',
    redirect: '/admin/distributors'
  },
  {
    path: '/promotion',
    redirect: '/admin/promotion'
  },
  {
    path: '/promotion/links',
    redirect: '/admin/promotion'
  },
  {
    path: '/promotion/landing-pages',
    redirect: '/admin/promotion'
  },
  {
    path: '/promotion/analytics',
    redirect: '/admin/analytics'
  },

  // 兼容旧路由 - 防红系统重定向
  {
    path: '/anti-block',
    redirect: '/admin/anti-block'
  },
  {
    path: '/anti-block/dashboard',
    redirect: '/admin/anti-block'
  },
  {
    path: '/anti-block/domains',
    redirect: '/admin/domains'
  },
  {
    path: '/anti-block/short-links',
    redirect: '/admin/short-links'
  },

  // 财务管理重定向 - 重定向到admin布局下的路由
  {
    path: '/finance',
    redirect: '/admin/finance'
  },
  {
    path: '/finance/commission',
    redirect: '/admin/commission-logs'
  },
  {
    path: '/finance/withdraw',
    redirect: '/admin/withdraw-manage'
  },

  // 支付设置重定向 - 重定向到admin布局下的路由
  {
    path: '/payment',
    redirect: '/admin/payment-settings'
  },
  {
    path: '/payment/settings',
    redirect: '/admin/payment-settings'
  },
  {
    path: '/payment/config',
    redirect: '/admin/payment-channels'
  },
  {
    path: '/payment/logs',
    redirect: '/admin/payment-logs'
  },
  {
    path: '/payment/orders',
    redirect: '/admin/payment-orders'
  },

  // 权限管理 - 使用现有组件
  {
    path: '/permission',
    name: 'Permission',
    redirect: '/permission/roles',
    meta: {
      title: '权限管理',
      icon: 'Lock',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/permission/roles',
    name: 'RoleManagement',
    component: () => import('@/views/permission/RoleManagement.vue'),
    meta: {
      title: '角色管理',
      icon: 'Avatar',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/permission/permissions',
    name: 'PermissionManagement',
    component: () => import('@/views/permission/PermissionManagement.vue'),
    meta: {
      title: '权限管理',
      icon: 'Lock',
      requiresAuth: true,
      roles: ['admin']
    }
  },

  // 系统设置重定向 - 重定向到admin布局下的路由
  {
    path: '/system',
    redirect: '/admin/system-monitor'
  },
  {
    path: '/system/settings',
    redirect: '/admin/system-monitor'
  },
  {
    path: '/system/monitor',
    redirect: '/admin/system-monitor'
  },
  {
    path: '/system/operation-logs',
    redirect: '/admin/operation-logs'
  },
  {
    path: '/system/notifications',
    redirect: '/admin/notifications'
  },
  {
    path: '/system/files',
    redirect: '/admin/file-management'
  },

  // 社群管理扩展功能重定向 - 重定向到admin布局下的路由
  {
    path: '/community/add-enhanced',
    redirect: '/admin/community/add-enhanced'
  },
  {
    path: '/community/content-moderation',
    redirect: '/admin/community/content-moderation'
  },
  
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/ErrorPage.vue'),
    meta: {
      title: '页面未找到',
      hideInMenu: true
    }
  }
]

// 合并数据大屏路由
const allRoutes = [...routes, ...dataScreenRoutes]

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes: allRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 简化的路由守卫 - 暂时禁用认证检查
router.beforeEach((to, from, next) => {
  console.log(`🛣️ 路由守卫: 从 ${from.path} 到 ${to.path}`)

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - LinkHub Pro 管理系统`
  }

  console.log('✅ 路由守卫通过，允许访问')
  next()
})

export default router
