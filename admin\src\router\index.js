// 优化后的路由配置 - 整合重复路由，优化导航结构
import { createRouter, createWebHashHistory } from 'vue-router'
import { dataScreenRoutes } from './dataScreen.js'
import { getToken } from '@/utils/auth'

// 主要路由配置
const routes = [
  // 重定向根路径到登录页
  {
    path: '/',
    redirect: '/login'
  },
  // 登录页
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      hideInMenu: true
    }
  },
  // 主布局路由
  {
    path: '/admin',
    component: () => import('@/components/layout/ModernLayout.vue'),
    redirect: '/admin/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      // === 核心仪表板 ===
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/ModernDashboard.vue'),
        meta: {
          title: '仪表板',
          icon: 'TrendCharts',
          requiresAuth: true,
          group: '核心功能'
        }
      },
      {
        path: 'analytics',
        name: 'DashboardAnalytics',
        component: () => import('@/views/dashboard/Analytics.vue'),
        meta: {
          title: '数据分析',
          icon: 'DataLine',
          requiresAuth: true,
          group: '核心功能'
        }
      },

      // === 用户管理 ===
      {
        path: 'users',
        name: 'UserManagement',
        redirect: '/admin/users/list',
        meta: {
          title: '用户管理',
          icon: 'User',
          requiresAuth: true,
          group: '用户管理'
        },
        children: [
          {
            path: 'list',
            name: 'UserList',
            component: () => import('@/views/user/UserList.vue'),
            meta: {
              title: '用户列表',
              icon: 'List',
              requiresAuth: true
            }
          },
          {
            path: 'analytics',
            name: 'UserAnalytics',
            component: () => import('@/views/user/UserAnalytics.vue'),
            meta: {
              title: '用户分析',
              icon: 'TrendCharts',
              requiresAuth: true
            }
          },
          {
            path: 'profile',
            name: 'UserProfile',
            component: () => import('@/views/user/Profile.vue'),
            meta: {
              title: '个人资料',
              icon: 'Avatar',
              requiresAuth: true
            }
          }
        ]
      },

      // === 社群管理 ===
      {
        path: 'community',
        name: 'CommunityManagement',
        redirect: '/admin/community/groups',
        meta: {
          title: '社群管理',
          icon: 'ChatDotRound',
          requiresAuth: true,
          group: '社群功能'
        },
        children: [
          {
            path: 'groups',
            name: 'GroupManagement',
            component: () => import('@/views/community/GroupList.vue'),
            meta: {
              title: '群组管理',
              icon: 'ChatDotRound',
              requiresAuth: true
            }
          },
          {
            path: 'templates',
            name: 'TemplateManagement',
            component: () => import('@/views/community/TemplateManagement.vue'),
            meta: {
              title: '模板管理',
              icon: 'Document',
              requiresAuth: true
            }
          }
        ]
      },

      // === 代理商管理 ===
      {
        path: 'agents',
        name: 'AgentManagement',
        redirect: '/admin/agents/list',
        meta: {
          title: '代理商管理',
          icon: 'Avatar',
          requiresAuth: true,
          roles: ['admin'],
          group: '业务管理'
        },
        children: [
          {
            path: 'list',
            name: 'AgentList',
            component: () => import('@/views/agent/AgentList.vue'),
            meta: {
              title: '代理商列表',
              icon: 'List',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'hierarchy',
            name: 'AgentHierarchy',
            component: () => import('@/views/agent/AgentHierarchy.vue'),
            meta: {
              title: '层级结构',
              icon: 'Grid',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'commission',
            name: 'AgentCommission',
            component: () => import('@/views/agent/AgentCommission.vue'),
            meta: {
              title: '佣金管理',
              icon: 'Money',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'performance',
            name: 'AgentPerformance',
            component: () => import('@/views/agent/AgentPerformance.vue'),
            meta: {
              title: '业绩统计',
              icon: 'TrendCharts',
              requiresAuth: true,
              roles: ['admin']
            }
          }
        ]
      },

      // === 财务管理 ===
      {
        path: 'finance',
        name: 'FinanceManagement',
        redirect: '/admin/finance/dashboard',
        meta: {
          title: '财务管理',
          icon: 'Money',
          requiresAuth: true,
          roles: ['admin'],
          group: '财务管理'
        },
        children: [
          {
            path: 'dashboard',
            name: 'FinanceDashboard',
            component: () => import('@/views/finance/FinanceDashboard.vue'),
            meta: {
              title: '财务概览',
              icon: 'DataBoard',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'transactions',
            name: 'TransactionList',
            component: () => import('@/views/finance/TransactionList.vue'),
            meta: {
              title: '交易记录',
              icon: 'CreditCard',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'commission',
            name: 'CommissionLog',
            component: () => import('@/views/finance/CommissionLog.vue'),
            meta: {
              title: '佣金日志',
              icon: 'Coin',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'withdraw',
            name: 'WithdrawManage',
            component: () => import('@/views/finance/WithdrawManage.vue'),
            meta: {
              title: '提现管理',
              icon: 'Upload',
              requiresAuth: true,
              roles: ['admin']
            }
          }
        ]
      },

      // === 支付管理 ===
      {
        path: 'payment',
        name: 'PaymentManagement',
        redirect: '/admin/payment/settings',
        meta: {
          title: '支付管理',
          icon: 'CreditCard',
          requiresAuth: true,
          roles: ['admin'],
          group: '支付系统'
        },
        children: [
          {
            path: 'settings',
            name: 'PaymentSettings',
            component: () => import('@/views/payment/PaymentSettings.vue'),
            meta: {
              title: '支付设置',
              icon: 'Setting',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'channels',
            name: 'PaymentChannelManagement',
            component: () => import('@/views/payment/PaymentChannelManagement.vue'),
            meta: {
              title: '支付渠道',
              icon: 'Connection',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'orders',
            name: 'PaymentOrders',
            component: () => import('@/views/payment/PaymentOrders.vue'),
            meta: {
              title: '支付订单',
              icon: 'Tickets',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'logs',
            name: 'PaymentLogs',
            component: () => import('@/views/payment/PaymentLogs.vue'),
            meta: {
              title: '支付日志',
              icon: 'Document',
              requiresAuth: true,
              roles: ['admin']
            }
          }
        ]
      },

      // === 订单管理 ===
      {
        path: 'orders',
        name: 'OrderManagement',
        component: () => import('@/views/orders/OrderList.vue'),
        meta: {
          title: '订单管理',
          icon: 'ShoppingCart',
          requiresAuth: true,
          group: '业务管理'
        }
      },

      // === 分销推广 ===
      {
        path: 'promotion',
        name: 'PromotionManagement',
        redirect: '/admin/promotion/distributors',
        meta: {
          title: '分销推广',
          icon: 'Share',
          requiresAuth: true,
          roles: ['admin'],
          group: '推广营销'
        },
        children: [
          {
            path: 'distributors',
            name: 'DistributorManagement',
            component: () => import('@/views/distribution/DistributorList.vue'),
            meta: {
              title: '分销商管理',
              icon: 'User',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'links',
            name: 'PromotionLinks',
            component: () => import('@/views/promotion/LinkManagement.vue'),
            meta: {
              title: '推广链接',
              icon: 'Link',
              requiresAuth: true,
              roles: ['admin']
            }
          }
        ]
      },

      // === 防红系统 ===
      {
        path: 'anti-block',
        name: 'AntiBlockSystem',
        redirect: '/admin/anti-block/dashboard',
        meta: {
          title: '防红系统',
          icon: 'Lock',
          requiresAuth: true,
          roles: ['admin'],
          group: '安全防护'
        },
        children: [
          {
            path: 'dashboard',
            name: 'AntiBlockDashboard',
            component: () => import('@/views/anti-block/Dashboard.vue'),
            meta: {
              title: '防红概览',
              icon: 'DataBoard',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'domains',
            name: 'DomainManagement',
            component: () => import('@/views/anti-block/DomainList.vue'),
            meta: {
              title: '域名管理',
              icon: 'Connection',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'links',
            name: 'ShortLinkManagement',
            component: () => import('@/views/anti-block/ShortLinkList.vue'),
            meta: {
              title: '短链管理',
              icon: 'Link',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'analytics',
            name: 'AntiBlockAnalytics',
            component: () => import('@/views/anti-block/Analytics.vue'),
            meta: {
              title: '防红分析',
              icon: 'TrendCharts',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'enhanced',
            name: 'AntiBlockEnhanced',
            component: () => import('@/views/anti-block/EnhancedDashboard.vue'),
            meta: {
              title: '增强防护',
              icon: 'Star',
              requiresAuth: true,
              roles: ['admin']
            }
          }
        ]
      },

      // === 权限管理 ===
      {
        path: 'permissions',
        name: 'PermissionSystem',
        redirect: '/admin/permissions/roles',
        meta: {
          title: '权限管理',
          icon: 'Lock',
          requiresAuth: true,
          roles: ['admin'],
          group: '系统管理'
        },
        children: [
          {
            path: 'roles',
            name: 'RoleManagement',
            component: () => import('@/views/permission/RoleManagement.vue'),
            meta: {
              title: '角色管理',
              icon: 'UserFilled',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'permissions',
            name: 'PermissionManagement',
            component: () => import('@/views/permission/PermissionManagement.vue'),
            meta: {
              title: '权限配置',
              icon: 'Key',
              requiresAuth: true,
              roles: ['admin']
            }
          }
        ]
      },

      // === 系统管理 ===
      {
        path: 'system',
        name: 'SystemManagement',
        redirect: '/admin/system/settings',
        meta: {
          title: '系统管理',
          icon: 'Setting',
          requiresAuth: true,
          roles: ['admin'],
          group: '系统管理'
        },
        children: [
          {
            path: 'settings',
            name: 'SystemSettings',
            component: () => import('@/views/system/Settings.vue'),
            meta: {
              title: '系统设置',
              icon: 'Setting',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'monitor',
            name: 'SystemMonitor',
            component: () => import('@/views/system/Monitor.vue'),
            meta: {
              title: '系统监控',
              icon: 'Monitor',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'logs',
            name: 'OperationLogs',
            component: () => import('@/views/system/OperationLogs.vue'),
            meta: {
              title: '操作日志',
              icon: 'Document',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'notifications',
            name: 'NotificationManagement',
            component: () => import('@/views/system/Notifications.vue'),
            meta: {
              title: '通知管理',
              icon: 'Bell',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'data-export',
            name: 'DataExport',
            component: () => import('@/views/system/DataExport.vue'),
            meta: {
              title: '数据导出',
              icon: 'Download',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'file-management',
            name: 'FileManagement',
            component: () => import('@/views/system/FileManagement.vue'),
            meta: {
              title: '文件管理',
              icon: 'Folder',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'function-test',
            name: 'FunctionTest',
            component: () => import('@/views/system/FunctionTest.vue'),
            meta: {
              title: '功能测试',
              icon: 'Tools',
              requiresAuth: true,
              roles: ['admin']
            }
          },
          {
            path: 'user-guide',
            name: 'UserGuide',
            component: () => import('@/views/system/UserGuide.vue'),
            meta: {
              title: '使用指南',
              icon: 'QuestionFilled',
              requiresAuth: true
            }
          }
        ]
      }
    ]
  },

  // === 兼容性重定向路由 ===
  // 旧路由重定向到新的嵌套路由结构
  {
    path: '/dashboard',
    redirect: '/admin/dashboard'
  },
  {
    path: '/community',
    redirect: '/admin/community/groups'
  },
  {
    path: '/community/groups',
    redirect: '/admin/community/groups'
  },
  {
    path: '/community/templates',
    redirect: '/admin/community/templates'
  },
  {
    path: '/users',
    redirect: '/admin/users/list'
  },
  {
    path: '/users/analytics',
    redirect: '/admin/users/analytics'
  },
  {
    path: '/agent',
    redirect: '/admin/agents/list'
  },
  {
    path: '/finance',
    redirect: '/admin/finance/dashboard'
  },
  {
    path: '/payment',
    redirect: '/admin/payment/settings'
  },
  {
    path: '/orders',
    redirect: '/admin/orders'
  },
  {
    path: '/distribution',
    redirect: '/admin/promotion/distributors'
  },
  {
    path: '/promotion',
    redirect: '/admin/promotion/links'
  },
  {
    path: '/anti-block',
    redirect: '/admin/anti-block/dashboard'
  },
  {
    path: '/permission',
    redirect: '/admin/permissions/roles'
  },
  {
    path: '/system',
    redirect: '/admin/system/settings'
  },

  // === 测试和开发路由 ===
  {
    path: '/test',
    name: 'TestRoute',
    component: () => import('@/views/TestRoute.vue'),
    meta: {
      title: '路由测试',
      hideInMenu: true
    }
  },
  {
    path: '/test-dashboard',
    name: 'TestDashboard',
    component: () => import('@/views/dashboard/ModernDashboard.vue'),
    meta: {
      title: '测试控制台',
      hideInMenu: true
    }
  },
  {
    path: '/navigation-test',
    name: 'NavigationTest',
    component: () => import('@/views/NavigationTest.vue'),
    meta: {
      title: '导航测试',
      hideInMenu: true
    }
  },

  // === 404 页面 ===
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/ErrorPage.vue'),
    meta: {
      title: '页面未找到',
      hideInMenu: true
    }
  }
]

// 合并数据大屏路由
const allRoutes = [...routes, ...dataScreenRoutes]

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes: allRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  console.log(`🛣️ 路由导航: ${from.path} → ${to.path}`)

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - LinkHub Pro 管理系统`
  }

  // TODO: 在这里可以添加权限验证逻辑
  // const token = getToken()
  // if (to.meta.requiresAuth && !token) {
  //   next('/login')
  //   return
  // }

  console.log('✅ 路由导航完成')
  next()
})

// 导航菜单配置 - 基于路由自动生成
export const generateMenuFromRoutes = (routes, basePath = '') => {
  return routes
    .filter(route => !route.meta?.hideInMenu)
    .map(route => ({
      path: basePath ? `${basePath}/${route.path}` : route.path,
      name: route.name,
      title: route.meta?.title,
      icon: route.meta?.icon,
      group: route.meta?.group,
      roles: route.meta?.roles,
      children: route.children ? generateMenuFromRoutes(route.children, route.path) : undefined
    }))
}

export default router