<template>
  <div class="group-detail-page">
    <!-- 群组概览卡片 -->
    <div class="group-overview-card modern-card">
      <div class="group-header">
        <div class="group-avatar">
          <el-image 
            :src="groupInfo.avatar" 
            class="avatar-image"
            :preview-src-list="[groupInfo.avatar]"
            fit="cover"
          >
            <template #error>
              <div class="avatar-placeholder">
                <el-icon size="40"><Comment /></el-icon>
              </div>
            </template>
          </el-image>
        </div>
        <div class="group-info">
          <div class="group-title-section">
            <h1 class="group-name">{{ groupInfo.name }}</h1>
            <div class="group-badges">
              <el-tag 
                v-for="tag in groupInfo.tags" 
                :key="tag"
                size="small"
                effect="light"
                class="group-tag"
              >
                {{ tag }}
              </el-tag>
              <el-tag 
                :type="groupInfo.status === 'active' ? 'success' : 'danger'"
                size="small"
              >
                {{ groupInfo.status === 'active' ? '活跃' : '非活跃' }}
              </el-tag>
            </div>
          </div>
          <p class="group-description">{{ groupInfo.description }}</p>
          <div class="group-meta">
            <div class="meta-item">
              <el-icon><User /></el-icon>
              <span>{{ groupInfo.memberCount }} 成员</span>
            </div>
            <div class="meta-item">
              <el-icon><Calendar /></el-icon>
              <span>创建于 {{ formatDate(groupInfo.createdAt) }}</span>
            </div>
            <div class="meta-item">
              <el-icon><Location /></el-icon>
              <span>{{ groupInfo.platform }}</span>
            </div>
          </div>
        </div>
        <div class="group-actions">
          <el-button @click="$router.go(-1)" class="action-btn">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <el-button @click="handleManageMembers" class="action-btn">
            <el-icon><UserFilled /></el-icon>
            管理成员
          </el-button>
          <el-button type="primary" @click="handleEditGroup" class="action-btn">
            <el-icon><Edit /></el-icon>
            编辑群组
          </el-button>
          <el-dropdown @command="handleCommand">
            <el-button class="action-btn">
              <el-icon><More /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="export">导出数据</el-dropdown-item>
                <el-dropdown-item command="share">分享群组</el-dropdown-item>
                <el-dropdown-item command="archive">归档群组</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除群组</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 数据统计区域 -->
    <div class="stats-grid">
      <div 
        v-for="stat in groupStats" 
        :key="stat.key"
        class="stat-card modern-card hover-lift"
      >
        <div class="stat-header">
          <div class="stat-icon" :style="{ background: stat.gradient }">
            <el-icon size="24">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-grid">
      <!-- 左侧内容 -->
      <div class="content-left">
        <!-- 活动时间线 -->
        <div class="timeline-card modern-card">
          <div class="card-header">
            <h3>活动时间线</h3>
            <el-button text @click="loadMoreActivities">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <div class="timeline-content">
            <el-timeline v-loading="timelineLoading">
              <el-timeline-item
                v-for="activity in activities"
                :key="activity.id"
                :timestamp="formatTime(activity.createdAt)"
                :type="getActivityType(activity.type)"
                :icon="getActivityIcon(activity.type)"
              >
                <div class="activity-content">
                  <div class="activity-header">
                    <span class="activity-title">{{ activity.title }}</span>
                    <el-tag size="small" :type="getActivityTagType(activity.type)">
                      {{ activity.typeName }}
                    </el-tag>
                  </div>
                  <p class="activity-description">{{ activity.description }}</p>
                  <div v-if="activity.metadata" class="activity-metadata">
                    <span v-for="(value, key) in activity.metadata" :key="key" class="metadata-item">
                      {{ key }}: {{ value }}
                    </span>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
            <div v-if="!hasMoreActivities" class="no-more-data">
              <el-icon><InfoFilled /></el-icon>
              <span>没有更多活动记录</span>
            </div>
          </div>
        </div>

        <!-- 成员概览 -->
        <div class="members-card modern-card">
          <div class="card-header">
            <h3>成员概览</h3>
            <el-button text @click="$router.push(`/community/group/${groupId}/members`)">
              查看全部
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div class="members-content">
            <div class="member-item" v-for="member in topMembers" :key="member.id">
              <el-avatar :src="member.avatar" :size="40">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="member-info">
                <div class="member-name">{{ member.name }}</div>
                <div class="member-role">{{ member.role }}</div>
              </div>
              <div class="member-stats">
                <span class="stat-item">消息: {{ member.messageCount }}</span>
                <span class="stat-item">活跃度: {{ member.activityScore }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="content-right">
        <!-- 收益统计 -->
        <div class="revenue-card modern-card">
          <div class="card-header">
            <h3>收益统计</h3>
            <el-select v-model="revenueTimePeriod" size="small" style="width: 120px">
              <el-option label="今日" value="today" />
              <el-option label="本周" value="week" />
              <el-option label="本月" value="month" />
              <el-option label="本年" value="year" />
            </el-select>
          </div>
          <div class="revenue-content">
            <div class="revenue-chart" ref="revenueChartRef"></div>
            <div class="revenue-summary">
              <div class="summary-item">
                <span class="label">总收益</span>
                <span class="value primary">¥{{ formatNumber(revenueData.total) }}</span>
              </div>
              <div class="summary-item">
                <span class="label">平均收益</span>
                <span class="value">¥{{ formatNumber(revenueData.average) }}</span>
              </div>
              <div class="summary-item">
                <span class="label">增长率</span>
                <span class="value" :class="revenueData.growth > 0 ? 'positive' : 'negative'">
                  {{ revenueData.growth > 0 ? '+' : '' }}{{ revenueData.growth }}%
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions-card modern-card">
          <div class="card-header">
            <h3>快速操作</h3>
          </div>
          <div class="quick-actions">
            <div 
              v-for="action in quickActions" 
              :key="action.key"
              class="action-item hover-lift"
              @click="handleQuickAction(action.key)"
            >
              <div class="action-icon" :style="{ background: action.color }">
                <el-icon size="20">
                  <component :is="action.icon" />
                </el-icon>
              </div>
              <div class="action-content">
                <div class="action-title">{{ action.title }}</div>
                <div class="action-desc">{{ action.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 相关推荐 -->
        <div class="recommendations-card modern-card">
          <div class="card-header">
            <h3>相关推荐</h3>
            <el-button text size="small">
              更多推荐
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div class="recommendations-content">
            <div 
              v-for="item in recommendations" 
              :key="item.id"
              class="recommendation-item"
              @click="handleRecommendationClick(item)"
            >
              <div class="recommendation-icon">
                <el-icon size="16">
                  <component :is="item.icon" />
                </el-icon>
              </div>
              <div class="recommendation-content">
                <div class="recommendation-title">{{ item.title }}</div>
                <div class="recommendation-desc">{{ item.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 成员管理弹窗 -->
    <el-dialog
      v-model="memberManageVisible"
      title="成员管理"
      width="80%"
      :destroy-on-close="true"
    >
      <div class="member-manage-content">
        <!-- 成员管理内容 -->
        <div class="member-search">
          <el-input
            v-model="memberSearchKeyword"
            placeholder="搜索成员..."
            prefix-icon="Search"
            clearable
            @input="handleMemberSearch"
          />
        </div>
        <el-table :data="members" v-loading="membersLoading">
          <el-table-column prop="name" label="姓名" width="150">
            <template #default="{ row }">
              <div class="member-cell">
                <el-avatar :src="row.avatar" size="small">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="member-name">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="role" label="角色" width="120">
            <template #default="{ row }">
              <el-tag size="small" :type="getRoleTagType(row.role)">
                {{ getRoleName(row.role) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="joinedAt" label="加入时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.joinedAt) }}
            </template>
          </el-table-column>
          <el-table-column prop="messageCount" label="消息数" width="100" />
          <el-table-column prop="activityScore" label="活跃度" width="100">
            <template #default="{ row }">
              <div class="activity-score">
                <el-progress
                  :percentage="row.activityScore"
                  size="small"
                  :show-text="false"
                />
                <span class="score-text">{{ row.activityScore }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button size="small" @click="handleMemberEdit(row)">
                编辑
              </el-button>
              <el-button size="small" type="warning" @click="handleMemberMute(row)">
                {{ row.isMuted ? '解禁' : '禁言' }}
              </el-button>
              <el-button size="small" type="danger" @click="handleMemberRemove(row)">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="memberCurrentPage"
            v-model:page-size="memberPageSize"
            :total="memberTotal"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleMemberPageSizeChange"
            @current-change="handleMemberCurrentPageChange"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import {
  Comment, User, Calendar, Location, UserFilled, Edit, More,
  Refresh, InfoFilled, ArrowRight, Search, ArrowLeft, ArrowUp, ArrowDown,
  ChatDotRound, Money, TrendCharts, PriceTag
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const groupId = computed(() => route.params.id)

// 响应式数据
const groupInfo = ref({
  id: '',
  name: '',
  description: '',
  avatar: '',
  memberCount: 0,
  status: 'active',
  platform: '',
  tags: [],
  createdAt: ''
})

const groupStats = ref([
  {
    key: 'totalMembers',
    label: '总成员数',
    value: 0,
    icon: 'User',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+12%'
  },
  {
    key: 'activeMembers',
    label: '活跃成员',
    value: 0,
    icon: 'ChatDotRound',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+8%'
  },
  {
    key: 'totalRevenue',
    label: '总收益',
    value: '0',
    icon: 'Money',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15%'
  },
  {
    key: 'conversionRate',
    label: '转化率',
    value: '0%',
    icon: 'TrendCharts',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'down',
    trendIcon: 'ArrowDown',
    change: '-3%'
  }
])

const activities = ref([])
const timelineLoading = ref(false)
const hasMoreActivities = ref(true)

const topMembers = ref([])

const revenueTimePeriod = ref('month')
const revenueData = ref({
  total: 0,
  average: 0,
  growth: 0
})

const quickActions = ref([
  {
    key: 'sendMessage',
    title: '发送消息',
    description: '向群组发送公告',
    icon: 'ChatDotRound',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    key: 'addMember',
    title: '添加成员',
    description: '邀请新成员加入',
    icon: 'UserFilled',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  },
  {
    key: 'createEvent',
    title: '创建活动',
    description: '组织群组活动',
    icon: 'Calendar',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    key: 'viewAnalytics',
    title: '查看分析',
    description: '详细数据分析',
    icon: 'TrendCharts',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
  }
])

const recommendations = ref([
  {
    id: 1,
    title: '优化群组描述',
    description: '更好的描述能提高转化率',
    icon: 'Edit'
  },
  {
    id: 2,
    title: '设置自动回复',
    description: '提高用户体验',
    icon: 'ChatDotRound'
  },
  {
    id: 3,
    title: '添加标签',
    description: '便于分类管理',
    icon: 'PriceTag'
  }
])

// 成员管理相关
const memberManageVisible = ref(false)
const members = ref([])
const membersLoading = ref(false)
const memberSearchKeyword = ref('')
const memberCurrentPage = ref(1)
const memberPageSize = ref(20)
const memberTotal = ref(0)

const revenueChartRef = ref(null)
let revenueChart = null

// 方法
const loadGroupInfo = async () => {
  try {
    // 模拟API调用
    groupInfo.value = {
      id: groupId.value,
      name: '高端社群交流群',
      description: '专注于高质量内容分享和深度交流的社群',
      avatar: 'https://via.placeholder.com/100x100?text=Group',
      memberCount: 1234,
      status: 'active',
      platform: '微信群',
      tags: ['高端', '交流', '学习'],
      createdAt: '2024-01-15'
    }
    
    // 加载统计数据
    groupStats.value[0].value = groupInfo.value.memberCount
    groupStats.value[1].value = Math.floor(groupInfo.value.memberCount * 0.75)
    groupStats.value[2].value = formatNumber(12450.67)
    groupStats.value[3].value = '3.2%'
  } catch (error) {
    console.error('加载群组信息失败:', error)
    ElMessage.error('加载群组信息失败')
  }
}

const loadActivities = async () => {
  timelineLoading.value = true
  try {
    // 模拟API调用
    const mockActivities = [
      {
        id: 1,
        title: '新成员加入',
        description: '张三加入了群组',
        type: 'member_join',
        typeName: '成员变动',
        createdAt: new Date().toISOString(),
        metadata: { memberName: '张三', memberCount: 1235 }
      },
      {
        id: 2,
        title: '收益更新',
        description: '今日收益增长15%',
        type: 'revenue_update',
        typeName: '收益统计',
        createdAt: new Date(Date.now() - 3600000).toISOString(),
        metadata: { revenue: '¥1,250', growth: '+15%' }
      }
    ]
    
    activities.value = mockActivities
  } catch (error) {
    console.error('加载活动记录失败:', error)
  } finally {
    timelineLoading.value = false
  }
}

const loadTopMembers = async () => {
  try {
    // 模拟API调用
    topMembers.value = [
      {
        id: 1,
        name: '张三',
        avatar: 'https://via.placeholder.com/40x40?text=Z',
        role: 'admin',
        messageCount: 245,
        activityScore: 95
      },
      {
        id: 2,
        name: '李四',
        avatar: 'https://via.placeholder.com/40x40?text=L',
        role: 'member',
        messageCount: 189,
        activityScore: 78
      }
    ]
  } catch (error) {
    console.error('加载成员信息失败:', error)
  }
}

const initRevenueChart = async () => {
  if (!revenueChartRef.value) return
  
  await nextTick()
  revenueChart = echarts.init(revenueChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '收益',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          color: '#3b82f6'
        },
        areaStyle: {
          color: 'rgba(59, 130, 246, 0.1)'
        },
        data: [120, 132, 101, 134, 90, 230, 210]
      }
    ]
  }
  
  revenueChart.setOption(option)
}

// 事件处理
const handleManageMembers = () => {
  memberManageVisible.value = true
  loadMembers()
}

const handleEditGroup = () => {
  router.push('/admin/community/groups')
}

const handleCommand = (command) => {
  switch (command) {
    case 'export':
      handleExportData()
      break
    case 'share':
      handleShareGroup()
      break
    case 'archive':
      handleArchiveGroup()
      break
    case 'delete':
      handleDeleteGroup()
      break
  }
}

const handleQuickAction = (actionKey) => {
  switch (actionKey) {
    case 'sendMessage':
      // 打开发送消息弹窗
      ElMessage.info('发送消息功能开发中')
      break
    case 'addMember':
      // 打开添加成员弹窗
      ElMessage.info('添加成员功能开发中')
      break
    case 'createEvent':
      // 跳转到创建活动页面
      router.push(`/community/group/${groupId.value}/events/create`)
      break
    case 'viewAnalytics':
      // 跳转到分析页面
      router.push(`/analytics/group/${groupId.value}`)
      break
  }
}

const handleRecommendationClick = (item) => {
  ElMessage.info(`点击了推荐: ${item.title}`)
}

const loadMoreActivities = () => {
  loadActivities()
}

const loadMembers = async () => {
  membersLoading.value = true
  try {
    // 模拟API调用
    members.value = [
      {
        id: 1,
        name: '张三',
        avatar: 'https://via.placeholder.com/40x40?text=Z',
        role: 'admin',
        joinedAt: '2024-01-15',
        messageCount: 245,
        activityScore: 95,
        isMuted: false
      },
      {
        id: 2,
        name: '李四',
        avatar: 'https://via.placeholder.com/40x40?text=L',
        role: 'member',
        joinedAt: '2024-02-01',
        messageCount: 189,
        activityScore: 78,
        isMuted: false
      }
    ]
    memberTotal.value = 100
  } catch (error) {
    console.error('加载成员列表失败:', error)
  } finally {
    membersLoading.value = false
  }
}

// 工具函数
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatTime = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatNumber = (number) => {
  return number.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const getActivityType = (type) => {
  const typeMap = {
    member_join: 'success',
    member_leave: 'warning',
    revenue_update: 'primary',
    system_update: 'info'
  }
  return typeMap[type] || 'info'
}

const getActivityIcon = (type) => {
  const iconMap = {
    member_join: 'UserFilled',
    member_leave: 'User',
    revenue_update: 'Money',
    system_update: 'Setting'
  }
  return iconMap[type] || 'InfoFilled'
}

const getActivityTagType = (type) => {
  const tagTypeMap = {
    member_join: 'success',
    member_leave: 'warning',
    revenue_update: 'primary',
    system_update: 'info'
  }
  return tagTypeMap[type] || 'info'
}

const getRoleTagType = (role) => {
  const roleTypeMap = {
    admin: 'danger',
    moderator: 'warning',
    member: 'info'
  }
  return roleTypeMap[role] || 'info'
}

const getRoleName = (role) => {
  const roleNameMap = {
    admin: '管理员',
    moderator: '版主',
    member: '成员'
  }
  return roleNameMap[role] || '成员'
}

// 成员管理相关方法
const handleMemberSearch = () => {
  // 实现成员搜索逻辑
  loadMembers()
}

const handleMemberEdit = (member) => {
  ElMessage.info(`编辑成员: ${member.name}`)
}

const handleMemberMute = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要${member.isMuted ? '解禁' : '禁言'}成员 ${member.name} 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    member.isMuted = !member.isMuted
    ElMessage.success(`${member.isMuted ? '禁言' : '解禁'}成功`)
  } catch {
    // 用户取消操作
  }
}

const handleMemberRemove = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除成员 ${member.name} 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 执行删除操作
    ElMessage.success('成员已移除')
    loadMembers()
  } catch {
    // 用户取消操作
  }
}

const handleMemberPageSizeChange = (size) => {
  memberPageSize.value = size
  loadMembers()
}

const handleMemberCurrentPageChange = (page) => {
  memberCurrentPage.value = page
  loadMembers()
}

// 其他处理函数
const handleExportData = () => {
  ElMessage.info('导出功能开发中')
}

const handleShareGroup = () => {
  ElMessage.info('分享功能开发中')
}

const handleArchiveGroup = () => {
  ElMessage.info('归档功能开发中')
}

const handleDeleteGroup = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个群组吗？此操作不可撤销。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 执行删除操作
    ElMessage.success('群组已删除')
    router.push('/community/groups')
  } catch {
    // 用户取消操作
  }
}

// 生命周期
onMounted(() => {
  loadGroupInfo()
  loadActivities()
  loadTopMembers()
  initRevenueChart()
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.group-detail-page {
  padding: 24px;
  background: $bg-color;
  min-height: 100vh;
}

.group-overview-card {
  margin-bottom: 24px;
  padding: 32px;
  
  .group-header {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    
    .group-avatar {
      flex-shrink: 0;
      
      .avatar-image {
        width: 120px;
        height: 120px;
        border-radius: $border-radius-lg;
        overflow: hidden;
        
        .avatar-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: $gray-100;
          color: $text-muted-color;
        }
      }
    }
    
    .group-info {
      flex: 1;
      
      .group-title-section {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 12px;
        
        .group-name {
          font-size: $font-size-3xl;
          font-weight: $font-weight-bold;
          color: $text-color;
          margin: 0;
        }
        
        .group-badges {
          display: flex;
          gap: 8px;
          
          .group-tag {
            font-size: $font-size-xs;
          }
        }
      }
      
      .group-description {
        color: $text-secondary-color;
        font-size: $font-size-lg;
        line-height: $line-height-relaxed;
        margin-bottom: 16px;
      }
      
      .group-meta {
        display: flex;
        gap: 24px;
        
        .meta-item {
          display: flex;
          align-items: center;
          gap: 8px;
          color: $text-muted-color;
          font-size: $font-size-sm;
        }
      }
    }
    
    .group-actions {
      display: flex;
      gap: 12px;
      
      .action-btn {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  
  .stat-card {
    padding: 24px;
    
    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: $border-radius-lg;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }
      
      .stat-trend {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: $font-size-sm;
        font-weight: $font-weight-medium;
        
        &.up {
          color: $success-color;
        }
        
        &.down {
          color: $danger-color;
        }
      }
    }
    
    .stat-content {
      .stat-value {
        font-size: $font-size-4xl;
        font-weight: $font-weight-bold;
        color: $text-color;
        line-height: 1.2;
        margin-bottom: 4px;
      }
      
      .stat-label {
        color: $text-secondary-color;
        font-size: $font-size-sm;
      }
    }
  }
}

.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  
  @media (max-width: $breakpoint-lg) {
    grid-template-columns: 1fr;
  }
}

.content-left {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.content-right {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.timeline-card,
.members-card,
.revenue-card,
.quick-actions-card,
.recommendations-card {
  padding: 24px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid $border-color;
    
    h3 {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-color;
      margin: 0;
    }
  }
}

.timeline-content {
  .activity-content {
    .activity-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .activity-title {
        font-weight: $font-weight-medium;
        color: $text-color;
      }
    }
    
    .activity-description {
      color: $text-secondary-color;
      margin-bottom: 8px;
    }
    
    .activity-metadata {
      display: flex;
      gap: 16px;
      
      .metadata-item {
        font-size: $font-size-xs;
        color: $text-muted-color;
        background: $gray-100;
        padding: 4px 8px;
        border-radius: $border-radius-sm;
      }
    }
  }
  
  .no-more-data {
    text-align: center;
    color: $text-muted-color;
    padding: 20px;
    
    span {
      margin-left: 8px;
    }
  }
}

.members-content {
  .member-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 0;
    border-bottom: 1px solid $border-light-color;
    
    &:last-child {
      border-bottom: none;
    }
    
    .member-info {
      flex: 1;
      
      .member-name {
        font-weight: $font-weight-medium;
        color: $text-color;
        margin-bottom: 4px;
      }
      
      .member-role {
        color: $text-muted-color;
        font-size: $font-size-sm;
      }
    }
    
    .member-stats {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
      
      .stat-item {
        font-size: $font-size-xs;
        color: $text-muted-color;
      }
    }
  }
}

.revenue-content {
  .revenue-chart {
    height: 300px;
    margin-bottom: 20px;
  }
  
  .revenue-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    
    .summary-item {
      text-align: center;
      padding: 16px;
      background: $gray-50;
      border-radius: $border-radius;
      
      .label {
        display: block;
        color: $text-muted-color;
        font-size: $font-size-sm;
        margin-bottom: 8px;
      }
      
      .value {
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: $text-color;
        
        &.primary {
          color: $primary-color;
        }
        
        &.positive {
          color: $success-color;
        }
        
        &.negative {
          color: $danger-color;
        }
      }
    }
  }
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .action-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border: 1px solid $border-light-color;
    border-radius: $border-radius;
    cursor: pointer;
    transition: $transition-base;
    
    &:hover {
      border-color: $primary-color;
      transform: translateY(-2px);
    }
    
    .action-icon {
      width: 40px;
      height: 40px;
      border-radius: $border-radius;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      flex-shrink: 0;
    }
    
    .action-content {
      .action-title {
        font-weight: $font-weight-medium;
        color: $text-color;
        margin-bottom: 4px;
      }
      
      .action-desc {
        color: $text-muted-color;
        font-size: $font-size-sm;
      }
    }
  }
}

.recommendations-content {
  .recommendation-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    cursor: pointer;
    border-bottom: 1px solid $border-light-color;
    transition: $transition-base;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background: $gray-50;
      margin: 0 -16px;
      padding: 12px 16px;
      border-radius: $border-radius;
    }
    
    .recommendation-icon {
      width: 32px;
      height: 32px;
      border-radius: $border-radius;
      background: $primary-light-color;
      color: $primary-color;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }
    
    .recommendation-content {
      flex: 1;
      
      .recommendation-title {
        font-weight: $font-weight-medium;
        color: $text-color;
        margin-bottom: 4px;
        font-size: $font-size-sm;
      }
      
      .recommendation-desc {
        color: $text-muted-color;
        font-size: $font-size-xs;
      }
    }
  }
}

// 成员管理弹窗样式
.member-manage-content {
  .member-search {
    margin-bottom: 20px;
  }
  
  .member-cell {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .member-name {
      font-weight: $font-weight-medium;
    }
  }
  
  .activity-score {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .score-text {
      font-size: $font-size-sm;
      color: $text-muted-color;
    }
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
  }
}
</style>