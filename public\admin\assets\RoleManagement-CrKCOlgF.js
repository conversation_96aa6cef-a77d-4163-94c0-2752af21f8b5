/* empty css             *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                        *//* empty css                         *//* empty css                */import{z as e,m as a,A as t,G as l,q as s,E as i,r as n,c as o,M as r,w as d,l as c,C as u,D as m,Y as p,F as g,n as _,ag as f,o as y,W as h,B as b}from"./vue-vendor-BcnDv-68.js";import{a3 as v,a4 as w,_ as k,aL as C,a2 as D,a1 as V,Z as S,W as j,X as x,ah as z,ak as P,bZ as U,b4 as E,ax as R,ac as T,aa as L,ab as A,af as M,aw as B,cg as F,aS as I,ch as K,bH as $,b9 as O,b3 as q,aT as N,bz as G,bG as H,a9 as W,bv as Y,b8 as Z,V as J,bR as Q,aB as X,aE as ee,at as ae,b1 as te,au as le,bS as se,a_ as ie,ci as ne,az as oe,aF as re,aD as de,ag as ce,u as ue,s as me,a5 as pe,a6 as ge,ai as _e,aj as fe,aH as ye,aI as he,aJ as be,a7 as ve,an as we}from"./element-plus-C2UshkXo.js";/* empty css                   *//* empty css                     *//* empty css                       *//* empty css                 */import{_ as ke}from"./index-eUTsTR3J.js";/* empty css                *//* empty css                */import"./utils-SdQ7DxjY.js";import"./echarts-D6CUuNS9.js";const Ce={__name:"RoleDialog",props:{modelValue:{type:Boolean,default:!1},roleData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{expose:a,emit:t}){a();const l=e,s=t,i=n(),c=n(!1),u=o({get:()=>l.modelValue,set:e=>s("update:modelValue",e)}),m=o(()=>!!l.roleData?.id),p=r({name:"",display_name:"",description:"",is_system:!1,status:"active"});d(()=>l.roleData,e=>{e&&Object.keys(e).length>0?Object.assign(p,{name:e.name||"",display_name:e.display_name||"",description:e.description||"",is_system:e.is_system||!1,status:e.status||"active"}):Object.assign(p,{name:"",display_name:"",description:"",is_system:!1,status:"active"})},{immediate:!0,deep:!0});const g=()=>{i.value?.resetFields(),u.value=!1},_={props:l,emit:s,formRef:i,loading:c,visible:u,isEdit:m,form:p,rules:{name:[{required:!0,message:"请输入角色标识",trigger:"blur"},{pattern:/^[a-z_]+$/,message:"角色标识只能包含小写字母和下划线",trigger:"blur"}],display_name:[{required:!0,message:"请输入角色名称",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]},handleSubmit:async()=>{try{await i.value.validate(),c.value=!0,await new Promise(e=>setTimeout(e,1e3)),x.success(m.value?"角色更新成功":"角色创建成功"),s("success"),g()}catch(e){}finally{c.value=!1}},handleClose:g,ref:n,reactive:r,computed:o,watch:d,get ElMessage(){return x}};return Object.defineProperty(_,"__isScriptSetup",{enumerable:!1,value:!0}),_}},De={class:"dialog-footer"};const Ve=ke(Ce,[["render",function(n,o,r,d,c,u){const m=w,p=v,g=C,_=k,f=D,y=V,h=j;return a(),e(h,{modelValue:d.visible,"onUpdate:modelValue":o[5]||(o[5]=e=>d.visible=e),title:d.isEdit?"编辑角色":"创建角色",width:"600px","before-close":d.handleClose,class:"modern-dialog"},{footer:t(()=>[s("div",De,[l(y,{onClick:d.handleClose},{default:t(()=>o[12]||(o[12]=[i("取消",-1)])),_:1,__:[12]}),l(y,{type:"primary",onClick:d.handleSubmit,loading:d.loading},{default:t(()=>[i(S(d.isEdit?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:t(()=>[l(f,{ref:"formRef",model:d.form,rules:d.rules,"label-width":"100px",size:"large"},{default:t(()=>[l(p,{label:"角色标识",prop:"name"},{default:t(()=>[l(m,{modelValue:d.form.name,"onUpdate:modelValue":o[0]||(o[0]=e=>d.form.name=e),placeholder:"请输入角色标识（英文）",disabled:d.isEdit},null,8,["modelValue","disabled"]),o[6]||(o[6]=s("div",{class:"form-tip"},"角色标识用于系统内部识别，创建后不可修改",-1))]),_:1,__:[6]}),l(p,{label:"角色名称",prop:"display_name"},{default:t(()=>[l(m,{modelValue:d.form.display_name,"onUpdate:modelValue":o[1]||(o[1]=e=>d.form.display_name=e),placeholder:"请输入角色显示名称"},null,8,["modelValue"])]),_:1}),l(p,{label:"角色描述",prop:"description"},{default:t(()=>[l(m,{modelValue:d.form.description,"onUpdate:modelValue":o[2]||(o[2]=e=>d.form.description=e),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),l(p,{label:"角色类型",prop:"is_system"},{default:t(()=>[l(_,{modelValue:d.form.is_system,"onUpdate:modelValue":o[3]||(o[3]=e=>d.form.is_system=e)},{default:t(()=>[l(g,{label:!1},{default:t(()=>o[7]||(o[7]=[i("自定义角色",-1)])),_:1,__:[7]}),l(g,{label:!0},{default:t(()=>o[8]||(o[8]=[i("系统角色",-1)])),_:1,__:[8]})]),_:1},8,["modelValue"]),o[9]||(o[9]=s("div",{class:"form-tip"},"系统角色具有特殊权限，请谨慎选择",-1))]),_:1,__:[9]}),l(p,{label:"状态",prop:"status"},{default:t(()=>[l(_,{modelValue:d.form.status,"onUpdate:modelValue":o[4]||(o[4]=e=>d.form.status=e)},{default:t(()=>[l(g,{label:"active"},{default:t(()=>o[10]||(o[10]=[i("启用",-1)])),_:1,__:[10]}),l(g,{label:"inactive"},{default:t(()=>o[11]||(o[11]=[i("禁用",-1)])),_:1,__:[11]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}],["__scopeId","data-v-d864b765"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/permission/components/RoleDialog.vue"]]),Se={__name:"PermissionConfigDialog",props:{modelValue:{type:Boolean,default:!1},roleData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{expose:a,emit:t}){a();const l=e,s=t,i=n(),c=n(!1),u=n([]),m=n([]),p=o({get:()=>l.modelValue,set:e=>s("update:modelValue",e)}),g=[{id:"dashboard",label:"数据看板",icon:"Monitor",color:"#409eff",type:"模块",description:"查看系统数据统计和分析",children:[{id:"dashboard.view",label:"查看数据看板",icon:"View",type:"查看"},{id:"dashboard.export",label:"导出数据",icon:"Download",type:"操作"}]},{id:"user",label:"用户管理",icon:"User",color:"#67c23a",type:"模块",description:"管理系统用户信息和权限",children:[{id:"user.list",label:"查看用户列表",icon:"List",type:"查看"},{id:"user.create",label:"创建用户",icon:"Plus",type:"创建"},{id:"user.edit",label:"编辑用户",icon:"Edit",type:"编辑"},{id:"user.delete",label:"删除用户",icon:"Delete",type:"删除"},{id:"user.export",label:"导出用户数据",icon:"Download",type:"操作"}]},{id:"community",label:"社群管理",icon:"Comment",color:"#e6a23c",type:"模块",description:"管理微信群组和社群活动",children:[{id:"community.list",label:"查看群组列表",icon:"List",type:"查看"},{id:"community.create",label:"创建群组",icon:"Plus",type:"创建"},{id:"community.edit",label:"编辑群组",icon:"Edit",type:"编辑"},{id:"community.delete",label:"删除群组",icon:"Delete",type:"删除"},{id:"community.analytics",label:"群组数据分析",icon:"DataLine",type:"分析"}]},{id:"finance",label:"财务管理",icon:"Money",color:"#f56c6c",type:"模块",description:"管理财务数据和交易记录",children:[{id:"finance.dashboard",label:"财务总览",icon:"Monitor",type:"查看"},{id:"finance.transactions",label:"交易记录",icon:"List",type:"查看"},{id:"finance.commission",label:"佣金管理",icon:"Money",type:"管理"},{id:"finance.withdraw",label:"提现管理",icon:"Upload",type:"管理"},{id:"finance.export",label:"导出财务数据",icon:"Download",type:"操作"}]},{id:"distribution",label:"分销管理",icon:"Share",color:"#909399",type:"模块",description:"管理分销网络和代理商",children:[{id:"distribution.list",label:"分销商列表",icon:"List",type:"查看"},{id:"distribution.create",label:"添加分销商",icon:"Plus",type:"创建"},{id:"distribution.edit",label:"编辑分销商",icon:"Edit",type:"编辑"},{id:"distribution.analytics",label:"分销数据分析",icon:"DataLine",type:"分析"}]},{id:"permission",label:"权限管理",icon:"Lock",color:"#606266",type:"模块",description:"管理系统角色和权限配置",children:[{id:"permission.roles",label:"角色管理",icon:"User",type:"管理"},{id:"permission.permissions",label:"权限配置",icon:"Key",type:"配置"},{id:"permission.assign",label:"分配权限",icon:"Setting",type:"操作"}]},{id:"system",label:"系统管理",icon:"Setting",color:"#303133",type:"模块",description:"系统设置和维护功能",children:[{id:"system.settings",label:"系统设置",icon:"Tools",type:"设置"},{id:"system.logs",label:"操作日志",icon:"List",type:"查看"},{id:"system.backup",label:"数据备份",icon:"Download",type:"操作"},{id:"system.monitor",label:"系统监控",icon:"Monitor",type:"监控"}]}],f=()=>{const e=i.value?.getCheckedKeys()||[],a=i.value?.getCheckedNodes()||[];m.value=a.filter(e=>!e.children||0===e.children.length),u.value=e};d(()=>l.roleData,e=>{if(e&&e.id){const e=["dashboard.view","user.list","user.create","community.list","finance.dashboard"];_(()=>{i.value?.setCheckedKeys(e),f()})}},{immediate:!0});const y=()=>{p.value=!1},h={props:l,emit:s,treeRef:i,loading:c,checkedPermissions:u,selectedPermissionsList:m,visible:p,treeProps:{children:"children",label:"label"},permissionTree:g,getPermissionTagType:e=>({"模块":"primary","查看":"info","创建":"success","编辑":"warning","删除":"danger","操作":"primary","管理":"warning","配置":"info","分析":"success","设置":"info","监控":"warning"}[e]||""),handlePermissionCheck:(e,a)=>{f()},updateSelectedPermissions:f,removePermission:e=>{i.value?.setChecked(e,!1),f()},expandAll:()=>{const e=[],a=t=>{t.forEach(t=>{t.children&&t.children.length>0&&(e.push(t.id),a(t.children))})};a(g),_(()=>{e.forEach(e=>{const a=i.value?.getNode(e);a&&(a.expanded=!0)})})},collapseAll:()=>{const e=[],a=t=>{t.forEach(t=>{t.children&&t.children.length>0&&(e.push(t.id),a(t.children))})};a(g),_(()=>{e.forEach(e=>{const a=i.value?.getNode(e);a&&(a.expanded=!1)})})},checkAll:()=>{const e=[],a=t=>{t.forEach(t=>{e.push(t.id),t.children&&t.children.length>0&&a(t.children)})};a(g),i.value?.setCheckedKeys(e),f()},uncheckAll:()=>{i.value?.setCheckedKeys([]),f()},handleSubmit:async()=>{try{c.value=!0;i.value?.getCheckedKeys();await new Promise(e=>setTimeout(e,1e3)),x.success("权限配置保存成功"),s("success"),y()}catch(e){x.error("保存失败")}finally{c.value=!1}},handleClose:y,ref:n,reactive:r,computed:o,watch:d,nextTick:_,get ElMessage(){return x},get Key(){return Z},get Monitor(){return Y},get User(){return W},get Comment(){return H},get Share(){return G},get Money(){return N},get Lock(){return q},get Setting(){return O},get Tickets(){return $},get Tools(){return K},get CreditCard(){return I},get DataLine(){return F},get List(){return B},get Plus(){return M},get Edit(){return A},get Delete(){return L},get View(){return T},get Download(){return R},get Upload(){return E}};return Object.defineProperty(h,"__isScriptSetup",{enumerable:!1,value:!0}),h}},je={class:"permission-content"},xe={class:"permission-tree"},ze={class:"tree-header"},Pe={class:"tree-actions"},Ue={class:"tree-node"},Ee={class:"node-content"},Re={class:"node-label"},Te={key:0,class:"node-description"},Le={class:"selected-permissions"},Ae={class:"permission-tags"},Me={class:"dialog-footer"};const Be=ke(Se,[["render",function(n,o,r,d,_,f){const y=V,h=z,b=P,v=U,w=j;return a(),e(w,{modelValue:d.visible,"onUpdate:modelValue":o[0]||(o[0]=e=>d.visible=e),title:`配置角色权限 - ${r.roleData?.display_name||r.roleData?.name}`,width:"800px","before-close":d.handleClose,class:"modern-dialog permission-dialog"},{footer:t(()=>[s("div",Me,[l(y,{onClick:d.handleClose},{default:t(()=>o[6]||(o[6]=[i("取消",-1)])),_:1,__:[6]}),l(y,{type:"primary",onClick:d.handleSubmit,loading:d.loading},{default:t(()=>o[7]||(o[7]=[i(" 保存权限配置 ",-1)])),_:1,__:[7]},8,["loading"])])]),default:t(()=>[s("div",je,[s("div",xe,[s("div",ze,[o[5]||(o[5]=s("h4",null,"权限配置",-1)),s("div",Pe,[l(y,{size:"small",onClick:d.expandAll},{default:t(()=>o[1]||(o[1]=[i("全部展开",-1)])),_:1,__:[1]}),l(y,{size:"small",onClick:d.collapseAll},{default:t(()=>o[2]||(o[2]=[i("全部收起",-1)])),_:1,__:[2]}),l(y,{size:"small",type:"primary",onClick:d.checkAll},{default:t(()=>o[3]||(o[3]=[i("全选",-1)])),_:1,__:[3]}),l(y,{size:"small",onClick:d.uncheckAll},{default:t(()=>o[4]||(o[4]=[i("取消全选",-1)])),_:1,__:[4]})])]),l(v,{ref:"treeRef",data:d.permissionTree,props:d.treeProps,"show-checkbox":"","node-key":"id","default-checked-keys":d.checkedPermissions,onCheck:d.handlePermissionCheck,class:"permission-tree-component"},{default:t(({node:n,data:o})=>[s("div",Ue,[s("div",Ee,[l(h,{class:"node-icon",color:o.color||"#409eff"},{default:t(()=>[(a(),e(m(o.icon||"Key")))]),_:2},1032,["color"]),s("span",Re,S(n.label),1),o.type?(a(),e(b,{key:0,size:"small",type:d.getPermissionTagType(o.type)},{default:t(()=>[i(S(o.type),1)]),_:2},1032,["type"])):u("",!0)]),o.description?(a(),c("div",Te,S(o.description),1)):u("",!0)])]),_:1},8,["default-checked-keys"])]),s("div",Le,[s("h4",null,"已选权限 ("+S(d.selectedPermissionsList.length)+")",1),s("div",Ae,[(a(!0),c(g,null,p(d.selectedPermissionsList,s=>(a(),e(b,{key:s.id,closable:"",onClose:e=>d.removePermission(s.id),class:"permission-tag"},{default:t(()=>[l(h,null,{default:t(()=>[(a(),e(m(s.icon||"Key")))]),_:2},1024),i(" "+S(s.label),1)]),_:2},1032,["onClose"]))),128))])])])]),_:1},8,["modelValue","title"])}],["__scopeId","data-v-871756ce"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/permission/components/PermissionConfigDialog.vue"]]),Fe={class:"modern-role-management"},Ie={class:"page-header"},Ke={class:"header-content"},$e={class:"header-left"},Oe={class:"header-icon"},qe={class:"header-actions"},Ne={class:"stats-section"},Ge={class:"stats-container"},He={class:"stat-content"},We={class:"stat-value"},Ye={class:"stat-label"},Ze={class:"filter-section"},Je={class:"filter-content"},Qe={class:"filter-left"},Xe={class:"filter-right"},ea={class:"table-section"},aa={class:"table-header"},ta={class:"table-title"},la={class:"table-actions"},sa={class:"role-info"},ia={class:"role-details"},na={class:"role-name"},oa={class:"role-code"},ra={class:"time-info"},da={class:"action-buttons"},ca={class:"pagination-wrapper"};const ua=ke({__name:"RoleManagement",setup(e,{expose:a}){a();const t=f(),l=n(!1),s=n([]),i=n([]),o=n(!1),d=n(!1),c=n({}),u=n(0),m=r({keyword:"",status:"",type:""}),p=r({page:1,size:20}),g=n([{key:"total",label:"总角色数",value:8,icon:"Avatar",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"ArrowUp",change:"+2"},{key:"active",label:"启用角色",value:6,icon:"UserFilled",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"ArrowUp",change:"+1"},{key:"permissions",label:"总权限数",value:45,icon:"Key",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"ArrowUp",change:"+5"},{key:"users",label:"已分配用户",value:156,icon:"Management",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"ArrowUp",change:"+12"}]),_=[{id:1,name:"admin",display_name:"超级管理员",description:"拥有系统所有权限，可以管理所有功能模块",permissions_count:45,users_count:3,is_system:!0,status:"active",created_at:"2024-01-01 10:00:00"},{id:2,name:"substation",display_name:"分站管理员",description:"管理分站运营，拥有分站内所有权限",permissions_count:32,users_count:8,is_system:!0,status:"active",created_at:"2024-01-15 14:20:00"},{id:3,name:"agent",display_name:"代理商",description:"管理下级分销员，拥有团队管理权限",permissions_count:25,users_count:23,is_system:!0,status:"active",created_at:"2024-02-01 09:30:00"},{id:4,name:"distributor",display_name:"分销员",description:"推广群组链接，获得佣金收益",permissions_count:18,users_count:89,is_system:!0,status:"active",created_at:"2024-02-15 16:10:00"},{id:5,name:"group_owner",display_name:"群主",description:"管理自己的群组，发布群组内容",permissions_count:12,users_count:34,is_system:!0,status:"active",created_at:"2024-03-01 11:45:00"},{id:6,name:"user",display_name:"普通用户",description:"基础用户权限，可以加入群组",permissions_count:8,users_count:1234,is_system:!0,status:"active",created_at:"2024-01-01 10:00:00"},{id:7,name:"custom_role",display_name:"自定义角色",description:"用户自定义的角色，可以灵活配置权限",permissions_count:15,users_count:5,is_system:!1,status:"active",created_at:"2024-04-01 15:30:00"},{id:8,name:"test_role",display_name:"测试角色",description:"用于测试的角色，已禁用",permissions_count:10,users_count:0,is_system:!1,status:"inactive",created_at:"2024-05-01 09:15:00"}],h=async e=>{try{await ce.confirm(`确定要复制角色"${e.display_name}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});const a={...e,id:Date.now(),name:`${e.name}_copy`,display_name:`${e.display_name}_副本`,is_system:!1,users_count:0,created_at:(new Date).toISOString()};s.value.push(a),u.value++,x.success("角色复制成功")}catch(a){"cancel"!==a&&x.error("复制失败")}},b=e=>{x.info(`用户管理功能开发中... 角色：${e.display_name}`)},v=async e=>{const a="active"===e.status?"inactive":"active",t="inactive"===a?"禁用":"启用";try{await ce.confirm(`确定要${t}角色"${e.display_name}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.status=a,x.success(`${t}成功`)}catch(l){"cancel"!==l&&x.error("操作失败")}},w=async e=>{if(e.is_system)x.warning("系统角色不能删除");else try{await ce.confirm(`确定要删除角色"${e.display_name}"吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"});const a=s.value.findIndex(a=>a.id===e.id);a>-1&&(s.value.splice(a,1),u.value--),x.success("删除成功")}catch(a){"cancel"!==a&&x.error("删除失败")}},k=()=>{s.value=[..._],u.value=_.length};y(()=>{k()});const C={router:t,loading:l,roleList:s,selectedRoles:i,editDialogVisible:o,permissionDialogVisible:d,currentRole:c,total:u,searchForm:m,pagination:p,roleStats:g,mockRoleList:_,getRoleIcon:e=>({admin:"Star",substation:"Management",agent:"Avatar",distributor:"Share",group_owner:"UserFilled",user:"User",custom_role:"Setting",test_role:"Key"}[e]||"Setting"),getRoleColor:e=>({admin:"linear-gradient(135deg, #f56c6c 0%, #ff9a9e 100%)",substation:"linear-gradient(135deg, #e6a23c 0%, #ffd666 100%)",agent:"linear-gradient(135deg, #409eff 0%, #4facfe 100%)",distributor:"linear-gradient(135deg, #67c23a 0%, #43e97b 100%)",group_owner:"linear-gradient(135deg, #909399 0%, #c0c4cc 100%)",user:"linear-gradient(135deg, #909399 0%, #c0c4cc 100%)",custom_role:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",test_role:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"}[e]||"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"),formatDate:e=>e?new Date(e).toLocaleDateString("zh-CN"):"",formatTime:e=>e?new Date(e).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):"",handleCreate:()=>{c.value={},o.value=!0},handleEdit:e=>{c.value={...e},o.value=!0},handlePermissions:e=>{c.value={...e},d.value=!0},handleCommand:(e,a)=>{switch(e){case"copy":h(a);break;case"users":b(a);break;case"toggle":v(a);break;case"delete":w(a)}},handleCopy:h,handleUsers:b,handleToggleStatus:v,handleDelete:w,handleSelectionChange:e=>{i.value=e},handleBatchDelete:async()=>{if(0===i.value.length)return void x.warning("请选择要删除的角色");if(i.value.filter(e=>e.is_system).length>0)x.warning("选中的角色包含系统角色，系统角色不能删除");else try{await ce.confirm(`确定要批量删除选中的 ${i.value.length} 个角色吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),i.value.forEach(e=>{const a=s.value.findIndex(a=>a.id===e.id);a>-1&&(s.value.splice(a,1),u.value--)}),i.value=[],x.success("批量删除成功")}catch(e){"cancel"!==e&&x.error("批量删除失败")}},handleSearch:()=>{l.value=!0,setTimeout(()=>{let e=[..._];if(m.keyword&&(e=e.filter(e=>e.display_name.includes(m.keyword)||e.name.includes(m.keyword)||e.description.includes(m.keyword))),m.status&&(e=e.filter(e=>e.status===m.status)),m.type){const a="system"===m.type;e=e.filter(e=>e.is_system===a)}s.value=e,u.value=e.length,l.value=!1},500)},handleReset:()=>{m.keyword="",m.status="",m.type="",s.value=[..._],u.value=_.length,x.info("搜索条件已重置")},handlePageChange:e=>{p.page=e},handleSizeChange:e=>{p.size=e,p.page=1},handleExport:async()=>{try{x.success("导出功能开发中...")}catch(e){x.error("导出失败")}},handleEditSuccess:()=>{s.value=[..._],x.success("角色信息更新成功")},handlePermissionSuccess:()=>{s.value=[..._],x.success("权限配置更新成功")},initData:k,ref:n,reactive:r,onMounted:y,get useRouter(){return f},get ElMessage(){return x},get ElMessageBox(){return ce},get Avatar(){return de},get Download(){return R},get Plus(){return M},get Search(){return re},get RefreshLeft(){return oe},get Edit(){return A},get Delete(){return L},get Key(){return Z},get User(){return W},get MoreFilled(){return ne},get DocumentCopy(){return ie},get Lock(){return q},get Unlock(){return se},get UserFilled(){return le},get TrendCharts(){return te},get ArrowUp(){return ae},get ArrowDown(){return ee},get Star(){return X},get Setting(){return O},get Management(){return Q},RoleEditDialog:Ve,PermissionConfigDialog:Be};return Object.defineProperty(C,"__isScriptSetup",{enumerable:!1,value:!0}),C}},[["render",function(n,o,r,d,_,f){const y=z,v=V,k=w,C=ge,D=pe,j=J,x=P,U=fe,E=be,R=he,T=ye,L=_e,A=we,M=ve;return a(),c("div",Fe,[s("div",Ie,[s("div",Ke,[s("div",$e,[s("div",Oe,[l(y,{size:"24"},{default:t(()=>[l(d.Avatar)]),_:1})]),o[7]||(o[7]=s("div",{class:"header-text"},[s("h1",null,"角色管理"),s("p",null,"管理系统角色权限，配置用户访问控制和功能权限")],-1))]),s("div",qe,[l(v,{onClick:d.handleExport,class:"action-btn secondary"},{default:t(()=>[l(y,null,{default:t(()=>[l(d.Download)]),_:1}),o[8]||(o[8]=i(" 导出角色 ",-1))]),_:1,__:[8]}),l(v,{type:"primary",onClick:d.handleCreate,class:"action-btn primary"},{default:t(()=>[l(y,null,{default:t(()=>[l(d.Plus)]),_:1}),o[9]||(o[9]=i(" 创建角色 ",-1))]),_:1,__:[9]})])])]),s("div",Ne,[s("div",Ge,[(a(!0),c(g,null,p(d.roleStats,i=>(a(),c("div",{class:"stat-card",key:i.key},[s("div",{class:"stat-icon",style:ue({background:i.color})},[l(y,{size:"20"},{default:t(()=>[(a(),e(m(i.icon)))]),_:2},1024)],4),s("div",He,[s("div",We,S(i.value),1),s("div",Ye,S(i.label),1)]),s("div",{class:me(["stat-trend",i.trend])},[l(y,{size:"14"},{default:t(()=>[(a(),e(m(i.trendIcon)))]),_:2},1024),s("span",null,S(i.change),1)],2)]))),128))])]),s("div",Ze,[l(j,{class:"filter-card",shadow:"never"},{default:t(()=>[s("div",Je,[s("div",Qe,[l(k,{modelValue:d.searchForm.keyword,"onUpdate:modelValue":o[0]||(o[0]=e=>d.searchForm.keyword=e),placeholder:"搜索角色名称、标识、描述","prefix-icon":"Search",clearable:"",class:"search-input",onKeyup:h(d.handleSearch,["enter"])},null,8,["modelValue"]),l(D,{modelValue:d.searchForm.status,"onUpdate:modelValue":o[1]||(o[1]=e=>d.searchForm.status=e),placeholder:"角色状态",clearable:"",class:"filter-select"},{default:t(()=>[l(C,{label:"全部状态",value:""}),l(C,{label:"启用",value:"active"}),l(C,{label:"禁用",value:"inactive"})]),_:1},8,["modelValue"]),l(D,{modelValue:d.searchForm.type,"onUpdate:modelValue":o[2]||(o[2]=e=>d.searchForm.type=e),placeholder:"角色类型",clearable:"",class:"filter-select"},{default:t(()=>[l(C,{label:"全部类型",value:""}),l(C,{label:"系统角色",value:"system"}),l(C,{label:"自定义角色",value:"custom"})]),_:1},8,["modelValue"])]),s("div",Xe,[l(v,{onClick:d.handleSearch,type:"primary",class:"search-btn"},{default:t(()=>[l(y,null,{default:t(()=>[l(d.Search)]),_:1}),o[10]||(o[10]=i(" 搜索 ",-1))]),_:1,__:[10]}),l(v,{onClick:d.handleReset,class:"reset-btn"},{default:t(()=>[l(y,null,{default:t(()=>[l(d.RefreshLeft)]),_:1}),o[11]||(o[11]=i(" 重置 ",-1))]),_:1,__:[11]})])])]),_:1})]),s("div",ea,[l(j,{class:"table-card",shadow:"never"},{header:t(()=>[s("div",aa,[s("div",ta,[o[12]||(o[12]=s("span",null,"角色列表",-1)),l(x,{size:"small",type:"info"},{default:t(()=>[i("共 "+S(d.total)+" 个角色",1)]),_:1})]),s("div",la,[d.selectedRoles.length>0?(a(),e(v,{key:0,onClick:d.handleBatchDelete,type:"danger",size:"small",plain:""},{default:t(()=>[l(y,null,{default:t(()=>[l(d.Delete)]),_:1}),i(" 批量删除 ("+S(d.selectedRoles.length)+") ",1)]),_:1})):u("",!0)])])]),default:t(()=>[b((a(),e(L,{data:d.roleList,onSelectionChange:d.handleSelectionChange,class:"modern-table",stripe:"",border:""},{default:t(()=>[l(U,{type:"selection",width:"55",align:"center"}),l(U,{label:"角色信息","min-width":"200"},{default:t(({row:i})=>[s("div",sa,[s("div",{class:"role-icon",style:ue({background:d.getRoleColor(i.name)})},[l(y,{size:"18",color:"white"},{default:t(()=>[(a(),e(m(d.getRoleIcon(i.name))))]),_:2},1024)],4),s("div",ia,[s("div",na,S(i.display_name||i.name),1),s("div",oa,S(i.name),1)])])]),_:1}),l(U,{label:"角色描述",prop:"description","min-width":"200"}),l(U,{label:"权限数量",width:"100",align:"center"},{default:t(({row:e})=>[l(x,{type:"info",size:"small"},{default:t(()=>[i(S(e.permissions_count||0),1)]),_:2},1024)]),_:1}),l(U,{label:"用户数量",width:"100",align:"center"},{default:t(({row:e})=>[l(x,{type:"primary",size:"small"},{default:t(()=>[i(S(e.users_count||0),1)]),_:2},1024)]),_:1}),l(U,{label:"角色类型",width:"100",align:"center"},{default:t(({row:e})=>[l(x,{type:e.is_system?"warning":"success",size:"small"},{default:t(()=>[i(S(e.is_system?"系统角色":"自定义"),1)]),_:2},1032,["type"])]),_:1}),l(U,{label:"状态",width:"80",align:"center"},{default:t(({row:e})=>[l(x,{type:"active"===e.status?"success":"danger",size:"small"},{default:t(()=>[i(S("active"===e.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),l(U,{label:"创建时间",width:"160"},{default:t(({row:e})=>[s("div",ra,[s("div",null,S(d.formatDate(e.created_at)),1),s("small",null,S(d.formatTime(e.created_at)),1)])]),_:1}),l(U,{label:"操作",width:"280",fixed:"right"},{default:t(({row:n})=>[s("div",da,[l(v,{onClick:e=>d.handleEdit(n),type:"primary",size:"small",plain:""},{default:t(()=>[l(y,null,{default:t(()=>[l(d.Edit)]),_:1}),o[13]||(o[13]=i(" 编辑 ",-1))]),_:2,__:[13]},1032,["onClick"]),l(v,{onClick:e=>d.handlePermissions(n),type:"success",size:"small",plain:""},{default:t(()=>[l(y,null,{default:t(()=>[l(d.Key)]),_:1}),o[14]||(o[14]=i(" 权限 ",-1))]),_:2,__:[14]},1032,["onClick"]),l(T,{onCommand:e=>d.handleCommand(e,n),trigger:"click"},{dropdown:t(()=>[l(R,null,{default:t(()=>[l(E,{command:"copy"},{default:t(()=>[l(y,null,{default:t(()=>[l(d.DocumentCopy)]),_:1}),o[16]||(o[16]=i(" 复制角色 ",-1))]),_:1,__:[16]}),l(E,{command:"users"},{default:t(()=>[l(y,null,{default:t(()=>[l(d.User)]),_:1}),o[17]||(o[17]=i(" 用户管理 ",-1))]),_:1,__:[17]}),l(E,{command:"toggle",divided:!0},{default:t(()=>[l(y,null,{default:t(()=>[(a(),e(m("active"===n.status?"Lock":"Unlock")))]),_:2},1024),i(" "+S("active"===n.status?"禁用角色":"启用角色"),1)]),_:2},1024),n.is_system?u("",!0):(a(),e(E,{key:0,command:"delete"},{default:t(()=>[l(y,null,{default:t(()=>[l(d.Delete)]),_:1}),o[18]||(o[18]=i(" 删除角色 ",-1))]),_:1,__:[18]}))]),_:2},1024)]),default:t(()=>[l(v,{type:"info",size:"small",plain:""},{default:t(()=>[l(y,null,{default:t(()=>[l(d.MoreFilled)]),_:1}),o[15]||(o[15]=i(" 更多 ",-1))]),_:1,__:[15]})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[M,d.loading]]),s("div",ca,[l(A,{"current-page":d.pagination.page,"onUpdate:currentPage":o[3]||(o[3]=e=>d.pagination.page=e),"page-size":d.pagination.size,"onUpdate:pageSize":o[4]||(o[4]=e=>d.pagination.size=e),total:d.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d.handleSizeChange,onCurrentChange:d.handlePageChange,class:"modern-pagination"},null,8,["current-page","page-size","total"])])]),_:1})]),l(d.RoleEditDialog,{modelValue:d.editDialogVisible,"onUpdate:modelValue":o[5]||(o[5]=e=>d.editDialogVisible=e),"role-data":d.currentRole,onSuccess:d.handleEditSuccess},null,8,["modelValue","role-data"]),l(d.PermissionConfigDialog,{modelValue:d.permissionDialogVisible,"onUpdate:modelValue":o[6]||(o[6]=e=>d.permissionDialogVisible=e),"role-data":d.currentRole,onSuccess:d.handlePermissionSuccess},null,8,["modelValue","role-data"])])}],["__scopeId","data-v-3dc8a123"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/permission/RoleManagement.vue"]]);export{ua as default};
