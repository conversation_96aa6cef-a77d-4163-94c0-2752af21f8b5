// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: process.env.NODE_ENV === 'development' },

  // 应用配置
  app: {
    head: {
      title: '晨鑫流量变现系统 - 智能社群营销平台',
      titleTemplate: '%s - 晨鑫流量变现系统',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1' },
        { hid: 'description', name: 'description', content: process.env.NUXT_APP_DESCRIPTION || '晨鑫流量变现系统 - 新一代智能社群营销与多级分销平台，助力流量变现，打造专业社群商业生态' },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'theme-color', content: '#3B82F6' },
        { name: 'keywords', content: '社群营销,微信群,流量变现,分销系统,社群商业,群组推广,营销工具' },
        { name: 'author', content: '晨鑫流量变现系统' },
        { name: 'robots', content: 'index,follow' },
        { property: 'og:title', content: '晨鑫流量变现系统 - 智能社群营销平台' },
        { property: 'og:description', content: '新一代智能社群营销与多级分销平台，助力流量变现' },
        { property: 'og:type', content: 'website' },
        { property: 'og:url', content: process.env.NUXT_PUBLIC_BASE_URL || 'http://localhost:3000' },
        { property: 'og:image', content: '/og-image.jpg' },
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: '晨鑫流量变现系统 - 智能社群营销平台' },
        { name: 'twitter:description', content: '新一代智能社群营销与多级分销平台，助力流量变现' },
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' },
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
        { rel: 'dns-prefetch', href: '//www.googletagmanager.com' },
        { rel: 'manifest', href: '/manifest.json' },
      ],
      script: [
        { src: 'https://unpkg.com/eruda@latest/eruda.min.js', defer: true, body: true, 'data-env': 'development' },
      ]
    }
  },

  // CSS 配置
  css: [
    '~/assets/css/main.css',
    '~/assets/css/color-optimization.css'
  ],

  // 模块配置
  modules: [
    '@pinia/nuxt',
    '@nuxtjs/tailwindcss',
    '@vueuse/nuxt',
  ],

  // 图片优化
  image: {
    quality: 80,
    format: ['webp', 'jpg'],
    provider: 'ipx',
    ipx: {
      maxAge: 31536000,
    },
  },

  // Robots 配置
  robots: {
    UserAgent: '*',
    Allow: '/',
    Disallow: ['/admin', '/api'],
    Sitemap: (req) => `${req.headers.host}/sitemap.xml`,
  },

  // Sitemap 配置
  sitemap: {
    hostname: process.env.NUXT_PUBLIC_BASE_URL || 'http://localhost:3000',
    gzip: true,
    routes: [
      '/',
      '/browse',
      '/groups',
      '/register',
      '/login',
    ],
  },

  // PWA 配置
  pwa: {
    registerType: 'autoUpdate',
    workbox: {
      navigateFallback: '/',
      globPatterns: ['**/*.{js,css,html,png,svg,ico}'],
    },
    client: {
      installPrompt: true,
    },
    devOptions: {
      enabled: process.env.NODE_ENV === 'development',
    },
  },

  // API 配置
  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || '/api/v1',
      baseURL: process.env.NUXT_PUBLIC_BASE_URL || 'http://localhost:3000',
      appName: '晨鑫流量变现系统',
      appVersion: '2.0.0',
      environment: process.env.NODE_ENV || 'development',
      googleTagManagerId: process.env.NUXT_GTM_ID || '',
    },
    private: {
      apiSecret: process.env.NUXT_API_SECRET || '',
    }
  },

  // 构建配置
  build: {
    transpile: ['@headlessui/vue', '@heroicons/vue'],
  },

  // Vite 配置
  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "~/assets/css/tailwind.scss" as *;',
        },
      },
    },
    build: {
      rollupOptions: {
        onwarn(warning, warn) {
          if (warning.code === 'MODULE_LEVEL_DIRECTIVE') return
          if (warning.code === 'SOURCEMAP_ERROR') return
          warn(warning)
        }
      },
      chunkSizeWarningLimit: 1000,
    }
  },

  // 开发配置
  nitro: {
    devProxy: {
      '/api': {
        target: process.env.NUXT_API_PROXY_TARGET || 'http://localhost:8000',
        changeOrigin: true,
        timeout: 10000,
      }
    },
    compressPublicAssets: true,
    minify: true,
    prerender: {
      routes: ['/'],
      ignore: ['/sitemap.xml', '/robots.txt'], // 这些文件在public目录中
    },
  },

  // 路由配置
  routeRules: {
    '/': { prerender: true },
    '/browse/**': { isr: 60 },
    '/groups/**': { isr: 60 },
    '/api/**': { cors: true },
  },

  // TypeScript 配置
  typescript: {
    typeCheck: false,
    strict: true,
  },

  // 兼容性配置
  compatibilityDate: '2024-07-17',

  // 组件自动导入
  components: [
    {
      path: '~/components',
      pathPrefix: false,
    },
  ],

  // 实验性功能整合
  experimental: {
    watcher: 'chokidar',
    payloadExtraction: false,
    inlineSSRStyles: false,
    sharedPrerenderData: false,
  },

  // 开发服务器优化
  devServer: {
    port: 3001,  // 用户端使用3001端口
    host: '0.0.0.0',
  },
})