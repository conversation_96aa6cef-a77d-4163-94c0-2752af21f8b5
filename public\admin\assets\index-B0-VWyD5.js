/* empty css             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 */import{l as e,m as a,G as t,c as i}from"./vue-vendor-BcnDv-68.js";import{_ as n}from"./index-eUTsTR3J.js";import{an as p}from"./element-plus-C2UshkXo.js";const r={__name:"index",props:{total:{type:Number,required:!0,default:0},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:()=>[10,20,30,50]},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},small:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},emits:["update:page","update:limit","pagination"],setup(e,{expose:a,emit:t}){a();const n=e,p=t,r=i({get:()=>n.page,set(e){p("update:page",e)}}),o=i({get:()=>n.limit,set(e){p("update:limit",e)}}),l={props:n,emit:p,currentPage:r,pageSize:o,handleSizeChange:e=>{p("pagination",{page:r.value,limit:e})},handleCurrentChange:e=>{p("pagination",{page:e,limit:o.value})},computed:i};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}},o={class:"pagination-container"};const l=n(r,[["render",function(i,n,r,l,s,u){const d=p;return a(),e("div",o,[t(d,{"current-page":l.currentPage,"onUpdate:currentPage":n[0]||(n[0]=e=>l.currentPage=e),"page-size":l.pageSize,"onUpdate:pageSize":n[1]||(n[1]=e=>l.pageSize=e),"page-sizes":r.pageSizes,total:r.total,layout:r.layout,background:r.background,small:r.small,disabled:r.disabled,onSizeChange:l.handleSizeChange,onCurrentChange:l.handleCurrentChange},null,8,["current-page","page-size","page-sizes","total","layout","background","small","disabled"])])}],["__scopeId","data-v-8c60cd3a"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/components/Pagination/index.vue"]]);export{l as P};
