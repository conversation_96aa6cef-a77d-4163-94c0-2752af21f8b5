import{s as t}from"./index-eUTsTR3J.js";const e={getSystemInfo:()=>t({url:"/admin/system/info",method:"get"}),getSystemSettings:()=>t({url:"/admin/system/settings",method:"get"}),updateSystemSettings:e=>t({url:"/admin/system/settings",method:"put",data:e}),clearCache:()=>t({url:"/admin/system/cache/clear",method:"post"}),testPaymentConfig:(e,s)=>t({url:"/admin/system/payment/test",method:"post",data:{provider:e,config:s}})},s=e.getSystemInfo,a=e.getSystemSettings,m=e.updateSystemSettings,n=e.clearCache,i=e.testPaymentConfig;export{a,n as c,s as g,i as t,m as u};
