/* empty css             *//* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                       *//* empty css                        */import{l as e,q as a,G as t,A as l,F as r,Y as o,B as i,z as s,r as n,c,o as d,I as u,n as h,m,E as v}from"./vue-vendor-BcnDv-68.js";import{e as f,L as p}from"./echarts-D6CUuNS9.js";import{_ as b,d as g,c as y}from"./index-eUTsTR3J.js";import{g as w,f as x,a as C,l as S}from"./mapLoader-Bhh47pun.js";import{a7 as _,Z as k,_ as j,a5 as L,a1 as M,ai as D,X as R,$ as z,s as T,u as O,bE as A,a6 as F,aj as V}from"./element-plus-C2UshkXo.js";import"./utils-SdQ7DxjY.js";const B={class:"analytics-dashboard"},E={class:"page-header"},U={class:"time-selector"},W={class:"analytics-content"},$={class:"metrics-grid"},I={class:"metric-info"},P={class:"metric-value"},q={key:0},N={class:"charts-section"},X={class:"chart-card trend-chart"},G={class:"chart-header"},H={class:"chart-controls"},Q={class:"chart-content",ref:"trendChartRef"},Y={class:"chart-card map-chart"},Z={class:"chart-header"},J={class:"chart-info"},K={class:"total-users"},ee={class:"chart-content",ref:"mapChartRef"},ae={class:"detailed-section"},te={class:"chart-card behavior-chart"},le={class:"chart-content",ref:"behaviorChartRef"},re={class:"chart-card device-chart"},oe={class:"chart-content",ref:"deviceChartRef"},ie={class:"data-table-section"},se={class:"table-header"},ne={class:"table-controls"},ce={class:"table-container"};const de=b({__name:"Analytics",setup(e,{expose:a}){a();const t=n(!0),l=n(!1),r=n("30d"),o=n("visits"),i=n(null),s=n(null),m=n(null),v=n(null),b=n({}),_=n([{key:"visits",title:"总访问量",value:125847,unit:"",change:12.5,icon:"el-icon-view",color:"#3b82f6"},{key:"users",title:"活跃用户",value:8432,unit:"",change:8.2,icon:"el-icon-user",color:"#10b981"},{key:"revenue",title:"收入",value:45678,unit:"¥",change:-2.1,icon:"el-icon-money",color:"#f59e0b"},{key:"conversion",title:"转化率",value:3.24,unit:"%",change:.5,icon:"el-icon-pie-chart",color:"#ef4444"}]),k=n([]),j=c(()=>_.value.find(e=>"users"===e.key)?.value||0),L=e=>e>=1e4?(e/1e4).toFixed(1)+"w":e>=1e3?(e/1e3).toFixed(1)+"k":e.toString(),M=async()=>{t.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),D(),await h(),z()}catch(e){R.error("数据加载失败，请重试")}finally{t.value=!1}},D=()=>{k.value=[];const e=new Date;for(let o=29;o>=0;o--){const a=new Date(e);a.setDate(a.getDate()-o),k.value.push({date:a.toISOString().split("T")[0],visits:Math.floor(2e3*Math.random())+1e3,users:Math.floor(500*Math.random())+200,pageviews:Math.floor(5e3*Math.random())+2e3,conversion:(5*Math.random()+1).toFixed(2),revenue:Math.floor(1e4*Math.random())+5e3,bounce:(30*Math.random()+20).toFixed(1)})}const a=k.value.reduce((e,a)=>e+a.visits,0),t=k.value.reduce((e,a)=>e+a.users,0),l=k.value.reduce((e,a)=>e+a.revenue,0),r=(k.value.reduce((e,a)=>e+parseFloat(a.conversion),0)/k.value.length).toFixed(2);_.value[0].value=a,_.value[1].value=t,_.value[2].value=l,_.value[3].value=parseFloat(r)},z=async()=>{try{await T(),await A(),await F(),await V()}catch(e){}},T=async()=>{if(i.value)try{const e=await y(i.value);b.value.trend=e;const a=k.value.map(e=>e.date.split("-").slice(1).join("/")),t=k.value.map(e=>e.visits),l=k.value.map(e=>e.users),r={backgroundColor:"transparent",tooltip:{trigger:"axis",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"rgba(255,255,255,0.2)",textStyle:{color:"#fff"}},legend:{data:["访问量","用户数"],textStyle:{color:"#666"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:a,axisLine:{lineStyle:{color:"#e5e7eb"}},axisLabel:{color:"#6b7280"}},yAxis:{type:"value",axisLine:{lineStyle:{color:"#e5e7eb"}},axisLabel:{color:"#6b7280"},splitLine:{lineStyle:{color:"#f3f4f6"}}},series:[{name:"访问量",type:"line",data:t,smooth:!0,lineStyle:{color:"#3b82f6",width:3},itemStyle:{color:"#3b82f6"},areaStyle:{color:new p(0,0,0,1,[{offset:0,color:"rgba(59, 130, 246, 0.3)"},{offset:1,color:"rgba(59, 130, 246, 0.05)"}])}},{name:"用户数",type:"line",data:l,smooth:!0,lineStyle:{color:"#10b981",width:3},itemStyle:{color:"#10b981"}}]};e.setOption(r)}catch(e){}},O=(e,a)=>function(t){const l=(e[t.name]||0)/a;return["北京","上海","广东","江苏","浙江","山东","河南","四川","湖北","湖南","河北","福建","安徽","辽宁","陕西"].includes(t.name)||l>.3?t.name:""},A=async()=>{if(s.value)try{if(!(await S("local")))return;const e=await y(s.value);b.value.map=e;const a=C(),t=x(a),l=Math.max(...Object.values(a)),r={backgroundColor:"transparent",tooltip:{trigger:"item",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"rgba(255,255,255,0.2)",textStyle:{color:"#fff"},formatter:function(e){try{return e&&e.name?e.data&&void 0!==e.data.value&&null!==e.data.value&&"number"==typeof e.data.value?`${e.name}<br/>用户数: ${e.data.value.toLocaleString()}`:`${e.name}<br/>暂无数据`:"数据加载中..."}catch(a){return"数据显示异常"}}},visualMap:{min:Math.min(...Object.values(a)),max:l,left:20,bottom:20,text:["高","低"],textStyle:{color:"#666",fontSize:12},inRange:{color:["#e6f3ff","#3b82f6"]},calculable:!0,orient:"horizontal",itemWidth:15,itemHeight:100},series:[{name:"用户分布",type:"map",map:"china",roam:!1,zoom:1.4,center:[104,35.5],data:t,label:{show:!0,color:"#333",fontSize:11,fontWeight:"bold",textBorderColor:"rgba(255,255,255,0.8)",textBorderWidth:1,formatter:O(a,l)},itemStyle:{borderColor:"#fff",borderWidth:1,areaColor:"#f0f9ff"},emphasis:{label:{show:!0,color:"#fff",fontSize:13,fontWeight:"bold",textBorderColor:"rgba(0,0,0,0.8)",textBorderWidth:2,formatter:function(e){return e.name}},itemStyle:{areaColor:"#3b82f6",borderColor:"#1d4ed8",borderWidth:2,shadowBlur:10,shadowColor:"rgba(59, 130, 246, 0.5)"}}}]};e.setOption(r)}catch(e){}},F=async()=>{if(m.value)try{const e=await y(m.value);b.value.behavior=e;const a={backgroundColor:"transparent",tooltip:{trigger:"item",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"rgba(255,255,255,0.2)",textStyle:{color:"#fff"}},legend:{orient:"vertical",left:"left",textStyle:{color:"#666"}},series:[{name:"用户行为",type:"pie",radius:["40%","70%"],center:["60%","50%"],data:[{value:1048,name:"页面浏览",itemStyle:{color:"#3b82f6"}},{value:735,name:"搜索",itemStyle:{color:"#10b981"}},{value:580,name:"下载",itemStyle:{color:"#f59e0b"}},{value:484,name:"分享",itemStyle:{color:"#ef4444"}},{value:300,name:"其他",itemStyle:{color:"#8b5cf6"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};e.setOption(a)}catch(e){}},V=async()=>{if(v.value)try{const e=await y(v.value);b.value.device=e;const a={backgroundColor:"transparent",tooltip:{trigger:"axis",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"rgba(255,255,255,0.2)",textStyle:{color:"#fff"}},legend:{data:["桌面端","移动端","平板"],textStyle:{color:"#666"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"],axisLine:{lineStyle:{color:"#e5e7eb"}},axisLabel:{color:"#6b7280"}},yAxis:{type:"value",axisLine:{lineStyle:{color:"#e5e7eb"}},axisLabel:{color:"#6b7280"},splitLine:{lineStyle:{color:"#f3f4f6"}}},series:[{name:"桌面端",type:"bar",data:[320,332,301,334,390,330,320],itemStyle:{color:"#3b82f6"}},{name:"移动端",type:"bar",data:[220,182,191,234,290,330,310],itemStyle:{color:"#10b981"}},{name:"平板",type:"bar",data:[150,232,201,154,190,330,410],itemStyle:{color:"#f59e0b"}}]};e.setOption(a)}catch(e){}};d(async()=>{await M(),window.addEventListener("resize",()=>{Object.values(b.value).forEach(e=>{e&&e.resize&&e.resize()})})}),u(()=>{Object.values(b.value).forEach(async e=>{e&&await g(e)})});const B={isLoading:t,isRefreshing:l,selectedTimeRange:r,trendChartType:o,trendChartRef:i,mapChartRef:s,behaviorChartRef:m,deviceChartRef:v,charts:b,metricsData:_,tableData:k,totalMapUsers:j,formatMetricValue:(e,a)=>"¥"===a?`¥${L(e)}`:"%"===a?`${e}%`:L(e),formatNumber:L,handleTimeRangeChange:e=>{M()},exportData:()=>{R.success("数据导出功能开发中...")},refreshData:async()=>{l.value=!0,await M(),l.value=!1,R.success("数据已刷新")},loadAnalyticsData:M,generateMockData:D,initCharts:z,initTrendChart:T,getSmartLabelFormatter:O,initMapChart:A,initBehaviorChart:F,initDeviceChart:V,ref:n,onMounted:d,onUnmounted:u,computed:c,nextTick:h,get ElMessage(){return R},get echarts(){return f},get createEChartsInstance(){return y},get disposeEChartsInstance(){return g},get loadChinaMap(){return S},get getProvinceDataMap(){return C},get formatMapData(){return x},get generateVisualMap(){return w}};return Object.defineProperty(B,"__isScriptSetup",{enumerable:!1,value:!0}),B}},[["render",function(n,c,d,u,h,f){const p=z,b=j,g=A,y=F,w=L,x=M,C=V,S=D,R=_;return m(),e("div",B,[a("div",E,[c[5]||(c[5]=a("div",{class:"header-left"},[a("h1",{class:"page-title"},"📊 数据分析"),a("p",{class:"page-description"},"深入了解系统运行数据和用户行为分析")],-1)),a("div",U,[t(b,{modelValue:u.selectedTimeRange,"onUpdate:modelValue":c[0]||(c[0]=e=>u.selectedTimeRange=e),onChange:u.handleTimeRangeChange},{default:l(()=>[t(p,{label:"7d"},{default:l(()=>c[2]||(c[2]=[v("最近7天",-1)])),_:1,__:[2]}),t(p,{label:"30d"},{default:l(()=>c[3]||(c[3]=[v("最近30天",-1)])),_:1,__:[3]}),t(p,{label:"90d"},{default:l(()=>c[4]||(c[4]=[v("最近90天",-1)])),_:1,__:[4]})]),_:1},8,["modelValue"])])]),a("div",W,[a("div",$,[(m(!0),e(r,null,o(u.metricsData,t=>(m(),e("div",{key:t.key,class:T(["metric-card",{loading:u.isLoading}])},[a("div",{class:"metric-icon",style:O({backgroundColor:t.color+"20",color:t.color})},[a("i",{class:T(t.icon)},null,2)],4),a("div",I,[a("h3",null,k(t.title),1),a("p",P,[u.isLoading?(m(),s(g,{key:1,variant:"text",style:{width:"80px"}})):(m(),e("span",q,k(u.formatMetricValue(t.value,t.unit)),1))]),u.isLoading?(m(),s(g,{key:1,variant:"text",style:{width:"50px"}})):(m(),e("span",{key:0,class:T(["metric-change",t.change>=0?"positive":"negative"])},[a("i",{class:T(t.change>=0?"el-icon-arrow-up":"el-icon-arrow-down")},null,2),v(" "+k(Math.abs(t.change))+"% ",1)],2))])],2))),128))]),a("div",N,[a("div",X,[a("div",G,[c[6]||(c[6]=a("h3",null,"访问趋势",-1)),a("div",H,[t(w,{modelValue:u.trendChartType,"onUpdate:modelValue":c[1]||(c[1]=e=>u.trendChartType=e),size:"small",style:{width:"120px"}},{default:l(()=>[t(y,{label:"访问量",value:"visits"}),t(y,{label:"用户数",value:"users"}),t(y,{label:"页面浏览",value:"pageviews"})]),_:1},8,["modelValue"])])]),i(a("div",Q,null,512),[[R,u.isLoading]])]),a("div",Y,[a("div",Z,[c[7]||(c[7]=a("h3",null,"用户地理分布",-1)),a("div",J,[a("span",K,"总用户: "+k(u.formatNumber(u.totalMapUsers)),1)])]),i(a("div",ee,null,512),[[R,u.isLoading]])])]),a("div",ae,[a("div",te,[c[8]||(c[8]=a("h3",null,"用户行为分析",-1)),i(a("div",le,null,512),[[R,u.isLoading]])]),a("div",re,[c[9]||(c[9]=a("h3",null,"设备分析",-1)),i(a("div",oe,null,512),[[R,u.isLoading]])])]),a("div",ie,[a("div",se,[c[12]||(c[12]=a("h3",null,"详细数据",-1)),a("div",ne,[t(x,{size:"small",onClick:u.exportData},{default:l(()=>c[10]||(c[10]=[a("i",{class:"el-icon-download"},null,-1),v(" 导出数据 ",-1)])),_:1,__:[10]}),t(x,{size:"small",onClick:u.refreshData},{default:l(()=>[a("i",{class:T(["el-icon-refresh",{spinning:u.isRefreshing}])},null,2),c[11]||(c[11]=v(" 刷新 ",-1))]),_:1,__:[11]})])]),a("div",ce,[i((m(),s(S,{data:u.tableData,stripe:"",style:{width:"100%"}},{default:l(()=>[t(C,{prop:"date",label:"日期",width:"120"}),t(C,{prop:"visits",label:"访问量",width:"100"}),t(C,{prop:"users",label:"用户数",width:"100"}),t(C,{prop:"pageviews",label:"页面浏览",width:"120"}),t(C,{prop:"conversion",label:"转化率",width:"100"},{default:l(e=>[v(k(e.row.conversion)+"% ",1)]),_:1}),t(C,{prop:"revenue",label:"收入",width:"120"},{default:l(e=>[v(" ¥"+k(u.formatNumber(e.row.revenue)),1)]),_:1}),t(C,{prop:"bounce",label:"跳出率",width:"100"},{default:l(e=>[v(k(e.row.bounce)+"% ",1)]),_:1})]),_:1},8,["data"])),[[R,u.isLoading]])])])])])}],["__scopeId","data-v-5f29bccc"],["__file","C:/Users/<USER>/Desktop/ffjq/admin/src/views/dashboard/Analytics.vue"]]);export{de as default};
